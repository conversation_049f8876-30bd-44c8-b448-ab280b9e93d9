module.exports = {
  root: true,
  env: {
    node: true,
  },
  extends: [
    'airbnb-base',
    'plugin:vue/recommended',
    'eslint:recommended',
  ],
  globals: {
    APP_VERSION: 'readonly',
  },
  parserOptions: {
    parser: '@babel/eslint-parser',
  },
  settings: {
    'import/resolver': {
      alias: {
        map: [
          [
            '@',
            './src',
          ],
          [
            '@components',
            './src/components',
          ],
          [
            '@views',
            './src/views',
          ],
          [
            '@plugins',
            './src/plugins',
          ],
          [
            '@store',
            './src/store',
          ],
        ],
      },
    },
  },
  rules: {
    'vue/no-empty-component-block': 2,
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'warn',
    'vue/attribute-hyphenation': 'error',
  },
};
