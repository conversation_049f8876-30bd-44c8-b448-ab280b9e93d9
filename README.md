# cm-front

## Project setup
```bash
yarn install
```

### Compiles and hot-reloads for development
```bash
yarn serve
```

### Compiles and minifies for production
```bash
yarn build
```

### Lints and fixes files
```bash
yarn lint
```

### weryfikacja tłumaczeń

Instalacja pakietów:

```bash
pip install gspread ruamel_yaml oauth2client
```

weryfikacja tłumaczeń:

```bash
python .\checkLang.py
```

import tłumaczeń z pliku https://docs.google.com/spreadsheets/d/1Rbg0nVgbAjPk4cY9bLZu4qS8_Xm9c96SP3G7QoZdqzI/edit#gid=0

```bash
python .\sync-translations.py
```
