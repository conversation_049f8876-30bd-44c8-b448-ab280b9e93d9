#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import yaml

lang = 'cs'
preview_lang = 'en'

file_path_new = './trans2/new/' + lang + '.yaml'
with open(file_path_new, 'r', encoding='utf-8') as file:
    yaml_data_new = yaml.safe_load(file)

file_path_old = './trans2/old/' + lang + '.yaml'
with open(file_path_old, 'r', encoding='utf-8') as file:
    yaml_data_old = yaml.safe_load(file)

file_path_preview = './trans2/new/' + preview_lang + '.yaml'
with open(file_path_preview, 'r', encoding='utf-8') as file:
    yaml_data_preview = yaml.safe_load(file)

def load_yaml_translations(yaml_data):
    return extract_keys_from_yaml(yaml_data)

def extract_keys_from_yaml(data, parent_key='', sep='.'):
    """Rekursywne wydobywanie kluczy z zagnieżdżonych struktur YAML."""
    keys = set()
    if isinstance(data, dict):
        for k, v in data.items():
            full_key = f"{parent_key}{sep}{k}" if parent_key else k
            keys.add(full_key)
            keys |= extract_keys_from_yaml(v, full_key, sep=sep)
    return keys

old_keys = load_yaml_translations(yaml_data_old)
new_keys = load_yaml_translations(yaml_data_new)

missing_translations = new_keys - old_keys

# tłumaczenia
# print(len(missing_translations))

missing_translations = sorted(missing_translations)

for key in missing_translations:
    preview_value = yaml_data_preview.get(key, '')
    new_value = yaml_data_new.get(key, '')
    print(key + ';' + yaml_data_new[key] + ';' + preview_value)
