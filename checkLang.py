#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import sys
import yaml

langs = ['cs']

# przekazanie parametru z językiem
if len(sys.argv) > 1:
    langs = sys.argv[1:]

missing_keys = {
    'turnover.devices.vacuum.singular',
    'turnover.devices.vacuum.plural',
    'subscriptions.types.basic',
    'subscriptions.types.free',
    'subscriptions.types.premium',
    'turnover.devices.distributor.plural',
    'turnover.devices.distributor.singular',
    'turnover.devices.stand.plural',
    'turnover.devices.stand.singular',
    'subscription.table.STANDARD',
    'subscription.table.UNSUBSCRIBED',
    'subscription.table.WARRANTY',
    'subscription.carwash-type.STANDARD',
    'subscription.carwash-type.UNSUBSCRIBED',
    'subscription.carwash-type.WARRANTY',
    'service.status.desc.completed',
    'service.status.desc.open',
    'moneycollect.devices.CAR_WASH',
    'moneycollect.devices.DISTRIBUTOR',
    'moneycollect.devices.VACUUM_CLEANER',
    'modal.loyal_card_top_up_invoice.error.owner_no_country',
    'modal.loyal_card_top_up_invoice.error.card_no_carwash',
    'modal.loyal_card_top_up_invoice.error.card_no_client',
    'modal.loyal_card_top_up_invoice.error.invoice_exists',
    'modal.loyal_card_top_up_invoice.error.invoice_generation_failed',
    'modal.loyal_card_top_up_invoice.error.invoice_not_generated',
    'modal.loyal_card_top_up_invoice.error.invoice_send_failed',
    'loyalsystem-widget.values-name.cardsTopUpSumFromCM',
    'loyalsystem-widget.values-name.cardsTopUpSumFromCarwash',
    'loyalsystem-widget.values-name.cardsUsedCount',
    'loyalsystem-widget.values-name.transactionsPayments',
    'loyalsystem-widget.values-name.transactionsTopups',
    'form.validation.file_max_size_mb',
    'fiscal_transactions.type.BANKCARDS',
    'fiscal_transactions.type.BANK_CARDS',
    'fiscal_transactions.type.CARWASH_MANAGER',
    'fiscal_transactions.type.CASH',
    'fiscal_transactions.type.CASHLESS',
    'fiscal_transactions.type.CHARGE_BAY',
    'fiscal_transactions.type.COINS',
    'fiscal_transactions.type.HOPPER_A',
    'fiscal_transactions.type.HOPPER_B',
    'fiscal_transactions.type.LOYALTY_PAYING',
    'fiscal_transactions.type.LOYALTY_PROMO',
    'fiscal_transactions.type.LOYALTY_RECHARGE',
    'fiscal_transactions.type.LOYALTY_SELLING',
    'fiscal_transactions.type.MOBILE',
    'fiscal_transactions.type.NOTES',
    'fiscal_transactions.type.PROMO',
    'fiscal_transactions.type.SERVICE',
    'fiscal_transactions.type.TOKENS',
    'fiscal_transactions.source.TERMINAL',
    'date.length.12m',
    'date.length.1m',
    'date.length.3m',
    'date.length.6m',
    'dashboard.moneycollect.CAR_WASH',
    'dashboard.moneycollect.MONEY_CHANGER',
    'dashboard.moneycollect.YETI',
    'client-modal.invoice.strategies.undefined',
    'actions.actualize',
    'actions.export_csv',
    'actions.export_pdf',
    'actions.export_transactions',
    'actions.export_xlsx',
    'actions.lock',
    'actions.show-more-details',
    'actions.unlock',
    'administration.subscription.status.error',
    'administration.subscription.status.initiated_proforma',
    'administration.subscription.status.manually_canceled',
    'Czechy',
}

return_error = 0

def check_translations_by_lang(lang):
    # Ścieżki
    vue_files_path = './src'
    yaml_file_path = './src/i18n/' + lang + '.yaml'

    # Regex do wyszukiwania odwołań do tłumaczeń w Vue
    vue_regex = re.compile(r"\$t\(\s?['\"](.*?)['\"]\s?\)")

    def find_translations_in_vue_files(path, regex):
        """Wyszukuje odwołania do tłumaczeń w plikach Vue."""
        translations = set()
        for root, _, files in os.walk(path):
            for file in files:
                if file.endswith('.vue'):
                    with open(os.path.join(root, file), 'r', encoding='utf-8') as f:
                        content = f.read()
                        matches = regex.findall(content)
                        translations.update(matches)
        return translations

    def load_yaml_translations(file_path):
        """Wczytuje klucze tłumaczeń z pojedynczego pliku YAML."""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = yaml.safe_load(f)
            return extract_keys_from_yaml(content)

    def extract_keys_from_yaml(data, parent_key='', sep='.'):
        """Rekursywne wydobywanie kluczy z zagnieżdżonych struktur YAML."""
        keys = set()
        if isinstance(data, dict):
            for k, v in data.items():
                full_key = f"{parent_key}{sep}{k}" if parent_key else k
                keys.add(full_key)
                keys |= extract_keys_from_yaml(v, full_key, sep=sep)
        return keys

    # Przetwarzanie
    vue_translations = find_translations_in_vue_files(vue_files_path, vue_regex).union(missing_keys)
    yaml_translations = load_yaml_translations(yaml_file_path)

    # Porównywanie kluczy
    missing_translations = vue_translations - yaml_translations
    unused_translations = yaml_translations - vue_translations - missing_keys

    dekorator="######################################"
    
    print(dekorator)
    print(" Sprawdzenie tłumaczeń ", lang)
    print("\n Brakujące tłumaczenia: ", len(missing_translations))
#     missing_translations = sorted(missing_translations)
#     print("\n".join(missing_translations))

    print("\n Niewykorzystane tłumaczenia: ", len(unused_translations))
    print(dekorator, "\n")
#     unused_translations = sorted(unused_translations)
#     print("\n".join(unused_translations))

    if (len(unused_translations) + len(missing_translations) > 0):
        return 1
    return 0

for lang in langs:
    return_error = check_translations_by_lang(lang) or return_error

sys.exit(return_error)