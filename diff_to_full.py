#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import yaml

lang = 'ru'
full_lang = 'pl'

file_path = './trans2/' + lang + '.yaml'
with open(file_path, 'r', encoding='utf-8') as file:
    yaml_data = yaml.safe_load(file)

file_path_full = './trans2/' + full_lang + '.yaml'
with open(file_path_full, 'r', encoding='utf-8') as file:
    yaml_data_full = yaml.safe_load(file)

def load_yaml_translations(yaml_data):
    return extract_keys_from_yaml(yaml_data)

def extract_keys_from_yaml(data, parent_key='', sep='.'):
    """Rekursywne wydobywanie kluczy z zagnieżdżonych struktur YAML."""
    keys = set()
    if isinstance(data, dict):
        for k, v in data.items():
            full_key = f"{parent_key}{sep}{k}" if parent_key else k
            keys.add(full_key)
            keys |= extract_keys_from_yaml(v, full_key, sep=sep)
    return keys

keys = load_yaml_translations(yaml_data)
keys_full = load_yaml_translations(yaml_data_full)

missing_translations = keys_full - keys

# tłumaczenia
# print(len(missing_translations))

missing_translations = sorted(missing_translations)

print(yaml_data)

for key in missing_translations:
    value = yaml_data.get(key, '')
    full_value = yaml_data_full.get(key,'')
    print(key + ';' + value + ';' + full_value)
