const { defineConfig } = require('@vue/cli-service');
const path = require('path');
const pkg = require('./package.json');

module.exports = defineConfig({
  productionSourceMap: false,
  transpileDependencies: [
    'vuetify',
  ],

  chainWebpack: (config) => {
    // Inject app version using DefinePlugin via chainWebpack
    config.plugin('define').tap((args) => {
      const [definitions] = args;
      const newDefinitions = {
        ...definitions,
        APP_VERSION: JSON.stringify(pkg.version),
      };
      return [newDefinitions];
    });

    // Configure aliases
    config.resolve.alias.merge({
      '@translations': path.resolve(__dirname, './src/translations'),
      '@components': path.resolve(__dirname, './src/components'),
      '@plugins': path.resolve(__dirname, './src/plugins'),
      '@core': path.resolve(__dirname, './src/core'),
      '@pages': path.resolve(__dirname, './src/pages'),
      '@store': path.resolve(__dirname, './src/store'),
      '@assets': path.resolve(__dirname, './src/assets'),
      '@views': path.resolve(__dirname, './src/views'),
    });

    // Configure translations' rules
    config.module
      .rule('yaml')
      .test(/\.ya?ml$/)
      .use('yaml-loader')
      .loader('yaml-loader');
  },

  pluginOptions: {
    i18n: {
      enableInSFC: false,
      includeLocales: false,
      enableBridge: false,
    },
  },
});
