{"name": "cm-front", "version": "1.0.4", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "fix": "vue-cli-service lint --fix", "i18n:report": "vue-cli-service i18n:report --src \"./src/**/*.?(js|vue)\" --locales \"./src/locales/**/*.json\""}, "dependencies": {"@sentry/tracing": "^7.14.1", "@sentry/vue": "^7.27.0", "axios": "^0.27.2", "babel-runtime": "^6.26.0", "core-js": "^3.25.1", "date-fns": "^1.30.1", "echarts": "5.3.3", "echarts-simple-transform": "^1.0.0", "es7-sleep": "^1.0.0", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.37", "string-to-color": "^2.2.2", "vee-validate": "^2.2.15", "vue": "^2.7.8", "vue-axios": "^3.4.1", "vue-echarts": "^6.2.3", "vue-i18n": "^8.27.2", "vue-markdown": "^2.2.4", "vue-router": "^3.6.5", "vue2-google-maps": "^0.10.7", "vuetify": "^2.6.10", "vuex": "^3.6.2", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.19.1", "@babel/eslint-parser": "^7.19.1", "@intlify/vue-i18n-loader": "^3.3.0", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-plugin-router": "~5.0.8", "@vue/cli-plugin-vuex": "~5.0.8", "@vue/cli-service": "~5.0.8", "@vue/eslint-config-airbnb": "^6.0.0", "eslint": "^7.32.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-import-resolver-webpack": "^0.13.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-vue": "^8.7.1", "eslint-plugin-vuejs-accessibility": "^1.2.0", "json-loader": "^0.5.7", "sass": "^1.32.7", "sass-loader": "^12.6.0", "stylus": "^0.59.0", "stylus-loader": "^7.0.0", "vue-cli-plugin-i18n": "~2.3.1", "vue-cli-plugin-vuetify": "^2.5.8", "vue-template-compiler": "^2.7.10", "vuetify-loader": "^1.9.2", "yaml-loader": "^0.8.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "engines": {"node": ">=16.0.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}