import ruamel.yaml
import sys
import re

lang = 'ru'

if len(sys.argv) == 2:
    lang = sys.argv[1]

regex_check = re.compile(r"(.*[-,\#\.:\{\}].*)")

def flatten_dict(d, parent_key='', sep='.'):
    items = []
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        else:
            items.append((new_key, v))
    return dict(items)

def convert_yaml_file_to_flattened(filename):
    with open(filename, 'r') as file:
        yaml = ruamel.yaml.YAML()
        data = yaml.load(file)
        flattened = flatten_dict(data)
        return flattened

def print_flattened_data_sorted(flattened_data):
    for key in sorted(flattened_data.keys()):
        value = add_quotes(flattened_data[key])
        print(f"{key}: {value}")

# method to quote needed translation values
def add_quotes(value):
    matches = regex_check.findall(value)
    if len(matches) > 0:
        value = f"'{matches[0]}'"

    return value

filename = lang + ".yaml"

flattened_data = convert_yaml_file_to_flattened(filename)

print_flattened_data_sorted(flattened_data)