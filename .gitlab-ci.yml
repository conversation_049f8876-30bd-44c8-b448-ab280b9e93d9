stages:
  - lint
  - build
  - deploy

lint_cm:
  stage: lint
  cache:
    - key:
        files:
          - yarn.lock
      paths:
        - node_modules/
        - .yarn
  before_script:
    - yarn install --cache-folder .yarn
  script:
    - yarn lint
  tags:
    - i2m_dev_server_shell

.check_translations_template:
  script:
    - ./checkLang.py $LANG

check_translations:
  stage: lint
  allow_failure: true
  parallel:
    matrix:
      - LANG: "pl"
      - LANG: "en"
      - LANG: "cs"
      - LANG: "ru"
      - LANG: "hr"
      - LANG: "lt"
  environment: $LANG
  extends: .check_translations_template
  tags:
    - i2m_dev_server_shell

build_cm_front:
  stage: build
  script:
    - yarn
    - yarn build
    - tar czvf cm-front.tar.gz dist/
  artifacts:
    paths:
      - cm-front.tar.gz
  when: on_success
  tags:
    - i2m_dev_server_shell

deploy_cm_web:
  stage: deploy
  only:
    - master
  when: on_success
  dependencies:
    - build_cm_front
  variables:
    APP_DIR: /srv/${CI_PROJECT_NAME}
    REV_DIR: ${APP_DIR}/${CI_JOB_ID}
  script:
    - mkdir -p ${REV_DIR}
    - tar xvzf cm-front.tar.gz -C ${REV_DIR}
    - cp -a ${REV_DIR}/dist/. ${REV_DIR} && rm -r ${REV_DIR}/dist
    - ln -sfn ${CI_JOB_ID} ${APP_DIR}/latest
  tags:
    - cm-web

deploy_cm_review:
  stage: deploy
  when: manual
  when: on_success
  dependencies:
    - build_cm_front
  variables:
    APP_DIR: /srv/${CI_PROJECT_NAME}
    REV_DIR: ${APP_DIR}/${CI_JOB_ID}
  script:
    - mkdir -p ${REV_DIR}
    - tar xvzf cm-front.tar.gz -C ${REV_DIR}
    - cp -a ${REV_DIR}/dist/. ${REV_DIR} && rm -r ${REV_DIR}/dist
    - ln -sfn ${CI_JOB_ID} ${APP_DIR}/review
  tags:
    - cm-web
