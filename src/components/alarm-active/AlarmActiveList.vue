<template>
  <div>
    <v-card-text>
      <v-data-table
        :headers="dataTable.headers"
        :items="alarms"
        :hide-default-footer="true"
        :loading="loading"
        :single-expand="true"
        :expanded.sync="expanded"
      >
        <template #item="{ item, expand, isExpanded }">
          <tr
            @click="onRowClick({item, expand, isExpanded})"
          >
            <td>{{ item.alarmDef.id }}</td>
            <td>
              <v-tooltip
                v-if="item.alarmDef.docUrl"
              >
                <template #activator="{ on, attrs }">
                  <v-btn
                    :key="item.alarmDef.id"
                    text
                    elevation="0"
                    v-bind="attrs"
                    :href="item.alarmDef.docUrl"
                    target="_blank"
                    small
                    color="primary"
                    v-on="on"
                  >
                    {{ item.text }}
                  </v-btn>
                </template>
                <span>{{ $t('actions.show_doc') }}</span>
              </v-tooltip>
              <template v-else>
                {{ item.text }}
              </template>
            </td>
            <td>{{ item.ct === null ? $t('common_dataUnknown') : item.ct }}</td>
            <td>
              <template v-if="item.et === null && item.ct === null">
                {{ $t('common_dataUnknown') }}
              </template>
              <template v-else>
                {{ calculateDuration(item.durationTimeInSec) }}
              </template>
            </td>
            <td
              align="center"
            >
              <v-tooltip bottom>
                <template #activator="{ on }">
                  <v-icon
                    :key="item.alarmId"
                    :color="getCarwashAlertColor(item.level)"
                    v-on="on"
                  >
                    {{ getCarwashAlertIcon(item.level) }}
                  </v-icon>
                </template>
                <span>{{ item.level }}</span>
              </v-tooltip>
            </td>
            <td
              align="end"
            >
              <v-btn
                v-if="item.details"
                icon
              >
                <v-icon>{{ isExpanded ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
              </v-btn>
              <div v-else />
            </td>
          </tr>
        </template>
        <template #expanded-item="{ headers: _headers, item: _item }">
          <td
            v-if="_item.details"
            :colspan="_headers.length"
            class="elevation-3"
          >
            <v-card
              class="px-3 py-3"
              outlined
              color="blue-grey lighten-5"
            >
              <h3 class="mb-3">
                {{ $t('common_alarmDetails') }}
              </h3>
              <v-simple-table
                dense
                class="elevation-3"
              >
                <template #default>
                  <tbody
                    v-for="(value, key) in _item.details"
                    :key="key"
                  >
                    <tr>
                      <td>{{ key }}</td>
                      <td>{{ value }}</td>
                    </tr>
                  </tbody>
                </template>
              </v-simple-table>
            </v-card>
          </td>
        </template>
      </v-data-table>
    </v-card-text>
  </div>
</template>

<script>
import moment from 'moment';
import CarwashAlertTypeMixin
  from '@components/common/badge/icon-text-mixin/CarwashAlertTypeMixin.vue';

export default {
  name: 'ActiveAlarmsList',
  mixins: [
    CarwashAlertTypeMixin,
  ],
  props: {
    alarms: {
      type: Array,
      default: () => ([]),
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      expanded: [],
      carwashSerialNumber: null,
      dataTable: {
        headers: [
          {
            text: this.$t('common_alarmId'),
            value: 'alarmDef.id',
            align: 'sm-start',
            sortable: false,
          },
          {
            text: this.$t('common_tableDescription'),
            value: 'text',
            align: 'sm-start',
            sortable: false,
          },
          {
            text: this.$t('common_createDate'),
            value: 'ct',
            sortable: false,
          },
          {
            text: this.$t('common_AlarmDuration'),
            value: 'durationTimeInSec',
            sortable: false,
          },
          {
            text: this.$t('common_alarmLevel'),
            value: 'level',
            align: 'sm-center',
            sortable: false,
          },
          {
            value: 'expand',
            sortable: false,
            text: '',
            align: 'end',
          },
        ],
      },
    };
  },
  methods: {
    onRowClick({ expand, isExpanded }) {
      expand(!isExpanded);
    },
    calculateDuration(seconds) {
      const duration = moment.duration(seconds, 'seconds');
      return duration.humanize();
    },
  },
};
</script>
