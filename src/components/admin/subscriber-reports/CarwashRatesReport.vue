<template>
  <v-container fluid>
    <v-row>
      <v-col>
        <div class="d-flex align-center ml-4">
          <div class="label-left">
            {{ $t('finance_valueForCredit') }} {{ currencySymbol }}
            /{{ $t('finance_minutes') }}
          </div>
          <v-switch
            v-model="timeUnit"
            class="switch ml-2"
            :disabled="loading"
          />
          <div class="label-right ml-2">
            {{ $t('finance_time') }}
          </div>
        </div>
      </v-col>
    </v-row>
    <rates-table
      :loader="loader"
      :rates="items"
      :time-unit="timeUnit"
      :currency="currency"
    />
  </v-container>
</template>

<script>
import { mapGetters } from 'vuex';
import DataFetchMixin from '@components/common/mixins/DataFetchMixin.vue';
import RatesTable from '@components/finance/rates/RatesTable.vue';

export default {
  name: 'CarwashRatesReport',
  components: {
    RatesTable,
  },
  mixins: [
    DataFetchMixin,
  ],
  props: {
    url: {
      type: String,
      required: true,
    },
  },

  data() {
    return {
      dataUrl: this.url,
      timeUnit: false,
      loading: false,
      currency: '',
    };
  },

  computed: {
    ...mapGetters({
      currencySymbol: 'auth/userCurrencySymbol',
    }),
  },

  watch: {
    timeUnit() {
      this.getData();
    },
  },
  mounted() {
    this.getData();
  },

  methods: {
    parseApiResponseData(data) {
      this.items = data;
    },
    getParams() {
      return {
        params: {
          report: 'v2\\FinanceCarwashRates',
          useTimeUnit: this.timeUnit,
        },
      };
    },
  },
};
</script>
