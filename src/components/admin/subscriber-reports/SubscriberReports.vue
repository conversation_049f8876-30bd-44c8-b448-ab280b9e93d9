<template>
  <div>
    <v-tabs
      slider-color="primary"
      centered
    >
      <v-tab
        v-for="(item) in tabs"
        :key="item.key"
        ripple
        :disabled="!item.show"
      >
        {{ item.text }}
        <v-icon> {{ item.icon }} </v-icon>
      </v-tab>
      <v-tab-item
        v-for="(item) in tabs"
        :key="item.key"
      >
        <component
          :is="item.component"
          v-bind="item.props"
        />
      </v-tab-item>
    </v-tabs>
  </div>
</template>

<script>
import FiscalReport from '@/components/admin/subscriber-reports/FiscalReport.vue';
import DosageReport from '@/components/admin/subscriber-reports/DosageReport.vue';
import CarwashRatesReport from '@/components/admin/subscriber-reports/CarwashRatesReport.vue';
import SoftwareReport from '@/components/admin/subscriber-reports/SoftwareReport.vue';

export default {
  name: 'SubscriberReports',
  components: {
    FiscalReport,
    DosageReport,
    CarwashRatesReport,
    SoftwareReport,
  },
  computed: {
    subscriberId() {
      return parseInt(this.$route.params.id, 10);
    },
    dataUrl() {
      return `/administration/subscriber/${this.subscriberId}/reports/data`;
    },
    tabs() {
      return [
        {
          component: FiscalReport,
          icon: 'mdi-cash-register',
          key: 'FiscalReport',
          show: true,
          text: this.$t('admin_fiscalization'),
          props: {
            url: this.dataUrl,
          },
        },
        {
          component: DosageReport,
          icon: 'mdi-eyedropper',
          key: 'DosageReport',
          show: true,
          text: this.$t('finance_dosage'),
          props: {
            url: this.dataUrl,
          },
        },
        {
          component: CarwashRatesReport,
          icon: 'mdi-currency-usd',
          key: 'CarwashRatesReport',
          show: true,
          text: this.$t('common_financeCarwashRates'),
          props: {
            url: this.dataUrl,
          },
        },
        {
          component: SoftwareReport,
          icon: 'mdi-update',
          key: 'SoftwareReport',
          show: true,
          text: this.$t('common_software'),
          props: {
            url: this.dataUrl,
          },
        },
      ];
    },
  },

};
</script>
