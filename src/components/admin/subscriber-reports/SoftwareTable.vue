<template>
  <v-data-table
    :headers="[
      { text: $t('common_carwash'), value: 'carwashName' },
      { text: $t('carwash_software_current_version'), value: 'software', align: 'center' },
      {
        text: $t('carwash_software_target_version'),
        value: 'softwareUpdateVersion',
        align: 'center'
      },
      { text: $t('carwash_software_ip'), value: 'ip', align: 'center' },
      { text: $t('carwash_software_plc'), value: 'plcSn', align: 'center' },
      { text: $t('carwash_software_mac'), value: 'plcMac', align: 'center' },
      { text: $t('carwash_software_start_date'), value: 'startDate', align: 'center' },
      { text: $t('carwash_software_warranty_to'), value: 'warrantyTo', align: 'center' },
      { text: $t('other_lastOnline'), value: 'lastContact', align: 'center' },
    ]"
    :items="items"
    item-key="serialNumber"
    :loading="loader"
    hide-default-footer
    disable-pagination
  >
    <template #item="{ item }">
      <tr>
        <td class="text-start">
          <b>{{ item.carwashName }}</b>
        </td>
        <td class="text-center">
          {{ item.software || '-' }}
        </td>
        <td class="text-center">
          {{ item.softwareUpdateVersion || '-' }}
        </td>
        <td class="text-center">
          {{ item.ip || '-' }}
        </td>
        <td class="text-center">
          {{ item.plcSn || '-' }}
        </td>
        <td class="text-center">
          {{ item.plcMac || '-' }}
        </td>
        <td class="text-center">
          {{ item.startDate | formatDateDay }}
        </td>
        <td class="text-center">
          {{ item.warrantyTo | formatDateDay }}
        </td>
        <td class="text-center">
          {{ item.lastContact | formatDateDayTimeWithSeconds }}
        </td>
      </tr>
    </template>
  </v-data-table>
</template>

<script>
export default {
  name: 'SoftwareTable',
  props: {
    items: {
      type: Array,
      default: () => [],
      required: true,
    },
    loader: {
      type: Boolean,
      default: false,
    },
  },
};
</script>
