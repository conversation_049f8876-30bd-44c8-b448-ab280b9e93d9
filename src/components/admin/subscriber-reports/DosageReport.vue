<template>
  <v-container fluid>
    <dosage-table
      :loader="loader"
      :items="items"
    />
  </v-container>
</template>

<script>
import DataFetchMixin from '@components/common/mixins/DataFetchMixin.vue';
import DosageTable from '@components/finance/program-usage/dosage/DosageTable.vue';

export default {
  name: 'DosageReport',
  components: {
    DosageTable,
  },
  mixins: [
    DataFetchMixin,
  ],
  props: {
    url: {
      type: String,
      required: true,
    },
  },

  data() {
    return {
      dataUrl: this.url,
    };
  },

  mounted() {
    this.getData();
  },

  methods: {
    parseApiResponseData(data) {
      this.items = data;
    },
    getParams() {
      return {
        params: {
          report: 'v2\\FinanceUsageDosage',
        },
      };
    },
  },
};
</script>
