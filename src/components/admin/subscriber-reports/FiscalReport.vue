<template>
  <v-container fluid>
    <fiscal-devices-status-table
      :loader="loader"
      :items="items"
    />
  </v-container>
</template>

<script>
import DataFetchMixin from '@components/common/mixins/DataFetchMixin.vue';
import FiscalDevicesStatusTable from '@components/finance/fiscal-devices-status/FiscalDevicesStatusTable.vue';

export default {
  name: 'FiscalReport',
  components: {
    FiscalDevicesStatusTable,
  },
  mixins: [
    DataFetchMixin,
  ],
  props: {
    url: {
      type: String,
      required: true,
    },
  },

  data() {
    return {
      dataUrl: this.url,
    };
  },

  mounted() {
    this.getData();
  },

  methods: {
    parseApiResponseData(data) {
      const rows = Object.values(data);
      this.items = rows.map((row, index) => ({
        ...row,
        id: index,
        carwashName: row.carwashName,
      }));
    },
    getParams() {
      return {
        params: {
          report: 'v2\\FinanceFiscalDevicesStatus',
        },
      };
    },
  },
};
</script>
