<template>
  <v-container fluid>
    <v-row>
      <v-col
        cols="10"
        class="text-sm-start my-4"
      >
        <div class="subscription-info subscription-header">
          {{ $t('subscriptions.subscription') }}
        </div>
        <div class="subscription-info subscription-type primary--text pl-1">
          {{ userBasicData|valueByPath('subscriber.subscription') }}
        </div>
        <div class="owner-header mt-1 ml-1">
          <strong class="text-subtitle-1 pt-1">
            {{ $t('common_owner') }}
          </strong>
          <b>{{ userBasicData.subscriber.name }}</b>
          <v-btn
            x-small
            tile
            rounded
            fab
            elevation="1"
            color="primary"
            class="ml-1 my-1 white--text"
            @click="goToSubscriber(userBasicData.subscriber.id)"
          >
            <v-icon>mdi-eye</v-icon>
          </v-btn>
        </div>
      </v-col>
    </v-row>

    <v-row v-show="userBasicData.isOwner === false">
      <v-col
        cols="12"
        class="text-sm-start"
      >
        <h2>
          <span>{{ $t('common_owner') }}</span>
        </h2>
      </v-col>
    </v-row>
    <v-row v-show="userBasicData.isOwner === false">
      <v-col
        cols="11"
        sm="5"
        class="pl-0 py-0"
      >
        <v-text-field
          v-model="userBasicData.owner.email"
          :label="$t('common_email')"
          prepend-icon="mdi-email"
          name="ownerEmail"
          readonly
          disabled
        />
      </v-col>
      <v-col
        cols="1"
        class="text-sm-end"
      >
        <v-btn
          x-small
          tile
          rounded
          fab
          elevation="1"
          color="primary"
          @click="goToUser(userBasicData.owner.id)"
        >
          <v-icon>mdi-eye</v-icon>
        </v-btn>
      </v-col>
    </v-row>
    <v-row>
      <v-col
        cols="12"
        class="px-0 pt-0 my-4"
      >
        <v-form
          ref="formUserDetails"
          lazy-validation
        >
          <v-row>
            <v-col
              cols="12"
              sm="6"
              class="pt-0 pb-0"
            >
              <v-text-field
                v-model="userBasicData.email"
                :label="$t('common_email')"
                prepend-icon="mdi-email"
                name="email"
                readonly
              />
            </v-col>
            <v-col
              cols="12"
              sm="6"
              class="pt-0 pb-0"
            >
              <v-text-field
                v-model="userBasicData.phone"
                :label="$t('common_phone')"
                prepend-icon="mdi-phone"
                name="phone"
                readonly
              />
            </v-col>
            <v-col
              cols="12"
              sm="6"
              class="pt-0 pb-0"
            >
              <v-text-field
                v-model="userBasicData.firstname"
                :label="$t('common_firstName')"
                prepend-icon="mdi-account-details"
                name="firstName"
                readonly
              />
            </v-col>
            <v-col
              cols="12"
              sm="6"
              class="pt-0 pb-0"
            >
              <v-text-field
                v-model="userBasicData.lastname"
                :label="$t('common_lastName')"
                prepend-icon="mdi-account-details"
                name="lastName"
                readonly
              />
            </v-col>
            <v-col
              cols="12"
              sm="6"
              class="pt-0 pb-0"
            >
              <v-text-field
                v-model="userBasicData.subscriber.ownerBkf"
                :label="$t('admin_ownerBkf')"
                prepend-icon="mdi-account-details"
                name="ownerBkf"
                readonly
              />
            </v-col>
            <v-col
              cols="12"
              sm="6"
              class="pt-0 pb-0"
            >
              <v-text-field
                v-model="userBasicData.lastLogin"
                :label="$t('admin_lastLogin')"
                prepend-icon="mdi-account-details"
                name="lastLogin"
                readonly
              />
            </v-col>
            <v-col
              cols="12"
              sm="6"
              class="pt-0 pb-0"
            >
              <v-text-field
                v-model="userBasicData.comment"
                :label="$t('common_comment')"
                prepend-icon="mdi-account-details"
                name="comment"
                readonly
              />
            </v-col>
            <v-col
              cols="12"
              sm="6"
              class="pt-0 pb-0"
            >
              <v-text-field
                v-model="userBasicData.mobileAppVer"
                :label="$t('mobileAppVersion')"
                prepend-icon="mdi-cellphone-information"
                name="mobileAppVersion"
                readonly
              />
            </v-col>
            <v-col
              cols="12"
              sm="12"
              class="pt-0 pb-0"
            >
              <v-combobox
                v-model="userBasicData.roles"
                :items="userBasicData.roles"
                prepend-icon="mdi-security"
                hide-selected
                :data-vv-as="$t('common_emailCopyEmail')"
                :label="$t('common_roles')"
                multiple
                small-chips
                readonly
              />
            </v-col>
          </v-row>
        </v-form>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>

export default {
  name: 'BasicData',
  props: {
    basicData: {
      type: [Array, Object],
      default: () => [],
    },
  },
  computed: {
    userBasicData() {
      // eslint-disable-next-line
      let a = this.basicData;

      if (typeof this.basicData.owner === 'undefined' || this.basicData.owner === null) {
        a.owner = {
          id: null,
          email: null,
        };
      }

      if (this.basicData.subscriber === null) {
        a.subscriber = {
          ownerBkf: null,
        };
      }

      return a;
    },
  },
  methods: {
    goToUser(id) {
      this.$router.push({ name: 'admin_users_details', params: { id } });
    },
    goToSubscriber(id) {
      this.$router.push({
        name: 'admin_subscribers_details',
        params: { id },
      });
    },
  },
};
</script>

<style scoped>

.subscription-info {
  display: inline-block;
}

.subscription-header {
  font-size: 1.5em;
  font-weight: bold;
}

.subscription-type {
  font-size: 1.5em;
  text-transform: uppercase;
}

.owner-header {
  font-size: 1.35em;
}

</style>
