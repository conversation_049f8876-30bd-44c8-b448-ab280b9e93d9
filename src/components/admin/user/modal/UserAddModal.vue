<template>
  <v-row>
    <v-dialog
      v-model="dialog"
      scrollable
      content-class="dialogWidth-3"
    >
      <v-card>
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">
              {{ $t('actions.add_user') }}
            </h5>
          </span>
        </v-card-title>
        <template v-if="!loaders.site">
          <v-card-text class="pt-4">
            <v-alert
              v-show="userExists"
              border="left"
              :value="true"
              type="error"
              icon="mdi-alert-circle"
            >
              {{ $t('common_userEmailAlreadyExist') }}
            </v-alert>
            <v-form
              ref="formUserEdit"
              v-model="form.valid"
              lazy-validation
            >
              <v-col
                sm="12"
                class="pa-0"
              >
                <v-text-field
                  v-model="user.email"
                  v-validate="'required|email|max:64|min:6'"
                  :label="$t('common_email')"
                  prepend-icon="mdi-email"
                  :data-vv-as="`${$t('common_email')}`"
                  :counter="64"
                  name="email"
                  :error-messages="errors.collect('email')"
                  required
                  @change="userExists = false"
                />
              </v-col>
              <v-col
                sm="12"
                class="pa-0"
              >
                <v-text-field
                  v-model="user.first_name"
                  v-validate="'alpha_spaces|max:32'"
                  :label="$t('common_firstName')"
                  prepend-icon="mdi-account-details"
                  name="firstName"
                  :data-vv-as="`${$t('common_firstName')}`"
                  :error-messages="errors.collect('firstName')"
                />
              </v-col>
              <v-col
                sm="12"
                class="pa-0"
              >
                <v-text-field
                  v-model="user.last_name"
                  v-validate="'alpha_spaces|max:64'"
                  :label="$t('common_lastName')"
                  prepend-icon="mdi-account-details"
                  name="lastName"
                  :data-vv-as="`${$t('common_lastName')}`"
                  :error-messages="errors.collect('lastName')"
                />
              </v-col>
              <v-col
                sm="12"
                class="pa-0"
              >
                <v-text-field
                  v-model="user.phone"
                  :label="$t('common_phone')"
                  prepend-icon="mdi-phone"
                  name="phone"
                  :data-vv-as="`${$t('common_phone')}`"
                  :error-messages="errors.collect('phone')"
                />
              </v-col>
              <v-col
                sm="12"
                class="pa-0"
              >
                <v-textarea
                  v-model="user.comment"
                  rows="3"
                  prepend-icon="mdi-comment"
                  counter="250"
                  :label="$t('admin_comment')"
                />
              </v-col>
            </v-form>
          </v-card-text>
        </template>
        <v-card-actions v-if="!loaders.site">
          <v-spacer />
          <v-btn
            color="gray"
            text
            @click.native="closeDialog"
          >
            {{ $t('actions.return_to_list') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            :loading="loaders.actualize"
            :disabled="!form.valid || userExists"
            @click.native="submit"
          >
            {{ $t('actions.add_user') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-row>
</template>

<script>
export default {
  props: {
    action: {
      type: String,
      default: 'add',
    },
    subscriberId: {
      type: Number,
      default: null,
    },
    editedUser: {
      type: Object,
      default: () => ({}),
    },
    onSuccess: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      subscriber: this.subscriberId,
      initiationEmail: this.email,
      userExists: false,
      modalAction: this.action,
      loaders: {
        site: true,
        actualize: false,
      },
      user: {
        email: null,
        first_name: null,
        last_name: null,
        phone: null,
        timezone: {
          id: 257,
        },
        language: {
          locale: 'pl',
        },
        comment: null,
      },
      dialog: false,
      form: {
        valid: false,
      },
      rules: {
        selectRequired: [(v) => !!v || this.$t('common_fieldRequired')],
      },
    };
  },
  watch: {
    dialog(val) {
      // get user data only when dialog shows up
      if (val) {
        this.loaders.actualize = false;
        this.resetValidationErrorsAndClearFields();
        this.loaders.site = false;
        if (this.modalAction === 'edit') {
          this.getData();
        }
      }
    },
  },
  methods: {
    validateAll() {
      this.$refs.formUserEdit.validate();
    },
    resetValidationErrorsAndClearFields() {
      this.clearFormData();
      if (this.$refs.formUserEdit) {
        this.$refs.formUserEdit.reset();
        this.$refs.formUserEdit.resetValidation();
        this.$validator.reset();
      }
    },
    addUser() {
      this.$validator.validate().then((success) => {
        if (!success) {
          return;
        }

        this.loaders.site = true;
        this.loaders.actualize = true;

        this.axios.post(
          `/administration/subscriber/${this.subscriber}/users`,
          this.user,
        )
          .then(() => {
            this.onSuccess();
            this.closeDialog();
          }).catch((error) => {
            if (error.response && error.response.status === 409) {
              this.userExists = true;
            }
          });
        this.loaders.site = false;
        this.loaders.actualize = false;
      });
    },
    submit() {
      if (this.modalAction === 'add') {
        this.addUser();
      } else {
        this.editUser();
      }
    },
    clearFormData() {
      this.user = {
        email: null,
        first_name: null,
        last_name: null,
        phone: null,
        timezone: { id: 257 },
        language: { locale: 'pl' },
        comment: null,
      };
    },
    closeDialog() {
      this.resetValidationErrorsAndClearFields();
      this.dialog = false;
    },
  },
};
</script>

<style scoped>
  .card-text-wrap {
    display: flex;
    flex-direction: column;
  }

  .card-text-wrap p {
    font-size: 18px;
    text-align: center;
  }

  .dialogWidth-3 {
      max-width: 700px;
  }
</style>
