<template>
  <v-row>
    <v-dialog
      v-model="dialog"
      scrollable
      content-class="dialogWidth-3"
    >
      <v-card>
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">
              {{ $t('actions.edit_user') }}
            </h5>
          </span>
        </v-card-title>
        <v-card-text class="pt-4">
          <v-alert
            v-show="userExists"
            :value="true"
            border="left"
            type="error"
            icon="mdi-alert-circle"
          >
            {{ $t('common_userEmailAlreadyExist') }}
          </v-alert>
          <v-form
            ref="formUserEdit"
            v-model="form.valid"
            lazy-validation
          >
            <v-col
              sm="12"
              class="pa-0"
            >
              <v-text-field
                v-model="user.email"
                v-validate="'required|email|max:64|min:6'"
                :label="$t('common_email')"
                prepend-icon="mdi-email"
                :data-vv-as="`${$t('common_email')}`"
                :counter="64"
                name="email"
                :error-messages="errors.collect('email')"
                required
                @change="userExists = false"
              />
            </v-col>
            <v-col
              sm="12"
              class="pa-0"
            >
              <v-text-field
                v-model="user.first_name"
                v-validate="'alpha_spaces|max:32'"
                :label="$t('common_firstName')"
                prepend-icon="mdi-account-details"
                name="firstName"
                :data-vv-as="`${$t('common_firstName')}`"
                :error-messages="errors.collect('firstName')"
              />
            </v-col>
            <v-col
              sm="12"
              class="pa-0"
            >
              <v-text-field
                v-model="user.last_name"
                v-validate="'alpha_spaces|max:64'"
                :label="$t('common_lastName')"
                prepend-icon="mdi-account-details"
                name="lastName"
                :data-vv-as="`${$t('common_lastName')}`"
                :error-messages="errors.collect('lastName')"
              />
            </v-col>
            <v-col
              sm="12"
              class="pa-0"
            >
              <v-text-field
                v-model="user.phone"
                :label="$t('common_phone')"
                prepend-icon="mdi-phone"
                name="phone"
                :data-vv-as="`${$t('common_phone')}`"
                :error-messages="errors.collect('phone')"
              />
            </v-col>
            <v-col
              sm="12"
              class="pa-0"
            >
              <v-autocomplete
                v-model="user.language"
                v-validate="'required|min:1'"
                item-value="locale"
                item-text="name"
                :label="$t('common_language')"
                :items="languagesOptions"
                :autocomplete="true"
                prepend-icon="mdi-web"
                name="language"
                :data-vv-as="`${$t('common_language')}`"
                :error-messages="errors.collect('language')"
                :rules="rules.selectRequired"
                required
              />
            </v-col>
            <v-col
              sm="12"
              class="pa-0"
            >
              <v-autocomplete
                v-model="user.timezone"
                v-validate="'required|min:1'"
                item-value="id"
                item-text="location"
                :label="$t('common_timezone')"
                :items="timezonesOptions"
                :autocomplete="true"
                prepend-icon="mdi-map-clock"
                name="timezone"
                :data-vv-as="`${$t('common_timezone')}`"
                :error-messages="errors.collect('timezone')"
                :rules="rules.selectRequired"
                required
              />
            </v-col>
            <v-col
              sm="12"
              class="pa-0"
            >
              <v-textarea
                v-model="user.comment"
                rows="3"
                prepend-icon="mdi-comment"
                counter="250"
                :label="$t('admin_comment')"
              />
            </v-col>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="gray"
            text
            @click.native="closeDialog"
          >
            {{ $t('actions.close') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            @click.native="editUser"
          >
            {{ $t('actions.save') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-row>
</template>

<script>
export default {
  props: {
    editedUser: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      userExists: false,
      loading: false,
      dialog: false,
      currenciesOptions: [],
      languagesOptions: [],
      timezonesOptions: [],
      form: {
        valid: false,
      },
      rules: {
        selectRequired: [(v) => !!v || this.$t('common_fieldRequired')],
      },
    };
  },
  computed: {
    user() {
      return {
        email: this.editedUser.email,
        first_name: this.editedUser.firstname,
        last_name: this.editedUser.lastname,
        phone: this.editedUser.phone,
        language: this.editedUser.language ? this.editedUser.language.locale : null,
        timezone: this.editedUser.timezone.id,
        comment: this.editedUser.comment,
      };
    },
  },
  mounted() {
    this.loading = true;
    this.getLanguges();
    this.getCurrencies();
    this.getTimezones();
    this.loading = false;
  },
  methods: {
    validateAll() {
      this.$refs.formUserEdit.validate();
    },
    getLanguges() {
      this.axios.get(
        '/api/lists/languages',
      )
        .then((response) => {
          this.languagesOptions = response.data.data;
        });
    },
    getCurrencies() {
      this.axios.get(
        '/api/lists/currencies',
      )
        .then((response) => {
          this.currenciesOptions = response.data;
        });
    },
    getTimezones() {
      this.axios.get(
        '/api/lists/timezones',
        {
          params: {
            perPage: 1000,
          },
        },
      )
        .then((response) => {
          this.timezonesOptions = response.data.data;
        });
    },
    editUser() {
      this.$validator.validate().then((success) => {
        if (!success) {
          return;
        }

        this.axios.put(
          `/administration/user/${this.editedUser.id}`,
          this.user,
        )
          .then(
            () => {
              this.propagateUserUpdate();
              this.closeDialog();
            },
          ).catch((error) => {
            if (error.response.status === 409) {
              this.userExists = true;
            }
          });
      });
    },
    propagateUserUpdate() {
      this.$emit('user-edited');
    },
    closeDialog() {
      this.dialog = false;
    },
  },
};
</script>
