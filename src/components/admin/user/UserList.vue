<template>
  <v-container fluid>
    <v-row>
      <v-col
        cols="12"
        class="text-sm-start"
      >
        <v-data-table
          mobile-breakpoint="0"
          :headers="headers"
          :items="usersData"
          :items-per-page="-1"
          dense
        >
          <template #item="{ item }">
            <!--          <template v-if="!loader">-->
            <tr>
              <td class="text-sm-begin">
                {{ item.email }}
              </td>
              <td class="text-sm-begin">
                {{ item.firstName }}
              </td>
              <td class="text-sm-begin">
                {{ item.lastName }}
              </td>
              <td class="text-sm-end">
                <v-btn
                  x-small
                  tile
                  rounded
                  fab
                  elevation="1"
                  color="primary"
                  class="my-1"
                  @click="goToUser(item.id)"
                >
                  <v-icon>mdi-eye</v-icon>
                </v-btn>
              </td>
            </tr>
          <!--          </template>-->
          </template>
        </v-data-table>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>

export default {
  name: 'UserUsersList',
  props: {
    users: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      usersData: this.users,
      headers: [
        {
          text: this.$t('common_email'),
          value: 'email',
          class: 'text-sm-begin',
        },
        {
          text: this.$t('common_firstName'),
          value: 'firstName',
          class: 'text-sm-begin',
        },
        {
          text: this.$t('common_lastName'),
          value: 'lastName',
          class: 'text-sm-begin',
        },
        {
          text: this.$t('actions.actions'),
          class: 'text-sm-end',
        },
      ],
    };
  },
  methods: {
    goToUser(userId) {
      this.$router.push({ name: 'admin_users_details', params: { id: userId } });
    },
  },
};
</script>
