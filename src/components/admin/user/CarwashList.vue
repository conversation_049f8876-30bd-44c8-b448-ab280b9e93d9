<template>
  <v-row>
    <v-col
      cols="12"
      class="text-sm-start"
    >
      <v-data-table
        mobile-breakpoint="0"
        :headers="headers"
        :items="carwashesData"
        :items-per-page="-1"
      >
        <template #item="{ item }">
          <tr
            class="clickable"
            @click="goToCarwash(item.serialNumber)"
          >
            <td class="text-sm-start">
              <rollover-badge v-if="item.isRollover" />
              <self-service-badge v-else />
              {{ item.serialNumber }}
            </td>
            <td class="text-sm-begin">
              {{ item.name ? item.name : '-' }}
            </td>
            <td class="text-sm-begin">
              {{ item.startDate|formatDateDay }}
            </td>
            <td class="text-sm-end">
              {{ item.warrantyVoided|formatDateDay }}
            </td>
            <td class="text-sm-end">
              <template v-if="!item.unsubscribed">
                <v-icon color="green">
                  mdi-check
                </v-icon>
              </template>
              <template v-else>
                <v-icon color="red">
                  mdi-close
                </v-icon>
              </template>
            </td>
          </tr>
        </template>
      </v-data-table>
    </v-col>
  </v-row>
</template>

<script>
import RolloverBadge from '@components/common/badge/RolloverBadge.vue';
import SelfServiceBadge from '@components/common/badge/SelfServiceBadge.vue';

export default {
  name: 'UserCarwashList',
  components: {
    RolloverBadge,
    SelfServiceBadge,
  },
  props: {
    carwashes: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      carwashesData: this.carwashes,
      headers: [
        {
          text: this.$t('admin_serialNumber'),
          value: 'serialNumber',
        },
        {
          text: this.$t('common_name'),
          value: 'name',
          class: 'text-sm-begin',
        },
        {
          text: this.$t('admin_startDate'),
          value: 'StartDate',
          class: 'text-sm-begin',
        },
        {
          text: this.$t('admin_warrantyVoided'),
          value: 'warranty',
          class: 'text-sm-end',
        },
        {
          text: this.$t('admin_allowSubscription'),
          value: 'unsubscribed',
          class: 'text-sm-end',
        },
      ],
    };
  },
  methods: {
    goToCarwash(serialNumber) {
      this.$router.push({ name: 'admin_carwash_details', params: { serialNumber } });
    },
  },
};
</script>
