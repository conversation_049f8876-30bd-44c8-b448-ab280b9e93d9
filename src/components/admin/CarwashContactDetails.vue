<template>
  <div>
    <h3
      class="mt-5 mb-3 fullwidth"
    >
      {{ $t('common_employeesDetails') }}
    </h3>
    <v-data-table
      :headers="headersConatct"
      :items="contactDetails.employees_details"
      :sort-desc="false"
      :hide-default-footer="true"
    />
    <h3
      class="mt-5 mb-3 fullwidth"
    >
      {{ $t('common_administratorDetails') }}
    </h3>
    <v-data-table
      :headers="headersConatct"
      :items="contactDetails.administrator_details"
      :sort-desc="false"
      :hide-default-footer="true"
    />
    <h3
      class="mt-5 mb-3 fullwidth"
    >
      {{ $t('common_noteDetails') }}
    </h3>
    <v-data-table
      :headers="headersNotes"
      :items="contactDetails.note_details"
      :sort-desc="false"
      :hide-default-footer="true"
    />
  </div>
</template>

<script>
export default {
  name: 'CarwashContactDetails',
  props: {
    contactDetails: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      headersConatct: [
        { text: this.$t('common_formName'), value: 'firstname', sortable: false },
        { text: this.$t('common_surname'), value: 'surname', sortable: false },
        { text: this.$t('common_phone'), value: 'gsm', sortable: false },
        { text: this.$t('common_email'), value: 'mail', sortable: false },
        { text: this.$t('common_stand'), value: 'job', sortable: false },
      ],
      headersNotes: [
        { text: this.$t('common_tableDescription'), value: 'note', sortable: false },
        { text: this.$t('common_tableCreateDate'), value: 'timestamp', sortable: false },
        { text: this.$t('common_username'), value: 'username', sortable: false },
      ],
      search: '',
      itemKey: 'id',
    };
  },
};
</script>
