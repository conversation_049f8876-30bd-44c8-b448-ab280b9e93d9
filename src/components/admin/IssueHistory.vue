<template>
  <div>
    <v-card-text>
      <v-data-table
        :headers="dataTable.headers"
        :sort-by="['ctime']"
        :sort-desc="[true]"
        :items="issues"
        :hide-default-footer="false"
        :items-per-page="10"
      />
    </v-card-text>
  </div>
</template>

<script>

export default {
  name: 'ActiveAlarmsList',
  props: {
    history: {
      type: Array,
      default: () => ([]),
    },
  },
  data() {
    return {
      alarmLevelIcons: {
        ignore: 'mdi-cancel',
        information: 'mdi-information',
        warning: 'mdi-alert',
        error: 'mdi-alert-circle',
        unspecified: 'mdi-help-circle',
        notice: 'mdi-close',
      },
      alarmLevelIconsColors: {
        ignore: 'brown',
        information: 'blue',
        warning: 'orange',
        error: 'red',
        unspecified: 'grey',
        notice: 'grey',
      },
      dataTable: {
        headers: [
          {
            text: this.$t('common_date'),
            value: 'ctime',
            align: 'sm-start',
            sortable: true,
          },
          {
            text: this.$t('common_comment'),
            value: 'comment',
            align: 'sm-start',
            sortable: false,
          },
          {
            text: this.$t('common_email'),
            value: 'email',
            align: 'sm-start',
            sortable: false,
          },
          {
            text: this.$t('common_state'),
            value: 'status',
            align: 'sm-start',
            sortable: false,
          },
        ],
        items: {},
        totalItems: 0,
      },
      items: [],
    };
  },
  computed: {
    issues() {
      this.history.forEach((element) => {
        // eslint-disable-next-line no-param-reassign
        element.status = this.$t(`issues-alarm.${element.status.toLowerCase()}`);
      });
      return this.history;
    },
  },
};
</script>
