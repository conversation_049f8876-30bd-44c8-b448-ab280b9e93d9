<template>
  <div>
    <div
      v-if="loading"
      class="d-flex align-center justify-center"
    >
      <v-progress-circular
        indeterminate
        class="mt-8 mb-5"
        color="primary"
      />
    </div>
    <div
      v-else
      class="mx-auto container"
    >
      <v-card class="mb-4">
        <v-card-title class="secondary white--text">
          {{ $t('carwash_software_info') }}
        </v-card-title>
        <v-card-text v-if="softwareInfo">
          <v-simple-table>
            <template #default>
              <tbody>
                <tr>
                  <td
                    class="font-weight-bold"
                    width="200"
                  >
                    {{ $t('carwash_software_plc') }}
                  </td>
                  <td>
                    {{ softwareInfo.plcSn || '-' }}
                  </td>
                </tr>
                <tr>
                  <td class="font-weight-bold">
                    {{ $t('carwash_software_mac') }}
                  </td>
                  <td>
                    {{ softwareInfo.plcMac || '-' }}
                  </td>
                </tr>
                <tr>
                  <td class="font-weight-bold">
                    {{ $t('carwash_software_serial_number') }}
                  </td>
                  <td>
                    {{ softwareInfo.sn || '-' }}
                  </td>
                </tr>
                <tr>
                  <td class="font-weight-bold">
                    {{ $t('carwash_software_owner') }}
                  </td>
                  <td>
                    {{ softwareInfo.owner?.name || '-' }}
                  </td>
                </tr>
                <tr>
                  <td class="font-weight-bold">
                    {{ $t('carwash_software_ip') }}
                  </td>
                  <td>
                    {{ softwareInfo.ip || '-' }}
                  </td>
                </tr>
                <tr>
                  <td class="font-weight-bold">
                    {{ $t('carwash_software_config') }}
                  </td>
                  <td>
                    {{ softwareInfo.config || '-' }}
                  </td>
                </tr>
                <tr>
                  <td class="font-weight-bold">
                    {{ $t('carwash_software_current_version') }}
                  </td>
                  <td>
                    {{ softwareInfo.software || '-' }}
                  </td>
                </tr>
                <tr>
                  <td class="font-weight-bold">
                    {{ $t('carwash_software_target_version') }}
                  </td>
                  <td>
                    {{ softwareInfo.softwareUpdate?.version || '-' }}
                  </td>
                </tr>
                <tr>
                  <td class="font-weight-bold">
                    {{ $t('carwash_software_ip_country') }}
                  </td>
                  <td>
                    {{ softwareInfo.ipCountry || '-' }}
                  </td>
                </tr>
                <tr>
                  <td class="font-weight-bold">
                    {{ $t('carwash_software_ip_city') }}
                  </td>
                  <td>
                    {{ softwareInfo.ipCity || '-' }}
                  </td>
                </tr>
                <tr>
                  <td class="font-weight-bold">
                    {{ $t('carwash_software_start_date') }}
                  </td>
                  <td>
                    {{ $options.filters.formatDateDay(softwareInfo.startDate) }}
                  </td>
                </tr>
                <tr>
                  <td class="font-weight-bold">
                    {{ $t('carwash_software_warranty_to') }}
                  </td>
                  <td>
                    {{ $options.filters.formatDateDay(softwareInfo.warrantyTo) }}
                  </td>
                </tr>
              </tbody>
            </template>
          </v-simple-table>
        </v-card-text>
        <v-card-text v-else>
          <p class="text-center">
            {{ $t('carwash_software_no_data') }}
          </p>
        </v-card-text>
      </v-card>

      <v-card>
        <v-card-title class="secondary white--text">
          {{ $t('carwash_software_target_software_version') }}
        </v-card-title>
        <v-card-text class="pt-4">
          <v-row
            align="center"
            class="my-2"
          >
            <v-col
              cols="12"
              sm="6"
              md="6"
            >
              <v-autocomplete
                v-model="selectedSoftware"
                :items="softwareItems"
                item-text="version"
                item-value="id"
                :label="$t('carwash_software_select_version')"
                :loading="availableSoftwareLoading"
                :disabled="availableSoftwareLoading"
                clearable
                outlined
                dense
                return-object
                hide-details
              >
                <template #no-data>
                  <div
                    v-if="availableSoftwareLoading"
                    class="d-flex align-center pa-4"
                  >
                    <v-progress-circular
                      indeterminate
                      size="24"
                      class="mr-2"
                      color="primary"
                    />
                    <span>{{ $t('carwash_software_loading_versions') }}</span>
                  </div>
                  <div
                    v-else
                    class="pa-4"
                  >
                    {{ $t('carwash_software_no_versions') }}
                  </div>
                </template>
              </v-autocomplete>
            </v-col>
            <v-col
              cols="12"
              sm="6"
              md="6"
              class="d-flex justify-end"
            >
              <div class="d-flex flex-wrap">
                <v-btn
                  color="primary"
                  :disabled="isSetButtonDisabled"
                  class="mr-2 mb-2 mb-sm-0"
                  @click="setTargetSoftware()"
                >
                  <v-progress-circular
                    v-if="setButtonLoading"
                    indeterminate
                    size="20"
                    width="2"
                    color="primary"
                    class="mr-2"
                  />
                  {{ $t('common_set') }}
                </v-btn>
              </div>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CarwashSoftwareTab',
  props: {
    serialNumber: {
      type: [Number, String],
      required: true,
    },
    initialDataFetch: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      loading: false,
      softwareInfoLoading: false,
      availableSoftwareLoading: false,
      setButtonLoading: false,
      softwareInfo: null,
      availableSoftware: [],
      selectedSoftware: null,
    };
  },

  computed: {
    availableSoftwareList() {
      if (!this.availableSoftware || !this.availableSoftware.data) {
        return [];
      }
      // Sortowanie alfabetyczne po polu version
      return [...this.availableSoftware.data].sort((a, b) => {
        if (a.version < b.version) return -1;
        if (a.version > b.version) return 1;
        return 0;
      });
    },
    softwareItems() {
      return this.availableSoftwareList;
    },
    currentSoftwareItem() {
      if (!this.softwareInfo) {
        return null;
      }

      if (this.softwareInfo.softwareUpdate) {
        return this.softwareInfo.softwareUpdate;
      }

      return null;
    },

    isSetButtonDisabled() {
      if (this.setButtonLoading) {
        return true;
      }

      // Jeśli wybrano wersję oprogramowania, sprawdź czy to ta sama co aktualna
      if (this.selectedSoftware && this.softwareInfo && this.softwareInfo.softwareUpdate
          && this.selectedSoftware.id === this.softwareInfo.softwareUpdate.id) {
        return true;
      }

      return false;
    },
  },

  watch: {
    softwareInfo: {
      immediate: true,
      handler() {
        this.updateSelectedSoftware();
      },
    },
    availableSoftwareList: {
      handler() {
        this.updateSelectedSoftware();
      },
    },
  },

  mounted() {
    if (this.initialDataFetch) {
      this.fetchData();
    }
  },

  methods: {
    updateSelectedSoftware() {
      if (this.softwareInfo && this.softwareInfo.softwareUpdate) {
        // Jeśli lista dostępnych wersji jest załadowana, znajdź odpowiedni obiekt
        if (this.availableSoftwareList.length > 0) {
          const foundItem = this.availableSoftwareList.find(
            (item) => item.id === this.softwareInfo.softwareUpdate.id,
          );

          if (foundItem) {
            // Znaleziono dokładne dopasowanie - ustaw obiekt z ID
            this.selectedSoftware = foundItem;
            return;
          }
        }

        // Jeśli nie znaleziono w liście lub lista nie jest jeszcze załadowana,
        // używamy bezpośrednio obiektu softwareUpdate
        this.selectedSoftware = this.softwareInfo.softwareUpdate;
        return;
      }

      // Jeśli nie mamy softwareUpdate, ustawiamy selectedSoftware na null
      this.selectedSoftware = null;
    },

    async fetchData() {
      this.loading = true;

      this.softwareInfoLoading = true;
      const fetchSoftwareInfo = async () => {
        try {
          const response = await this.axios.get(
            `/administration/carwash-software/${this.serialNumber}`,
          );
          this.softwareInfo = response.data;
        } catch (error) {
          this.snackbar.showMessage('error', this.$t('common_error_occurred'));
        } finally {
          this.softwareInfoLoading = false;
          this.loading = false;
        }
      };

      this.availableSoftwareLoading = true;
      const fetchAvailableSoftware = async () => {
        try {
          const response = await this.axios.get(
            `/administration/carwash-software/${this.serialNumber}/available-softwares`,
          );
          this.availableSoftware = response.data;
        } catch (error) {
          this.snackbar.showMessage('error', this.$t('common_error_occurred'));
        } finally {
          this.availableSoftwareLoading = false;
        }
      };

      fetchSoftwareInfo();
      fetchAvailableSoftware();
    },

    async setTargetSoftware() {
      this.setButtonLoading = true;
      try {
        // Jeśli nie wybrano żadnej wersji oprogramowania, wywołujemy endpoint DELETE
        if (!this.selectedSoftware) {
          await this.axios.delete(
            `/administration/carwash-software/${this.serialNumber}/remove-software-update`,
          );
        } else {
          // Jeśli to tymczasowy obiekt (id = -1), nie możemy ustawić go jako docelowy
          if (this.selectedSoftware.id === -1) {
            this.setButtonLoading = false;
            return;
          }

          await this.axios.put(
            `/administration/carwash-software/${this.serialNumber}/set-software/${this.selectedSoftware.id}`,
          );
        }

        await this.refreshData();
        this.setButtonLoading = false;
      } catch (error) {
        this.snackbar.showMessage('error', this.$t('common_error_occurred'));
        this.setButtonLoading = false;
      }
    },

    async refreshData() {
      try {
        const response = await this.axios.get(
          `/administration/carwash-software/${this.serialNumber}`,
        );
        this.softwareInfo = response.data;
      } catch (error) {
        this.snackbar.showMessage('error', this.$t('common_error_occurred'));
      }
    },
  },
};
</script>
