<template>
  <v-container fluid>
    <v-row>
      <v-col
        cols="3"
        class="py-2"
      >
        <strong class="text-subtitle-1">{{ label }}</strong>
      </v-col>
      <v-col
        cols="9"
        class="py-2"
      >
        <template v-if="format == 'plain'">
          <span class="text-subtitle-1">{{ value }}</span>
        </template>
        <template v-else-if="format == 'date'">
          <span class="text-subtitle-1">{{ $d(new Date(value)) }}</span>
        </template>
        <template v-if="goTo !== null">
          <v-btn
            x-small
            tile
            rounded
            fab
            elevation="1"
            color="primary"
            class="ml-2 my-1 white--text"
            @click="goToPath()"
          >
            <v-icon>{{ `mdi-${goToIcon}` }}</v-icon>
          </v-btn>
        </template>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>

export default {
  name: 'CarwashDataRow',
  props: {
    label: {
      type: String,
      default: '',
    },
    value: {
      type: [String, Number, Boolean],
      default: '',
    },
    goTo: {
      type: [Object, Array],
      default: null,
    },
    goToIcon: {
      type: String,
      default: 'link',
    },
    format: {
      type: String,
      default: 'plain',
    },
  },
  methods: {
    goToPath() {
      this.$router.push(this.goTo);
    },
  },
};
</script>
