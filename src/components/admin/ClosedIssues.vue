<template>
  <div>
    <v-col
      cols="4"
      offset="8"
      class="d-flex mt-6 pb-0"
    >
      <v-text-field
        v-model="search"
        :label="$t('common_search')"
        prepend-inner-icon="mdi-magnify"
        class="mt-0 pt-0 "
      />
    </v-col>
    <v-card-text>
      <v-data-table
        :headers="dataTable.headers"
        :items="issues"
        :single-expand="true"
        show-expand
        :hide-default-footer="false"
      >
        <template #item="{ item, expand: expandSub, isExpanded}">
          <tr
            :class="isExpanded ? 'selected' : ''"
            @click="expandSub(!isExpanded);getIssueDetails(item.id)"
          >
            <td />
            <td class="text-start">
              {{ item.ctime }}
            </td>
            <td class="text-start">
              {{ item.etime }}
            </td>
            <td class="text-start">
              {{ item.status }}
            </td>
            <td class="text-start">
              {{ item.alarmDefinition.text }}
            </td>
            <td>
              <v-tooltip bottom>
                <template
                  #activator="{ on }"
                >
                  <v-icon
                    class="pl-2 pr-2 text-right"
                    :color="alarmLevelIconsColors[item.level.name]"
                    v-on="on"
                  >
                    {{ alarmLevelIcons[item.level.name] }}
                  </v-icon>
                </template>
                <span>{{ item.level.name }}</span>
              </v-tooltip>
            </td>
            <td />
          </tr>
        </template>
        <template #expanded-item="{}">
          <tr
            v-if="loading"
          >
            <td
              colspan="5"
              class="pl-9"
              align="center"
            >
              <v-progress-circular
                indeterminate
                size="16"
                class="circleProgress"
                color="primary"
              />
            </td>
          </tr>
          <tr
            v-if="theIssue !== null"
            class="selected"
          >
            <td
              colspan="7"
              class="pl-9"
            >
              <alarm-data-row
                :label="$t('common_tableCreateDate')"
                :value="theIssue.ctime"
                class="mt-2"
              />
              <alarm-data-row
                :label="$t('common_tableDescription')"
                :value="theIssue.alarmDefinition.text"
              />
              <alarm-data-row
                :label="$t('common_alarmLevel')"
                :level="theIssue.level.name"
              />
              <alarm-data-row
                v-if="theIssue.lastTime"
                :label="$t('common_lastTime')"
                :value="theIssue.lastTime"
              />
              <alarm-data-row
                v-if="theIssue.etime"
                :label="$t('admin_etime')"
                :value="theIssue.etime"
              />
              <alarm-data-row
                v-if="theIssue.dueTime"
                :label="$t('admin_dueDate')"
                :value="theIssue.dueTime"
              />
              <alarm-data-row
                v-if="theIssue.comment"
                :label="$t('admin_lastComment')"
                :value="theIssue.comment"
              />
              <alarm-data-row
                v-if="theIssue.status"
                :label="$t('common_status')"
                :value="$t('issues-alarm.' + (theIssue.status).toLowerCase())"
              />
              <alarm-data-row
                :label="$t('admin_lastUser')"
                :level="theIssue.lastUser ?? '-'"
              />
              <v-col
                cols="3"
                class="py-2"
              >
                <strong class="text-subtitle-3">{{ $t('admin_issuesList') }}</strong>
              </v-col>
              <v-col
                cols="12"
                offset="0"
                class="py-2"
              >
                <v-data-table
                  :headers="dataTable.headers3"
                  :items="theIssue.alarm"
                  :hide-default-footer="false"
                  class="mb-2"
                >
                  <template #[`item.alarmDef.level`]="{ item }">
                    <v-tooltip bottom>
                      <template #activator="{ on }">
                        <v-icon
                          :key="item.id"
                          :color="alarmLevelIconsColors[item.alarmDef.level.name]"
                          v-on="on"
                        >
                          {{ alarmLevelIcons[item.alarmDef.level.name] }}
                        </v-icon>
                      </template>
                      <span>{{ item.alarmDef.level.name }}</span>
                    </v-tooltip>
                  </template>
                </v-data-table>
              </v-col>
              <v-col
                cols="3"
                class="py-2"
              >
                <strong class="text-subtitle-3">{{ $t('admin_actionHistory') }}</strong>
              </v-col>
              <v-col
                cols="12"
                offset="0"
                class="py-2"
              >
                <v-data-table
                  :headers="dataTable.headers2"
                  :items="theIssue.history"
                  :hide-default-footer="false"
                  class="mb-2"
                >
                  <template #item="{ item}">
                    <tr>
                      <td>{{ item.ctime }}</td>
                      <td>{{ item.email }}</td>
                      <td>{{ $t('issues-alarm.' + (item.status).toLowerCase()) }}</td>
                      <td>{{ item.comment }}</td>
                      <td>
                        <v-tooltip bottom>
                          <template #activator="{ on }">
                            <v-icon
                              class="pl-2 pr-2"
                              :color="alarmLevelIconsColors[item.level.toLowerCase()]"
                              v-on="on"
                            >
                              {{ alarmLevelIcons[item.level.toLowerCase()] }}
                            </v-icon>
                          </template>
                          <span>{{ item.level.toLowerCase() }}</span>
                        </v-tooltip>
                      </td>
                      <td />
                    </tr>
                  </template>
                </v-data-table>
              </v-col>
            </td>
          </tr>
        </template>
      </v-data-table>
    </v-card-text>
  </div>
</template>

<script>
import AlarmDataRow from '@components/admin/alarm/DataRow.vue';

export default {
  name: 'ClosedIssues',
  components: {
    AlarmDataRow,
  },
  props: {
    closedIssues: {
      type: Array,
      default: () => ([]),
    },
  },
  data() {
    return {
      loading: false,
      theIssue: null,
      alarmLevelIcons: {
        ignore: 'mdi-cancel',
        information: 'mdi-information',
        warning: 'mdi-alert',
        error: 'mdi-alert-circle',
        unspecified: 'mdi-help-circle',
        notice: 'mdi-close',
      },
      alarmLevelIconsColors: {
        ignore: 'brown',
        information: 'blue',
        warning: 'orange',
        error: 'red',
        unspecified: 'grey',
        notice: 'grey',
      },
      dataTable: {
        headers: [
          {
            text: this.$t('common_tableCreateDate'),
            value: 'ctime',
            align: 'sm-start',
            sortable: true,
          },
          {
            text: this.$t('admin_etime'),
            value: 'etime',
            align: 'sm-start',
            sortable: true,
          },
          {
            text: this.$t('common_state'),
            value: 'status',
            align: 'sm-start',
            sortable: false,
          },
          {
            text: this.$t('common_tableDescription'),
            value: 'alarmDefinition.text',
            align: 'sm-start',
            sortable: false,
          },
          {
            text: this.$t('common_alarmLevel'),
            value: 'level',
            sortable: false,
          },
          {
            text: '',
            value: null,
            align: null,
            sortable: false,
          },
        ],
        headers2: [
          {
            text: this.$t('common_tableCreateDate'),
            value: 'ctime',
            align: 'sm-start',
            sortable: true,
          },
          {
            text: this.$t('common_email'),
            value: 'email',
            align: 'sm-start',
            sortable: true,
          },
          {
            text: this.$t('common_state'),
            value: 'status',
            align: 'sm-start',
            sortable: false,
          },
          {
            text: this.$t('common_tableDescription'),
            value: 'comment',
            align: 'sm-start',
            sortable: false,
          },
          {
            text: this.$t('common_alarmLevel'),
            value: 'level',
            sortable: false,
          },
          {
            text: '',
            value: null,
            align: null,
            sortable: false,
          },
        ],
        headers3: [
          {
            text: this.$t('admin_issueId'),
            value: 'alarmDef.id',
            align: 'sm-start',
            sortable: false,
          },
          {
            text: this.$t('common_createDate'),
            value: 'ct',
            align: 'sm-start',
            sortable: true,
          },
          {
            text: this.$t('common_endDate'),
            value: 'et',
            align: 'sm-start',
            sortable: true,
          },
          {
            text: this.$t('common_tableDescription'),
            value: 'text',
            align: 'sm-start',
            sortable: false,
          },
          {
            text: this.$t('common_alarmLevel'),
            value: 'alarmDef.level',
            align: 'sm-start',
            sortable: false,
          },
        ],
      },
      items: [],
      search: '',
    };
  },
  computed: {
    issues() {
      const elements = this.closedIssues.filter((item) => item.alarmDefinition.text.toLowerCase()
        .includes(this.search.toLowerCase()));
      elements.forEach((element) => {
        // eslint-disable-next-line no-param-reassign
        element.status = this.$t('common_close');
      });
      return elements;
    },
  },
  methods: {
    getIssueDetails(id) {
      this.theIssue = null;
      this.loading = true;
      this.axios.get(
        `/administration/carwashissue/${id}`,
      )
        .then(
          (response) => {
            if (response.status === 200) {
              this.theIssue = response.data;
            }
            this.loading = false;
          },
        );
    },
  },
};
</script>
<style lang="css" scoped>
  .selected {
    background-color: #efefef;
    border-left: 1px solid #d6d6d6;
    border-right: 1px solid #d6d6d6;
  }
  .selected-row, .expand-table tr.issue:hover {
    background-color: #d6d6d6 !important;
  }
</style>
