<template>
  <div class="pa-0">
    <v-tabs
      v-model="activeTabs"
      dens
      dark
      background-color="secondary lighten-1"
    >
      <v-tab
        v-for="(item) in tabsTitle"
        :key="item.key"
        ripple
      >
        {{ item.text }}
      </v-tab>
    </v-tabs>
    <alerts-list
      :disable-links="true"
      :alerts="user.info ?? []"
    />
    <v-tabs-items
      v-model="activeTabs"
    >
      <v-tab-item
        v-for="(item) in tabsItems"
        :key="item.key"
        class="pl-4 pr-4"
      >
        <component
          :is="item.component"
          v-bind="{
            ...item.properties
          }"
        />
      </v-tab-item>
    </v-tabs-items>
  </div>
</template>

<script>
import InvoiceCompanyDataEdit from '@components/user/InvoiceCompanyDataEdit.vue';
import { mapGetters } from 'vuex';
import AlertsList from '@components/common/AlertsList.vue';
import UserCarwashList from './user/CarwashList.vue';
import UserBasicData from './user/BasicData.vue';

export default {
  name: 'UserDetails',
  components: {
    AlertsList,
    UserCarwashList,
    InvoiceCompanyDataEdit,
    UserBasicData,
  },
  props: {
    user: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      activeTabs: null,
      loader: false,
      tabs: [
        {
          text: this.$t('common_clientBasicData'),
          key: 'user-basic-data',
          showOnUser: true,
        },
        {
          text: this.$t('common_companyData'),
          key: 'invoice-company-data-edit',
          showOnUser: false,
        },
        {
          key: 'user-carwash-list',
          text: this.$t('common_carwashes'),
          showOnUser: true,
        },
      ],
    };
  },
  computed: {
    ...mapGetters({
      canAccess: 'auth/canAccess',
    }),
    tabsContent() {
      return [
        {
          component: 'user-basic-data',
          key: 'user-basic-data',
          properties: {
            basicData: this.user,
          },
          showOnUser: true,
        },
        {
          component: 'invoice-company-data-edit',
          key: 'invoice-company-data-edit',
          properties: {
            isEditable: false,
            dataUrl: '/administration/subscriber',
            subscriber: this.user.subscriber,
          },
          showOnUser: false,
        },
        {
          component: 'user-carwash-list',
          key: 'user-carwash-list',
          properties: {
            carwashes: this.user.carwashes,
          },
          showOnUser: true,
        },
      ];
    },
    isOwner() {
      return this.user.isOwner ?? false;
    },
    tabsTitle() {
      if (this.isOwner) {
        return this.tabs;
      }

      return this.tabs.filter((item) => item.showOnUser === true);
    },
    tabsItems() {
      if (this.isOwner) {
        return this.tabsContent;
      }

      return this.tabsContent.filter((item) => item.showOnUser === true);
    },
  },
};
</script>
