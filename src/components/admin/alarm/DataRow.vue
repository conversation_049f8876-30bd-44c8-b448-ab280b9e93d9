<template>
  <v-container fluid>
    <v-row>
      <v-col
        cols="3"
        class="py-2"
      >
        <strong class="text-subtitle-3">{{ label }}</strong>
      </v-col>
      <v-col
        cols="9"
        class="py-2"
      >
        <template v-if="format == 'plain'">
          <span class="text-subtitle-1">{{ value }}</span>
        </template>
        <template v-else-if="format == 'date'">
          <span class="text-subtitle-1">{{ $d(new Date(value)) }}</span>
        </template>
        <v-btn
          v-if="copy"
          icon
          x-small
          class="ml-5"
          @click="copyValue"
        >
          <v-icon>mdi-content-copy</v-icon>
        </v-btn>
        <template v-if="goTo !== null">
          <v-btn
            small
            tile
            elevation="1"
            color="primary"
            :href="goTo"
            target="_blank"
          >
            {{ $t('actions.show_doc') }}
          </v-btn>
        </template>
        <template v-if="level !== null">
          <v-icon
            :color="alarmLevelIconsColors[level]"
          >
            {{ alarmLevelIcons[level] }}
          </v-icon>
          <span>{{ level }}</span>
        </template>
        <v-tooltip
          v-if="icon"
          right
        >
          <template #activator="{ on, attrs }">
            <span
              v-bind="attrs"
              v-on="on"
            >
              <v-icon
                v-bind="icon.icon"
                small
                class="ml-1"
              >
                {{ (icon.icon ?? '') }}
              </v-icon>
            </span>
          </template>
          <div class="text-center">
            <div
              v-for="(content, tkey) in icon.content"
              :key="`tooltip${tkey}`"
            >
              <div
                v-bind="content"
                v-text="content.text"
              />
            </div>
          </div>
        </v-tooltip>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>

export default {
  name: 'AlarmDataRow',
  props: {
    label: {
      type: String,
      default: '',
    },
    value: {
      type: [String, Number, Boolean],
      default: '',
    },
    goTo: {
      type: String,
      default: null,
    },
    goToIcon: {
      type: String,
      default: 'link',
    },
    format: {
      type: String,
      default: 'plain',
    },
    level: {
      type: String,
      default: '',
    },
    icon: {
      type: Object,
      default: () => ({}),
    },
    copy: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      alarmLevelIcons: {
        ok: 'mdi-check-circle',
        ignore: 'mdi-cancel',
        information: 'mdi-information',
        warning: 'mdi-alert',
        error: 'mdi-alert-circle',
        unspecified: 'mdi-help-circle',
      },
      alarmLevelIconsColors: {
        ok: 'green',
        ignore: 'brown',
        information: 'blue',
        warning: 'orange',
        error: 'red',
        unspecified: 'grey',
      },
    };
  },
  methods: {
    copyValue() {
      navigator.clipboard.writeText(this.value);
    },
  },
};
</script>
