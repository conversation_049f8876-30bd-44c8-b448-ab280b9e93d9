<template>
  <v-tabs
    dens
    dark
    background-color="secondary lighten-1"
  >
    <v-tab>
      {{ $t('admin_detailsButton') }}
    </v-tab>
    <v-tab>
      {{ $t('common_processData') }}
    </v-tab>
    <v-tab>
      {{ $t('common_alarms') }}
    </v-tab>
    <v-tab>
      {{ $t('common_software') }}
    </v-tab>
    <v-tab-item>
      <div class="pa-8">
        <v-row v-if="carwash !== null">
          <carwash-data-row
            v-if="ownerEmail !== null"
            :label="$t('common_owner')"
            :value="ownerEmail"
            :go-to="{ name: 'admin_subscribers_details', params: { id: carwash.subscriber.id }}"
            go-to-icon="eye"
          />
          <carwash-data-row
            :label="$t('admin_product')"
            :value="carwash.product"
          />
          <carwash-data-row
            :label="$t('admin_serialNumber')"
            :value="carwash.serialNumber"
          />
          <carwash-data-row
            :label="$t('name')"
            :value="carwash.name"
          />
          <carwash-data-row
            v-if="carwash.startDate !== null"
            :label="$t('admin_startDate')"
            :value="carwash.startDate"
            format="date"
          />
          <carwash-data-row
            v-if="carwash.warrantyVoided !== null"
            :label="$t('admin_warrantyVoided')"
            :value="carwash.warrantyVoided"
            format="date"
          />
          <v-container fluid>
            <v-row>
              <v-col
                cols="3"
                class="py-2"
              >
                <strong class="text-subtitle-1">
                  {{ $t('admin_allowSubscription') }}
                </strong>
              </v-col>
              <v-col
                cols="9"
                class="py-2"
              >
                <template v-if="!carwash.unsubscribed">
                  <v-icon color="green">
                    mdi-check
                  </v-icon>
                </template>
                <template v-else>
                  <v-icon color="red">
                    mdi-close
                  </v-icon>
                </template>
              </v-col>
            </v-row>
          </v-container>
        </v-row>
      </div>
    </v-tab-item>
    <v-tab-item>
      <process-data-tab
        class="pa-8"
        :serial-number="parseInt($route.params.serialNumber)"
        :initial-data-fetch="true"
      />
    </v-tab-item>
    <v-tab-item>
      <alarms-tab
        class="pa-8"
        :serial-number="parseInt($route.params.serialNumber)"
        :initial-data-fetch="true"
      />
    </v-tab-item>
    <v-tab-item>
      <carwash-software-tab
        class="pa-8"
        :serial-number="$route.params.serialNumber"
        :initial-data-fetch="true"
      />
    </v-tab-item>
  </v-tabs>
</template>

<script>
import CarwashDataRow from '@components/admin/carwash/DataRow.vue';
import CarwashSoftwareTab from '@components/admin/carwash/CarwashSoftwareTab.vue';
import ProcessDataTab from '../predictive-maintenance/details/ProcessDataTab.vue';
import AlarmsTab from '../predictive-maintenance/details/AlarmsTab.vue';

export default {
  name: 'CarwashDetails',
  components: {
    CarwashDataRow,
    ProcessDataTab,
    AlarmsTab,
    CarwashSoftwareTab,
  },
  data() {
    return {
      carwash: null,
      loader: true,
    };
  },
  computed: {
    ownerEmail() {
      if (this.carwash.subscriber === null) {
        return null;
      }

      return this.carwash.subscriber.name;
    },
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      this.loader = true;
      this.axios.get(
        `/administration/carwash/${this.$route.params.serialNumber}`,
      )
        .then((response) => {
          this.carwash = response.data;
        });
      this.loader = false;
    },
  },
};
</script>
