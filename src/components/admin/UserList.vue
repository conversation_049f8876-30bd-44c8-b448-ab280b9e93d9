<template>
  <v-container
    class="pl-6 pr-6"
    grid-list-md
    text-sm-center
    fluid
  >
    <v-row>
      <v-col
        md="4"
      >
        <text-search
          v-model="search"
          :disabled="loader"
          clearable
          @input="getDataDebounced"
        />
      </v-col>
      <v-col
        md="4"
      >
        <v-autocomplete
          v-model="country"
          item-value="shortName"
          item-text="name"
          :label="$t('admin_country')"
          :items="countriesOptions"
          :autocomplete="true"
          :disabled="loader"
          clearable
          prepend-icon="mdi-earth"
          @input="getDataDebounced"
        />
      </v-col>
    </v-row>
    <v-row>
      <v-col class="text-sm-end">
        <div>
          <v-menu offset-y>
            <template #activator="{ on }">
              <v-btn
                small
                transition="slide-y-transition"
                class="expand-menu"
                color="secondary"
                dark
                v-on="on"
              >
                {{ $t('actions.export') }}
                <v-icon right>
                  mdi-menu-down
                </v-icon>
              </v-btn>
            </template>
            <v-list>
              <v-list-item
                v-for="(item, index) in exportItems"
                :key="index"
                @click="exportMenuAction(item.action)"
              >
                <v-list-item-content>
                  <v-list-item-title>
                    {{ $t(item.title) }}
                  </v-list-item-title>
                </v-list-item-content>
              </v-list-item>
            </v-list>
          </v-menu>
        </div>
      </v-col>
    </v-row>
    <v-row>
      <v-col
        cols="12"
        class="pa-0"
      >
        <div
          v-if="loader"
          class="loader-background"
        />
        <v-data-table
          v-resize="onResize"
          mobile-breakpoint="0"
          :headers="dataTable.headers"
          :items="dataTable.items"
          item-key="id"
          :options.sync="pagination"
          :loading="loader"
          :server-items-length="dataTable.totalItems"
          :footer-props="dataTable.footerProps"
        >
          <template #item="{ item }">
            <template v-if="!loader">
              <tr>
                <td
                  class="clickable text-sm-center border-right"
                  @click="goToUser(item.id)"
                >
                  {{ item.id }}
                </td>
                <td
                  class="clickable text-sm-start border-right"
                  @click="goToUser(item.id)"
                >
                  {{ item.email }}
                </td>
                <td
                  class="clickable text-sm-end hidden-sm-and-down md-and-up"
                  @click="goToUser(item.id)"
                >
                  {{ item.firstname ? item.firstname : '-' }}
                </td>
                <td
                  class="clickable text-sm-end border-right border-right"
                  @click="goToUser(item.id)"
                >
                  {{ item.lastname ? item.lastname : '-' }}
                </td>
                <td
                  class="clickable text-sm-center"
                  @click="goToUser(item.id)"
                >
                  <template v-if="item.owner">
                    <v-icon color="green">
                      mdi-check
                    </v-icon>
                  </template>
                  <template v-else>
                    <v-icon color="orange">
                      mdi-close
                    </v-icon>
                  </template>
                </td>
                <td
                  class="clickable text-sm-end"
                  @click="goToUser(item.id)"
                >
                  {{ item.subscriber ? item.subscriber.name : '-' }}
                </td>
                <td
                  class="clickable text-sm-end"
                  @click="goToUser(item.id)"
                >
                  {{ item.subscriber && item.subscriber.country ?
                    item.subscriber.country : '-'
                  }}
                </td>
                <td
                  class="clickable text-sm-end border-right"
                  @click="goToUser(item.id)"
                >
                  {{ item.lastLogin|formatDateDayTime }}
                </td>
                <td
                  class="clickable text-sm-end"
                  @click="goToUser(item.id)"
                >
                  {{ item.subscriber && item.subscriber.subscription ?
                    item.subscriber.subscription : '-'
                  }}
                </td>
                <td
                  class="clickable text-sm-end"
                  @click="goToUser(item.id)"
                >
                  {{ item.subscriber && item.subscriber.details ?
                    $options.filters.formatDateDayTime(
                      item.subscriber.details.subscription.expired_at
                    ) : '-'
                  }}
                </td>
                <td
                  class="text-sm-end"
                >
                  <notification-user-modal
                    v-if="item.notificable"
                    :url="`/administration/user/${item.id}/send`"
                    @success="message => snackbar.showMessage('success', message)"
                    @error="message => snackbar.showMessage('error', message)"
                  />
                </td>
              </tr>
            </template>
          </template>
        </v-data-table>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import TextSearch from '@components/common/filters/TextSearch.vue';
import FiltersHandlingMixin from '@components/common/mixins/FiltersHandlingMixin.vue';
import formatDate from 'date-fns/format';
import debounce from 'lodash/debounce';
import NotificationUserModal from '@/components/common/modal/NotificationModal.vue';

export default {
  components: {
    TextSearch,
    NotificationUserModal,
  },
  mixins: [
    FiltersHandlingMixin,
  ],
  data() {
    return {
      exportItems: [
        {
          title: 'actions.export_csv',
          action: 'exportCsv',
          icon: 'get_app',
        },
        {
          title: 'actions.export_xlsx',
          action: 'exportXlsx',
          icon: 'description',
        },
      ],
      country: null,
      countriesOptions: [],
      singleExpand: true,
      expanded: [],
      search: null,
      searchRule: [
        (value) => /^((?!%|\?|!|\)|\(|'|\\|\/|&).)*$/.test(value) || this.$t(
          'form.validation.invalid_value',
        ),
      ],
      loader: true,
      pagination: {
        page: 1,
        itemsPerPage: 25,
        sortBy: ['email'],
        sortDesc: [true],
      },
      dataForExportItems: [],
      dataTable: {
        totalItems: 0,
        items: [],
        footerProps: {
          'items-per-page-options': [25, 50],
          'items-per-page-text': this.$t('common_rowsPerPage'),
        },
        headers: [
          {
            text: this.$t('common_id'),
            value: 'id',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_email'),
            value: 'email',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_formName'),
            value: 'firstname',
            class: 'hidden-sm-and-down md-and-up',
            showInRowExpand: true,
            align: 'right',
            sortable: false,
          },
          {
            text: this.$t('common_surname'),
            value: 'lastname',
            showInRowExpand: false,
            align: 'right',
            sortable: false,
          },
          {
            text: this.$t('admin_isOwner'),
            value: 'owner',
            class: 'text-sm-center',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_usersOwner'),
            value: 'ownerEmail',
            showInRowExpand: false,
            align: 'right',
            sortable: false,
          },
          {
            text: this.$t('admin_country'),
            value: 'ownerCountry',
            showInRowExpand: false,
            align: 'right',
            sortable: false,
          },
          {
            text: this.$t('admin_lastLogin'),
            value: 'lastLogin',
            class: 'text-sm-end',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('admin_subscriptionCode'),
            value: 'subscriptionCode',
            showInRowExpand: false,
            align: 'right',
            sortable: false,
          },
          {
            text: this.$t('admin_subscriptionEnds'),
            value: 'subscriptionEnds',
            showInRowExpand: false,
            align: 'right',
            sortable: false,
          },
          {
            text: this.$t('common_actions'),
            value: 'actions',
            showInRowExpand: false,
            align: 'right',
            sortable: false,
          },
        ],
      },
    };
  },
  watch: {
    filtering: {
      handler() {
        this.pagination.page = 1;
        this.getData();
      },
      deep: true,
    },
    pagination: {
      handler(newValue, oldValue) {
        if (oldValue.itemsPerPage !== newValue.itemsPerPage) {
          this.pagination.page = 1;
        }
        this.getData();
      },
      deep: true,
    },
  },
  created() {
    this.getDataDebounced = debounce(() => {
      this.pagination.page = 1;
      this.getData();
    }, 1000);
  },
  mounted() {
    this.getCurrencies();
    this.getData();
  },
  methods: {
    exportMenuAction(actionName) {
      if (actionName === 'exportCsv') {
        this.getDataForExport('csv');
      } else if (actionName === 'exportXlsx') {
        this.getDataForExport('xlsx');
      }
    },
    getExportFileName(name, fileType) {
      const today = formatDate(new Date(), 'YYYY-MM-DD');
      const nameArray = [[name, today].join('_'), fileType];

      return nameArray.join('.');
    },
    getCurrencies() {
      this.axios.get(
        '/api/lists/countries',
      )
        .then((response) => {
          if ((response.status === 200) && response.data) {
            this.countriesOptions = response.data;
          }
        });
    },
    getDataForExport(fileType = 'csv') {
      const fileName = this.getExportFileName(
        `${this.$t('admin_usersHeading')}`,
        fileType,
      );

      this.axios.get(
        '/administration/users',
        {
          params: {
            page: 1,
            perPage: 9999,
            search: this.search,
            sortBy: this.pagination.sortBy[0],
            sortDesc: this.pagination.sortDesc[0],
            country: this.country,
          },
        },
      )
        .then((response) => {
          const dataToExport = this.mapForExport(response.data.data);
          if (!this.exporter.export(dataToExport, fileType, fileName)) {
            this.snackbar.showMessage(
              'error',
              this.$t('transactions.export_error'),
            );
          }
        });
    },
    mapForExport(data) {
      const headers = [
        this.$t('common_email'),
        this.$t('common_formName'),
        this.$t('common_surname'),
        this.$t('admin_isOwner'),
        this.$t('common_usersOwner'),
        this.$t('admin_ownerBkf'),
        this.$t('admin_country'),
        this.$t('admin_lastLogin'),
        this.$t('admin_subscriptionCode'),
        this.$t('admin_subscriptionEnds'),
      ];

      const filtered = data.map(
        (row) => [
          row.email,
          row.firstname ?? '-',
          row.lastname ?? '-',
          row.owner,
          row.subscriber ? row.subscriber.name : '-',
          row.subscriber.ownerBkf ?? '-',
          row.subscriber.country ? row.subscriber.country : '-',
          this.$options.filters.formatDateDayTime(row.lastLogin) ?? '-',
          row.subscriber.subscription ?? '-',
          this.$options.filters.formatDateDayTime(row.subscriber.details.subscription.expired_at) ?? '-',
        ],
      );
      filtered.unshift(headers);
      return filtered;
    },
    openModal() {
      this.$refs.addModal.dialog = true;
    },
    onResize() {
      this.windowWidth = window.innerWidth;
    },
    getData() {
      this.loader = true;
      this.axios.get(
        '/administration/users',
        {
          params: {
            page: this.pagination.page,
            perPage: this.pagination.itemsPerPage,
            search: this.search,
            sortBy: this.pagination.sortBy[0],
            sortDesc: this.pagination.sortDesc[0],
            country: this.country,
          },
        },
      )
        .then((response) => {
          this.dataTable.items = response.data.data;
          this.dataTable.totalItems = response.data.count;
          this.loader = false;
        });
    },
    goToUser(id) {
      this.$router.push({ name: 'admin_users_details', params: { id } });
    },
    openNotificationDialog(userId) {
      this.$refs.notificationDialog.open(userId);
    },
  },
};
</script>

<style scoped>

.clickable {
  cursor: pointer;
}

.loader {
  top: 35%;
  z-index: 5;
}

.loader-background {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, .3);
  z-index: 4;
}

.expand-table {
  width: 100%
}
</style>
