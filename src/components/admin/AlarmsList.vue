<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
    main-container
  >
    <v-row>
      <div
        v-if="loading"
        class="progress-overlay"
      >
        <span>
          <v-progress-circular
            indeterminate
            size="32"
          />
        </span>
      </div>
      <v-col
        v-if="!history"
        cols="3"
        align="left"
      >
        <v-row>
          <v-col
            cols="4"
            class="pl-1 pr-1"
          >
            <v-btn
              block
              width="100%"
              elevation="0"
              target="_blank"
              color="primary"
              :disabled="!selectedIssues.length"
              @click="openDialog('close')"
            >
              <v-icon
                dark
                small
                class="mr-1"
              >
                mdi-close
              </v-icon>
              {{ $t('actions.close') }}
            </v-btn>
          </v-col>
          <v-col
            cols="4"
            class="pl-1 pr-1"
          >
            <v-btn
              block
              width="100%"
              elevation="0"
              target="_blank"
              color="secondary"
              :disabled="!selectedIssues.length"
              @click="openDialog('postpone')"
            >
              <v-icon
                dark
                small
                class="mr-1"
              >
                mdi-arrow-right
              </v-icon>
              {{ $t('actions.postpone') }}
            </v-btn>
          </v-col>
          <v-col
            cols="4"
            class="pl-1 pr-1"
          >
            <v-btn
              block
              width="100%"
              elevation="0"
              target="_blank"
              color="green lighten-2"
              :disabled="!selectedIssues.length"
              @click="openDialog('service')"
            >
              <v-icon
                dark
                small
                class="mr-1"
              >
                mdi-wrench
              </v-icon>
              {{ $t('actions.service') }}
            </v-btn>
          </v-col>
        </v-row>
        <v-row>
          <v-col
            cols="3"
          >
            <v-btn
              color="secondary"
              class="mt-2"
              @click="getData"
            >
              <v-icon>mdi-refresh</v-icon>
            </v-btn>
          </v-col>
          <v-col
            cols="8"
          >
            <v-select
              v-model="selectedIssueLevels"
              :label="$t('admin_alarmLevel')"
              :items="issueLevels"
              multiple
              chips
              small-chips
            >
              <template #item="{ item, on }">
                <v-icon
                  class="pl-2 pr-2 text-right"
                  :color="alarmLevelIconsColors[item]"
                  v-on="on"
                >
                  {{ alarmLevelIcons[item] }}
                </v-icon>
                {{ item }}
              </template>
              <template #selection="{ item, on }">
                <v-icon
                  class="pl-2 pr-2 text-right"
                  :color="alarmLevelIconsColors[item]"
                  v-on="on"
                >
                  {{ alarmLevelIcons[item] }}
                </v-icon>
              </template>
            </v-select>
          </v-col>
        </v-row>
      </v-col>
      <v-col
        v-if="!history"
        cols="5"
        class="d-flex"
      >
        <v-text-field
          v-model="search"
          :label="$t('common_search')"
          prepend-inner-icon="mdi-magnify"
          class="mt-0 pt-0 mr-2"
        />

        <v-select
          v-model="selectedIssueGroups"
          :label="$t('common_status')"
          :items="issuesGroups"
          prepend-inner-icon="mdi-group"
          item-value="value"
          item-text="name"
          multiple
          chips
          small-chips
          clearable
          class="ml-2 mt-0 pt-0"
          @blur="getData"
        />
      </v-col>
      <v-col
        v-if="!history"
        cols="4"
      >
        <v-row>
          <v-col
            cols="6"
            class="pl-1 pr-1"
          >
            <v-btn
              v-if="items.length"
              block
              width="100%"
              outlined
              @click="selectAllCarwashes"
            >
              {{ (openedCarwashes.length) ?
                $t('admin_closeAllCarwashes') : $t('admin_openAllCarwashes') }}
            </v-btn>
          </v-col>
          <v-col
            cols="6"
            class="pl-1 pr-1"
          >
            <v-btn
              v-if="items.length"
              block
              width="100%"
              outlined
              class="ml-2"
              :disabled="!openedCarwashes.length"
              @click="selectAllAlarms"
            >
              {{ (issuesCheckboxes.length) ?
                $t('admin_deselectAllIssues') : $t('admin_selectAllIssues') }}
            </v-btn>
          </v-col>
        </v-row>
      </v-col>
      <v-col
        cols="6"
        align="left"
        class="pt-0"
      >
        <div
          class="table-wrapper"
          :style="{
            'height': `${computeHeight}px`
          }"
        >
          <v-data-table
            :headers="headers"
            :items="items"
            :single-expand="false"
            :expanded.sync="openedCarwashes"
            :footer-props="dataTable.footerProps"
            :hide-default-footer="true"
            mobile-breakpoint="0"
            :disable-pagination="true"
            item-key="sn"
            show-expand
            hide-default-header
            class="grey--text text--darken-1 mt-6"
          >
            <template #progress>
              <div class="text-center mx-n4">
                <v-progress-linear
                  class="loader"
                  indeterminate
                  color="primary"
                />
              </div>
            </template>
            <template #item="{ item, expand: expandSub, isExpanded }">
              <template v-if="!loading">
                <tr
                  :class="isExpanded ? 'selected' : ''"
                  @click="expandSub(!isExpanded);setSelectedCarawsh(item.sn);"
                >
                  <td
                    width="100"
                  >
                    <v-tooltip bottom>
                      <template #activator="{ on }">
                        <v-avatar
                          size="22"
                          class="rounded-full transparent-with-border text-right"
                          v-on="on"
                        >
                          {{ item.issues.length }}
                        </v-avatar>
                      </template>
                      <span>
                        {{ $t('common_issueQuantity') }}
                        {{ item.issues.length }}
                      </span>
                    </v-tooltip>
                    <v-tooltip bottom>
                      <template #activator="{ on }">
                        <v-icon
                          class="pr-2 text-right"
                          :color="alarmLevelIconsColors[item.level.name]"
                          v-on="on"
                        >
                          {{ alarmLevelIcons[item.level.name] }}
                        </v-icon>
                      </template>
                      <span>
                        {{ item.level.name }}
                      </span>
                    </v-tooltip>
                  </td>
                  <td class="text-start">
                    <strong class="text-subtitle-2 grey--text text--darken-3">
                      <span
                        v-if="item.name"
                        class="grey--text text--darken-3"
                      >
                        {{ item.name }}
                        {{ item.sn }}
                      </span>
                    </strong>
                    <br>
                    <span
                      v-if="item.address"
                      class="grey--text text--darken-3 pb-1"
                    >
                      {{ item.address }}
                    </span>
                  </td>
                </tr>
              </template>
            </template>
            <template #expanded-item="{ item, index: carwashIndex }">
              <tr
                style="border-bottom: 1px solid"
              >
                <td
                  class="pl-0 pr-0"
                  colspan="5"
                >
                  <v-card flat>
                    <v-simple-table
                      class="expand-table"
                      dense
                    >
                      <template #default>
                        <tbody>
                          <tr>
                            <td
                              class="text-center"
                            >
                              {{ $t('common_status') }}
                            </td>
                            <td
                              v-if="selectedGroup !== 'close'"
                              class="text-center"
                            >
                              {{ $t('actions.actions') }}
                            </td>
                            <td
                              class="text-center"
                            >
                              {{ $t('common_tableDate') }}
                            </td>
                            <td
                              class="text-center"
                            >
                              {{ $t('common_tableDescription') }}
                            </td>
                          </tr>
                          <tr
                            v-for="(alarm, index) in item.issues"
                            :key="alarm.id"
                            :class="`issue ${selectedIndex === `${carwashIndex}${index}`
                              ? 'selected-row' : ''}`"
                            @click="onRowClick(alarm, carwashIndex, index)"
                          >
                            <td
                              width="100"
                            >
                              <tr class="text-right pr-3">
                                <td>
                                  <v-tooltip bottom>
                                    <template #activator="{ on }">
                                      <v-icon
                                        class="pl-10 pl-11 pr-2"
                                        :color="alarmLevelIconsColors[alarm.level.name]"
                                        v-on="on"
                                      >
                                        {{ alarmLevelIcons[alarm.level.name] }}
                                      </v-icon>
                                    </template>
                                    <span>{{ alarm.level.name }}</span>
                                  </v-tooltip>
                                </td>
                                <td>
                                  <v-tooltip bottom>
                                    <template #activator="{ on }">
                                      <v-avatar
                                        size="22"
                                        class="rounded-full transparent-with-border mr-3"
                                        v-on="on"
                                      >
                                        {{ alarm.activeAlarms.length }}
                                      </v-avatar>
                                    </template>
                                    <span>
                                      {{ $t('common_quantity') }}
                                      {{ alarm.activeAlarms.length }}
                                    </span>
                                  </v-tooltip>
                                </td>
                                <td>
                                  {{ $t('issues-alarm.' + (alarm.status).toLowerCase()) }}
                                </td>
                              </tr>
                            </td>
                            <td
                              v-if="selectedGroup !== 'close'"
                              class="pt-1 pb-0"
                              width="40"
                              @click.stop
                            >
                              <v-checkbox
                                :key="alarm.index"
                                v-model="issuesCheckboxes[alarm.index]"
                                dense
                                class="pt-3"
                                @change="onChange($event, alarm)"
                              />
                            </td>
                            <td
                              class="no-white-space-wrap text-center"
                              width="100"
                            >
                              {{
                                translateDateToDaysAgo(parseDate(alarm.ctime))
                              }}
                              <br>
                              {{
                                parseHour(alarm.ctime)
                              }}
                            </td>
                            <td>
                              {{ alarm.alarmDefinition.text }}
                            </td>
                          </tr>
                        </tbody>
                      </template>
                    </v-simple-table>
                  </v-card>
                </td>
              </tr>
            </template>
          </v-data-table>
        </div>
      </v-col>
      <v-col
        cols="6"
        class="pt-2 pl-4 tab-right"
        align="left"
        :style="{
          'height': `${computeHeight}px`
        }"
      >
        <alarms-tab
          v-if="items.length"
          :selected-carwash="selectedCarwash"
          :issue="theIssue"
          :carwash="theCarwash"
          :contact-details="contactDetails"
          :service-visits="serviceVisits"
          :closed-issues="closedIssues"
          :closed-issues-qty="closedIssuesQty"
          :other-issues="otherIssues"
          :loading="loadingDetails"
        />
      </v-col>
    </v-row>
    <v-dialog
      v-model="dialog"
      width="500"
      content-class="dialogWidth-3"
      persistent
      header-color="primary"
    >
      <v-card>
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">
              {{ $t('common_management') }}
            </h5>
          </span>
        </v-card-title>
        <v-card-text>
          <div
            class="mt-5"
          >
            <div class="mb-1 subtitle-1">
              {{ $t('common_action') }}
              <strong>{{ $t('actions.' + issueAction) }}</strong>
              <br>
              {{ $t('common_refersTo') }}
            </div>
          </div>
          <div
            class="mt-5"
          >
            <v-chip
              v-for="(issue, index) in selectedIssues"
              :key="index"
              density="compact"
              class="mr-2"
              small
            >
              {{ issue.carwashSerialNumber }}/{{ issue.alarmDefinition }}
            </v-chip>
          </div>
          <v-form
            ref="formUserEdit"
            v-model="form.valid"
            lazy-validation
          >
            <v-menu
              v-if="issueAction === 'postpone'"
              ref="menu"
              v-model="menu"
              :close-on-content-click="false"
              :return-value.sync="date"
              transition="scale-transition"
              offset-y
              min-width="auto"
            >
              <template #activator="{ on, attrs }">
                <v-text-field
                  :label="$t('common_postpone')"
                  prepend-icon="mdi-calendar-clock"
                  name="date"
                  v-bind="attrs"
                  readonly
                  :value="datetime"
                  required
                  :rules="nameRules"
                  :error-messages="nameErrors"
                  v-on="on"
                />
                <div
                  v-if="nameErrors.length > 0"
                  class="red--text"
                >
                  <ul>
                    <li
                      v-for="error in nameErrors"
                      :key="error"
                    >
                      {{ error }}
                    </li>
                  </ul>
                </div>
              </template>
              <v-card>
                <v-card-text class="px-0 py-0">
                  <v-row>
                    <v-col class="pr-0">
                      <v-date-picker
                        v-model="date"
                        :min="today"
                      />
                    </v-col>
                    <v-col>
                      <v-time-picker
                        v-model="time"
                        format="24hr"
                      />
                    </v-col>
                  </v-row>
                </v-card-text>
                <v-card-actions>
                  <v-spacer />
                  <v-btn
                    color="primary"
                    @click="$refs.menu.save(date)"
                  >
                    OK
                  </v-btn>
                </v-card-actions>
              </v-card>
            </v-menu>
            <v-select
              v-if="issueAction === 'service'"
              v-model="selectedPriority"
              :items="issuePriority"
              item-value="val"
              item-text="text"
              :label="$t('common_priority')"
              prepend-icon="mdi-priority-high"
              class="mt-6"
              required
              :rules="rules.selectRequired"
            />
            <v-text-field
              ref="explanation"
              v-model="explanation"
              required
              :rules="rules.textRequired"
              :label="$t('common_description')"
              prepend-icon="mdi-text"
              class="mt-2"
            />
          </v-form>
        </v-card-text>
        <v-divider />
        <v-card-actions>
          <v-spacer />
          <v-btn
            v-if="issueAction === 'postpone'"
            color="green lighten-2"
            tile
            @click="postponeForOneHour"
          >
            {{ $t('common_postponeForOneHour') }}
          </v-btn>
          <v-btn
            color="secondary"
            tile
            @click="dialog = false"
          >
            {{ $t('actions.cancel') }}
          </v-btn>
          <v-btn
            color="primary"
            tile
            @click="saveIssue"
          >
            {{ $t('actions.save') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import moment from 'moment';
import 'moment-timezone';
import FiltersHandlingMixin from '@components/common/mixins/FiltersHandlingMixin.vue';
import { mapGetters } from 'vuex';
import AlarmsTab from '@components/admin/AlarmsTabs.vue';

export default {
  name: 'AlarmsList',
  components: {
    AlarmsTab,
  },
  mixins: {
    FiltersHandlingMixin,
  },
  props: {
    history: {
      type: Boolean,
      default: false,
    },
    selectedGroup: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      form: {
        valid: false,
      },
      rules: {
        textRequired: [(v) => !!v || this.$t('common_fieldRequired')],
        selectRequired: [(v) => (!!v || v === '') || this.$t('common_fieldRequired')],
      },
      alarmLevelIcons: {
        ok: 'mdi-check-circle',
        ignore: 'mdi-cancel',
        information: 'mdi-information',
        warning: 'mdi-alert',
        error: 'mdi-alert-circle',
        unspecified: 'mdi-help-circle',
      },
      alarmLevelIconsColors: {
        ok: 'green',
        ignore: 'brown',
        information: 'blue',
        warning: 'orange',
        error: 'red',
        unspecified: 'grey',
      },
      expanded: [],
      loading: false,
      loadingDetails: false,
      filtering: {
        search: {
          searchText: null,
        },
      },
      dataTable: {
        items: [],
      },
      theIssue: null,
      otherIssues: [],
      theCarwash: {},
      search: '',
      dialog: false,
      selectedIssues: [],
      date: (new Date(Date.now() - (new Date()).getTimezoneOffset()
        * 60000)).toISOString().substr(0, 10),
      time: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000))
        .toISOString()
        .substr(-13, 8),
      menu: false,
      explanation: '',
      issueAction: 'close',
      nameRules: [
        (v) => !!v || this.$t('common_fieldRequired'),
        (v) => (v && v.length <= 16) || 'Name must be less than 16 characters',
      ],
      nameErrors: [],
      issuesGroups: [
        {
          name: this.$t('common_new'),
          value: 'new',
        },
        {
          name: this.$t('common_open'),
          value: 'open',
        },
        {
          name: this.$t('common_duedate'),
          value: 'duedate',
        },
        {
          name: this.$t('common_jr'),
          value: 'JR',
        },
      ],
      selectedIssueGroups: [],
      selectedIssueLevels: [],
      selectedCarwash: null,
      selectedIndex: null,
      issuesCheckboxes: [],
      openedCarwashes: [],
      issuePriority: [
        { val: '', text: this.$t('common_none') },
        { val: 'AWK', text: this.$t('common_awk') },
        { val: 'AWC', text: this.$t('common_awc') },
        { val: 'AWP', text: this.$t('common_awp') },
      ],
      selectedPriority: null,
      contactDetails: {},
      serviceVisits: {},
      closedIssues: [],
      closedIssuesQty: null,
      today: new Date().toISOString().substr(0, 10),
      headers: [
        { text: this.$t('common_formName'), value: 'sn', sortable: false },
        { text: this.$t('common_surname'), value: 'name', sortable: false },
      ],
    };
  },
  computed: {
    computeHeight() {
      return window.innerHeight - 350;
    },
    datetime() {
      return `${this.date} ${this.time}`;
    },
    items() {
      let i = 0;
      const elements = this.dataTable.items.filter((item) => item.name.toLowerCase()
        .includes(this.search.toLowerCase()));
      elements.forEach((item) => {
        item.issues.forEach((issue) => {
          // eslint-disable-next-line no-param-reassign
          issue.index = i;
          i += 1;
        });
      });
      return elements.filter((item) => this.selectedIssueLevels.includes(item.level.name));
    },
    issueLevels() {
      const elements = this.dataTable.items.filter((item) => item.name.toLowerCase()
        .includes(this.search.toLowerCase()));
      const uniqueLevelNames = [...new Set(elements.map((item) => item.level.name))];
      return uniqueLevelNames;
    },
    ...mapGetters({
      user: 'auth/getUser',
    }),
  },
  mounted() {
    const statusArray = this.selectedGroup.split(',');
    statusArray.forEach((el) => {
      this.selectedIssueGroups.push(el);
    });
    this.getData();
  },
  methods: {
    getData() {
      this.selectedCarwash = null;
      this.selectedIssues = [];
      this.dataTable.items = [];
      this.loading = true;
      this.theIssue = null;
      this.closedIssues = [];
      this.closedIssuesQty = null;
      if (this.selectedIssueGroups.length === 0) {
        this.loading = false;
        return;
      }
      this.axios.get(
        '/administration/carwashissues',
        {
          params: {
            groups: (this.selectedIssueGroups.length) ? this.selectedIssueGroups.join() : null,
          },
        },
      )
        .then((response) => {
          this.dataTable.items = Object.values(response.data.data);
          const elements = this.dataTable.items.filter((item) => item.name.toLowerCase()
            .includes(this.search.toLowerCase()));
          const uniqueLevelNames = [...new Set(elements.map((item) => item.level.name))];
          this.selectedIssueLevels = uniqueLevelNames;
          this.loading = false;
        });
    },
    carwashHasAlarms: (carwash) => carwash.alarmsStatus === 'warning',
    carwashAlarmsTooltip(carwash) {
      if (!this.carwashHasAlarms(carwash)) {
        return this.$t('common_noAlarms');
      }

      return this.$t('actions.click_to_show_more_details');
    },
    onRowClick(item, carwashIndex, index) {
      this.theIssue = false;
      this.selectedIndex = `${carwashIndex}${index}`;
      this.selectedCarwash = item.carwash.sn;
      this.getCarwashAlarms(item.carwash.sn);
      this.getIssueDetails(item.id);
      this.getOtherIssues(item.carwash.sn);
      this.getContactDetails(item.carwash.sn);
      this.getServiceVisits(item.carwash.sn);
      this.getClosedIssues(item.carwash.sn);
    },
    getOtherIssues(id) {
      const groups = this.issuesGroups.map((obj) => obj.value);
      const nonSelectedGroups = groups.filter((item) => !this.selectedIssueGroups.includes(item));
      this.loadingDetails = true;
      this.axios.get(
        `/administration/carwashissues/list?groups=${nonSelectedGroups.join(',')}&serial=${id}`,
      )
        .then(
          (response) => {
            if (response.status === 200) {
              this.otherIssues = response.data.data;
              this.loadingDetails = false;
            }
          },
        );
    },
    getIssueDetails(id) {
      this.loadingDetails = true;
      this.axios.get(
        `/administration/carwashissue/${id}`,
      )
        .then(
          (response) => {
            if (response.status === 200) {
              this.theIssue = response.data;
              this.loadingDetails = false;
            }
          },
        );
    },
    getClosedIssues(id) {
      this.closedIssues = [];
      this.closedIssuesQty = null;
      this.loadingDetails = true;
      this.axios.get(
        `/administration/carwashissues/list?groups=close&serial=${id}`,
      )
        .then(
          (response) => {
            if (response.status === 200) {
              this.closedIssues = response.data.data;
              this.closedIssuesQty = response.data.total;
              this.loadingDetails = false;
            }
          },
        );
    },
    getCarwashAlarms(sn) {
      this.closedIssues = [];
      this.closedIssuesQty = null;
      this.theCarwash = {};
      this.loadingDetails = true;
      this.axios.get(
        `/cm/alarm/by_serials/${sn}`,
      )
        .then(
          (response) => {
            if (response.status === 200) {
              const object = response.data.shift();
              this.theCarwash = object;
              this.loadingDetails = false;
            }
          },
        );
    },
    getContactDetails(sn) {
      this.contactDetails = {};
      this.loadingDetails = true;
      this.axios.get(
        `/administration/bkf_info/carwash_contact_details/${sn}`,
      )
        .then(
          (response) => {
            if (response.status === 200) {
              this.contactDetails = response.data;
              this.loadingDetails = false;
            }
          },
          () => {
            // on error
            this.loadingDetails = false;
          },
        );
    },
    getServiceVisits(sn) {
      this.serviceVisits = {};
      this.loadingDetails = true;
      this.axios.get(
        `/administration/bkf_info/service_visits/${sn}`,
      )
        .then(
          (response) => {
            if (response.status === 200) {
              this.serviceVisits = response.data;
              this.loadingDetails = false;
            }
          },
        ).catch((error) => {
          if (error.response) {
            this.loadingDetails = false;
          }
        });
    },
    openDialog(action) {
      this.dialog = true;
      this.issueAction = action;
    },
    postponeForOneHour() {
      const currentMoment = moment();
      this.date = (new Date(Date.now() - (new Date()).getTimezoneOffset()
        * 60000)).toISOString().substr(0, 10);
      this.time = currentMoment.add(1, 'hour').format('HH:mm');
      this.saveIssue();
    },
    saveIssue() {
      this.$validator.validateAll();
      if (this.$refs.formUserEdit.validate()) {
        this.loading = true;
        this.axios.post(
          '/administration/carwashissues/update',
          {
            action: this.issueAction,
            explanation: this.explanation,
            date: this.datetime,
            issues: this.selectedIssues,
            priority: (this.selectedPriority === '') ? '' : this.selectedPriority,
          },
        )
          .then(() => {
            this.dialog = false;
            this.issueAction = '';
            this.explanation = '';
            this.selectedIssues = [];
            this.date = (new Date(Date.now() - (new Date()).getTimezoneOffset()
            * 60000)).toISOString().substr(0, 10);
            this.getData();
          });
      }
    },
    onChange(newValue, alarm) {
      this.selectedCarwash = alarm.carwash.sn;
      const data = {
        id: alarm.id,
        alarmDefinition: alarm.alarmDefinition.id,
        carwashSerialNumber: alarm.carwash.sn,
        description: alarm.alarmDefinition.text,
        level: alarm.level.name,
      };
      if (newValue) {
        this.selectedIssues.push(data);
      } else {
        const ids = this.selectedIssues.map((el) => el.id);
        const index = ids.indexOf(alarm.id);
        if (index !== -1) {
          this.selectedIssues.splice(index, 1);
        }
      }
    },
    parseDate(dateString) {
      const date = moment(dateString);
      return date.format('YYYY-MM-DD');
    },
    parseHour(dateString) {
      const date = moment(dateString);
      return date.format('HH:mm:ss');
    },
    translateDateToDaysAgo(dateString) {
      const inputMoment = moment(dateString, 'YYYY-MM-DD');
      const currentDate = moment();
      const diffInDays = currentDate.diff(inputMoment, 'days');
      let result = dateString;
      if (diffInDays === 0) {
        result = this.$t('common_now');
      } else if (diffInDays === 1) {
        result = this.$t('common_yesterday');
      }
      return result;
    },
    setSelectedCarawsh(serialNumber) {
      this.issuesCheckboxes = [];
      this.selectedIssues = [];
      this.theIssue = null;
      this.selectedCarwash = serialNumber;
      this.getCarwashAlarms(serialNumber);
      this.getContactDetails(serialNumber);
      this.getServiceVisits(serialNumber);
      this.getOtherIssues(serialNumber);
      this.getClosedIssues(serialNumber);
      this.selectedIndex = null;
      this.openedCarwashes = this.manageObjectByPropertyValue(this.openedCarwashes, 'sn', serialNumber);
    },
    selectAllAlarms() {
      this.selectedIssues = [];
      if (this.issuesCheckboxes.length === 0) {
        const indexes = [];
        this.openedCarwashes.forEach((carwash) => {
          carwash.issues.forEach((issue) => {
            this.onChange(true, issue);
            indexes.push(issue.index);
          });
        });
        for (let i = 0; i <= Math.max(...indexes); i += 1) {
          if (indexes.includes(i)) {
            this.issuesCheckboxes.push(1);
          } else {
            this.issuesCheckboxes.push(null);
          }
        }
      } else {
        this.issuesCheckboxes = [];
        this.selectedIssues = [];
      }
    },
    selectAllCarwashes() {
      if (this.openedCarwashes.length === 0) {
        this.items.forEach((item) => {
          this.openedCarwashes.push({ sn: item.sn });
        });
      } else {
        this.openedCarwashes = [];
        this.issuesCheckboxes = [];
        this.selectedIssues = [];
      }
    },
    manageObjectByPropertyValue(array, property, value) {
      const index = array.findIndex((item) => item[property] === value);
      if (index !== -1) {
        array.splice(index, 1);
      } else {
        array.push({ sn: value });
      }
      return array;
    },
  },
};
</script>

<style lang="css" scoped>

.expand-table {
  width: 100%;
  border-left: 1px solid #d6d6d6;
  border-right: 1px solid #d6d6d6;
  border-radius: 0px !important;
}
.progress-overlay {
  position: absolute;
  width: 96%;
  height: 68%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255,255,255,1);
  z-index: 5;
  color: #3B81E3;
}
.selected {
  background-color: #efefef;
  border-left: 1px solid #d6d6d6;
  border-right: 1px solid #d6d6d6;
}
.selected-row, .expand-table tr.issue:hover {
  background-color: #e9f5fd !important;
}
.table-wrapper {
  overflow-y: auto;
}
.transparent-with-border {
  background-color: transparent;
  border: 1px solid #cacaca;
}
.text-right {
  float: right;
}
.tab-right{
  overflow-y:auto;
}
.main-container{
  overflow: hidden;
}
</style>
