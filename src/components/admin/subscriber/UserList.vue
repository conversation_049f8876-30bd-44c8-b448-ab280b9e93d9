<template>
  <v-container fluid>
    <div class="d-flex justify-end">
      <v-btn
        class="mb-4"
        small
        color="primary"
        @click.stop
        @click.native="openModal('addSubscriptionModal')"
      >
        <v-icon left>
          mdi-plus
        </v-icon>
        {{ $t('actions.add_user') }}
      </v-btn>
    </div>
    <v-row>
      <v-col
        cols="12"
        class="text-sm-start"
      >
        <v-data-table
          mobile-breakpoint="0"
          :headers="headers"
          :items="items"
          :items-per-page="-1"
          :loading="loader"
          dense
        >
          <template #[`item.actions`]="{ item }">
            <td class="text-sm-end">
              <v-btn
                x-small
                tile
                rounded
                fab
                elevation="1"
                color="primary"
                class="my-1"
                @click="goToUser(item.id)"
              >
                <v-icon>mdi-eye</v-icon>
              </v-btn>
            </td>
          </template>

          <template #[`item.lastLogin`]="{ item }">
            {{ item.lastLogin|formatDateDayTime }}
          </template>
        </v-data-table>
      </v-col>
    </v-row>
    <user-add-modal
      ref="addModal"
      :subscriber-id="subscriberId"
      :on-success="getData"
    />
  </v-container>
</template>

<script>

import UserAddModal from '@components/admin/user/modal/UserAddModal.vue';

export default {
  name: 'SubscriberUsersList',
  components: { UserAddModal },
  props: {
    subscriberId: {
      type: Number,
      default: null,
      required: false,
    },
  },
  data() {
    return {
      items: [],
      loader: true,
      id: this.subscriberId,
      headers: [
        {
          text: this.$t('common_email'),
          value: 'email',
          class: 'text-sm-begin',
        },
        {
          text: this.$t('common_firstName'),
          value: 'firstname',
          class: 'text-sm-begin',
        },
        {
          text: this.$t('common_lastName'),
          value: 'lastname',
          class: 'text-sm-begin',
        },
        {
          text: this.$t('common_lastLogin'),
          value: 'lastLogin',
          class: 'text-sm-begin',
        },
        {
          text: this.$t('admin_isOwner'),
          value: 'owner',
          class: 'text-sm-begin',
        },
        {
          text: this.$t('actions.actions'),
          value: 'actions',
          class: 'text-sm-end',
        },
      ],
    };
  },
  created() {
    this.getData();
  },
  methods: {
    getData() {
      this.loader = true;
      this.axios.get(
        `/administration/subscriber/${this.subscriberId}/users`,
      )
        .then((response) => {
          this.items = response.data;
          this.loader = false;
        });
    },
    goToUser(userId) {
      this.$router.push({ name: 'admin_users_details', params: { id: userId } });
    },
    openModal() {
      this.$refs.addModal.dialog = true;
    },
  },
};
</script>
