<template>
  <div>
    <div
      v-if="loading"
      class="d-flex justify-center align-center"
    >
      <v-progress-circular
        color="primary"
        indeterminate
      />
    </div>
    <v-row v-else>
      <v-col
        sm="12"
        md="6"
      >
        <div
          v-for="(item, id) in filteredData"
          :key="`detailsField${id}`"
        >
          <v-text-field
            v-if="item.type === 'integer'"
            v-model="item.value"
            :readonly="item.editable !== true"
            :disabled="item.editable !== true"
            :label="item.title"
            :prepend-icon="item.prependIcon"
            :append-icon="item.appendIcon"
            max="100"
            min="0"
            type="number"
          />
          <v-checkbox
            v-else-if="item.type === 'checkbox'"
            v-model="item.value"
            :readonly="item.editable !== true"
            :disabled="item.editable !== true"
            :label="item.title"
            :prepend-icon="item.prependIcon"
            :append-icon="item.appendIcon"
          />
          <v-autocomplete
            v-else-if="item.type === 'dealer'"
            v-model="item.value"
            item-value="id"
            item-text="name"
            :prepend-icon="item.prependIcon"
            :loading="dealersIsLoading"
            :label="item.title"
            :items="dealersOptions"
            :autocomplete="true"
            :readonly="item.editable !== true"
            :disabled="item.editable !== true"
            clearable
          />
          <v-autocomplete
            v-else-if="item.type === 'currency'"
            v-model="item.value"
            :prepend-icon="item.prependIcon"
            :label="item.title"
            :items="currenciesOptions"
            :autocomplete="true"
            :readonly="item.editable !== true"
            :disabled="item.editable !== true"
            item-value="id"
            item-text="code"
          />
          <v-text-field
            v-else
            v-model="item.value"
            :readonly="item.editable !== true"
            :disabled="item.editable !== true"
            :label="item.title"
            :prepend-icon="item.prependIcon"
            :append-icon="item.appendIcon"
          />
        </div>
      </v-col>
    </v-row>
    <v-row>
      <v-col
        cols="12"
        align="right"
        class="pt-2"
      >
        <v-btn
          color="primary"
          @click="save"
        >
          {{ $t('actions.save') }}
        </v-btn>
      </v-col>
    </v-row>
  </div>
</template>

<script>

export default {
  name: 'DetailsList',
  props: {
    subscriberId: {
      type: Number,
      default: null,
      required: false,
    },
    subscriber: {
      type: [Array, Object],
      default: null,
      required: false,
    },
  },
  data() {
    return {
      dealersIsLoading: true,
      dealersOptions: [],
      currenciesOptions: [],
      data: [],
      loading: false,
      properties: [
        {
          title: this.$t('common_discount'),
          valuePath: 'subscriptionDiscount',
          type: 'integer',
          editable: true,
          prependIcon: 'mdi-gift',
          appendIcon: 'mdi-percent',
        },
        {
          title: this.$t('admin_tableOwnerBkf'),
          valuePath: 'ownerBkf',
          prependIcon: 'mdi-account',
        },
        {
          title: this.$t('admin_isDealer'),
          valuePath: 'isDealer',
          type: 'checkbox',
          prependIcon: 'mdi-account-outline',
        },
        {
          title: this.$t('common_dealer'),
          valuePath: 'dealer.id',
          type: 'dealer',
          editable: true,
          prependIcon: 'mdi-account-outline',
        },
        {
          title: this.$t('common_currency'),
          valuePath: 'currency.id',
          type: 'currency',
          editable: true,
          prependIcon: 'mdi-currency-usd',
        },
      ],
    };
  },
  computed: {
    filteredData() {
      return this.properties.map((item) => (
        {
          prependIcon: item.prependIcon,
          appendIcon: item.appendIcon,
          title: item.title,
          value: this.valueByPath.byPath(this.data, item.valuePath),
          type: item.type,
          editable: item.editable ?? false,
          valuePath: item.valuePath,
        }
      ));
    },
  },
  mounted() {
    this.getDealers();
    this.getCurrencies();
    this.fetchData();
  },
  methods: {
    getCurrencies() {
      this.axios.get(
        '/api/lists/currencies',
      )
        .then((response) => {
          this.currenciesOptions = response.data;
        });
    },
    getDealers() {
      this.dealersIsLoading = true;
      this.axios.get(
        '/administration/subscribers',
        {
          params: {
            isDealer: true,
          },
        },
      )
        .then((response) => {
          if ((response.status === 200) && response.data) {
            const subscribers = response.data.data;
            this.dealersOptions = this.multiselectHelper.toSelect(subscribers);
          }
        });

      this.dealersIsLoading = false;
    },
    getParams() {
      const result = {};

      const flatParams = this.filteredData.filter((item) => item.editable).map((item) => (
        {
          title: item.title, value: item.value, type: item.type, valuePath: item.valuePath,
        }
      ));

      flatParams.forEach((item) => {
        const pathArray = item.valuePath.split('.');
        let tempObj = result;

        if (item.value == null) {
          tempObj[pathArray[0]] = null;
          return;
        }

        for (let i = 0; i < pathArray.length - 1; i += 1) {
          if (!tempObj[pathArray[i]]) {
            tempObj[pathArray[i]] = {};
          }
          tempObj = tempObj[pathArray[i]];
        }

        let tmpVal = item.value;
        if (item.type === 'integer') {
          tmpVal = parseInt(item.value, 10);
        }
        tempObj[pathArray[pathArray.length - 1]] = tmpVal;
      });

      return result;
    },
    save() {
      let url = '/administration/subscriber/';

      if (this.subscriberId) {
        url += this.subscriberId;
      }

      const params = this.getParams();

      this.axios.patch(
        url,
        params,
      )
        .then(
          (response) => {
            if ((response.status === 200) && response.data) {
              this.loading = false;
              // this.onSuccessSubmit();
            }
          },
          () => {
            this.loading = false;
          },
        );
    },
    async fetchData() {
      this.loading = true;
      const response = await this.axios.get(`/administration/subscriber/${this.subscriberId}`);
      this.data = response.data;
      this.loading = false;
    },
  },
};
</script>
