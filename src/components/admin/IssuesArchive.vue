<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
  >
    <v-row>
      <v-col
        cols="3"
        class="pt-4"
      >
        <v-select
          v-model="selectedIssueGroups"
          :label="$t('common_status')"
          :items="issuesGroups"
          prepend-inner-icon="mdi-group"
          item-value="value"
          item-text="name"
          multiple
          chips
          small-chips
          clearable
          class="ml-2 mt-0 pt-0"
          @blur="getData"
        />
      </v-col>
      <v-col
        cols="3"
        class="pt-4"
      >
        <v-combobox
          v-model="selectedSerialNumbers"
          :label="$t('admin_serialNumber')"
          :items="uniqueSerialNumbers"
          prepend-inner-icon="mdi-group"
          multiple
          clearable
          class="ml-2 mt-0 pt-0"
          :return-object="false"
          @blur="getData"
        />
      </v-col>
      <v-col
        cols="3"
        class="pt-4"
      >
        <v-text-field
          v-model="search"
          clearable
          :label="$t('common_search')"
          prepend-inner-icon="mdi-magnify"
          class="mt-0 pt-0 mr-2"
          @blur="getData"
        />
      </v-col>
      <v-col
        cols="3"
        class="pt-0"
      >
        <date-range-picker
          key="dateRange"
          ref="dateRange"
          prepend-icon="mdi-calendar-range"
          :show-presets="true"
          :show-custom="true"
          :presets="dateRangePresets"
          :settings-namespace="settingsNamespace"
          class="mt-0 pt-0"
          @reload-transaction-list="onDateRangeChange"
        />
      </v-col>
    </v-row>
    <v-row>
      <v-col
        cols="6"
        align="left"
        class="pt-0"
      >
        <div
          class="table-wrapper mt-6"
        >
          <v-data-table
            :items="items"
            :headers="dataTable.headers"
            :footer-props="dataTable.footerProps"
            class="grey--text text--darken-1 mt-6 issuesTable"
            :options.sync="pagination"
            :loading="loading"
            :server-items-length="dataTable.total"
            @click:row="getIssueDetails"
          >
            <template #[`item.status`]="{ item }">
              {{ $t('issues-alarm.' + (item.status).toLowerCase()) }}
            </template>
            <template #[`item.level`]="{ item }">
              <v-tooltip bottom>
                <template #activator="{ on }">
                  <v-icon
                    :key="item.id"
                    :color="alarmLevelIconsColors[item.level.name]"
                    v-on="on"
                  >
                    {{ alarmLevelIcons[item.level.name] }}
                  </v-icon>
                </template>
                <span>{{ item.level.name }}</span>
              </v-tooltip>
            </template>
          </v-data-table>
        </div>
      </v-col>
      <v-col
        cols="6"
        class="pt-2 pl-4"
        align="left"
      >
        <v-container
          v-if="theIssue !== null && loadingDetails === false"
        >
          <alarm-data-row
            :label="$t('admin_serialNumber')"
            :value="theIssue.carwash.name"
            class="mt-6"
          />
          <alarm-data-row
            :label="$t('admin_alarmId')"
            :value="theIssue.alarmDefinition.id"
          />
          <alarm-data-row
            :label="$t('common_tableCreateDate')"
            :value="parseDate(theIssue.ctime)"
          />
          <alarm-data-row
            :label="$t('common_tableDescription')"
            :value="theIssue.alarmDefinition.text"
          />
          <alarm-data-row
            :label="$t('admin_alarmLevel')"
            :level="theIssue.level.name"
          />
          <alarm-data-row
            v-if="theIssue.lastTime"
            :label="$t('common_lastTime')"
            :value="parseDate(theIssue.lastTime)"
          />
          <alarm-data-row
            v-if="theIssue.etime"
            :label="$t('admin_etime')"
            :value="parseDate(theIssue.etime)"
          />
          <alarm-data-row
            v-if="theIssue.dueTime"
            :label="$t('admin_dueDate')"
            :value="parseDate(theIssue.dueTime)"
          />
          <alarm-data-row
            v-if="theIssue.comment"
            :label="$t('admin_lastComment')"
            :value="theIssue.comment"
          />
          <alarm-data-row
            v-if="theIssue.status"
            :label="$t('common_status')"
            :value="$t('issues-alarm.' + (theIssue.status).toLowerCase())"
          />
          <alarm-data-row
            :label="$t('admin_lastUser')"
            :level="theIssue.lastUser ?? '-'"
          />
          <v-col
            cols="3"
            class="py-2"
          >
            <strong class="text-subtitle-3">{{ $t('admin_issuesList') }}</strong>
          </v-col>
          <v-col
            cols="12"
            offset="0"
            class="py-2"
          >
            <v-data-table
              :headers="dataTable.headers2"
              :items="theIssue.alarm"
              :hide-default-footer="false"
              class="mb-2"
            >
              <template #[`item.alarmDef.level`]="{ item }">
                <v-tooltip bottom>
                  <template #activator="{ on }">
                    <v-icon
                      :key="item.id"
                      :color="alarmLevelIconsColors[item.alarmDef.level.name]"
                      v-on="on"
                    >
                      {{ alarmLevelIcons[item.alarmDef.level.name] }}
                    </v-icon>
                  </template>
                  <span>{{ item.alarmDef.level.name }}</span>
                </v-tooltip>
              </template>
            </v-data-table>
          </v-col>
          <v-col
            cols="3"
            class="py-2"
          >
            <strong class="text-subtitle-3">{{ $t('admin_actionHistory') }}</strong>
          </v-col>
          <v-col
            cols="12"
            offset="0"
            class="py-2"
          >
            <v-data-table
              :headers="dataTable.headers3"
              :items="theIssue.history"
              :hide-default-footer="false"
              class="mb-2"
            >
              <template #item="{ item}">
                <tr>
                  <td>{{ item.ctime }}</td>
                  <td>{{ item.email }}</td>
                  <td>{{ $t('issues-alarm.' + (item.status).toLowerCase()) }}</td>
                  <td>{{ item.comment }}</td>
                  <td>
                    <v-tooltip bottom>
                      <template #activator="{ on }">
                        <v-icon
                          class="pl-2 pr-2"
                          :color="alarmLevelIconsColors[item.level.toLowerCase()]"
                          v-on="on"
                        >
                          {{ alarmLevelIcons[item.level.toLowerCase()] }}
                        </v-icon>
                      </template>
                      <span>{{ item.level.toLowerCase() }}</span>
                    </v-tooltip>
                  </td>
                  <td />
                </tr>
              </template>
            </v-data-table>
          </v-col>
        </v-container>
        <div
          v-if="theIssue !== null && loadingDetails === true"
          class="loading-details mt-15"
        >
          <span>
            <v-progress-circular
              indeterminate
              size="32"
              class="circleProgress"
              color="primary"
            />
          </span>
        </div>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import FiltersHandlingMixin from '@components/common/mixins/FiltersHandlingMixin.vue';
import { mapGetters } from 'vuex';
import moment from 'moment';
import 'moment-timezone';
import AlarmDataRow from '@components/admin/alarm/DataRow.vue';
import DateRangePicker from '@components/common/DateRangePicker.vue';
import {
  endOfToday,
  endOfYesterday,
  startOfDay, startOfToday,
  startOfYesterday,
  subDays,
} from 'date-fns';

export default {
  name: 'IssuesArchive',
  components: {
    AlarmDataRow,
    DateRangePicker,
  },
  mixins: {
    FiltersHandlingMixin,
  },
  props: {
  },
  data() {
    return {
      selectedSerialNumbers: [],
      selectedIssueGroups: ['close'],
      issuesGroups: [
        {
          name: this.$t('common_new'),
          value: 'new',
        },
        {
          name: this.$t('common_open'),
          value: 'open',
        },
        {
          name: this.$t('common_duedate'),
          value: 'duedate',
        },
        {
          name: this.$t('common_jr'),
          value: 'JR',
        },
        {
          name: this.$t('common_close'),
          value: 'close',
        },
      ],
      loading: false,
      loadingDetails: false,
      theIssue: null,
      alarmLevelIcons: {
        ok: 'mdi-check-circle',
        ignore: 'mdi-cancel',
        information: 'mdi-information',
        warning: 'mdi-alert',
        error: 'mdi-alert-circle',
        unspecified: 'mdi-help-circle',
        notice: 'mdi-close',
      },
      alarmLevelIconsColors: {
        ok: 'green',
        ignore: 'brown',
        information: 'blue',
        warning: 'orange',
        error: 'red',
        unspecified: 'grey',
        notice: 'grey',
      },
      dataTable: {
        footerProps: {
          'items-per-page-options': [25, 50, 100],
          'items-per-page-text': this.$t('common_rowsPerPage'),
        },
        items: [],
        headers: [
          {
            text: this.$t('admin_serialNumber'),
            value: 'carwash.name',
            align: 'sm-start',
            sortable: false,
          },
          {
            text: this.$t('common_tableCreateDate'),
            value: 'ctime',
            align: 'sm-start',
          },
          {
            text: this.$t('admin_etime'),
            value: 'etime',
            align: 'sm-start',
            sortable: false,
          },
          {
            text: this.$t('common_status'),
            value: 'status',
            align: 'sm-start',
            sortable: false,
          },
          {
            text: this.$t('admin_alarmLevel'),
            value: 'level',
            align: 'sm-start',
            sortable: false,
          },
          {
            text: this.$t('admin_dueDate'),
            value: 'dueTime',
            align: 'sm-start',
            sortable: false,
          },
          {
            text: this.$t('common_tableDescription'),
            value: 'alarmDefinition.text',
            align: 'sm-start',
            sortable: false,
          },
          {
            text: this.$t('common_comment'),
            value: 'comment',
            align: 'sm-start',
            sortable: false,
          },
        ],
        headers2: [
          {
            text: this.$t('admin_issueId'),
            value: 'alarmDef.id',
            align: 'sm-start',
            sortable: false,
          },
          {
            text: this.$t('common_createDate'),
            value: 'ct',
            align: 'sm-start',
            sortable: true,
          },
          {
            text: this.$t('common_endDate'),
            value: 'et',
            align: 'sm-start',
            sortable: true,
          },
          {
            text: this.$t('common_tableDescription'),
            value: 'text',
            align: 'sm-start',
            sortable: false,
          },
          {
            text: this.$t('common_alarmLevel'),
            value: 'alarmDef.level',
            align: 'sm-start',
            sortable: false,
          },
        ],
        headers3: [
          {
            text: this.$t('common_tableCreateDate'),
            value: 'ctime',
            align: 'sm-start',
            sortable: true,
          },
          {
            text: this.$t('common_email'),
            value: 'email',
            align: 'sm-start',
            sortable: true,
          },
          {
            text: this.$t('common_state'),
            value: 'status',
            align: 'sm-start',
            sortable: false,
          },
          {
            text: this.$t('common_tableDescription'),
            value: 'comment',
            align: 'sm-start',
            sortable: false,
          },
          {
            text: this.$t('common_alarmLevel'),
            value: 'level',
            sortable: false,
          },
          {
            text: '',
            value: null,
            align: null,
            sortable: false,
          },
        ],
      },
      pagination: {
        page: 1,
        itemsPerPage: 25,
        sortBy: ['ctime'],
        sortDesc: [true],
      },
      search: '',
      serialNumbers: [],
      settingsNamespace: 'process-data:chart:date',
      dateRangePresets: [
        {
          value: 'today',
          text: this.$t('common_today'),
          start: startOfToday(),
          end: endOfToday(),
          default: true,
        },
        {
          value: 'yesterday',
          text: this.$t('common_daterangeYesterday'),
          start: startOfYesterday(),
          end: endOfYesterday(),
          default: true,
        },
        {
          value: 'last7Days',
          text: this.$t('common_p7d'),
          start: startOfDay(subDays(new Date(), 6)),
          end: endOfToday(),
        },
        {
          value: 'last14Days',
          text: this.$t('common_p14d'),
          start: startOfDay(subDays(new Date(), 13)),
          end: endOfToday(),
        },
      ],
      dateFrom: '',
      dateTo: '',
      dates: [],
    };
  },
  computed: {
    items() {
      return this.dataTable.items[0];
    },
    uniqueSerialNumbers() {
      const numbers = this.carwashes.map((obj) => obj.serialNumber);
      const stringArray = numbers.map(String);
      return stringArray.sort((a, b) => parseInt(b, 10) - parseInt(a, 10));
    },
    ...mapGetters({
      user: 'auth/getUser',
      carwashes: 'carwashes/carwashes',
    }),
  },
  watch: {
    pagination: {
      handler(newValue, oldValue) {
        if (oldValue.sortDesc !== newValue.sortDesc
          || oldValue.page !== newValue.page
          || oldValue.itemsPerPage !== newValue.itemsPerPage) {
          this.getData();
        }
      },
      deep: true,
    },
  },
  mounted() {
    this.pagination.page = 1;
    this.getData();
  },
  methods: {
    getData() {
      this.loading = true;
      this.dataTable.items = [];
      this.theIssue = null;
      this.axios.get(
        '/administration/carwashissues/list',
        {
          params: {
            groups: (this.selectedIssueGroups.length) ? this.selectedIssueGroups.join() : null,
            page: this.pagination.page,
            perPage: this.pagination.itemsPerPage,
            serial: (this.selectedSerialNumbers.length) ? this.selectedSerialNumbers.join() : null,
            search: this.search,
            sortByColumnName: this.pagination.sortBy[0],
            sortDirection: (this.pagination.sortDesc[0] === true) ? 'DESC' : 'ASC',
            dateFrom: this.dateFrom,
            dateTo: this.dateTo,
          },
        },
      )
        .then((response) => {
          this.dataTable.items = Object.values(response.data);
          this.dataTable.total = response.data.total;
          this.loading = false;
          this.serialNumbers = response.data.data.map((obj) => obj.carwash.sn);
        });
    },
    getIssueDetails(item) {
      this.loadingDetails = true;
      this.axios.get(
        `/administration/carwashissue/${item.id}`,
      )
        .then(
          (response) => {
            if (response.status === 200) {
              this.theIssue = response.data;
              this.theIssue.carwash.name = item.carwash.name;
              this.loadingDetails = false;
            }
          },
        );
    },
    parseDate(dateString) {
      const date = moment(dateString);
      return date.format('YYYY-MM-DD HH:mm:ss');
    },
    onDateRangeChange({
      from,
      to,
    }) {
      const dateFromArray = from.split(' ');
      const dateFrom = dateFromArray[0];
      this.dateFrom = dateFrom;
      const dateToArray = to.split(' ');
      const dateTo = dateToArray[0];
      this.dateTo = dateTo;
      this.dates = [this.dateFrom, this.dateTo];
      this.getData();
    },
  },
};
</script>

<style lang="css" >
  .expand-table {
    width: 100%;
    border-left: 1px solid #d6d6d6;
    border-right: 1px solid #d6d6d6;
    border-radius: 0px !important;
  }
  .progress-overlay {
    position: absolute;
    width: 96%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255,255,255,1);
    z-index: 5;
    color: #3B81E3;
  }
  .loading-details{
    text-align: center;
  }
  .selected {
    background-color: #efefef;
    border-left: 1px solid #d6d6d6;
    border-right: 1px solid #d6d6d6;
  }
  .selected-row, .expand-table tr.issue:hover {
    background-color: #d6d6d6 !important;
  }
  .issuesTable thead tr th{
    vertical-align: middle;
  }
  .issuesTable tbody tr td:nth-child(2), .issuesTable tbody tr td:nth-child(3) {
    padding:0 !important;
    text-align: center !important;
  }
</style>
