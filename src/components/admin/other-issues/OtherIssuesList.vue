<template>
  <div>
    <v-card-text>
      <v-data-table
        :headers="dataTable.headers"
        :items="issues"
        :hide-default-footer="true"
        :loading="loading"
      >
        <template #[`item.durationTimeInSec`]="{ item }">
          <template v-if="item.et === null && item.ct === null">
            {{ $t('common_dataUnknown') }}
          </template>
          <template v-else>
            {{ calculateDuration(item.durationTimeInSec) }}
          </template>
        </template>
        <template #[`item.ct`]="{ item }">
          <template v-if="item.ct === null">
            {{ $t('common_dataUnknown') }}
          </template>
          <template v-else>
            {{ item.ct }}
          </template>
        </template>
        <template #[`item.status`]="{ item }">
          <tr>
            <td>
              {{ $t('issues-alarm.' + (item.status).toLowerCase()) }}
            </td>
            <td>
              <v-tooltip bottom>
                <template #activator="{ on }">
                  <v-icon
                    class="pl-2 pr-2"
                    :color="alarmLevelIconsColors[item.level.name]"
                    v-on="on"
                  >
                    {{ alarmLevelIcons[item.level.name] }}
                  </v-icon>
                </template>
                <span>{{ item.level.name }}</span>
              </v-tooltip>
            </td>
          </tr>
        </template>
      </v-data-table>
    </v-card-text>
  </div>
</template>

<script>
import moment from 'moment';

export default {
  name: 'ActiveAlarmsList',
  props: {
    issues: {
      type: Array,
      default: () => ([]),
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      alarmLevelIcons: {
        ignore: 'mdi-cancel',
        information: 'mdi-information',
        warning: 'mdi-alert',
        error: 'mdi-alert-circle',
        unspecified: 'mdi-help-circle',
      },
      alarmLevelIconsColors: {
        ignore: 'brown',
        information: 'blue',
        warning: 'orange',
        error: 'red',
        unspecified: 'grey',
      },
      carwashSerialNumber: null,
      dataTable: {
        headers: [
          {
            text: this.$t('common_alarmId'),
            value: 'alarmDefinition.id',
            align: 'sm-start',
            sortable: false,
            width: '5%',
          },
          {
            text: this.$t('common_tableDescription'),
            value: 'alarmDefinition.text',
            align: 'sm-start',
            sortable: false,
            width: '25%',
          },
          {
            text: this.$t('common_createDate'),
            value: 'ctime',
            sortable: true,
            width: '15%',
          },
          {
            text: this.$t('common_AlarmDuration'),
            value: 'durationTimeInSec',
            showInRowExpand: true,
            sortable: true,
            width: '10%',
          },
          {
            text: this.$t('common_status'),
            value: 'status',
            showInRowExpand: true,
            sortable: true,
            width: '10%',
          },
          {
            text: this.$t('common_comment'),
            value: 'comment',
            showInRowExpand: true,
            sortable: true,
            width: '35%',
          },
        ],
        items: {},
        totalItems: 0,
      },
      items: [],
    };
  },
  methods: {
    calculateDuration(seconds) {
      const duration = moment.duration(seconds, 'seconds');
      return duration.humanize();
    },
  },
};
</script>
