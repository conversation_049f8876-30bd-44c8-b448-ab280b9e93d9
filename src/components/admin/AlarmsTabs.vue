<template>
  <v-app>
    <v-tabs v-model="activeTab">
      <v-tab
        v-for="(tab, index) in tabs"
        :key="index"
      >
        {{ tab.name }}
        <v-avatar
          v-if="index == 0 && issue"
          size="22"
          class="ml-2 rounded-full transparent-with-border"
        >
          {{ activeAlarmsQty }}
        </v-avatar>
        <v-avatar
          v-if="index == 3 && closedIssuesQty"
          size="22"
          class="ml-2 rounded-full transparent-with-border"
        >
          {{ closedIssuesQty }}
        </v-avatar>
      </v-tab>
      <v-tab-item
        v-for="(tab, index) in tabs"
        :key="index"
      >
        <v-row
          v-if="tab.id === 1 && loading === false"
          class=" pl-5"
        >
          <alarm-data-row
            :label="$t('common_carwashSn')"
            :value="(carwash.sn) ? carwash.sn : '-'"
            :copy="true"
            class="mt-6"
          />
          <alarm-data-row
            :label="$t('common_carwashIp')"
            :value="(carwash.ipVnc) ? carwash.ipVnc : '-'"
            :copy="true"
          />
          <alarm-data-row
            :label="$t('common_software')"
            :value="(carwash.software) ? carwash.software : '-'"
          />
          <alarm-data-row
            :label="$t('common_lastAlarm')"
            :value="(carwash.lastAlarmContact) ? parseDate(carwash.lastAlarmContact) : '-'"
            :icon="
              (carwash.lastAlarmContact) ? getParamTooltipData(carwash.lastAlarmContact) : null"
          />
          <alarm-data-row
            :label="$t('common_lastSynchro')"
            :value="(carwash.lastStatsContact) ? parseDate(carwash.lastStatsContact) : '-'"
            :icon="
              (carwash.lastStatsContact) ? getParamTooltipData(carwash.lastStatsContact) : null"
          />
          <alarm-data-row
            :label="$t('common_lastOnline')"
            :value="(carwash.lastMobileOnline) ? parseDate(carwash.lastMobileOnline) : '-'"
            :icon="
              (carwash.lastMobileOnline) ? getParamTooltipData(carwash.lastMobileOnline) : null"
          />
          <h3
            class="mt-5 fullwidth"
          >
            {{ $t('admin_alarmActiveAllIssues') }}
          </h3>
          <alarm-active-list
            class="fullwidth"
            :alarms="carwash.alarms"
            :loading="loading"
          />
          <h3
            class="mt-5 fullwidth"
          >
            {{ $t('admin_otherIssues') }}
          </h3>
          <other-issues-list
            class="fullwidth"
            :loading="loading"
            :issues="otherIssues"
          />
        </v-row>
        <div
          v-if="tab.id === 1 && loading === true"
          class="progress-overlay mt-15"
        >
          <span>
            <v-progress-circular
              indeterminate
              size="32"
              class="circleProgress"
              color="primary"
            />
          </span>
        </div>
        <v-row
          v-if="tab.id === 2 && loading === false"
          class="pl-0 ml-0"
        >
          <v-container
            v-if="issue !== false && issue === null"
          >
            <p
              class="text-center grey--text lighten-5 mt-10"
            >
              {{ $t('common_noData') }}
            </p>
          </v-container>
          <v-container
            v-if="issue !== null && issue !== false"
          >
            <alarm-data-row
              :label="$t('admin_serialNumber')"
              :value="issue.carwash.sn"
              class="mt-6"
            />
            <!--
            <alarm-data-row
              :label="$t('admin_alarmId')"
              :value="issue.alarmDefinition.id"
            />
            <alarm-data-row
              :label="$t('common_tableCreateDate')"
              :value="parseDate(issue.ctime)"
            />
            <alarm-data-row
              :label="$t('common_tableDescription')"
              :value="issue.alarmDefinition.text"
            />
            <alarm-data-row
              :label="$t('admin_alarmLevel')"
              :level="issue.level.name"
            />
            <alarm-data-row
              v-if="issue.lastTime"
              :label="$t('common_lastTime')"
              :value="parseDate(issue.lastTime)"
            />
            <alarm-data-row
              v-if="issue.etime"
              :label="$t('admin_etime')"
              :value="parseDate(issue.etime)"
            />
            <alarm-data-row
              v-if="issue.dueTime"
              :label="$t('admin_dueDate')"
              :value="parseDate(issue.dueTime)"
            />
            <alarm-data-row
              v-if="issue.comment"
              :label="$t('admin_lastComment')"
              :value="issue.comment"
            />
            <alarm-data-row
              v-if="issue.status"
              :label="$t('common_status')"
              :value="$t('issues-alarm.' + (issue.status).toLowerCase())"
            />
            -->
            <alarm-data-row
              :label="$t('admin_previousIssue')"
              :value="closedDateFromClosedIssues"
            />
            <alarm-data-row
              v-if="checkCommentAuthors()"
              :label="$t('admin_lastUser')"
              :value="(checkCommentAuthors) ? getLastComment().email : ''"
            />
            <alarm-data-row
              v-if="checkCommentAuthors()"
              :label="$t('admin_lastComment')"
              :value="(checkCommentAuthors) ? getLastComment().comment : ''"
            />
            <alarm-data-row
              :label="$t('common_lastVisit')"
              :value="(serviceVisits.last_completed_visit)
                ? `${serviceVisits.last_completed_visit.visit_planned_date} -
                ${serviceVisits.last_completed_visit.serviceman}` : '-'"
            />
            <alarm-data-row
              :label="$t('common_lastAttendance')"
              :value="(serviceVisits.last_attendance)
                ? `${serviceVisits.last_attendance.visit_attended_timestamp}` : '-'"
            />
            <alarm-data-row
              :label="$t('common_nextVisit')"
              :value="(serviceVisits.next_planned_visit)
                ? `${serviceVisits.next_planned_visit.visit_planned_date} -
                ${serviceVisits.next_planned_visit.serviceman}` : '-'"
            />
            <h3
              class="mt-4 fullwidth"
            >
              {{ $t('admin_alarmActive') }}
            </h3>
            <alarm-active-list
              :alarms="issue.activeAlarms"
            />
            <v-expansion-panels
              class="ml-0 pl-0 pb-1 pr-1"
              multiple
            >
              <v-expansion-panel>
                <v-expansion-panel-header>
                  <v-row align="center">
                    <h3 class="mt-3 mb-2 fullwidth">
                      {{ $t('common_issueHistory') }}
                    </h3>
                  </v-row>
                </v-expansion-panel-header>
                <v-expansion-panel-content
                  class="ml-0 pl-0"
                >
                  <issue-history
                    :history="issue.history"
                  />
                </v-expansion-panel-content>
              </v-expansion-panel>
              <v-expansion-panel>
                <v-expansion-panel-header>
                  <v-row align="center">
                    <h3 class="mt-3 mb-2 fullwidth">
                      {{ $t('admin_allAlarmsForIssue') }}
                    </h3>
                  </v-row>
                </v-expansion-panel-header>
                <v-expansion-panel-content
                  class="ml-0 pl-0"
                >
                  <alarm-active-list
                    :alarms="filterDuplicates"
                  />
                </v-expansion-panel-content>
              </v-expansion-panel>
            </v-expansion-panels>
          </v-container>
        </v-row>
        <div
          v-if="tab.id === 2 && loading === true"
          class="progress-overlay mt-15"
        >
          <span>
            <v-progress-circular
              indeterminate
              size="32"
              class="circleProgress"
              color="primary"
            />
          </span>
        </div>
        <v-row
          v-if="tab.id == 3 && loading === false"
          class="pl-0 ml-0"
        >
          <v-container
            v-if="contactDetails !== null"
          >
            <carwash-contact-details
              :contact-details="contactDetails"
            />
          </v-container>
        </v-row>
        <div
          v-if="tab.id === 3 && loading === true"
          class="progress-overlay mt-15"
        >
          <span>
            <v-progress-circular
              indeterminate
              size="32"
              class="circleProgress"
              color="primary"
            />
          </span>
        </div>
        <v-row
          v-if="tab.id == 4 && loading === false"
          class="pl-0 ml-0"
        >
          <v-container
            v-if="contactDetails !== null"
          >
            <closed-issues
              :closed-issues="closedIssues"
            />
          </v-container>
        </v-row>
        <div
          v-if="tab.id === 4 && loading === true"
          class="progress-overlay mt-15"
        >
          <span>
            <v-progress-circular
              indeterminate
              size="32"
              class="circleProgress"
              color="primary"
            />
          </span>
        </div>
      </v-tab-item>
    </v-tabs>
  </v-app>
</template>

<script>
import moment from 'moment';
import 'moment-timezone';
import AlarmActiveList from '@components/alarm-active/AlarmActiveList.vue';
import AlarmDataRow from '@components/admin/alarm/DataRow.vue';
import CarwashContactDetails from '@components/admin/CarwashContactDetails.vue';
import IssueHistory from '@components/admin/IssueHistory.vue';
import ClosedIssues from '@/components/admin/ClosedIssues.vue';
import { mapGetters } from 'vuex';
import OtherIssuesList from '@components/admin/other-issues/OtherIssuesList.vue';

export default {
  name: 'CarwashIssuesTab',
  components: {
    AlarmActiveList,
    AlarmDataRow,
    CarwashContactDetails,
    IssueHistory,
    ClosedIssues,
    OtherIssuesList,
  },
  props: {
    selectedCarwash: {
      type: Number,
      default: null,
    },
    issue: {
      type: [Object, Boolean],
      default: () => ({}),
    },
    closedIssues: {
      type: Array,
      default: () => ([]),
    },
    otherIssues: {
      type: Array,
      default: () => ([]),
    },
    closedIssuesQty: {
      type: Number,
      default: null,
    },
    carwash: {
      type: Object,
      default: () => ({}),
    },
    contactDetails: {
      type: Object,
      default: () => ({}),
    },
    serviceVisits: {
      type: Object,
      default: () => ({}),
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      activeTab: 0,
      tabs:
        [
          { id: 2, name: this.$t('admin_alarmDetails') },
          { id: 1, name: `${this.$t('admin_carwashStatus')}` },
          { id: 3, name: this.$t('admin_contactDetails') },
          { id: 4, name: this.$t('admin_pastIssues') },
        ],
    };
  },
  computed: {
    closedDateFromClosedIssues() {
      const issue = this.closedIssues
        .find((obj) => obj.alarmDefinition.id === this.issue.alarmDefinition.id);
      return (issue) ? issue.etime : '-';
    },
    activeAlarmsQty() {
      return this.issue.activeAlarms.length;
    },
    filterDuplicates() {
      return this.issue.alarm
        .filter((obj) => !this.issue.activeAlarms.some((otherObj) => otherObj.id === obj.id));
    },
    ...mapGetters({
      user: 'auth/getUser',
    }),
  },
  methods: {
    parseDate(dateString) {
      const date = moment(dateString);
      return date.format('YYYY-MM-DD HH:mm:ss');
    },
    getParamTooltipData(value) {
      const momentObj = moment(value);
      const adjustedDate = momentObj.add(momentObj.utcOffset(), 'minutes');
      const age = this.getAgeInMinutes(adjustedDate.format());
      const lastSync = (value) ? this.parseDate(value) : '-';
      const lastUpdateText = `${this.$t('common_updateTime')}: ${lastSync}`;
      if (age >= 60 || age === null) {
        return {
          icon: {
            text: 'mdi-alert-circle-outline',
            color: 'error',
          },
          content: [
            {
              text: lastUpdateText,
            },
            {
              text: (age) ? this.$t('common_itemOld') : '',
              class: 'error white--text',
            },
          ],
        };
      }
      if (age >= 15) {
        return {
          icon: {
            text: 'mdi-alert-outline',
            color: 'warning',
          },
          content: [
            {
              text: lastUpdateText,
            },
            {
              text: this.$t('common_itemOld'),
              class: 'warning white--text',
            },
          ],
        };
      }
      return {
        icon: {
          text: 'mdi-information-outline',
          color: 'grey lighten-1',
        },
        content: [
          {
            text: lastUpdateText,
          },
        ],
      };
    },
    getAgeInMinutes(updateTime) {
      const update = moment(updateTime);
      const now = moment.tz(moment(), this.user.timezone);
      const diff = now - update;
      return Math.floor((diff / 1000) / 60);
    },
    checkCommentAuthors() {
      const lastData = this.issue.history.filter((el) => el.email !== 'auto');
      return lastData.length ?? false;
    },
    getLastComment() {
      const lastData = this.issue.history.filter((el) => el.email !== 'auto');
      return lastData.length ? { email: lastData[0].email, comment: lastData[0].comment } : '';
    },
  },
};
</script>
<style>
.progress-overlay {
  display: flex;
  justify-content: center;
  align-items: center;
}
.fullwidth{
  width: 100%;
}
.transparent-with-border {
  background-color: transparent;
  border: 1px solid #cacaca;
}
.v-expansion-panel-content__wrap{
  padding-left:0px !important;
}
.v-expansion-panel-header{
  font-size: inherit;
}
</style>
