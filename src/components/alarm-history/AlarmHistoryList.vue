<template>
  <div>
    <v-card-text>
      <v-layout
        row
        wrap
      >
        <v-col
          md="1"
          sm="1"
          cols="12"
          class="pt-8"
        >
          <export-list
            :items="exportItems"
            @click="onExport"
          />
        </v-col>
        <v-col
          md="2"
          sm="2"
          cols="12"
        >
          <v-text-field
            v-model="alarmSearch"
            :label="$t('common_tableDescription')"
            prepend-icon="mdi-text"
            @blur="getData"
          />
        </v-col>
        <v-col
          md="3"
          sm="3"
          cols="12"
          class="pt-8"
        />
        <v-col
          md="6"
          sm="6"
          cols="12"
        >
          <v-combobox
            ref="combobox"
            :key="comboboxKey"
            v-model="filteredAlarms"
            :items="filterItems"
            :label="comboboxLabel"
            prepend-icon="mdi-filter"
            item-key="id"
            item-text="alarm_def_label"
            class="input-group mt-5"
            multiple
            dense
            :search-input.sync="filterItems.alarm_def_label"
            :return-object="false"
            @click="comboboxOpen=true"
            @blur="getDataEvent"
          >
            <template #item="{item}">
              <v-list-item
                @mousedown.prevent.stop="onComboboxChange(
                  item, selectedAlarms.includes(item.alarm_def_id))"
              >
                <v-list-item-action
                  class="mr-3"
                >
                  <v-checkbox
                    :disabled="true"
                    class="custom-checkbox"
                    :input-value="selectedAlarms.includes(item.alarm_def_id)"
                  />
                </v-list-item-action>
                <v-list-item-content>
                  <v-list-item-title>{{ item.alarm_def_label }}</v-list-item-title>
                </v-list-item-content>
              </v-list-item>
            </template>
            <template #selection="" />
            <template #prepend-inner="">
              <span class="pt-2 mr-3">
                {{ deselectedAlarms }}
              </span>
            </template>
            <template #prepend-item="">
              <v-btn
                elevation="2"
                small
                color="primary"
                tile
                class="mb-2"
                block
                @mousedown.prevent.stop="selectAll"
              >
                {{ selectAllLabel }}
              </v-btn>
            </template>
          </v-combobox>
        </v-col>
      </v-layout>
    </v-card-text>
    <v-card-text>
      <v-data-table
        :headers="dataTable.headers"
        :items="dataTable.items"
        :loading="loading"
        :options.sync="pagination"
        :server-items-length="dataTable.totalItems"
        :footer-props="dataTable.footerProps"
      >
        <template #item="{ item, expand, isExpanded }">
          <tr
            @click="onRowClick({item, expand, isExpanded})"
          >
            <td>{{ item.alarmId }}</td>
            <td>
              <v-tooltip
                v-if="item.docUrl"
              >
                <template #activator="{ on, attrs }">
                  <v-btn
                    :key="item.alarmId"
                    text
                    elevation="0"
                    v-bind="attrs"
                    :href="item.docUrl"
                    target="_blank"
                    small
                    color="primary"
                    v-on="on"
                  >
                    {{ item.text }}
                  </v-btn>
                </template>
                <span>{{ $t('actions.show_doc') }}</span>
              </v-tooltip>
              <template v-else>
                {{ item.text }}
              </template>
            </td>
            <td>{{ item.ct === null ? $t('common_dataUnknown') : item.ct }}</td>
            <td>{{ item.et === null ? $t('common_dataUnknown') : item.et }}</td>
            <td>
              <template v-if="item.et === null && item.ct === null">
                {{ $t('common_dataUnknown') }}
              </template>
              <template v-else>
                {{ calculateDuration(item.durationTimeInSec) }}
              </template>
            </td>
            <td
              align="center"
            >
              <v-tooltip bottom>
                <template #activator="{ on }">
                  <v-icon
                    :key="item.alarmId"
                    :color="getCarwashAlertColor(item.level)"
                    v-on="on"
                  >
                    {{ getCarwashAlertIcon(item.level) }}
                  </v-icon>
                </template>
                <span>{{ item.level }}</span>
              </v-tooltip>
            </td>
            <td
              align="end"
            >
              <v-btn
                v-if="item.details"
                icon
              >
                <v-icon>{{ isExpanded ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
              </v-btn>
              <div v-else />
            </td>
          </tr>
        </template>
        <template #expanded-item="{ headers: _headers, item: _item }">
          <td
            v-if="_item.details"
            :colspan="_headers.length"
            class="elevation-3"
          >
            <v-card
              class="px-3 py-3"
              outlined
              color="blue-grey lighten-5"
            >
              <h3 class="mb-3">
                {{ $t('common_alarmDetails') }}
              </h3>
              <v-simple-table
                dense
                class="elevation-3"
              >
                <template #default>
                  <tbody
                    v-for="(value, key) in _item.details"
                    :key="key"
                  >
                    <tr>
                      <td>{{ key }}</td>
                      <td>{{ value }}</td>
                    </tr>
                  </tbody>
                </template>
              </v-simple-table>
            </v-card>
          </td>
        </template>
      </v-data-table>
    </v-card-text>
  </div>
</template>

<script>
import moment from 'moment';
import { mapGetters } from 'vuex';
import {
  endOfToday, endOfYesterday, startOfDay, startOfYesterday, subDays,
} from 'date-fns';
import ExportList from '@components/common/ExportList.vue';
import ExportMixin from '@components/common/mixins/ExportMixin.vue';
import CarwashAlertTypeMixin
  from '@components/common/badge/icon-text-mixin/CarwashAlertTypeMixin.vue';

export default {
  name: 'HistoryAlarmsList',
  components: {
    ExportList,
  },
  mixins: [
    ExportMixin,
    CarwashAlertTypeMixin,
  ],
  props: {
    selectedCarwash: {
      type: Number,
      default: null,
    },
    dates: {
      type: Array,
      default: () => [],
    },
    triggerGetData: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      settingsNamespace: 'alarms:history-list:dates',
      dateRangePresets: [
        {
          value: 'yesterday',
          text: this.$t('common_daterangeYesterday'),
          start: startOfYesterday(),
          end: endOfYesterday(),
          default: true,
        },
        {
          value: 'last7Days',
          text: this.$t('common_p7d'),
          start: startOfDay(subDays(new Date(), 6)),
          end: endOfToday(),
        },
        {
          value: 'last14Days',
          text: this.$t('common_p14d'),
          start: startOfDay(subDays(new Date(), 13)),
          end: endOfToday(),
        },
      ],
      loading: true,
      pagination: {
        page: 1,
        itemsPerPage: 50,
        sortBy: ['created'],
        sortDesc: [true],
      },
      filtering: {
        carwashSerialNumber: null,
        dateFrom: '',
        dateTo: '',
      },
      dataTable: {
        footerProps: {
          'items-per-page-options': [50, 100],
          'items-per-page-text': this.$t('common_rowsPerPage'),
        },
        headers: [
          {
            text: this.$t('common_alarmId'),
            value: 'alarmId',
            align: 'sm-start',
            sortable: false,
          },
          {
            text: this.$t('common_tableDescription'),
            value: 'text',
            align: 'sm-start',
            sortable: false,
          },
          {
            text: this.$t('common_createDate'),
            value: 'ct',
            sortable: true,
          },
          {
            text: this.$t('common_endDate'),
            value: 'et',
            showInRowExpand: true,
            sortable: true,
          },
          {
            text: this.$t('common_AlarmDuration'),
            value: 'durationTimeInSec',
            showInRowExpand: true,
            sortable: true,
          },
          {
            text: this.$t('common_alarmLevel'),
            value: 'level',
            align: 'sm-center',
            sortable: false,
          },
          {
            value: 'expand',
            sortable: false,
            text: '',
            align: 'end',
          },
        ],
        items: [],
        totalItems: 0,
      },
      filteredAlarms: [],
      filterItems: [],
      comboboxKey: 0,
      selectAllLabel: this.$t('admin_deselectAll'),
      deselectedAlarms: 0,
      exportItems: [
        {
          title: 'actions.export_csv',
          fileType: 'csv',
        },
        {
          title: 'actions.export_xlsx',
          fileType: 'xlsx',
        },
      ],
      timeout: null,
      alarmSearch: '',
      loaded: false,
      deselectAll: false,
      comboboxOpen: false,
      comboboxLabel: this.$t('admin_filters'),
    };
  },
  computed: {
    selectedAlarms() {
      return this.filteredAlarms
        .filter((el, index) => this.filteredAlarms.indexOf(parseInt(el, 10)) === index);
    },
    ...mapGetters({
      carwashes: 'carwashes/carwashes',
    }),
  },
  watch: {
    dates: {
      immediate: true,
      handler() {
        this.filterItems = [];
        this.filteredAlarms = [];
        this.dataTable.items = [];
        this.getData();
      },
    },
    selectedCarwash: {
      immediate: true,
      handler(val, oldValue) {
        this.filtering.carwashSerialNumber = val;
        if (val !== oldValue && oldValue !== undefined) {
          this.filterItems = [];
          this.filteredAlarms = [];
          this.dataTable.items = [];
          this.getData();
        }
      },
    },
    triggerGetData: {
      handler(val) {
        if (val === true) {
          if (!this.loaded) {
            this.getData();
          }
        }
      },
    },
    pagination: {
      handler(newValue, oldValue) {
        if (oldValue.sortDesc !== newValue.sortDesc
          || oldValue.page !== newValue.page
          || oldValue.itemsPerPage !== newValue.itemsPerPage
          || oldValue.sortBy !== newValue.sortBy) {
          // return to first page when sorting has change
          if (oldValue.sortDesc !== newValue.sortDesc
            || oldValue.sortBy !== newValue.sortBy
            || oldValue.itemsPerPage !== newValue.itemsPerPage
          ) {
            // eslint-disable-next-line no-param-reassign
            newValue.page = 1;
          }
          if (newValue !== oldValue && oldValue !== undefined) {
            this.getData();
          }
        }
      },
      deep: true,
    },
  },
  mounted() {
    if (this.selectedAlarms.length === 0) {
      if (!this.loaded) {
        this.getData();
        this.loaded = true;
      }
    }
  },
  methods: {
    onRowClick({ expand, isExpanded }) {
      expand(!isExpanded);
    },
    calculateDuration(seconds) {
      const duration = moment.duration(seconds, 'seconds');
      return duration.humanize();
    },
    onResize() {
      this.windowWidth = window.innerWidth;
    },
    async getData() {
      this.loading = true;
      this.dataTable.items = [];
      if (this.filtering.carwashSerialNumber) {
        this.comboboxLabel = (this.selectedAlarms.length === 0
          || this.selectedAlarms.join() === '99999'
          || this.selectedAlarms.length === this.filterItems.length)
          ? this.$t('admin_filters') : this.selectedAlarms.join();
        const dateFrom = this.dates[0];
        const dateTo = this.dates[1];
        if (dateFrom == null || dateTo == null) return;
        await this.axios.get(
          `/cm/alarm/history/${this.filtering.carwashSerialNumber}`,
          {
            params: {
              page: this.pagination.page,
              length: this.pagination.itemsPerPage,
              startDate: dateFrom,
              endDate: dateTo,
              sortByColumnName: this.pagination.sortBy[0],
              sortDirection: (this.pagination.sortDesc[0] === true) ? 'DESC' : 'ASC',
              alarmDefinitionId:
                (this.selectedAlarms.length === this.filterItems.length)
                  ? null : this.selectedAlarms.join(),
              search: this.alarmSearch,
            },
          },
        )
          .then((response) => {
            if (this.filterItems.length === 0) {
              response.data.possibleAlarmId.forEach((item) => {
                this.filteredAlarms.push(item.alarm_def_id);
              });
            }
            this.filterItems = response.data.possibleAlarmId;
            this.dataTable.items = response.data.data;
            this.dataTable.totalItems = response.data.allRows;
            const event = new KeyboardEvent('keydown', { key: 'Escape' });
            document.dispatchEvent(event);
            this.$emit('resetTriggerGetData');
            this.deselectedAlarms = (this.selectedAlarms.includes(99999))
              ? this.selectedAlarms.length - 1 : this.selectedAlarms.length;
          });
      }
      this.loading = false;
    },
    selectAll() {
      const typed = this.$refs.combobox.internalSearch;
      if (this.deselectAll === false) {
        this.deselectAll = true;
        if (typed) {
          const elements = this.filterItems
            .filter((el) => el.alarm_def_label.includes(typed))
            .map((el) => el.alarm_def_id);
          this.filteredAlarms = this.filteredAlarms.filter((item) => !elements.includes(item));
        } else {
          this.filteredAlarms = [99999];
        }
        this.selectAllLabel = this.$t('admin_selectAll');
      } else {
        this.deselectAll = false;
        if (typed) {
          this.filteredAlarms = this.filterItems
            .filter((el) => el.alarm_def_label.includes(typed))
            .map((el) => el.alarm_def_id);
        } else {
          this.filteredAlarms = this.filterItems.map((el) => el.alarm_def_id);
        }
        this.selectAllLabel = this.$t('admin_deselectAll');
      }
      this.deselectedAlarms = (this.selectedAlarms.includes(99999))
        ? this.selectedAlarms.length - 1 : this.selectedAlarms.length;
    },
    onComboboxChange(selected, active) {
      if (active) {
        this.selectedAlarms.forEach((item, index) => {
          if (item === selected.alarm_def_id) {
            this.filteredAlarms.splice(index, 1);
          }
        });
      } else {
        this.selectedAlarms.forEach((item, index) => {
          if (item === 99999) {
            this.filteredAlarms.splice(index, 1);
          }
        });
        this.filteredAlarms.push(selected.alarm_def_id);
      }
    },
    getDataEvent() {
      if (this.comboboxOpen === false) {
        return;
      }
      this.comboboxOpen = false;
      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        this.getData();
        this.countDeselected();
        this.$refs.combobox.focus();
      }, 100);
    },
    countDeselected() {
      this.deselectedAlarms = (this.selectedAlarms.includes(99999))
        ? this.selectedAlarms.length - 1 : this.selectedAlarms.length;
    },
    onExport({ fileType }) {
      const fileName = this.getExportFileName(this.$t('common_alarmHistory'), fileType);
      const dataToExport = this.mapForExport(this.dataTable.items);
      if (!this.exporter.export(dataToExport, fileType, fileName)) {
        this.snackbar.showMessage(
          'error',
          this.$t('transactions.export_error'),
        );
      }
    },
    getExportFileName(name, fileType) {
      const dateFromArray = this.filtering.dateFrom.split(' ');
      const dateFrom = dateFromArray[0];
      const dateToArray = this.filtering.dateTo.split(' ');
      const dateTo = dateToArray[0];
      const nameArray = [[name, 'sn', this.filtering.carwashSerialNumber, dateFrom, dateTo].join('_'), fileType];
      return nameArray.join('.');
    },
    mapForExport(data) {
      const headers = [
        this.$t('common_alarmId'),
        this.$t('common_tableDescription'),
        this.$t('common_createDate'),
        this.$t('common_endDate'),
        this.$t('common_AlarmDuration'),
        this.$t('common_alarmLevel'),
      ];
      const filtered = data.map(
        (row) => [
          row.alarmId,
          row.text,
          row.ct,
          row.et,
          row.durationTimeInSec,
          row.level,
        ],
      );
      filtered.unshift(headers);
      return filtered;
    },
  },
};
</script>
<style scoped>
.v-application--is-ltr .v-text-field .v-input__prepend-inner {
  padding-top: 4px;
}

.v-input__prepend-outer {
  margin-top: 0px;
}

.v-text-field.v-input--dense .v-label {
  left: -17px !important;
}
.custom-checkbox{
  color:#48a7f2 !important;
}
</style>
