<template>
  <div id="ErrorReportForm">
    <bkf-alert
      :show.sync="responseAlert.show"
      :message="responseAlert.message"
      :type="responseAlert.type"
    />
    <v-layout
      row
      wrap
    >
      <v-col
        cols="12"
        class="pt-0"
      >
        <div
          v-if="loader"
          class="loader-background"
        />
        <v-form
          ref="form"
          v-model="form.edit.valid"
          lazy-validation
        >
          <v-layout wrap>
            <v-col
              cols="12"
              class="text-sm-start"
            >
              <h2><span>{{ $t('service_heading') }}</span></h2>
            </v-col>
            <v-col
              cols="12"
            >
              <v-text-field
                v-model="errorReport.subject"
                :label="$t('service_errorReportSubject')"
                prepend-icon="mdi-format-title"
                required
                :rules="form.validationRules.notEmpty"
              />
              <v-autocomplete
                v-model="errorReport.carwash"
                :items="carwashes"
                item-value="serialNumber"
                item-text="name"
                prepend-icon="mdi-car-wash"
                :label="$t('common_filtersCarwash')"
                :value="null"
                clearable
              />
            </v-col>
            <v-col
              cols="12"
            >
              <v-textarea
                v-model="errorReport.description"
                solo
                :label="$t('service_description')"
                rows="8"
                required
                :rules="form.validationRules.notEmpty"
                clearable
              />
              <div>
                <div
                  ref="attachments"
                />
                <div class="d-flex justify-end">
                  <v-btn
                    small
                    dark
                    color="green"
                    :disabled="isAttachmentsAdditionDisabled"
                    @click="onAttachmentAdd"
                  >
                    <v-icon
                      left
                      small
                    >
                      mdi-paperclip
                    </v-icon>
                    {{ $t('actions.add_attachment') }}
                  </v-btn>
                </div>
              </div>
            </v-col>
            <v-col
              cols="12"
              class="d-flex pt-0 justify-center"
            >
              <btn-send
                :loading="loader"
                @click.native="sendReport"
              />
            </v-col>
          </v-layout>
        </v-form>
      </v-col>
    </v-layout>
  </div>
</template>

<script>
import BtnSend from '@components/common/button/BtnSend.vue';
import AddAttachmentMixin from '@components/common/mixins/AddAttachmentMixin.vue';
import { mapGetters } from 'vuex';
import BkfAlert from '@components/common/BkfAlert.vue';

export default {
  components: { BtnSend, BkfAlert },
  mixins: [AddAttachmentMixin],
  data() {
    return {
      loader: false,
      isOverlay: false,
      form: {
        edit: {
          valid: true,
        },
        validationRules: {
          notEmpty: [
            (v) => !!v || this.$t('common_fieldRequired'),
          ],
        },
      },
      types: [
        {
          id: 'loyalty_cards',
          name: this.$t('service_loyaltyCards'),
        },
        {
          id: 'finance_data',
          name: this.$t('service_financeData'),
        },
        {
          id: 'client_add',
          name: this.$t('service_clientAdd'),
        },
        {
          id: 'subscirption',
          name: this.$t('service_subscirption'),
        },
        {
          id: 'other',
          name: this.$t('service_other'),
        },
      ],
      responseAlert: {
        show: false,
        type: 'success',
        message: '',
        messages: {
          success: this.$t('service_reportSent'),
          error: this.$t('service_reportSentProblem'),
        },
      },
      errorReport: {
        subject: '',
        description: '',
        carwash: null,
      },
    };
  },
  computed: {
    ...mapGetters({
      carwashes: 'carwashes/carwashes',
    }),
  },
  methods: {
    clear() {
      this.$refs.form.reset();
      this.showAlert = false;
      this.clearAttachments();
    },
    onError() {
      this.loader = false;
    },
    showResponseAlert({
      message,
      type = 'success',
    }) {
      this.responseAlert.show = true;
      this.responseAlert.message = message;
      this.responseAlert.type = type;
    },
    sendReport() {
      if (this.$refs.form.validate()) {
        this.loader = true;
        const formData = new FormData();

        Object.entries(this.errorReport).forEach(([name, value]) => {
          formData.append(name, value);
        });
        this.appendFilesToForm(formData);

        this.axios({
          method: 'POST',
          url: '/cm/report_error/post',
          data: formData,
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }).then((response) => {
          if (response.data && response.data.status) {
            this.showResponseAlert({ message: this.responseAlert.messages.success });
            this.clear();
          } else {
            this.showResponseAlert({
              message: this.responseAlert.messages.error,
              type: 'error',
            });
          }
        })
          .catch(() => {
            this.showResponseAlert({
              message: this.responseAlert.messages.error,
              type: 'error',
            });
          })
          .finally(() => {
            // this.$eventHub.$emit('error_report_table_refresh');
            this.loader = false;
          });
      }
    },
  },
};
</script>

<style lang="css" scoped>
.loader {
    top: 35%;
    z-index: 5;
}

.loader-background {
    position: absolute;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, .3);
    z-index: 4;
}

.overlayDIV {
    position: fixed; /* Sit on top of the page content */
    display: block; /* Hidden by default */
    width: 100%; /* Full width (cover the whole page) */
    height: 100%; /* Full height (cover the whole page) */
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5); /* Black background with opacity */
    cursor: pointer; /* Add a pointer on hover */
    z-index: 1000000 !important;
}

.overlayDIV span {
    padding: 5px;
    position: relative;
    top: 50%;
}

.v-textarea textarea {
    line-height: 2.75rem !important;
}
</style>
