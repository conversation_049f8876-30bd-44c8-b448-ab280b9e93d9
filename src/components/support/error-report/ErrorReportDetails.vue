<template>
  <div>
    <div
      v-show="items.length || description"
      class="px-3"
    >
      <v-card
        v-if="description"
      >
        <v-card-text class="text-body-1">
          <vue-markdown
            class="description"
            :source="description"
            :anchor-attributes="{target: '_blank', rel: 'nofollow'}"
          />
        </v-card-text>
      </v-card>
      <v-timeline
        v-show="items.length"
        dense
      >
        <v-timeline-item
          v-for="(item, i) in items"
          :key="i"
          :icon="item.icon"
          :color="item.color"
          fill-dot
        >
          <template
            v-if="item.user"
            #icon
          >
            <v-avatar :color="stc(item.user)">
              <v-icon
                v-if="item.user === '–'"
                dark
              >
                mdi-account
              </v-icon>
              <span
                v-else
                class="text-h5 font-weight-bold text-uppercase white--text"
              >
                {{ item.user[0] }}
              </span>
            </v-avatar>
          </template>
          <v-row
            align="center"
          >
            <v-col
              v-if="item.comment"
              cols="10"
            >
              <v-card
                :color="item.color"
                :disabled="item.loading || item.error"
              >
                <v-card-text
                  class="white text--primary"
                >
                  <vue-markdown
                    class="comment"
                    :source="item.comment"
                    :anchor-attributes="{target: '_blank', rel: 'nofollow'}"
                  />
                </v-card-text>
              </v-card>
            </v-col>
            <v-col
              v-if="item.attachments"
              cols="10"
            >
              <v-icon
                large
                color="grey lighten-1"
                class="mr-2"
              >
                mdi-paperclip mdi-rotate-45
              </v-icon>
              <span class="text-h6 pt-1">
                {{ $t('service_attachments') }}
              </span>
            </v-col>
            <v-col>
              <div v-if="item.loading">
                <v-progress-circular
                  color="primary"
                  indeterminate
                  size="10"
                  width="2"
                />
                <span class="ml-1 text-italic text-caption">{{ $t('service_sending') }} </span>
              </div>
              <div v-if="item.error">
                <v-icon
                  small
                  color="red"
                >
                  mdi-alert-circle
                </v-icon>
                <span class="ml-1 red--text text-caption">{{ $t('common_errorHeader') }}</span>
              </div>
              <div
                v-if="item.date"
                class="text-caption"
              >
                {{ item.date }}
              </div>
              <div
                v-if="item.user"
                class="text-h5"
              >
                {{ item.user }}
              </div>
              <div
                v-if="item.caption"
                class="text-h6"
              >
                {{ item.caption }}
              </div>
            </v-col>
          </v-row>
        </v-timeline-item>

        <!--  Response form -->
        <v-timeline-item
          v-if="canReply && !loader"
          key="respond"
          icon="mdi-message-arrow-right-outline"
          color="primary"
          fill-dot
        >
          <template
            v-if="user.username"
            #icon
          >
            <v-avatar :color="stc(user.username)">
              <span
                class="text-h5 font-weight-bold text-uppercase white--text"
              >
                {{ user.username }}
              </span>
            </v-avatar>
          </template>
          <error-report-reply-form
            :id="id"
            :is-ready="isReady"
            @send="onReplySend"
            @success="onReplySendSuccess"
            @failure="onReplySendFailure"
          />
        </v-timeline-item>
      </v-timeline>
    </div>
    <v-progress-linear
      v-show="loader"
      class="mt-2"
      indeterminate
      color="primary"
    />
  </div>
</template>

<script>
import stc from 'string-to-color';
import VueMarkdown from 'vue-markdown';
import DataFetchMixin from '@components/common/mixins/DataFetchMixin.vue';
import ErrorReportReplyForm from '@components/support/error-report/ErrorReportReplyForm.vue';
import { mapGetters } from 'vuex';

export default {
  name: 'ErrorReportDetails',
  components: { ErrorReportReplyForm, VueMarkdown },
  mixins: [
    DataFetchMixin,
  ],
  props: {
    id: {
      type: Number,
      required: true,
    },
    content: {
      type: [String, Object],
      default: null,
    },
  },
  data() {
    return {
      description: null,
      canReply: true,
      isReady: false,
    };
  },
  computed: {
    ...mapGetters({
      user: 'auth/getUser',
    }),
  },
  created() {
    this.getData();
  },
  methods: {
    stc,
    onReplySend(message) {
      if (typeof this.items[this.items.length - 1] === 'object') {
        const { event } = this.items[this.items.length - 1];
        if (event === 'waiting_for_response' || event === 'ready_to_close') {
          this.items.pop();
        }
      }
      this.items.push(message);
    },
    onReplySendSuccess() {
      this.getData();
    },
    onReplySendFailure() {
      this.items[this.items.length - 1].loading = false;
      this.items[this.items.length - 1].error = true;
    },
    getUrl() {
      return `/cm/report_error/show/${this.id}`;
    },
    parseApiResponseData(data) {
      this.description = data.issue.description;
      this.canReply = data.issue.status !== 'closed';
      this.isReady = false;
      this.items = ((item) => {
        const events = [
          {
            event: 'start',
            date: item.issue.start_date,
            caption: this.$t('service_start'),
            color: 'secondary',
            icon: 'mdi-sticker-plus-outline',
          },
          ...item.conversation
            // Remove to show attachments event
            .filter((message) => message.comment.indexOf('[Attachments]') === -1)
            .map((message) => {
              if (message.comment.indexOf('[Attachments]') > -1) {
                return {
                  event: 'attachments',
                  ...message,
                  attachments: true,
                  comment: null,
                  user: message.user || '–',
                };
              }
              return {
                event: 'message',
                ...message,
                comment: message.comment
                  .replace(/<br\s*[/]?>/gi, '\n')
                  .replace(/<\/?[^>]+(>|$)/g, '')
                ?? null,
                user: message.user || '–',
              };
            }),
        ];

        if (data.issue.status === 'ready_to_close' && item.issue.end_date) {
          events.push({
            event: 'ready_to_close',
            date: item.issue.end_date,
            icon: 'mdi-account-check',
            color: 'secondary',
            caption: this.$t('service_readyToClose'),
          });
          this.isReady = true;
        }

        if (data.issue.status === 'closed') {
          events.push({
            event: 'close',
            date: item.issue.end_date,
            icon: 'mdi-check',
            color: 'secondary',
            caption: this.$t('service_eventsClose'),
          });
        }

        if (
          item.issue.waiting_for_response
          && data.issue.status !== 'ready_to_close'
          && data.issue.status !== 'closed'
        ) {
          events.push({
            event: 'waiting_for_response',
            icon: 'mdi-account-clock',
            color: 'secondary',
            caption: this.$t('service_waitingForResponse'),
          });
        }

        return events;
      })(data);
    },
  },
};
</script>

<style>
.comment,
.description {
  font-size: 1.12em;
  line-height: 1.5;
}
.comment p:last-child,
.description p:last-child {
  margin-bottom: 0;
}
</style>
