<template>
  <div>
    <v-row>
      <v-col>
        <v-select
          :value="filtering.status"
          class="d-flex"
          prepend-icon="mdi-state-machine"
          :items="statusOptions"
          :label="$t('common_state')"
          :disabled="loader"
          :footer-props="footerProps"
          @change="onStatusChange"
        />
      </v-col>
    </v-row>
    <v-row>
      <v-col
        cols="12"
        sm="8"
        class="text-sm-start"
      >
        <h2><span>{{ $t('service_listHeading') }}</span></h2>
      </v-col>
      <v-col
        cols="12"
        sm="4"
        class="d-flex justify-end"
      >
        <btn-add
          @click="$vuetify.goTo('#ErrorReportForm')"
        />
        <btn-refresh
          class="ml-2"
          :disabled="loader"
          @click="getData"
        />
      </v-col>
    </v-row>
    <v-row>
      <v-col
        cols="12"
        class="pb-0 px-0"
      >
        <v-data-table
          :headers="dataTable.headers"
          :items="items"
          item-key="id"
          :loading="loader"
          :single-expand="true"
          show-expand
          :server-items-length="totalItems"
          mobile-breakpoint="0"
          :options="options"
          @update:options="onOptionsChange"
        >
          <template #item="{ item, expand, isExpanded }">
            <template v-if="!loader">
              <tr @click="expand(!isExpanded)">
                <td class="text-start">
                  <time class="datetime-start_date">{{ item.start_date }}</time>
                </td>
                <td class="text-sm-start">
                  <v-avatar
                    v-if="item.isReporterKnown"
                    class="mr-1"
                    size="15"
                    :color="stc(item.reporter)"
                  >
                    <span class="text-caption white--text text-uppercase">
                      {{ item.reporter[0] }}
                    </span>
                  </v-avatar>
                  {{ item.reporter }}
                </td>
                <td class="text-sm-start">
                  {{ item.carwashName }}
                </td>
                <td class="text-sm-start subject">
                  {{ item.subject }}
                </td>
                <td class="text-center">
                  <v-icon
                    :color="getStatusData(item).color || 'normal'"
                    small
                  >
                    {{ getStatusData(item).icon }}
                  </v-icon>
                  <div
                    :class="`${getStatusData(item).color || 'normal'}--text`"
                  >
                    {{ getStatusData(item).text }}
                  </div>
                </td>
                <td class="text-sm-end">
                  <v-icon
                    v-show="!isExpanded"
                  >
                    mdi-chevron-down
                  </v-icon>
                  <v-icon
                    v-show="isExpanded"
                  >
                    mdi-chevron-up
                  </v-icon>
                </td>
              </tr>
            </template>
          </template>
          <template #expanded-item="{ headers, item }">
            <td :colspan="headers.length">
              <v-card
                color="blue-grey lighten-5"
                elevation="1"
                outlined
                tile
              >
                <v-card-title>{{ item.subject }}</v-card-title>
                <v-card-subtitle>
                  {{ item.reporter }} • {{ item.start_date }}
                </v-card-subtitle>
                <v-card-text class="text-body-1 text-sm-start px-0 py-0">
                  <error-report-details
                    :id="item.id"
                    :key="item.id"
                  />
                </v-card-text>
              </v-card>
            </td>
          </template>
        </v-data-table>
      </v-col>
    </v-row>
  </div>
</template>

<script>
import stc from 'string-to-color';
import DataFetchMixin from '@components/common/mixins/DataFetchMixin.vue';
import FiltersHandlingMixin from '@components/common/mixins/FiltersHandlingMixin.vue';
import ErrorReportDetails from '@components/support/error-report/ErrorReportDetails.vue';
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import BtnAdd from '@components/common/button/BtnAdd.vue';
import { mapGetters } from 'vuex';

export default {
  components: { BtnAdd, ErrorReportDetails, BtnRefresh },
  mixins: [
    DataFetchMixin,
    FiltersHandlingMixin,
  ],
  data() {
    return {
      dataUrl: '/cm/report_error/',
      filtering: {
        type: null,
        options: {},
        status: null,
      },
      footerProps: {
        'items-per-page-options': [10, 25, 50],
        'items-per-page-text': this.$t('common_rowsPerPage'),
      },
      statusOptions: [
        {
          value: null,
          text: this.$t('common_all'),
        },
        {
          value: 'new',
          text: this.$t('service_new'),
          icon: 'mdi-message-plus-outline',
        },
        {
          value: 'open',
          icon: 'mdi-message-processing-outline',
          text: this.$t('service_open'),
        },
        {
          value: 'ready_to_close',
          icon: 'mdi-account-check',
          text: this.$t('service_statusesReadyToClose'),
        },
        {
          value: 'closed',
          icon: 'mdi-check',
          text: this.$t('service_statusesClose'),
        },
        {
          value: 'waiting',
          icon: 'mdi-message-text-clock-outline',
          text: this.$t('service_waiting'),
          color: 'red',
        },
      ],
      dataTable: {
        expanded: [],
        headers: [
          {
            text: this.$t('service_createdAt'),
            value: 'start_date',
            sortable: false,
          },
          {
            text: this.$t('service_user'),
            value: 'reporter',
            sortable: false,
          },
          {
            text: this.$t('carwash'),
            value: 'carwash',
            sortable: false,
          },
          {
            text: this.$t('service_subject'),
            value: 'subject',
            sortable: false,
          },
          {
            text: this.$t('service_status'),
            value: 'status',
            align: 'center',
            sortable: false,
          },
          {
            text: '',
            value: 'data-table-expand',
            class: 'text-sm-end',
            sortable: false,
          },
        ],
        subheaders: {
          start_date: this.$t('service_createdAt'),
          end_date: this.$t('service_closedAt'),
        },
      },
    };
  },
  computed: {
    ...mapGetters({
      getCarwashBySerial: 'carwashes/getCarwashBySerial',
    }),
  },
  mounted() {
    this.getData();
  },
  methods: {
    stc,
    onOptionsChange(options) {
      this.dataTable.expanded = [];
      this.$set(this.filtering, 'options', options);
    },
    onStatusChange(status) {
      this.dataTable.expanded = [];
      this.$set(this.filtering, 'status', status);
      this.$set(this.filtering.options, 'page', 1);
    },
    getCarwash(serialNumber) {
      const sn = parseInt(serialNumber, 10);

      if (typeof (this.getCarwashBySerial(sn)) === 'undefined') {
        return '-';
      }

      return this.getCarwashBySerial(sn).name;
    },
    parseApiResponseData(data) {
      this.items = data.map((issue) => (
        {
          ...issue,
          id: parseInt(issue.id, 10),
          carwashName: this.getCarwash(issue.sn),
          reporter: issue.reporter || '–',
          isReporterKnown: !!issue.reporter,
        }
      ));
    },
    getParams() {
      return {
        params: {
          page: this.filtering.options.page,
          limit: this.filtering.options.itemsPerPage,
          type: this.filtering.type,
          status: this.filtering.status,
        },
      };
    },
    getStatusData(item) {
      const statuses = this.statusOptions.filter((s) => s.value === item.status);
      return statuses[0] ?? {
        icon: 'mdi-help-circle',
        text: item.status,
      };
    },
  },
};
</script>

<style scoped>
  .subject {
    max-width: 30%;
  }
</style>
