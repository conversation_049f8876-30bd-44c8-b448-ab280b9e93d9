<template>
  <div :class="wrapperClass">
    <v-file-input
      :class="inputClass"
      :rules="validationRules"
      :label="$t('service_attachment')"
      :accept="acceptedFileTypes.join(',')"
      truncate-length="15"
      :dense="dense"
      :hide-details="hideDetails"
      @change="(file) => $emit('change', file)"
    />
    <div class="pl-3">
      <v-btn
        icon
        text
        tile
        color="red"
        dark
        @click="() => $emit('remove')"
      >
        <v-icon>
          mdi-delete
        </v-icon>
      </v-btn>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FileInput',
  props: {
    dense: {
      type: Boolean,
      default: false,
    },
    hideDetails: {
      type: Boolean,
      default: false,
    },
    wrapperClass: {
      type: String,
      default: 'd-flex align-center',
    },
    inputClass: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      validationRules: [
        (file) => !file
          || file.size < 5000000
          || this.$t('form.validation.file_max_size_mb', { size: 5 }),
      ],
      acceptedFileTypes: [
        'image/png',
        'image/jpeg',
        'image/bmp',
        'application/vnd.oasis.opendocument.spreadsheet',
        'application/vnd.oasis.opendocument.text',
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/csv',
      ],
    };
  },
};
</script>
