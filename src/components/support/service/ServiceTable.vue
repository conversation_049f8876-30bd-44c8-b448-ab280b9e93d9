<template>
  <v-data-table
    item-key="id"
    :headers="headers"
    :items="tableItems"
    :loading="loader"
    :options="options"
    :server-items-length="itemsTotal"
    :footer-props="footerProps"
    :expanded="expanded"
    :single-expand="true"
    mobile-breakpoint="0"
    @update:options="onOptionsChange"
  >
    <!--Loader-->
    <template #progress>
      <div class="text-center mx-n4">
        <v-progress-linear
          class="loader"
          indeterminate
          color="primary"
        />
      </div>
    </template>

    <!--Item-->
    <template #item="{ item, isExpanded, expand }">
      <tr @click="expand(!isExpanded)">
        <td class="text-start">
          {{ item.reportTime | formatDateDayTime }}
        </td>
        <td>
          <v-avatar
            v-if="item.reportedBy"
            class="mr-1"
            size="15"
            :color="stc(item.reportedBy)"
          >
            <span class="text-caption white--text text-uppercase">
              {{ item.reportedBy[0] }}
            </span>
          </v-avatar>
          <span>
            {{ item.reportedBy || '–' }}
          </span>
        </td>
        <td class="text-start">
          {{ item.device.serialNumber }} - {{ item.device.objectName }}
        </td>
        <td class="text-start">
          {{ item.title }}
        </td>
        <td class="text-center">
          <v-icon
            small
            class="mr-1"
          >
            {{ statusOptions[item.status].icon || statusOptions.default.icon }}
          </v-icon>
          <div>
            {{ $t(`service.status.desc.${item.status}`) }}
          </div>
        </td>
        <td class="text-right">
          <v-icon v-if="isExpanded">
            mdi-chevron-up
          </v-icon>
          <v-icon v-else>
            mdi-chevron-down
          </v-icon>
        </td>
      </tr>
    </template>

    <template #expanded-item="{ headers: _headers, item }">
      <tr>
        <td
          :colspan="_headers.length"
          class="elevation-3 px-0"
        >
          <v-card
            outlined
            color="blue-grey lighten-5"
          >
            <v-card-title class="flex-column align-start mb-2">
              <div class="d-block text-caption mb-1">
                {{ `#${item.id}` }}
              </div>
              <div class="d-block text-h6">
                {{ item.title }}
              </div>
            </v-card-title>
            <v-card-subtitle>
              <div>
                <span class="font-weight-bold">{{ $t('service_issueReportSource') }}</span>
                <span class="ml-3">{{ item.reportSource }}</span>
              </div>
            </v-card-subtitle>
            <v-card-text>
              <v-card
                v-if="item.content.length"
              >
                <v-card-text
                  class="text-body-1"
                >
                  <vue-markdown
                    :key="item.id"
                    class="description"
                    :source="item.content"
                    :anchor-attributes="{target: '_blank', rel: 'nofollow'}"
                  />
                </v-card-text>
              </v-card>
              <i v-else>{{ $t('service_noContent') }}</i>
            </v-card-text>
          </v-card>
        </td>
      </tr>
    </template>
  </v-data-table>
</template>

<script>
import stc from 'string-to-color';
import VueMarkdown from 'vue-markdown';
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';

export default {
  name: 'ServiceTable',
  components: {
    VueMarkdown,
  },
  mixins: [
    FilterMixin,
  ],
  props: {
    items: {
      type: Array,
      default() {
        return [];
      },
    },
    itemsTotal: {
      type: Number,
      default: -1,
    },
    loader: {
      type: Boolean,
      default: false,
    },
    options: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      footerProps: {
        'items-per-page-options': [10, 25, 50],
        'items-per-page-text': this.$t('common_rowsPerPage'),
      },
      expanded: [],
      filtering: {
        options: {},
      },
      statusOptions: {
        open: {
          icon: 'mdi-comment-processing-outline',
        },
        completed: {
          icon: 'mdi-comment-check',
        },
      },
      headers: [
        {
          value: 'reportTime',
          text: this.$t('service_time'),
        },
        {
          value: 'reportedBy',
          text: this.$t('service_issueReportedBy'),
        },
        {
          value: 'serialnumber',
          text: this.$t('common_carwash'),
        },
        {
          value: 'title',
          text: this.$t('service_issueTitle'),
        },
        {
          value: 'status',
          text: this.$t('service_tableStatus'),
          align: 'center',
        },
        {
          value: 'expand',
          text: '',
        },
      ],
    };
  },
  computed: {
    tableItems() {
      if (this.loader) {
        return [];
      }
      return this.items;
    },
  },
  methods: {
    stc,
    onOptionsChange(options) {
      this.$set(this.filtering, 'options', options);
    },
  },
};
</script>
