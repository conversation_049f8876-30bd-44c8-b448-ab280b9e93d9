<template>
  <v-card-text class="px-0 pb-0">
    <v-row>
      <v-col
        class="py-0"
      >
        <v-autocomplete
          class="d-flex"
          :disabled="disabled"
          :items="carwashesOptions"
          item-value="serialNumber"
          item-text="longName"
          prepend-icon="mdi-car-wash"
          :label="$t('common_filtersCarwash')"
          :value="filtering.carwash"
          @change="onCarwashChange"
        />
      </v-col>
      <v-col
        class="py-0"
      >
        <v-select
          :value="filtering.status"
          class="d-flex"
          prepend-icon="mdi-state-machine"
          :items="statusOptions"
          :label="$t('common_state')"
          :disabled="disabled"
          @change="onStatusChange"
        />
      </v-col>
    </v-row>
  </v-card-text>
</template>

<script>

import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';

export default {
  name: 'FilteringService',
  mixins: [
    FilterMixin,
  ],
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    carwashes: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      statusOptions: [
        {
          id: 'open',
          text: this.$t('service_filtersOpen'),
          value: 'open',
        },
        {
          id: 'completed',
          text: this.$t('service_completed'),
          value: 'completed',
        },
        {
          id: 'all',
          text: this.$t('service_allFeminine'),
          value: null,
        },
      ],
      filtering: {
        carwash: null,
        dates: {
          from: null,
          to: null,
        },
        status: 'open',
      },
    };
  },
  computed: {
    carwashesOptions() {
      const carwashesOptions = this.multiselectHelper.toSelect(this.carwashes);
      carwashesOptions.unshift({
        serialNumber: null,
        longName: this.$t('common_all'),
      });
      return carwashesOptions;
    },
  },
  methods: {
    onFiltersChange(filtering) {
      this.filtering = {
        ...this.filtering,
        ...filtering,
      };
    },
    onDateRangeChange(dates) {
      this.$set(this.filtering, 'dates', dates);
    },
    onCarwashChange(carwash) {
      this.$set(this.filtering, 'carwash', carwash);
    },
    onStatusChange(status) {
      this.$set(this.filtering, 'status', status);
    },
  },
};
</script>
