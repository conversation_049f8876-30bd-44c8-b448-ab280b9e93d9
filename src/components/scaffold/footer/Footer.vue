<template>
  <v-footer
    color="white"
    elevation="3"
  >
    <v-layout
      right
      justify-start
      align-center
      row
      wrap
    >
      <img
        class="logo left p-20 hidden-xs-only"
        src="@assets/logo.png"
      >

      <v-layout
        class="footer-v2"
        row
        wrap
      >
        <div
          class="justify-end pl-6"
          style="display: inline-flex;"
        >
          <img
            class="hidden-xs-only"
            src="@assets/icon-mail.png"
          >
          <div id="contact_mail">
            <p class="contact-box text mt-2 mb-0">
              {{ $t('common_email') }}
            </p>
            <a
              :href="`mailto:${ contactEmail }`"
              class="contact-box link"
            >{{ contactEmail }}</a>
          </div>
        </div>
      </v-layout>
    </v-layout>
  </v-footer>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'FooterComponent',
  data() {
    return {
      contact: {
        email: '<EMAIL>',
      },
    };
  },
  computed: {
    ...mapGetters({
      contactEmail: 'auth/contactEmail',
    }),
  },
};
</script>

<style scoped>
  footer .footer-v2 {
      align-items: center;
      display: flex;
      justify-content: right;
      text-align: right;
  }

  footer {
    height: 74px;
  }

  .row {
    margin: 0;
  }

  p.contact-box {
    color: #537598;
    font-size: 10px;
    font-weight: 700;
    text-align: left;
  }
  a.contact-box {
    font-size: 16px;
    font-weight: 700;
    margin-top: 10px;
  }
</style>
