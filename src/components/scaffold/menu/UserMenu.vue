<template>
  <div>
    <v-menu
      offset-y
      :nudge-top="-10"
      transition="slide-y-transition"
    >
      <template #activator="{ on, attrs }">
        <v-btn
          icon
          class="mr-3"
          v-bind="attrs"
          v-on="on"
        >
          <v-avatar
            color="#26364f"
            size="36"
          >
            <v-icon color="white">
              mdi-account
            </v-icon>
          </v-avatar>
        </v-btn>
      </template>
      <v-list class="profile-menu">
        <v-list-item
          v-for="(menuProfilePosition, i) in menuProfilePositions"
          v-show="menuProfilePosition.show"
          :key="i"
          @click="openProfileMenuItem(menuProfilePosition)"
        >
          <v-list-item-icon>
            <v-icon>
              {{ menuProfilePosition.icon }}
            </v-icon>
          </v-list-item-icon>
          <v-list-item-content>
            <v-list-item-title color="primary">
              {{ menuProfilePosition.name }}
            </v-list-item-title>
          </v-list-item-content>
        </v-list-item>
      </v-list>
    </v-menu>
  </div>
</template>

<script>

import { mapActions, mapGetters } from 'vuex';

export default {
  props: {
    user: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      common: {},
    };
  },
  computed: {
    ...mapGetters({
      isOwner: 'auth/isOwner',
    }),
    menuProfilePositions() {
      return [
        {
          type: 'username',
          name: this.user.username,
          icon: 'mdi-email',
          show: true,
        },
        {
          type: 'profile-configuration',
          name: this.$t('menu_profile'),
          icon: 'mdi-account',
          url: '/user/profile',
          show: true,
        },
        {
          type: 'subscription',
          name: `${this.$t('common_subscription')}`,
          icon: 'mdi-cash',
          url: '/subscription',
          show: this.isOwner,
        },
        {
          type: 'logout',
          name: this.$t('menu_logout'),
          icon: 'mdi-logout',
          callback: 'logoutClick',
          show: true,
        },
      ];
    },
  },
  methods: {
    ...mapActions({
      logout: 'auth/logout',
    }),
    async logoutClick() {
      await this.logout();
      this.$router.push({ name: 'login' });
      this.$router.go(0);
    },
    openProfileMenuItem(item) {
      if (item.url) {
        this.$router.replace(item.url);
        return;
      }
      if (item.modal) {
        this.$refs[item.modal].dialog = true;
        this.drawer = false;
        return;
      }
      if (item.callback) {
        this[item.callback]();
        return;
      }
      if (item.location) {
        window.location.href = item.location;
      }
    },
  },
};
</script>
