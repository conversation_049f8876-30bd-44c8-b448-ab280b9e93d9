<template>
  <div>
    <v-menu
      ref="menu"
      offset-y
      :nudge-top="-10"
      transition="slide-y-transition"
      :close-on-content-click="false"
    >
      <template #activator="{ on, attrs }">
        <v-btn
          icon
          class="mr-4"
          v-bind="attrs"
          v-on="on"
        >
          <v-badge
            color="red"
            :value="count"
            :content="count"
            overlap
          >
            <v-icon
              color="#26364f"
            >
              mdi-bell
            </v-icon>
          </v-badge>
        </v-btn>
      </template>

      <v-card width="420">
        <v-list v-if="count > 0">
          <v-list-item
            v-for="(message, id) in messages"
            :key="id"
            :three-line="message.content
              && message.content.carwash
              && message.content.carwash.length > 0"
          >
            <v-list-item-avatar v-if="message.content && message.content.icon">
              <v-icon :color="getIconColor(message)">
                {{ message.content.icon }}
              </v-icon>
            </v-list-item-avatar>

            <v-list-item-content>
              <v-list-item-title>
                {{ message.content.title }}
              </v-list-item-title>
              <v-list-item-subtitle v-if="message.content && message.content.carwash">
                {{ message.content.carwash }}
              </v-list-item-subtitle>
              <v-list-item-subtitle>
                {{ message.created_at|formatDateDayTime }}
              </v-list-item-subtitle>
            </v-list-item-content>

            <v-list-item-action>
              <v-tooltip bottom>
                <template #activator="{ on, attrs }">
                  <v-btn
                    icon
                    small
                    v-bind="attrs"
                    @click="markRead(message.id)"
                    v-on="on"
                  >
                    <v-icon>
                      mdi-eye
                    </v-icon>
                  </v-btn>
                </template>
                <span>{{ $t('common_markRead') }}</span>
              </v-tooltip>
            </v-list-item-action>
          </v-list-item>
          <v-divider />
        </v-list>

        <no-unread-messages v-else />

        <v-card-actions class="justify-center">
          <v-btn
            text
            @click="goToList()"
          >
            {{ $t('menu_more') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-menu>
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import NoUnreadMessages from './NoUnreadMessages.vue';

export default {
  components: {
    NoUnreadMessages,
  },
  data() {
    return {
    };
  },
  computed: {
    ...mapGetters({
      count: 'messages/count',
      messages: 'messages/menu_messages',
    }),
  },
  mounted() {
    this.fetchMessages();
  },
  methods: {
    ...mapActions({
      fetchMessages: 'messages/fetch',
      markRead: 'messages/mark_read',
    }),
    getIconColor: (message) => message.content.icon_color ?? 'primary',
    goToList() {
      if (this.$route.name !== 'messages_list') {
        this.$router.push('/messages');
      }
      this.$refs.menu.save();
    },
  },
};
</script>
