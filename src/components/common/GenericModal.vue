<template>
  <div>
    <v-dialog
      v-model="dialog"
      :max-width="maxWidth"
      :persistent="loader"
      :fullscreen="fullscreenInternal"
      :scrollable="scrollable"
    >
      <v-card :loading="loader">
        <v-card-title class="title">
          <slot name="title">
            <span class="headline">
              <h5 class="text-uppercase">{{ title }}</h5>
            </span>
            <v-spacer />
            <v-btn
              icon
              text
              tile
              small
              dark
              @click="closeDialog"
            >
              <v-icon>
                mdi-close
              </v-icon>
            </v-btn>
          </slot>
        </v-card-title>
        <v-card-text>
          <slot />
        </v-card-text>
        <v-card-actions v-if="showActions">
          <slot name="actions">
            <v-spacer />
            <v-btn
              color="gray"
              text
              :disabled="loader"
              @click="closeDialog"
            >
              {{ cancelButtonText || $t('actions.cancel') }}
            </v-btn>
            <v-btn
              v-if="showConfirmButton"
              color="primary"
              :loading="loading"
              :disabled="loader || disabled"
              @click="submit"
            >
              {{ confirmButtonText || $t('actions.save') }}
            </v-btn>
          </slot>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
export default {
  name: 'GenericModal',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      required: true,
    },
    confirmButtonText: {
      type: String,
      default: null,
    },
    cancelButtonText: {
      type: String,
      default: null,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    maxWidth: {
      type: [String, Number],
      default: '700',
    },
    fullscreen: {
      type: Boolean,
      default: false,
    },
    scrollable: {
      type: Boolean,
      default: false,
    },
    showActions: {
      type: Boolean,
      default: true,
    },
    showConfirmButton: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data: () => ({
    loadingInternal: false,
  }),
  computed: {
    dialog: {
      get() {
        return this.value;
      },
      set(value) {
        this.$emit('input', value);
      },
    },
    fullscreenInternal() {
      return this.fullscreen || this.$vuetify.breakpoint.smAndDown;
    },
    loader() {
      return this.loading || this.loadingInternal;
    },
  },
  watch: {
    dialog(newVal, oldVal) {
      if (oldVal === true && newVal === false) {
        this.$nextTick(() => {
          this.$emit('close');
        });
      }
    },
  },
  methods: {
    submit() {
      this.$emit('submit');
    },
    openDialog() {
      this.dialog = true;
      this.$emit('open');
    },
    closeDialog() {
      this.dialog = false;
      this.$emit('close');
    },
  },
};
</script>
