<template>
  <v-tooltip bottom>
    <template #activator="{ on, attrs }">
      <span
        v-bind="attrs"
        v-on="on"
      >{{ boolTranslate.text }}</span>
    </template>
    <span>{{ boolTranslate.text }}</span>
  </v-tooltip>
</template>

<script>
export default {
  name: 'BoolTranslate',
  props: {
    boolValue: {
      type: [Boolean, String, Number],
      required: true,
    },
  },
  computed: {
    boolTranslate() {
      const normalized = String(this.boolValue).toLowerCase();
      const BOOL_MAP = {
        true: this.$t('yes'),
        false: this.$t('no'),
      };

      if (['true', '1'].includes(normalized)) {
        return { text: BOOL_MAP.true };
      }
      if (['false', '0'].includes(normalized)) {
        return { text: BOOL_MAP.false };
      }

      return { text: '-' };
    },
  },
};
</script>
