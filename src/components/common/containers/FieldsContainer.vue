<template>
  <titled-container
    :title="title"
  >
    <div class="d-flex justify-space-around white py-3">
      <div
        v-for="(item) in items"
        :key="item.label"
        class="px-3"
      >
        <div>
          <slot
            :name="`${item.field}.label`"
          >
            <b>{{ item.label }}</b>
          </slot>
        </div>
        <div>
          <slot
            :name="`${item.field}.value`"
          >
            {{ item.value ?? '-' }}
          </slot>
        </div>
      </div>
    </div>
  </titled-container>
</template>

<script>
import TitledContainer from './TitledContainer.vue';

export default {
  name: 'FieldsContainer',

  components: {
    TitledContainer,
  },

  props: {
    title: {
      type: String,
      default: '',
      required: true,
    },
    items: {
      type: Array,
      default: () => ([]),
      required: true,
    },
  },
};
</script>
