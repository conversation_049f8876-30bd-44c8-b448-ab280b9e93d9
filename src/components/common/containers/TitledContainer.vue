:<template>
  <v-container fluid>
    <v-card
      flat
      outlined
      color="blue-grey lighten-5"
    >
      <h3 class="py-3">
        {{ title }}
      </h3>
      <slot />
      <!-- <div class="d-flex justify-space-around white py-3">
        <div class="px-3">
          <div><b>Lokalizacja</b></div>
          <a
            v-if="item.location"
            :href="getLocationLink(item.location)"
            target="_blank"
            rel="noopener noreferrer"
          >{{ item.location.address }}, {{ item.location.city }}
          </a>
          <div v-else>
            -
          </div>
        </div>
        <div class="px-3">
          <div><b>Data startu</b></div>
          <div>{{ item.startDate|formatDateDayTime }}</div>
        </div>
        <div class="px-3">
          <div><b>Koniec gwarancji</b></div>
          <div>{{ item.warrantyEnd|formatDateDayTime }}</div>
        </div>
        <div class="px-3">
          <div><b>Kod</b></div>
          <div>{{ item.code ?? '-' }}</div>
        </div>
      </div> -->
    </v-card>
  </v-container>
</template>

<script>
export default {
  name: 'TitledContainer',

  props: {
    title: {
      type: String,
      default: '',
      required: true,
    },
  },
};
</script>
