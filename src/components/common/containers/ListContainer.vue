<template>
  <titled-container
    :title="title"
  >
    <v-simple-table
      class="no-hover-highlight"
    >
      <tbody>
        <tr>
          <td
            v-for="(label) in headers.map(obj => obj.label)"
            :key="label"
          >
            <b>{{ label }}</b>
          </td>
        </tr>
        <tr
          v-for="(item) in items"
          :key="item.id"
        >
          <td
            v-for="(field) in headers.map(obj => obj.field)"
            :key="field"
          >
            <slot
              :name="field"
              :value="item"
            >
              {{ item[field] ?? '-' }}
            </slot>
          </td>
        </tr>
      </tbody>
    </v-simple-table>
  </titled-container>
</template>

<script>
import TitledContainer from './TitledContainer.vue';

export default {
  name: 'ListContainer',

  components: {
    TitledContainer,
  },

  props: {
    title: {
      type: String,
      default: '',
      required: true,
    },
    headers: {
      type: Array,
      default: () => ([]),
      required: true,
    },
    items: {
      type: Array,
      default: () => ([]),
      required: true,
    },
  },
};
</script>

<style scoped>
.no-hover-highlight tbody tr:hover {
  background-color: initial !important;
}
</style>
