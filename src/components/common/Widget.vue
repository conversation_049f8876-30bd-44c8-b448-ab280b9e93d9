<template>
  <v-card
    :loading="loading"
    :class="cardClass"
  >
    <v-card-title :class="titleClass">
      <slot name="title">
        <v-icon
          v-if="titlePrependIcon"
          class="mr-2"
          dark
        >
          {{ titlePrependIcon }}
        </v-icon>
        <span class="text-uppercase">{{ title }}</span>
        <v-spacer />
        <div class="title-actions">
          <slot name="titleActions">
            <v-btn
              v-if="expandable"
              class="ml-2"
              icon
              text
              tile
              small
              dark
              @click="onExpansion"
            >
              <v-icon>
                {{ expansionIcon }}
              </v-icon>
            </v-btn>
          </slot>
        </div>
      </slot>
    </v-card-title>
    <v-card-text v-if="isExpanded">
      <div v-if="!loading">
        <slot />
      </div>
      <div
        v-if="loading"
        class="loader-background wrapperClass text-center"
      >
        <v-progress-circular
          class="loader"
          indeterminate
          color="primary"
          size="42"
        />
      </div>
    </v-card-text>
  </v-card>
</template>

<script>
export default {
  name: 'CmWidget',
  props: {
    titleClass: {
      type: [String, Object],
      default: 'title',
    },
    cardClass: {
      type: [String],
      default: '',
    },
    expandable: {
      type: Boolean,
      default: false,
    },
    expanded: {
      type: Boolean,
      default: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
    titlePrependIcon: {
      type: [String, undefined],
      default: undefined,
    },
    expandIcon: {
      type: String,
      default: 'mdi-chevron-down',
    },
    shrinkIcon: {
      type: String,
      default: 'mdi-chevron-up',
    },
  },
  data() {
    return {
      isExpanded: this.expanded,
    };
  },
  computed: {
    expansionIcon() {
      if (this.isExpanded) {
        return this.shrinkIcon;
      }
      return this.expandIcon;
    },
  },
  methods: {
    onExpansion() {
      this.isExpanded = !this.isExpanded;
      this.$emit('update:expanded', this.isExpanded);
    },
  },
};
</script>

<style>
.wrapperClass {
  min-height: 150px;
}

.loader {
  top: 35%;
}

.loader-background {
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, .3);
}

.title {
  min-height: 56px;
}
</style>
