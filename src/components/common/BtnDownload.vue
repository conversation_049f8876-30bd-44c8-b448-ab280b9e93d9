<template>
  <v-tooltip bottom>
    <template #activator="{ on, attrs }">
      <v-btn
        v-bind="attrs"
        color="accent"
        elevation="1"
        :small="small"
        :x-small="xSmall"
        :fab="tile"
        :tile="tile"
        :href="href"
        :loading="loading"
        :disabled="disabled"
        :class="buttonClass"
        v-on="on"
        @click="onClick"
      >
        <template
          v-if="xSmall && !tile"
          #loader
        >
          <v-progress-circular
            indeterminate
            size="12"
            width="2"
            color="white"
          />
        </template>
        <v-icon
          :left="!tile"
        >
          mdi-cloud-download
        </v-icon>
        <span v-if="!tile">
          {{ text ? text : $t('common_download') }}
        </span>
      </v-btn>
    </template>
    <span>
      {{ text ? text : $t('common_download') }}
    </span>
  </v-tooltip>
</template>
<script>
/**
 * @deprecated użyj ActDownload
 */
export default {
  name: 'BtnDownload',
  props: {
    disabled: {
      type: <PERSON>olean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    tile: {
      type: Boolean,
      default: true,
    },
    small: {
      type: Boolean,
      default: false,
    },
    xSmall: {
      type: Boolean,
      default: true,
    },
    href: {
      type: [String, Object],
      default: null,
    },
    text: {
      type: String,
      default: null,
    },
    buttonClass: {
      type: String,
      default: null,
    },
    url: {
      type: String,
      default: null,
    },
  },
  methods: {
    onClick() {
      if (this.url) {
        this.download(this.url);
      }
      this.$emit('click');
    },
    async download(url) {
      let fileName = null;
      const { status, data, headers } = await this.axios.get(
        url,
        {
          responseType: 'blob',
        },
      );

      if (status === 200) {
        const fileType = headers['content-type'] || 'application/octet-stream';
        // spróbuj pobrać nazwę z nagłówka
        const disposition = headers['content-disposition'] || headers['Content-Disposition'];

        if (disposition) {
          const match = disposition.match(/filename="?([^"]+)"?/);
          if (match && match[1]) {
            fileName = decodeURIComponent(match[1]);
          } else {
            throw Error('header content-disposition filename empty');
          }
        } else {
          throw Error('CORSE not allow content-disposition');
        }

        const blob = new Blob([data], { type: fileType });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = fileName;
        link.click();
        URL.revokeObjectURL(link.href);
        this.showDialog = false;
      }
    },
  },
};
</script>
