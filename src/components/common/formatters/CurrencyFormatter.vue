<template>
  <span>{{ formattedAmount }}</span>
</template>

<script>
export default {
  props: {
    // Number to format, e.g. 1234.56
    value: {
      type: [Number, null],
      required: true,
    },
    // Currency symbol, e.g. "zł"
    symbol: {
      type: String,
      required: true,
    },
    digits: {
      type: Number,
      default: 2, // Default to 2 decimal places
    },
  },
  computed: {
    formattedAmount() {
      if (this.value === null) {
        return '-';
      }
      // Format number with 2 decimal places and thousand separators
      const formattedNumber = this.value?.toLocaleString(undefined, {
        minimumFractionDigits: this.digits,
        maximumFractionDigits: this.digits,
      }) ?? '-';
      // Combine with provided symbol
      return `${formattedNumber} ${this.symbol}`;
    },
  },
};
</script>
