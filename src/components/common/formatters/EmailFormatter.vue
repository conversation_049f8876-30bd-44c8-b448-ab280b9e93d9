<template>
  <div
    v-if="value"
    class="d-flex align-center"
  >
    <v-btn
      icon
      x-small
      class="mr-2"
      :class="{ 'primary--text': copied }"
      @click="copyToClipboard"
    >
      <v-icon small>
        {{ copied ? 'mdi-check' : 'mdi-content-copy' }}
      </v-icon>
    </v-btn>
    <blurred-formatter :value="value" />
  </div>
  <span v-else>-</span>
</template>

<script>
import BlurredFormatter from './BlurredFormatter.vue';

export default {
  components: {
    BlurredFormatter,
  },
  props: {
    value: {
      type: String,
      default: null,
      required: false,
    },
  },
  data() {
    return {
      copied: false,
    };
  },
  methods: {
    async copyToClipboard() {
      try {
        await navigator.clipboard.writeText(this.value);
        this.copied = true;
        setTimeout(() => {
          this.copied = false;
        }, 2000);
      } catch (err) {
        // Failed to copy
      }
    },
  },
};
</script>
