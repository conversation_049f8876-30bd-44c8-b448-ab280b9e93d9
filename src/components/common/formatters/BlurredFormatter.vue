<template>
  <span
    v-if="value"
    :class="{ blurred }"
    @click="toggleBlur"
  >
    {{ value }}
  </span>
  <span v-else>-</span>
</template>

<script>
// na potrzeby prezentacji i robienia filmów, możemy domyślnie ukrywać wrażliwe dane
// ustawienie zmiennej w localStorage tylko w web console.
const STORAGE_KEY = 'hideSensitiveData';
export default {
  props: {
    value: {
      type: [String, Number, null],
      default: null,
      required: false,
    },
  },
  data() {
    return {
      blurred: false, // domyślnie
    };
  },
  created() {
    let hide = localStorage.getItem(STORAGE_KEY);

    // Jeś<PERSON> klucz nie istnieje, ustaw na 'false'
    if (hide === null) {
      localStorage.setItem(STORAGE_KEY, 'false');
      hide = 'false';
    }
    this.blurred = hide === 'true';
  },
  methods: {
    toggleBlur() {
      this.blurred = false;
    },
  },
};
</script>

<style scoped>
.blurred {
  filter: blur(6px);
  user-select: none;
  cursor: pointer;
  transition: filter 0.3s ease;
}
</style>
