<template>
  <td
    :class="`text-${align} font-weight-${weight} ${additionalClass}`"
    :colspan="colspan"
    :rowspan="rowspan"
  >
    {{ value|currencySymbol(currency) }}
  </td>
</template>

<script>

/**
 * @deprecated Use ValueCurrency
 */
export default {
  name: 'CustomCurrencySymbolCell',
  props: {
    currency: {
      type: String,
      default: null,
    },
    append: {
      type: String,
      default: '',
    },
    value: {
      type: Number,
      default: 0,
    },
    align: {
      type: String,
      default: 'right',
    },
    weight: {
      type: String,
      default: 'regular',
    },
    additionalClass: {
      type: String,
      default: '',
    },
    colspan: {
      type: String,
      default: '1',
    },
    rowspan: {
      type: String,
      default: '1',
    },
  },
};
</script>
