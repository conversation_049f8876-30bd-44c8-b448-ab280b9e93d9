<template>
  <v-tooltip
    v-if="documentData"
    bottom
  >
    <template #activator="{ on, attrs }">
      <v-icon
        v-bind="attrs"
        v-on="on"
      >
        {{ documentData.icon }}
      </v-icon>
    </template>
    <span> {{ documentData.text }}</span>
  </v-tooltip>
</template>

<script>

export default {
  props: {
    type: {
      type: String,
      required: true,
    },
  },
  computed: {
    documentData() {
      if (this.type === 'invoice') {
        return {
          icon: 'mdi-file-document-outline',
          text: this.$t('common_invoice'),
        };
      }

      if (this.type === 'receipt') {
        return {
          icon: 'mdi-receipt-text',
          text: this.$t('common_receipt'),
        };
      }

      return null;
    },
  },
};
</script>
