<script>
export default {
  name: 'ReportNameTranslation',
  data() {
    return {
      texts: {
        'card_client_report.card_report_email_header': this.$t('card_client_report.card_report_email_header'),
        'card_client_report.cards_transactions': this.$t('card_client_report.cards_transactions'),
        'finance.fiscal-transactions': this.$t('finance.fiscal-transactions'),
        'finance.mobile-payments': this.$t('common_mobilePayments'),
        'finance.program-usage': this.$t('common_programUsage'),
        'finance.turnover-from': this.$t('common_turnoverFrom'),
      },
    };
  },
  methods: {
    getReportTitleTranslation(reportName) {
      if (reportName in this.texts) {
        return this.texts[reportName];
      }

      return reportName;
    },
  },
};
</script>
