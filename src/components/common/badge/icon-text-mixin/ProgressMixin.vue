<script>
export default {
  name: 'ProgressMixin',
  data() {
    return {
      icons: {
        WAITING: 'mdi-timer-sand-empty',
        NOT_FULLY_REFILLED: 'mdi-sync',
        REFILLED: 'mdi-check-underline',
        DEFAULT: 'mdi-help-circle-outline',
      },
      texts: {
        WAITING: this.$t('common_toSent'),
        NOT_FULLY_REFILLED: this.$t('common_notFullySent'),
        REFILLED: this.$t('common_sendToCard'),
      },
    };
  },
  methods: {
    getTopUpProgressIcon(progress) {
      if (progress in this.icons) {
        return {
          icon: this.icons[progress],
          text: this.texts[progress],
        };
      }

      return {
        icon: this.icons.DEFAULT,
        text: progress,
      };
    },
  },
};
</script>
