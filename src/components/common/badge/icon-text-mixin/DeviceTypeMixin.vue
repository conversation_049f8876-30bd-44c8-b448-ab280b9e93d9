<script>
/**
 * @deprecated u<PERSON><PERSON><PERSON><PERSON>con
 */
export default {
  name: 'DeviceTypeMixin',
  data() {
    return {
      icons: {
        CAR_WASH: 'mdi-car-wash',
        VACUUM_CLEANER: 'mdi-auto-fix',
        DISTRIBUTOR: 'mdi-cup-water',
        MONEY_CHANGER: 'mdi-cash-100',
        UNKNOWN: 'mdi-help',
        INTERNET: 'mdi-wifi',
        SCRIPT: 'mdi-dns',
        DEFAULT: 'mdi-help-circle-outline',
      },
      texts: {
        CAR_WASH: this.$t('fiscal_transactions.source.CAR_WASH'),
        VACUUM_CLEANER: this.$t('fiscal_transactions.source.VACUUM_CLEANER'),
        DISTRIBUTOR: this.$t('fiscal_transactions.source.DISTRIBUTOR'),
        MONEY_CHANGER: this.$t('fiscal_transactions.source.MONEY_CHANGER'),
        UNKNOWN: this.$t('fiscal_transactions.source.UNKNOWN'),
        INTERNET: this.$t('fiscal_transactions.source.INTERNET'),
        SCRIPT: this.$t('fiscal_transactions.source.SCRIPT'),
      },
    };
  },
  methods: {
    getTopUpSourceIcon(source) {
      if (source in this.icons) {
        return {
          icon: this.icons[source],
          text: this.texts[source],
        };
      }

      return {
        icon: this.icons.DEFAULT,
        text: source,
      };
    },
  },
};
</script>
