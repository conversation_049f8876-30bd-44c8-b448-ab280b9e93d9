<script>
export default {
  name: 'CarwashAlertTypeMixin',
  data() {
    return {
      icons: {
        ok: 'mdi-check-circle',
        warning: 'mdi-alert',
        error: 'mdi-alert-circle',
        info: 'mdi-information',
        information: 'mdi-information',
        ignore: 'mdi-cancel',
        notice: 'mdi-close-thick',
        default: 'mdi-help-circle',
        unspecified: 'mdi-help-circle',
        fatal: 'mdi-error',
      },
      colors: {
        ok: 'green',
        error: 'red',
        warning: 'orange',
        info: 'green',
        information: 'blue',
        ignore: 'brown',
        notice: 'grey',
        default: 'grey',
        unspecified: 'grey',
        fatal: 'red',
      },
    };
  },
  methods: {
    getCarwashAlertIcon(alertLevel) {
      if (alertLevel in this.icons) {
        return this.icons[alertLevel];
      }

      return this.icons.default;
    },
    getCarwashAlertColor(alertLevel) {
      if (alertLevel in this.colors) {
        return this.colors[alertLevel];
      }

      return this.colors.default;
    },
  },
};
</script>
