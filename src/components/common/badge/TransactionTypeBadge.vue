<template>
  <div>
    <v-icon
      :small="small"
      :color="color"
    >
      {{ icons[type] }}
    </v-icon>
    {{ $t(`fiscal_transactions.source.${type}`) }} {{ stand ? `#${stand}` : '' }}
  </div>
</template>

<script>
export default {
  name: 'DeviceTypeBadge',
  props: {
    deviceType: {
      type: String,
      default: null,
    },
    standId: {
      type: Number,
      default: null,
    },
    xSmall: {
      type: Boolean,
      default: true,
    },
    small: {
      type: Boolean,
      default: false,
    },
    color: {
      type: String,
      nullable: true,
      default: null,
    },
    iconClass: {
      type: String,
      default: 'mr-1',
    },
  },
  data() {
    return {
      type: this.deviceType,
      stand: this.standId,
      icons: {
        CAR_WASH: 'mdi-car-wash',
        VACUUM_CLEANER: 'mdi-auto-fix',
        DISTRIBUTOR: 'mdi-cup-water',
        MONEY_CHANGER: 'mdi-sync',
        UNKNOWN: 'mdi-help',
        INTERNET: 'mdi-wifi',
      },
    };
  },
};
</script>
