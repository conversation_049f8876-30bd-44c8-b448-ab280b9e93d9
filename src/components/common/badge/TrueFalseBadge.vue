<template>
  <v-tooltip bottom>
    <template #activator="{ on, attrs }">
      <v-icon
        v-bind="attrs"
        :color="color"
        v-on="on"
      >
        {{ icon }}
      </v-icon>
    </template>
    <span> {{ customTooltipText || text }}</span>
  </v-tooltip>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      required: true,
    },
    customTooltipText: {
      type: String,
      default: null,
    },
  },
  computed: {
    icon() {
      return this.value ? 'mdi-check-circle-outline' : 'mdi-close-circle-outline';
    },
    text() {
      return this.value ? this.$t('yes') : this.$t('no');
    },
    color() {
      return this.value ? 'green darken-2' : 'error';
    },
  },
};
</script>
