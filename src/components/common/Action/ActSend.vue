<template>
  <v-tooltip bottom>
    <template #activator="{ on }">
      <v-btn
        class="ml-2"
        x-small
        tile
        rounded
        fab
        elevation="1"
        color="primary"
        :disabled="disabled"
        v-on="on"
        @click.stop
        @click.native="send"
      >
        <v-icon>mdi-email</v-icon>
      </v-btn>
    </template>
    <span>{{ text ? text : $t('actions.send_invoice') }}</span>
  </v-tooltip>
</template>
<script>

export default {
  props: {
    url: {
      type: String,
      required: true,
    },
    text: {
      type: String,
      default: null,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    async send() {
      await this.axios.post(this.url);
    },
  },
};
</script>
