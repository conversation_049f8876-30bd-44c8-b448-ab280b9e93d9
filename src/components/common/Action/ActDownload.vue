<template>
  <btn-download
    :disabled="disabled"
    :type="type"
    @click="download()"
  >
    <span>
      {{ text ? text : $t('common_download') }}
    </span>
  </btn-download>
</template>
<script>
import BtnDownload from '@components/common/button/BtnDownload.vue';

export default {
  components: { BtnDownload },
  props: {
    url: {
      type: String,
      required: true,
    },
    text: {
      type: String,
      default: null,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: 'icon',
    },
  },
  methods: {
    async download() {
      try {
        const params = {};
        const response = await this.axios.get(this.url, {
          params,
          responseType: 'blob',
        });

        const { status, data, headers } = response;

        if (status === 200) {
          const fileType = headers['content-type'] || 'application/octet-stream';

          let fileName = null;
          // Try to extract the filename from the content-disposition header
          const disposition = headers['content-disposition'];
          if (disposition) {
            const match = disposition.match(/filename="?([^"]+)"?/);
            if (match && match[1]) {
              fileName = decodeURIComponent(match[1]);
            } else {
              Error('Filename not found in content-disposition header.');
            }
          } else {
            Error('File content disposition not found. '
                + 'Remedy: update expose_headers in nelmio_cors.yaml');
          }

          const blob = new Blob([data], { type: fileType });
          const link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.download = fileName;
          link.click();
          URL.revokeObjectURL(link.href);
        } else {
          Error(`Failed to download file. Status:${status}`);
        }
      } catch (error) {
        Error(`Error during file download::${error}`);
      }
    },
  },
};
</script>
