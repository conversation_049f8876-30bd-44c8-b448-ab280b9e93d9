<template>
  <btn-confirm
    v-if="visible"
    small
    :confirm-hint-text="text ? text : $t('loyalApp_confirmPaymentAndInvoie')"
    @confirm="confirm"
  />
</template>
<script>
import BtnConfirm from '@components/common/BtnConfirm.vue';

export default {
  components: { BtnConfirm },
  props: {
    url: {
      type: String,
      required: true,
    },
    text: {
      type: String,
      default: null,
    },
    visible: {
      type: Boolean,
      default: true,
    },

  },
  methods: {
    async confirm() {
      await this.axios.post(this.url);
      this.$emit('confirmed');
    },
  },
};
</script>
