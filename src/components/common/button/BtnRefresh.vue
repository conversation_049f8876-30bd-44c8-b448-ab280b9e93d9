<template>
  <v-btn
    text
    :x-small="size === 'x-small'"
    :small="size === 'small'"
    :large="size === 'large'"
    :x-large="size === 'x-large'"
    fab
    :color="color"
    class="mt-0"
    :disabled="disabled"
    @click="$emit('click')"
  >
    <v-icon>mdi-cached</v-icon>
  </v-btn>
</template>

<script>
export default {
  name: 'BtnRefresh',
  props: {
    color: {
      type: String,
      default: 'green',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String,
      default: 'x-small',
    },
  },
};
</script>
