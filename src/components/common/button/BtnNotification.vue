<template>
  <v-tooltip bottom>
    <!-- slot activatora -->
    <template #activator="{ on, attrs }">
      <v-btn
        v-bind="attrs"
        x-small
        tile
        rounded
        fab
        elevation="1"
        color="primary"
        class="ml-2 white--text"
        :disabled="disabled"
        :loading="loading"
        v-on="on"
        @click.native="$emit('click')"
      >
        <v-icon>mdi-message-badge-outline</v-icon>
      </v-btn>
    </template>
    <span v-if="text">{{ text }}</span>
  </v-tooltip>
</template>

<script>
export default {
  props: {
    text: {
      type: String,
      default: null,
      nullable: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
};
</script>
