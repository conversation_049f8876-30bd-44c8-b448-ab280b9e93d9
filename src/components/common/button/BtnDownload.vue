<template>
  <v-btn
    v-if="type==='icon'"
    class="ml-2"
    color="primary"
    x-small
    fab
    elevation="1"
    :disabled="disabled"
    @click="$emit('click')"
  >
    <v-icon>mdi-cloud-download</v-icon>
  </v-btn>
  <v-btn
    v-else-if="type==='button'"
    class="ml-2"
    color="primary"
    @click="$emit('click')"
  >
    {{ $t('common_downloadReport') }}
  </v-btn>
</template>

<script>
export default {
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: 'icon',
    },
  },
};
</script>
