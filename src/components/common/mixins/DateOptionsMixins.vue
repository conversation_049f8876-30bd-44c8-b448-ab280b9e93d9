<script>
import {
  endOfMonth,
  endOfToday, endOfYear,
  endOfYesterday,
  startOfDay,
  startOfMonth,
  startOfToday, startOfYear,
  startOfYesterday,
  subDays,
  subMonths, subYears,
} from 'date-fns';

/**
 * @deprecated
 */
export default {
  name: 'DatesOptionsMixins',
  computed: {
    dateItemsOptions() {
      return [
        {
          value: 'today',
          text: this.$t('common_today'),
          from: startOfToday(),
          to: endOfToday(),
        },
        {
          value: 'yesterday',
          text: this.$t('common_daterangeYesterday'),
          from: startOfYesterday(),
          to: endOfYesterday(),
        },
        {
          value: 'last7Days',
          text: this.$t('common_p7d'),
          from: startOfDay(subDays(new Date(), 6)),
          to: endOfToday(),
        },
        {
          value: 'last14Days',
          text: this.$t('common_p14d'),
          from: startOfDay(subDays(new Date(), 13)),
          to: endOfToday(),
          default: true,
        },
        {
          value: 'currentMonth',
          text: this.$t('common_sinceMonthStart'),
          from: startOfMonth(new Date()),
          to: endOfToday(),
        },
        {
          value: 'previousMonth',
          text: this.$t('common_previousMonth'),
          from: startOfMonth(subMonths(new Date(), 1)),
          to: endOfMonth(subMonths(new Date(), 1)),
        },
        {
          value: 'currentYear',
          text: this.$t('common_currentYear'),
          from: startOfYear(new Date()),
          to: endOfToday(),
        },
        {
          value: 'previousYear',
          text: this.$t('common_previousYear'),
          from: startOfYear(subYears(new Date(), 1)),
          to: endOfYear(subYears(new Date(), 1)),
        },
      ];
    },
  },
  methods: {
    getDateItemByValue(value) {
      return this.dateItemsOptions.find((obj) => obj.value === value);
    },
  },
};
</script>
