<script>
/**
 * @deprecated
 */
export default {
  name: 'SalesDocumentMixin',
  methods: {
    getDocumentTypeIcon(type) {
      if (type === 'invoice') {
        return 'mdi-file-document-outline';
      }
      if (type === 'receipt') {
        return 'mdi-receipt-text';
      }
      if (type === 'code') {
        return 'mdi-sale';
      }

      return 'unknown';
    },
    getDocumentTypeName(type) {
      if (type === 'invoice') {
        return this.$t('common_invoice');
      }
      if (type === 'receipt') {
        return this.$t('common_receipt');
      }
      if (type === 'code') {
        return this.$t('common_topupCode');
      }

      return this.$t('transactions.unknown');
    },
  },
};
</script>
