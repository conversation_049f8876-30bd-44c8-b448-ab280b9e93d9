<script>
import { mapGetters } from 'vuex';
/**
 * @deprecated
 */
export default {
  data() {
    return {
      filtering: {
        carwash: null,
        dates: {
          value: null,
          isLastCollection: false,
          from: null,
          to: null,
        },
      },

    };
  },
  computed: {
    filteringOptions() {
      return {
        carwash: {
          carwashes: this.carwashes,
          rollovers: this.portalCarwashes,
          selfService: this.selfServiceCarwashes,
        },
      };
    },
    ...mapGetters({
      carwashes: 'carwashes/carwashes',
      portalCarwashes: 'carwashes/portalCarwashes',
      selfServiceCarwashes: 'carwashes/selfServiceCarwashes',
    }),
  },
  mounted() {
    this.filtering.carwash = null;
    // if (this.carwashes.length > 0) {
    //   this.filtering.carwash = this.carwashes[0].serialNumber;
    // }
  },
  methods: {
    onFiltersChange(filtering) {
      this.filtering = {
        ...this.filtering,
        ...filtering,
      };
    },
  },
};
</script>
