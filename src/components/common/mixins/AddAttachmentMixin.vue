<script>
import Vue from 'vue';
import FileInput from '@components/support/error-report//FileInput.vue';
/**
 * @deprecated
 */
export default {
  name: 'AddAttachmentMixin',
  data() {
    return {
      attachmentProps: {},
      attachments: [],
      attachmentsShow: false,
    };
  },
  computed: {
    files() {
      return this.attachments.map((att) => att.file);
    },
    isAttachmentsAdditionDisabled() {
      return this.attachments.length >= 5;
    },
  },
  methods: {
    onAttachmentAdd() {
      if (this.isAttachmentsAdditionDisabled) {
        return;
      }
      const FileInputComponent = Vue.extend(FileInput);
      const instance = new FileInputComponent({
        parent: this,
        propsData: this.attachmentProps,
      });
      instance.$mount();
      const attachment = {
        id: this.attachments.length,
        component: instance,
        file: null,
      };
      instance.$on('change', (file) => {
        attachment.file = file;
      }).$on('remove', () => {
        this.attachments.splice(this.attachments.findIndex((att) => att.id === attachment.id), 1);
        instance.$el.remove();
        instance.$destroy();
        if (!this.attachments.length) {
          this.attachmentsShow = false;
        }
      });
      this.attachments.push(attachment);
      this.$refs.attachments.appendChild(instance.$el);
      this.attachmentsShow = true;
    },
    appendFilesToForm(formData) {
      this.files.forEach((file) => {
        formData.append('files[]', file);
      });
    },
    clearAttachments() {
      while (this.$refs.attachments.firstChild) {
        this.$refs.attachments.firstChild.remove();
      }
      this.attachments = [];
      this.attachmentsShow = false;
    },
  },
};
</script>
