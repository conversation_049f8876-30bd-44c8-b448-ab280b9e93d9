<script>
/**
 * @deprecated
 */
export default {
  name: 'ModalMixin',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    show: {
      get() {
        return this.value;
      },
      set(value) {
        if (value) {
          this.open();
        } else {
          this.close();
        }
      },
    },
  },
  methods: {
    beforeOpen() {},
    open() {
      this.beforeOpen();
      this.afterOpen();
    },
    afterOpen() {
      this.$emit('open');
      this.$emit('input', true);
    },
    beforeClose() {},
    close() {
      this.beforeClose();
      this.afterClose();
    },
    afterClose() {
      this.$emit('close');
      this.$emit('input', false);
    },
  },
};
</script>
