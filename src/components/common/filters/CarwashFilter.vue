<template>
  <v-card-text class="px-0 pb-0 py-0">
    <v-autocomplete
      key="carwash"
      ref="carwash"
      v-model="currentCarwash"
      :items="carwashesOptions"
      item-value="serialNumber"
      item-text="longName"
      prepend-icon="mdi-car-wash"
      :label="$t('common_filtersCarwash')"
      :disabled="disabled"
      @change="onCarwashChange"
    />
  </v-card-text>
</template>

<script>
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import SettingsMixin from '@components/common/mixins/SettingsMixin.vue';

export default {
  name: 'CarwashFilter',
  components: {
  },
  mixins: [
    FilterMixin,
    SettingsMixin,
  ],
  props: {
    carwashes: {
      type: Array,
      default() {
        return [];
      },
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      current: null,
      filtering: {
        carwash: null,
      },
    };
  },
  computed: {
    currentCarwash: {
      get() {
        return this.filtering.carwash;
      },
      set(value) {
        this.filtering.carwash = value;
      },
    },
    carwashesOptions() {
      const all = {
        serialNumber: null,
        longName: this.$t('common_all'),
      };
      return [all, ...this.carwashes];
    },
  },
  methods: {
    onCarwashChange(carwash) {
      this.$set(this.filtering, 'carwash', carwash);
    },
  },
};
</script>
