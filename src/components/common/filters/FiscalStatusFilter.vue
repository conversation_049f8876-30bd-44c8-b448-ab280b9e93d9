<template>
  <v-card-text class="px-0 pb-0">
    <v-layout
      row
      wrap
      justify-end
    >
      <v-select
        v-model="currentPreset"
        multiple
        :items="fiscalOptions"
        :label="$t('fiscal_transactions.table.fiscal')"
        prepend-icon="mdi-flag-variant"
        :disabled="disabled"
        @change="onFiscalChange"
      >
        <template
          #item="{item, attrs, on}"
        >
          <v-list-item
            :disabled="disabled"
            v-bind="attrs"
            v-on="on"
          >
            <v-list-item-action>
              <v-checkbox
                :input-value="attrs.inputValue"
                :disabled="item.disabled"
              />
            </v-list-item-action>
            <v-list-item-content>
              <v-list-item-title>
                <v-row
                  no-gutters
                  align="center"
                >
                  <span>{{ item }}</span>
                </v-row>
              </v-list-item-title>
            </v-list-item-content>
          </v-list-item>
        </template>
      </v-select>
    </v-layout>
  </v-card-text>
</template>

<script>
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import SettingsMixin from '@components/common/mixins/SettingsMixin.vue';

export default {
  name: 'FiscalStatusFilter',
  mixins: [
    FilterMixin,
    SettingsMixin,
  ],
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      startPreset: 'fiscalized',
      settingsNamespace: 'finance:fiscal:status',
      filtering: {
        fiscal: 'fiscalized',
      },
      fiscalOptions: [
        'none',
        'transaction',
        'fiscalized',
        'test',
        'not_fiscalized',
        'error',
      ],
    };
  },
  computed: {
    currentPreset: {
      get() {
        if (this.settingsNamespace !== null) {
          const sets = this.getSetts(this.settingsNamespace, this.startPreset);

          if (sets !== null && sets !== '') {
            return sets.split(',');
          }

          return null;
        }

        return this.current;
      },
      set(value) {
        let tmpValue = '';

        if (value !== null) {
          tmpValue = value.join(',');
        }

        this.setSetts(this.settingsNamespace, tmpValue);

        this.current = value;
      },
    },
    carwashesOptions() {
      const carwashesOptions = [];
      carwashesOptions.push({
        serialNumber: null,
        longName: this.$t('common_all'),
      });
      carwashesOptions.push(...this.carwashes);
      return carwashesOptions;
    },
  },
  mounted() {
    // this.$set(this.filtering, 'fiscal', this.filtering.fiscal);
    this.onFiscalChange(this.currentPreset);
  },
  methods: {
    onFiscalChange(fiscal) {
      this.$emit('onChangeFiscal', fiscal.join(','));
      this.$set(this.filtering, 'fiscal', fiscal.join(','));
    },
  },
};
</script>
