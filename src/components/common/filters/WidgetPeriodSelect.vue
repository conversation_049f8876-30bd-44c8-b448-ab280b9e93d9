<template>
  <v-menu offset-y>
    <template #activator="{ on, attrs }">
      <v-btn
        color="white"
        text
        v-bind="attrs"
        v-on="on"
      >
        {{ value.text }}
        <i class="mdi-navigation-expand-more" />
      </v-btn>
    </template>
    <v-list>
      <v-list-item
        v-for="dateItem in dateItems"
        :key="dateItem.id"
        link
        @click="$emit('input', dateItem)"
      >
        <v-list-item-title>{{ dateItem.text }}</v-list-item-title>
      </v-list-item>
    </v-list>
  </v-menu>
</template>

<script>
import {
  endOfToday,
  startOfDay,
  subDays,
} from 'date-fns';
import DateOptionsMixins from '@components/common/mixins/DateOptionsMixins.vue';

export default {
  name: 'WidgetPeriodSelect',
  mixins: [
    DateOptionsMixins,
  ],
  props: {
    value: {
      type: Object,
      default() {
        return {
          value: 'last7Days',
          text: this.$t('common_p7d'),
          from: startOfDay(subDays(new Date(), 6)),
          to: endOfToday(),
        };
      },
    },
    dateItems: {
      type: Array,
      default() {
        return this.dateItemsOptions;
      },
    },
  },
};
</script>
