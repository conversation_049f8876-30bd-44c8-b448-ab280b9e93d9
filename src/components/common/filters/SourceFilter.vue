<template>
  <multiselect
    :items="getSources"
    :disabled="disabled"
    prepend-icon="mdi-point-of-sale"
    :label="$t('common_deviceType')"
    @input="onButtonClick"
  />
</template>

<script>
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import Multiselect from './Multiselect.vue';

export default {
  name: 'SourceFilter',
  components: { Multiselect },
  mixins: [
    FilterMixin,
  ],
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      sources: [
        {
          value: 'CAR_WASH',
          icon: 'mdi-car-wash',
          active: true,
        },
        {
          value: 'VACUUM_CLEANER',
          icon: 'mdi-auto-fix',
          active: true,
        },
        {
          value: 'DISTRIBUTOR',
          icon: 'mdi-cup-water',
          active: true,
        },
        {
          value: 'MONEY_CHANGER',
          icon: 'mdi-sync',
          active: true,
        },
      ],
    };
  },
  computed: {
    getSources() {
      return this.sources.map((source) => ({
        ...source,
        text: source.text ?? this.$t(`fiscal_transactions.source.${source.value}`),
      }));
    },
  },
  methods: {
    onButtonClick(sourcesActive) {
      this.$set(this.filtering, 'sources', sourcesActive.length ? sourcesActive.join(',') : null);
    },
  },
};
</script>
