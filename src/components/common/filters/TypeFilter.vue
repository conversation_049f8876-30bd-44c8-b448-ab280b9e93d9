<template>
  <v-card-text class="px-0 pb-0">
    <v-layout
      row
      wrap
      justify-end
    >
      <v-select
        v-model="filtering.grouping"
        class="d-flex"
        prepend-icon="mdi-clipboard-list-outline"
        :items="groupingOptions"
        :label="$t('common_type')"
        :disabled="disabled"
        @change="onGroupingChange"
      />
    </v-layout>
  </v-card-text>
</template>

<script>
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';

export default {
  name: 'TypeFilter',
  mixins: [
    FilterMixin,
  ],
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      filtering: {
        grouping: 'daily',
      },
    };
  },
  computed: {
    groupingOptions() {
      return [
        {
          value: 'daily',
          text: this.$t('common_daily'),
        },
        {
          value: 'monthly',
          text: this.$t('common_monthly'),
        },
      ];
    },
  },
  mounted() {
    this.$set(this.filtering, 'grouping', this.filtering.grouping);
  },
  methods: {
    onGroupingChange(groupType) {
      const [grouping] = this.groupingOptions.filter((s) => s.value === groupType);
      this.$set(this.filtering, 'grouping', grouping.value);
    },
  },
};
</script>
