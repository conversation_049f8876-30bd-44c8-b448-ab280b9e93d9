<template>
  <div class="alert-container">
    <v-alert
      v-for="(alert, index) in alerts"
      :key="`dashboard-alert-${index}`"
      :value="alert.value"
      dense
      prominent
      border="left"
      text
      :dismissible="dismissible"
      :type="alert.level"
      :color="`${alert.level}`"
    >
      <div class="d-flex justify-space-between flex-row strec">
        <div class="d-flex flex-column justify-center">
          <span class="text-h7 font-weight-medium">{{ alert.title }}</span>
          <template v-if="alert.text">
            <span class="text-subtitle-1">{{ alert.text }}</span>
          </template>
        </div>
        <div
          v-if="alert.link || (alert.apiCall && alert.apiCallMethod)"
          class="flex-center"
        >
          <v-btn
            color="primary"
            small
            dense
            :disabled="disableLinks"
            @click="click(alert)"
          >
            {{ alert.linkText }}
          </v-btn>
        </div>
      </div>
    </v-alert>
  </div>
</template>

<script>
import Vue from 'vue';

export default {
  name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  props: {
    show: {
      type: Boolean,
      default: true,
    },
    alerts: {
      type: [Object, Array],
      required: true,
    },
    disableLinks: {
      type: Boolean,
      default: false,
    },
    dismissible: {
      type: Boolean,
      default: true,
    },
  },
  methods: {
    goToUrl(link) {
      window.location.href = link;
    },
    click(alert) {
      if (alert.apiCall && alert.apiCallMethod) {
        if (alert.apiCallMethod === 'POST') {
          Vue.axios.post(
            alert.apiCall,
          )
            .then(() => {
              this.snackbar.showMessage(
                'success',
                this.$t('common_success'),
              );
              this.$emit('refresh');
            }).catch(() => {
              this.snackbar.showMessage(
                'error',
                this.$t('common_errorHeader'),
              );
            });
        }

        if (alert.apiCallMethod === 'GET') {
          Vue.axios.get(
            alert.apiCall,
          )
            .then(() => {
              this.snackbar.showMessage(
                'success',
                this.$t('common_success'),
              );
              this.$emit('refresh');
            }).catch(() => {
              this.snackbar.showMessage(
                'error',
                this.$t('common_errorHeader'),
              );
            });
        }
        return;
      }

      const target = alert.link;
      if (target.startsWith('https://')) {
        this.goToUrl(target);
      }

      if (target.startsWith('#/')) {
        this.$router.push({ name: target.substring(2).replace('/', '_') });
      }
    },
  },
};
</script>

<style lang="css" scoped>
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
