<template>
  <div>
    <v-chart
      ref="chart"
      class="chart"
      :option="option"
      autoresize
      :loading="loader"
      :loading-options="{
        text: $t('common_loading'),
        color: '#48a7f2',
        lineWidth: 2,
      }"
      @legendselectchanged="(selected) => $emit('legendselectchanged', selected)"
    />
  </div>
</template>

<script>
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { BarChart, LineChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
} from 'echarts/components';
import VChart from 'vue-echarts';

use([
  CanvasRenderer,
  BarChart,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
]);

export default {
  name: 'DualAxisChart',
  components: {
    VChart,
  },
  props: {
    loader: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
    titleAlign: {
      type: String,
      default: 'left',
    },
    legend: {
      type: Array,
      default() {
        return [];
      },
    },
    legendOrientation: {
      type: String,
      default: 'horizontal',
    },
    legendAlign: {
      type: String,
      default: 'center',
    },
    legendVerticalAlign: {
      type: String,
      default: '0',
    },
    xData: {
      type: Array,
      required: true,
    },
    barSeriesValues: {
      type: Array,
      default() {
        return [];
      },
    },
    lineSeriesValues: {
      type: Array,
      default() {
        return [];
      },
    },
    currencyUnit: {
      type: String,
      default: '',
    },
    barMaxWidth: {
      type: String,
      default: '90',
    },
    selectedLegendOptions: {
      type: Object,
      default: null,
    },
    leftAxisName: {
      type: String,
      default: '',
    },
    rightAxisName: {
      type: String,
      default: '',
    },
  },
  computed: {
    legendVerticalAlignWithTitle() {
      if (this.title.length) {
        return (parseInt(this.legendVerticalAlign, 10) + 50).toString();
      }
      return this.legendVerticalAlign;
    },
    option() {
      return {
        title: {
          text: this.title,
          left: this.titleAlign,
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
          },
          formatter: (params) => {
            let message = '';
            message += `<strong>${params[0].axisValue}</strong><br/>`;

            // Grupuj parametry według indeksu osi Y
            const leftAxisParams = params.filter((param) => {
              // Wykres słupkowy zawsze na lewej osi
              if (param.seriesType === 'bar') {
                return true;
              }

              // Linia z yAxisIndex=0 (lewa oś)
              if (param.seriesType === 'line' && param.seriesIndex >= this.barSeriesValues.length) {
                const lineIndex = param.seriesIndex - this.barSeriesValues.length;
                if (lineIndex < this.lineSeriesValues.length) {
                  return this.lineSeriesValues[lineIndex].yAxisIndex === 0;
                }
              }

              return false;
            });

            const rightAxisParams = params.filter((param) => {
              // Tylko linie mogą być na prawej osi
              if (param.seriesType !== 'line') {
                return false;
              }

              // Sprawdź, czy to linia z yAxisIndex=1 lub undefined
              if (param.seriesIndex >= this.barSeriesValues.length) {
                const lineIndex = param.seriesIndex - this.barSeriesValues.length;
                if (lineIndex < this.lineSeriesValues.length) {
                  const { yAxisIndex } = this.lineSeriesValues[lineIndex];
                  return yAxisIndex === 1 || yAxisIndex === undefined;
                }
              }

              return false;
            });

            if (leftAxisParams.length > 0) {
              message += '<div style="margin-bottom: 5px;">';
              leftAxisParams.forEach((param) => {
                message += `${param.marker}${param.seriesName}: ${param.value}${this.currencyUnit}<br/>`;
              });
              message += '</div>';
            }

            if (rightAxisParams.length > 0) {
              message += '<div>';
              rightAxisParams.forEach((param) => {
                message += `${param.marker}${param.seriesName}: ${param.value}<br/>`;
              });
              message += '</div>';
            }

            return message;
          },
        },
        legend: {
          orient: this.legendOrientation,
          left: this.legendAlign,
          top: this.legendVerticalAlignWithTitle,
          data: this.legend.map((item) => item.name),
          selected: this.selectedLegendOptions,
          padding: [5, 10, 15, 10],
          itemGap: 20,
          itemWidth: 25,
          itemHeight: 14,
          textStyle: {
            fontSize: 12,
          },
          formatter: (name) => name,
        },
        grid: {
          left: '3%',
          right: '8%',
          bottom: '3%',
          top: '25%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: this.xData,
        },
        yAxis: [
          {
            type: 'value',
            name: this.leftAxisName,
            position: 'left',
            axisLabel: {
              formatter: `{value}${this.currencyUnit}`,
            },
          },
          {
            type: 'value',
            name: this.rightAxisName,
            position: 'right',
            axisLabel: {
              formatter: '{value}',
            },
          },
        ],
        series: [
          ...this.barSeriesValues.map((series) => ({
            name: series.name,
            type: 'bar',
            stack: 'total',
            data: series.data,
            barMaxWidth: this.barMaxWidth,
            itemStyle: {
              color: series.color,
            },
            emphasis: {
              focus: 'series',
            },
          })),
          ...this.lineSeriesValues.map((series) => ({
            name: series.name,
            type: 'line',
            yAxisIndex: series.yAxisIndex !== undefined ? series.yAxisIndex : 1,
            data: series.data,
            symbol: 'circle',
            symbolSize: 8,
            itemStyle: {
              color: series.color,
            },
            emphasis: {
              focus: 'series',
            },
          })),
        ],
      };
    },
  },
};
</script>

<style scoped>
.chart {
  width: 100%;
  height: 35vh;
}
</style>
