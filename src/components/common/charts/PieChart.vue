<script>
import { use } from 'echarts/core';
import { <PERSON>vasRenderer } from 'echarts/renderers';
import { <PERSON><PERSON><PERSON> } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
} from 'echarts/components';
import ChartMixin from '@components/common/mixins/ChartMixin.vue';

use([
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
]);

export default {
  name: 'Pie<PERSON>hart',
  mixins: [
    ChartMixin,
  ],
  props: {
    legendOrientation: {
      type: String,
      default: 'vertical',
    },
    legendAlign: {
      type: String,
      default: 'left',
    },
    legendVerticalAlign: {
      type: String,
      default: 'top',
    },
    name: {
      type: String,
      default: 'A series of data',
    },
    values: {
      type: Array,
      default() {
        return [
          { value: 335, name: 'Data type 1' },
          { value: 310, name: 'Data type 2' },
          { value: 234, name: 'Data type 3' },
          { value: 135, name: 'Data type 4' },
          { value: 1548, name: 'Data type 5' },
        ];
      },
    },
    emphasizedItemStyle: {
      type: Object,
      default() {
        return {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)',
        };
      },
    },
    unit: {
      type: String,
      default: '',
    },
    tooltip: {
      type: Object,
      default() {
        return {
          trigger: 'item',
          formatter: (param) => `<strong>${param.name}</strong><br/>${param.value}${this.unit} (${param.percent}%)`,
        };
      },
    },
  },
  computed: {
    centerYAlign() {
      if (this.title.length) {
        return '55%';
      }
      return '50%';
    },
    option() {
      return {
        title: {
          text: this.title,
          left: this.titleAlign,
        },
        tooltip: this.tooltip,
        legend: {
          orient: this.legendOrientation,
          left: this.legendAlign,
          data: this.legend,
        },
        series: [{
          name: this.name,
          type: 'pie',
          radius: '70%',
          center: ['50%', this.centerYAlign],
          data: this.values,
          emphasis: {
            itemStyle: this.emphasizedItemStyle,
          },
          label: {
            position: 'outer',
            alignTo: 'labelLine',
            formatter: (param) => `${param.name}\n(${param.percent}%)`,
            minMargin: 10,
            lineHeight: 15,
            fontSize: 10,
          },
          animationDelay(idx) {
            return idx * 10;
          },
        }],
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelayUpdate(idx) {
          return idx * 8 + 300;
        },
      };
    },
  },
};
</script>

<style scoped>
.chart {
  height: 30vh;
}
</style>
