<script>
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { HeatmapChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  VisualMapComponent,
} from 'echarts/components';
import ChartMixin from '@components/common/mixins/ChartMixin.vue';

use([
  Canvas<PERSON>enderer,
  HeatmapChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
  VisualMapComponent,
]);
export default {
  name: 'HeatmapChart',
  mixins: [
    ChartMixin,
  ],
  props: {
    xAxisData: {
      type: Array,
      required: true,
    },
    yAxisData: {
      type: Array,
      required: true,
    },
    data: {
      type: Array,
      required: true,
    },
    maxValue: {
      type: Number,
      required: true,
    },
  },
  computed: {
    gridHeight() {
      const heightPerOneRow = 25;

      return heightPerOneRow * this.yAxisData.length;
    },
    option() {
      return {
        tooltip: {
          position: 'top',
        },
        grid: {
          top: 0,
          height: this.gridHeight,
        },
        xAxis: {
          type: 'category',
          data: this.xAxisData,
          splitArea: {
            show: true,
          },
        },
        yAxis: {
          type: 'category',
          data: this.yAxisData,
          splitArea: {
            show: true,
          },
        },
        visualMap: {
          type: 'piecewise',
          min: 0,
          max: this.maxValue,
          show: false,
          color: ['#373e4b', '#6e7d96', '#d3d8df'],
        },
        series: [
          {
            type: 'heatmap',
            data: this.data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
          },
        ],
      };
    },
  },
};
</script>

<style scoped>
.chart {
  width: 100%;
  height: 150px;
}
</style>
