<script>
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { <PERSON><PERSON><PERSON>, LineChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
} from 'echarts/components';
import ChartMixin from '@components/common/mixins/ChartMixin.vue';

use([
  Canvas<PERSON>enderer,
  <PERSON><PERSON>hart,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
]);
export default {
  name: 'StackBarWithLineChart',
  mixins: [
    ChartMixin,
  ],
  props: {
    gridLeft: {
      type: String,
      default: '85',
    },
    gridRight: {
      type: String,
      default: '50',
    },
    gridTop: {
      type: String,
      default: '50',
    },
    gridBottom: {
      type: String,
      default: '30',
    },
    xData: {
      type: Array,
      required: true,
    },
    axisSumLabel: {
      type: Boolean,
      default: true,
    },
    unit: {
      type: String,
      default: '',
    },
    barMaxWidth: {
      type: String,
      default: '90%',
    },
    xAxisName: {
      type: String,
      default: '',
    },
    yAxisName: {
      type: String,
      default: '',
    },
    xAxisFormatter: {
      type: [String, Function],
      default: '{value}',
    },
    yAxisFormatter: {
      type: [String, Function],
      default: '{value}',
    },
    emphasisFocus: {
      type: String,
      default: 'series',
    },
    chartTooltipDisabled: {
      type: Boolean,
      default: false,
    },
    seriesTooltipDisabled: {
      type: Boolean,
      default: false,
    },
    barSeriesValues: {
      type: Array,
      default() {
        return [
          {
            data: [10, 30, 70, 77, 90, 110, 150],
            name: 'Data series 1',
          },
          {
            data: [120, 200, 150, 80, 70, 110, 130],
            name: 'Data series 2',
          },
        ];
      },
    },
    lineSeriesValues: {
      type: Array,
      default() {
        return [];
      },
    },
    selectedLegendOptions: {
      type: Object,
      default: null,
    },
  },
  computed: {
    gridTopWithTitle() {
      if (this.title.length) {
        return (parseInt(this.gridTop, 10) + 70).toString();
      }
      return this.gridTop;
    },
    legendVerticalAlignWithTitle() {
      if (this.title.length) {
        return (parseInt(this.legendVerticalAlign, 10) + 50).toString();
      }
      return this.legendVerticalAlign + 10;
    },
    option() {
      return {
        title: {
          text: this.title,
          left: this.titleAlign,
        },
        tooltip: {
          show: !this.chartTooltipDisabled,
          trigger: 'axis',
          formatter: (params) => {
            let message = '';
            message += `<strong>${params[0].axisValue}</strong>`;
            message += `<br/>${params[0].axisValueLabel}`;
            params.forEach((param) => {
              if (param.seriesType === 'line') {
                message += `<hr/>${param.marker}${param.seriesName}: ${param.value}${this.unit}`;
              } else {
                message += `<br/>${param.marker}${param.seriesName}: ${param.value}${this.unit}`;
              }
            });

            return message;
          },
          axisPointer: {
            type: 'shadow',
            label: {
              show: this.axisSumLabel,
              formatter: (params) => {
                const seriesSum = Math.round(this.$options.filters.sumProperty(
                  params.seriesData.filter((item) => item.seriesType !== 'line'),
                  'data',
                ) * 100 + Number.EPSILON) / 100;
                return `${this.$t('turnover.table.sum')}: ${seriesSum}${this.unit}`;
              },
            },
          },
        },
        legend: {
          orient: this.legendOrientation,
          left: this.legendAlign,
          top: this.legendVerticalAlignWithTitle,
          data: this.legend,
          selected: this.selectedLegendOptions,
        },
        xAxis: {
          type: 'category',
          data: this.xData,
          name: this.xAxisName,
          axisLabel: {
            formatter: this.xAxisFormatter,
          },
        },
        yAxis: {
          type: 'value',
          name: this.yAxisName,
          axisLabel: {
            formatter: this.yAxisFormatter,
          },
        },
        grid: {
          left: this.gridLeft,
          right: this.gridRight,
          top: this.gridTopWithTitle,
          bottom: this.gridBottom,
        },
        series: [
          ...this.barSeriesValues.map((series) => ({
            data: series.data,
            name: series.name,
            type: 'bar',
            stack: 'total',
            barMaxWidth: this.barMaxWidth,
            tooltip: {
              show: !this.seriesTooltipDisabled,
            },
            itemStyle: {
              color: series.color,
            },
            emphasis: {
              focus: this.emphasisFocus,
            },
            animationDelay(idx) {
              return idx * 5 + 200;
            },
            labelLine: {
              show: true,
            },
          })),
          ...this.lineSeriesValues.map((series) => ({
            data: series.data,
            name: series.name,
            type: 'line',
            tooltip: {
              show: !this.seriesTooltipDisabled,
            },
            itemStyle: {
              color: series.color,
            },
            emphasis: {
              focus: this.emphasisFocus,
            },
            animationDelay(idx) {
              return idx * 5 + 200;
            },
            labelLine: {
              show: true,
            },
          })),
        ],
        animationEasing: 'elasticOut',
        animationDelayUpdate(idx) {
          return idx * 2 + 300;
        },
      };
    },
  },
};
</script>

<style scoped>
.chart {
  width: 100%;
  height: 30vh;
}
</style>
