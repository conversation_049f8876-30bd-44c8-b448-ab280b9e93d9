<script>
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import { Line<PERSON>hart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
} from 'echarts/components';
import ChartMixin from '@components/common/mixins/ChartMixin.vue';

use([
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  LineChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
]);
export default {
  mixins: {
    ChartMixin,
  },
  props: {
    xData: {
      type: Array,
      required: true,
    },
    seriesValues: {
      type: Array,
      default() {
        return [
          {
            data: [10, 30, 70, 77, 90, 110, 150],
            name: 'Data series 1',
          },
          {
            data: [120, 200, 150, 80, 70, 110, 130],
            name: 'Data series 2',
          },
        ];
      },
    },
  },
  computed: {
    option() {
      return {
        title: {
          text: this.title,
          left: this.titleAlign,
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        legend: {
          orient: this.legendOrientation,
          left: this.legendAlign,
          data: this.legend,
        },
        xAxis: {
          type: 'category',
          data: this.xData,
        },
        yAxis: {
          type: 'value',
        },
        grid: {
          left: 50,
          right: 0,
          bottom: 20,
        },
        series: this.seriesValues.map((series) => ({
          data: series.data,
          name: series.name,
          type: 'line',
          symbol: 'circle',
          symbolSize: 7,
          lineStyle: {
            type: 'dashed',
            width: 1,
          },
          emphasis: {
            focus: 'series',
          },
        })),
      };
    },
  },
};
</script>

<style scoped>
.chart {
  height: 40vh;
}
</style>
