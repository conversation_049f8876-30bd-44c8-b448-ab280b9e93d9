<template>
  <div>
    <btn-delete
      :disabled="disabled"
      @click="openDialog()"
    />
    <generic-modal
      v-model="dialog"
      :title="text"
    >
      <template #actions>
        <v-spacer />
        <v-btn
          text
          color="primary"
          @click="dialog = false"
        >
          {{ $t('actions.cancel') }}
        </v-btn>
        <v-btn
          color="red"
          class="white--text text-center"
          @click="deleteAndCloseDialog()"
        >
          {{ $t('actions.delete') }}
        </v-btn>
      </template>
    </generic-modal>
  </div>
</template>

<script>
import BtnDelete from '@components/common/button/BtnDelete.vue';
import GenericModal from '@components/common/GenericModal.vue';

export default {
  components: {
    GenericModal,
    BtnDelete,
  },
  props: {
    url: {
      type: String,
      required: true,
    },
    text: {
      type: String,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialog: false,
    };
  },
  methods: {
    openDialog() {
      this.dialog = true;
    },
    deleteAndCloseDialog() {
      this.deleteAction();
      this.dialog = false;
    },
    deleteAction() {
      this.axios.delete(
        this.url,
      )
        .then((response) => {
          if (response.status === 200) {
            this.snackbar.showMessage(
              'success',
              this.$t('common_success'),
            );
            this.$emit('success');
          }
        })
        .catch(() => {
          this.snackbar.showMessage(
            'error',
            this.$t('common_errorHeader'),
          );
          this.$emit('error');
        });
    },
  },
};
</script>
