<template>
  <v-menu offset-y>
    <template #activator="{ on, attrs }">
      <v-btn
        v-bind="attrs"
        transition="slide-y-transition"
        :class="`expand-menu ${btnClass}`"
        color="secondary"
        small
        :disabled="disabled"
        style="z-index: 2"
        v-on="on"
      >
        {{ $t('actions.export') }}
        <v-icon right>
          mdi-menu-down
        </v-icon>
      </v-btn>
    </template>
    <v-list>
      <v-list-item
        v-for="(exportItem, index) in items"
        :key="index"
        @click="$emit('click', exportItem)"
      >
        <v-list-item-title>
          {{ $t(exportItem.title) }}
        </v-list-item-title>
      </v-list-item>
    </v-list>
  </v-menu>
</template>

<script>
export default {
  name: 'ExportList',
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    items: {
      type: Array,
      required: true,
    },
    btnClass: {
      type: String,
      default: '',
    },
  },
};
</script>
