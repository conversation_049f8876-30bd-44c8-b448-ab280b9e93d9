<template>
  <div>
    <v-tooltip
      v-if="!confirmed"
      bottom
    >
      <template #activator="{ on, attrs }">
        <v-btn
          key="btnConfirm"
          block
          v-bind="attrs"
          color="warning"
          elevation="0"
          outlined
          :small="small"
          :x-small="xSmall"
          v-on="on"
        >
          <template
            v-if="xSmall"
            #loader
          >
            <v-progress-circular
              indeterminate
              size="12"
              width="2"
              color="white"
            />
          </template>
          <span>
            {{ $t('common_notConfirmed') }}
          </span>
        </v-btn>
      </template>
      <span>
        {{ $t('common_notConfirmed') }}
      </span>
    </v-tooltip>
    <v-tooltip
      v-else
      bottom
    >
      <template #activator="{ on, attrs }">
        <v-btn
          block
          v-bind="attrs"
          color="green"
          outlined
          elevation="0"
          :small="small"
          :x-small="xSmall"
          v-on="on"
        >
          {{ $t('common_confirmed') }}
        </v-btn>
      </template>
      <span>
        {{ $t('common_invoiceConfirmed') }} {{ confirmDate|formatDateDayTime }}
        {{ $t('common_by') }} {{ confirmUser }}
      </span>
    </v-tooltip>
  </div>
</template>
<script>
export default {
  name: 'ConfirmInfo',
  props: {
    tile: {
      type: Boolean,
      default: false,
    },
    small: {
      type: Boolean,
      default: false,
    },
    xSmall: {
      type: Boolean,
      default: false,
    },
    confirmed: {
      type: Boolean,
      default: false,
    },
    confirmDate: {
      type: [String, Object],
      default: null,
    },
    confirmUser: {
      type: [String, Object],
      default: null,
    },
    confirmHintText: {
      type: [String, Object],
      default: null,
    },
  },
};
</script>
