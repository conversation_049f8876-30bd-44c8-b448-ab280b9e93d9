<template>
  <div class="alert-container">
    <v-alert
      class="mb-0"
      transition="scroll-y-transition"
      border="left"
      text
      :value="show"
      :type="type"
      :dismissible="dismissible"
      @input="onInput"
    >
      {{ message }}
    </v-alert>
  </div>
</template>

<script>
export default {
  name: 'BkfAlert',
  props: {
    show: {
      type: Boolean,
      default: true,
    },
    message: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      default: 'success',
    },
    dismissible: {
      type: Boolean,
      default: true,
    },
  },
  methods: {
    onInput(input) {
      this.$emit('update:show', input);
    },
  },
};
</script>
