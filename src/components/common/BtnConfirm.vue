<template>
  <div>
    <v-tooltip
      v-if="!confirmed"
      bottom
    >
      <template #activator="{ on, attrs }">
        <v-btn
          key="btnConfirm"
          block
          v-bind="attrs"
          color="warning"
          elevation="1"
          :small="small"
          :x-small="xSmall"
          :fab="tile"
          :tile="tile"
          :href="href"
          :loading="loading"
          v-on="on"
          @click="showDialog()"
        >
          <template
            v-if="xSmall && !tile"
            #loader
          >
            <v-progress-circular
              indeterminate
              size="12"
              width="2"
              color="white"
            />
          </template>
          <v-icon
            v-if="tile"
          >
            mdi-checkbox-blank-outline
          </v-icon>
          <span v-else>
            {{ $t('common_confirm') }}
          </span>
        </v-btn>
      </template>
      <span>
        {{ $t('common_clickToConfirm') }}
      </span>
    </v-tooltip>
    <v-tooltip
      v-else
      bottom
    >
      <template #activator="{ on, attrs }">
        <v-btn
          block
          v-bind="attrs"
          color="green"
          outlined
          elevation="0"
          :small="small"
          :x-small="xSmall"
          :fab="tile"
          :tile="tile"
          :loading="loading"
          v-on="on"
        >
          {{ $t('common_confirmed') }}
        </v-btn>
      </template>
      <span>
        {{ $t('common_invoiceConfirmed') }} {{ confirmDate|formatDateDayTime }}
        {{ $t('common_by') }} {{ confirmUser }}
      </span>
    </v-tooltip>
    <v-dialog
      v-model="dialog"
      width="auto"
      style="z-index: 1300"
    >
      <v-card>
        <v-card-text class="pt-8 card-text-wrap">
          <div class="text-center">
            <v-icon
              large
              color="warning"
              class="pb-4"
            >
              mdi-alert
            </v-icon>
            <h3>{{ confirmHintText }}</h3>
          </div>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            text
            color="primary"
            @click="hideDialog()"
          >
            {{ $t('actions.cancel') }}
          </v-btn>
          <v-btn
            color="warning"
            class="white--text text-center"
            @click="confirm()"
          >
            {{ $t('common_confirm') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
export default {
  name: 'BtnConfirm',
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    tile: {
      type: Boolean,
      default: false,
    },
    small: {
      type: Boolean,
      default: false,
    },
    xSmall: {
      type: Boolean,
      default: false,
    },
    href: {
      type: [String, Object],
      default: null,
    },
    confirmed: {
      type: Boolean,
      default: false,
    },
    confirmDate: {
      type: [String, Object],
      default: null,
    },
    confirmUser: {
      type: [String, Object],
      default: null,
    },
    confirmHintText: {
      type: [String, Object],
      default: null,
    },
  },
  data() {
    return {
      dialog: false,
    };
  },
  methods: {
    showDialog() {
      this.dialog = true;
    },
    hideDialog() {
      this.dialog = false;
    },
    confirm() {
      this.$emit('confirm');
      this.hideDialog();
    },
  },
};
</script>
