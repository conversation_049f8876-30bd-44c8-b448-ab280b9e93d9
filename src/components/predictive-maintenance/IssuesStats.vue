<template>
  <v-container>
    <v-row>
      <v-col
        cols="5"
      >
        <v-card
          height="100%"
        >
          <v-row no-gutters>
            <v-col>
              <v-card-title>
                <PERSON><PERSON><PERSON><PERSON> wszystkich problemów
              </v-card-title>
            </v-col>
            <v-col
              cols="1"
              class="mr-4"
            >
              <v-avatar
                size="40"
                class="rounded-full transparent-with-border mt-2 text-h6"
              >
                {{ stats.issuesTotal }}
              </v-avatar>
            </v-col>
          </v-row>
          <v-divider
            class="mb-4"
          />
          <v-row no-gutters>
            <v-col
              v-if="stats.incidentsTotal > 0"
              class="d-flex flex-column align-center"
            >
              <v-tooltip
                top
              >
                <template
                  #activator="{ on, attrs }"
                >
                  <v-icon
                    v-bind="attrs"
                    size="35"
                    class="cursor-pointer"
                    v-on="on"
                    @click="onIncidentsClick"
                  >
                    mdi-email-alert
                  </v-icon>
                </template>
                <span>Zgłoszenia</span>
              </v-tooltip>

              <v-avatar
                size="22"
                class="rounded-full transparent-with-border mt-3 mb-3"
                :class="{ 'selected-background': filterIncidents }"
              >
                {{ stats.incidentsTotal }}
              </v-avatar>
            </v-col>
            <v-col
              v-for="item in stats.levels"
              :key="item.id"
              class="d-flex flex-column align-center"
            >
              <v-tooltip
                top
              >
                <template
                  #activator="{ on, attrs }"
                >
                  <v-icon
                    v-bind="attrs"
                    size="35"
                    class="cursor-pointer"
                    :color="alarmLevels[item.name].color"
                    v-on="on"
                    @click="onAlarmLevelClick(item.name)"
                  >
                    {{ alarmLevels[item.name].icon }}
                  </v-icon>
                </template>
                <span>{{ item.name }}</span>
              </v-tooltip>

              <v-avatar
                size="22"
                class="rounded-full transparent-with-border mt-3 mb-3"
                :class="{ 'selected-background': selectedAlarmLevels.includes(item.name) }"
              >
                {{ item.count }}
              </v-avatar>
            </v-col>
          </v-row>
        </v-card>
      </v-col>
      <v-col>
        <v-card
          height="100%"
        >
          <v-row no-gutters>
            <v-col>
              <v-card-title>
                Ilość myjni z problemami
              </v-card-title>
            </v-col>
            <v-col
              cols="1"
              class="mr-4"
            >
              <v-avatar
                size="40"
                class="rounded-full transparent-with-border mt-2 text-h6"
              >
                {{ stats.carwashTotal }}
              </v-avatar>
            </v-col>
          </v-row>
          <v-divider
            class="mb-4"
          />
          <v-row no-gutters>
            <v-col
              v-for="item in stats.owners"
              :key="item.id"
              class="d-flex flex-column align-center"
            >
              <v-tooltip
                top
              >
                <template
                  #activator="{ on, attrs }"
                >
                  <img
                    v-if="item.logo"
                    v-bind="attrs"
                    :src="item.logo"
                    alt="Image"
                    style="max-height: 35px; width: auto;"
                    class="cursor-pointer"
                    v-on="on"
                    @click="onOwnerClick(item.id)"
                  >
                  <v-icon
                    v-else
                    v-bind="attrs"
                    size="35"
                    class="cursor-pointer"
                    v-on="on"
                    @click="onOwnerClick(item.id)"
                  >
                    mdi-help
                  </v-icon>
                </template>
                <span>{{ item.name }}</span>
              </v-tooltip>

              <v-avatar
                size="22"
                class="rounded-full transparent-with-border mt-3 mb-3"
                :class="{ 'selected-background': selectedOwners.includes(item.id) }"
              >
                {{ item.count }}
              </v-avatar>
            </v-col>
          </v-row>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import alarmLevels from './AlarmLevels';

export default {
  name: 'IssuesStats',
  props: {
    stats: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    selectedAlarmLevels: {
      type: Array,
      required: true,
      default: () => ([]),
    },
    selectedOwners: {
      type: Array,
      required: true,
      default: () => ([]),
    },
    filterIncidents: {
      type: Boolean,
      required: true,
      default: false,
    },
  },

  data() {
    return {
      alarmLevels,
    };
  },

  mounted() {

  },

  methods: {
    onAlarmLevelClick(name) {
      this.$emit('selected-alarm-levels-changed', name);
    },
    onOwnerClick(id) {
      this.$emit('selected-owners-changed', id);
    },
    onIncidentsClick() {
      this.$emit('incidents-filter-changed');
    },
  },
};
</script>

<style scoped>
  .full-height {
    height: 100%;
  }

  .transparent-with-border {
    background-color: transparent;
    border: 1px solid #cacaca;
  }

  .selected-background {
    background-color: #6e7d96;
    color: white;
  }

  .cursor-pointer {
     cursor: pointer;
  }
</style>
