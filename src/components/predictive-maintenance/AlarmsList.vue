<template>
  <v-container fluid>
    <v-expansion-panels
      accordion
      multiple
    >
      <v-expansion-panel
        v-for="(item, index) in data"
        :key="index"
      >
        <v-expansion-panel-header class="selectable-text">
          <v-row>
            <v-col
              cols="1"
              class="d-flex align-center justify-center"
            >
              <v-tooltip
                v-if="item.level?.name != null"
                bottom
              >
                <template #activator="{ on }">
                  <v-icon
                    class="pr-2"
                    :color="alarmLevels[item.level.name].color"
                    v-on="on"
                  >
                    {{ alarmLevels[item.level.name].icon }}
                  </v-icon>
                </template>
                <span>
                  {{ item.level.name }}
                </span>
              </v-tooltip>
              <div
                v-else
                style="height: 0px; width: 30px;"
              />
              <v-tooltip bottom>
                <template #activator="{ on }">
                  <v-avatar
                    size="22"
                    class="rounded-full transparent-with-border"
                    v-on="on"
                  >
                    {{ item.issues.length }}
                  </v-avatar>
                </template>
                <span>
                  {{ $t('common_issueQuantity') }}:
                  {{ item.issues.length }}
                </span>
              </v-tooltip>
              <v-tooltip
                v-if="item.incidents.length"
                bottom
              >
                <template #activator="{ on }">
                  <v-icon
                    class="pl-2"
                    v-on="on"
                  >
                    mdi-email-alert
                  </v-icon>
                </template>
                <span>
                  Ilość zgłoszeń:
                  {{ item.incidents.length }}
                </span>
              </v-tooltip>
              <div
                v-else
                style="height: 30px; width: 30px;"
              />
            </v-col>
            <v-col>
              <div>
                <div class="pl-0">
                  <strong class="text-subtitle-2 grey--text text--darken-3">
                    <span
                      class="grey--text text--darken-3"
                    >
                      {{ item.product }}
                      {{ item.sn }}
                      -
                      {{ item.name }}
                    </span>
                  </strong>
                  <br>
                  <span
                    v-if="item.address"
                    class="grey--text text--darken-3 pb-1"
                  >
                    {{ item.address }}
                  </span>
                </div>
              </div>
            </v-col>
            <v-col
              class="d-flex align-center justify-end"
            >
              <v-tooltip left>
                <template #activator="{ on, attrs }">
                  <img
                    v-if="item.owner.logo"
                    v-bind="attrs"
                    :src="item.owner.logo"
                    alt="Image"
                    style="max-height: 35px; width: auto;"
                    v-on="on"
                  >
                  <v-icon
                    v-else
                    size="35"
                    v-bind="attrs"
                    v-on="on"
                  >
                    mdi-help
                  </v-icon>
                </template>
                <span>{{ item.owner.name }}</span>
              </v-tooltip>
              <v-tooltip
                top
              >
                <template #activator="{ on, attrs }">
                  <v-avatar
                    v-bind="attrs"
                    class="ml-4"
                    :color="getOnlineStatusColor(item.online?.status)"
                    size="10"
                    v-on="on"
                  />
                </template>
                <span>
                  {{ $t('other_lastOnline') }}: {{ item.online?.lastContact ?? 'brak danych' }}
                </span>
              </v-tooltip>
              <v-dialog
                fullscreen
                @input="(value) => onDialogToggle(item.sn, value)"
              >
                <template #activator="{ on, attrs }">
                  <v-btn
                    class="ml-4 mr-4"
                    color="secondary"
                    v-bind="attrs"
                    v-on="on"
                  >
                    Szczegóły
                  </v-btn>
                </template>
                <template #default="dialog">
                  <issue-details-page
                    :key="item.sn"
                    :ref="'IssueDetailsPage_' + item.sn"
                    :data="item"
                    :issue-groups="issueGroups"
                    @close-dialog="dialog.value = false"
                  />
                </template>
              </v-dialog>
            </v-col>
          </v-row>
        </v-expansion-panel-header>
        <v-expansion-panel-content>
          <v-card
            flat
            class="px-3 py-3"
            outlined
            color="blue-grey lighten-5"
          >
            <v-card
              flat
              outlined
              color="blue-grey lighten-5"
            >
              <h3 class="mb-3">
                Status myjni
              </h3>
              <v-simple-table
                class="no-hover-highlight"
                dense
              >
                <template #default>
                  <tbody
                    v-for="(value, key) in item.details"
                    :key="key"
                  >
                    <tr>
                      <td>{{ key }}</td>
                      <td>{{ value }}</td>
                    </tr>
                  </tbody>
                </template>
              </v-simple-table>
            </v-card>
            <v-card
              v-if="item.incidents.length"
              flat
              outlined
              color="blue-grey lighten-5"
              class="mt-4"
            >
              <h3 class="mb-3">
                Zgłoszenia
              </h3>
              <v-simple-table class="no-hover-highlight">
                <template #default>
                  <tbody>
                    <tr>
                      <td
                        class="text-center"
                      >
                        Data utworzenia <!-- TODO: tłumaczenie -->
                      </td>
                      <td
                        class="text-center"
                      >
                        {{ $t('common_status') }}
                      </td>
                      <td
                        class="text-center"
                      >
                        Czas docelowy <!-- TODO: tłumaczenie -->
                      </td>
                      <td
                        class="text-center"
                      >
                        Priorytet <!-- TODO: tłumaczenie -->
                      </td>
                      <td
                        class="text-center"
                      >
                        Tytuł
                      </td>
                      <td
                        class="text-center"
                      >
                        {{ $t('common_tableDescription') }}
                      </td>
                    </tr>
                    <tr
                      v-for="incident in item.incidents"
                      :key="incident.id"
                    >
                      <td
                        class="no-white-space-wrap text-center"
                      >
                        {{
                          translateDateToDaysAgo(parseDate(incident.ctime))
                        }}
                        <br>
                        {{
                          parseHour(incident.ctime)
                        }}
                      </td>
                      <td>
                        {{ incident.status }}
                      </td>
                      <td
                        class="no-white-space-wrap text-center"
                      >
                        {{
                          translateDateToDaysAgo(parseDate(incident.targetTime))
                        }}
                        <br>
                        {{
                          parseHour(incident.targetTime)
                        }}
                      </td>
                      <td>
                        {{ incident.priority.length ? incident.priority ?? '-' : '-' }}
                      </td>
                      <td>
                        {{ incident.title }}
                      </td>
                      <td>
                        {{ incident.description }}
                      </td>
                    </tr>
                  </tbody>
                </template>
              </v-simple-table>
            </v-card>
            <v-card
              v-if="item.issues.length"
              flat
              outlined
              color="blue-grey lighten-5"
              class="mt-4"
            >
              <h3 class="mb-3">
                Problemy na myjni
              </h3>
              <v-simple-table class="no-hover-highlight">
                <template #default>
                  <tbody>
                    <tr>
                      <td
                        class="text-center"
                      >
                        {{ $t('common_status') }}
                      </td>
                      <td
                        class="text-center"
                      >
                        Data utworzenia <!-- TODO: tłumaczenie -->
                      </td>
                      <td
                        class="text-center"
                      >
                        Ostatnie wystąpienie <!-- TODO: tłumaczenie -->
                      </td>
                      <td
                        class="text-center"
                      >
                        {{ $t('common_tableDescription') }}
                      </td>
                      <td />
                    </tr>
                    <tr
                      v-for="alarm in item.issues"
                      :key="alarm.id"
                    >
                      <td class="text-center">
                        <v-tooltip bottom>
                          <template #activator="{ on }">
                            <v-icon
                              class="mr-3"
                              :color="alarmLevels[alarm.level.name].color"
                              v-on="on"
                            >
                              {{ alarmLevels[alarm.level.name].icon }}
                            </v-icon>
                          </template>
                          <span>{{ alarm.level.name }}</span>
                        </v-tooltip>
                        <v-tooltip bottom>
                          <template #activator="{ on }">
                            <v-avatar
                              size="22"
                              class="rounded-full transparent-with-border mr-3"
                              v-on="on"
                            >
                              {{ alarm.activeAlarms.length }}
                            </v-avatar>
                          </template>
                          <span>
                            {{ $t('common_quantity') }}:
                            {{ alarm.activeAlarms.length }}
                          </span>
                        </v-tooltip>
                        {{ $t('issues-alarm.' + (alarm.status).toLowerCase()) }}
                      </td>
                      <td
                        class="no-white-space-wrap text-center"
                      >
                        {{
                          translateDateToDaysAgo(parseDate(alarm.ctime))
                        }}
                        <br>
                        {{
                          parseHour(alarm.ctime)
                        }}
                      </td>
                      <td
                        class="no-white-space-wrap text-center"
                      >
                        {{
                          translateDateToDaysAgo(parseDate(alarm.lastTime))
                        }}
                        <br>
                        {{
                          parseHour(alarm.lastTime)
                        }}
                      </td>
                      <td>
                        {{ alarm.alarmDefinition.text }}
                      </td>
                      <td>
                        <v-tooltip
                          v-if="alarm.stats.similar"
                          right
                        >
                          <template
                            #activator="{ on, attrs }"
                          >
                            <v-icon
                              v-bind="attrs"
                              class="rotate"
                              v-on="on"
                            >
                              mdi-reload
                            </v-icon>
                          </template>
                          <span>Powracające problemy: {{ alarm.stats.similar }}</span>
                        </v-tooltip>
                      </td>
                    </tr>
                  </tbody>
                </template>
              </v-simple-table>
            </v-card>
          </v-card>
        </v-expansion-panel-content>
      </v-expansion-panel>
    </v-expansion-panels>
  </v-container>
</template>
<script>
import moment from 'moment';
import 'moment-timezone';
import alarmLevels from '@components/predictive-maintenance/AlarmLevels';
import IssueDetailsPage from '@components/predictive-maintenance/details/IssueDetailsPage.vue';

export default {
  name: 'AlarmsList',
  components: {
    IssueDetailsPage,
  },
  props: {
    data: {
      type: Array,
      required: true,
      default: () => ([]),
    },
    issueGroups: {
      type: Array,
      required: true,
      default: () => ([]),
    },
  },
  data() {
    return {
      alarmLevels,
    };
  },
  mounted() {

  },
  methods: {
    getOnlineStatusColor(status) {
      if (status == null) {
        return 'grey';
      }

      switch (status) {
        case 'ok': return 'green';
        case 'warning': return 'yellow';
        default: return 'red';
      }
    },
    onDialogToggle(sn, isOpened) {
      if (isOpened) {
        this.$nextTick(() => {
          const issueDetailsPageRef = this.$refs[`IssueDetailsPage_${sn}`][0];
          issueDetailsPageRef.fetchData();
        });
      } else {
        const issueDetailsPageRef = this.$refs[`IssueDetailsPage_${sn}`][0];
        const wasActionPerformed = issueDetailsPageRef.actionPerformed;
        if (wasActionPerformed) {
          this.$emit('action-performed');
        }

        issueDetailsPageRef.reset();
      }
    },
    parseDate(dateString) {
      if (dateString == null) return null;

      const date = moment(dateString);
      return date.format('YYYY-MM-DD');
    },
    parseHour(dateString) {
      if (dateString == null) return null;

      const date = moment(dateString);
      return date.format('HH:mm:ss');
    },
    translateDateToDaysAgo(dateString) {
      if (dateString == null) return '-';

      const inputMoment = moment(dateString, 'YYYY-MM-DD');
      const currentDate = moment();
      const diffInDays = currentDate.diff(inputMoment, 'days');
      let result = dateString;
      if (diffInDays === 0) {
        result = this.$t('common_now');
      } else if (diffInDays === 1) {
        result = this.$t('common_yesterday');
      }
      return result;
    },
  },
};
</script>
<style scoped>

.transparent-with-border {
  background-color: transparent;
  border: 1px solid #cacaca;
}

.selectable-text {
  user-select: text;
}

.no-hover-highlight tbody tr:hover {
  background-color: initial !important;
}

.rotate {
  transform: rotate(-90deg);
}

</style>
