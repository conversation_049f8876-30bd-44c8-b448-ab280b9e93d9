<template>
  <v-card
    color="blue-grey lighten-5 mt-5 pa-5"
    flat
  >
    <v-tabs>
      <v-tab>Status myjni</v-tab>
      <v-tab>Kontakty i notatki</v-tab>
      <v-tab>Wizyty</v-tab>

      <v-tab-item class="pa-4">
        <carwash-status
          :data="carwashStatusData"
        />
      </v-tab-item>
      <v-tab-item class="pa-4">
        <contacts-and-notes
          :data="contactsAndNotesData"
        />
      </v-tab-item>
      <v-tab-item class="pa-4">
        <visits-tab
          :data="visitsTabData"
        />
      </v-tab-item>
    </v-tabs>
  </v-card>
</template>

<script>
import CarwashStatus from './CarwashStatus.vue';
import ContactsAndNotes from './ContactsAndNotes.vue';
import VisitsTab from './VisitsTab.vue';

export default {
  name: 'CarwashDetailsTabs',

  components: {
    CarwashStatus,
    ContactsAndNotes,
    VisitsTab,
  },

  props: {
    serialNumber: {
      type: Number,
      required: true,
    },
  },

  data() {
    return {
      carwashStatusData: null,
      contactsAndNotesData: null,
      visitsTabData: null,
    };
  },

  mounted() {

  },

  methods: {
    fetchData() {
      this.axios.get(
        `/cm/alarm/by_serials/${this.serialNumber}`,
      ).then((response) => {
        const [data] = response.data;
        this.carwashStatusData = data;
      });

      this.axios.get(
        `/administration/bkf_info/carwash_contact_details/${this.serialNumber}`,
      ).then((response) => {
        this.contactsAndNotesData = response.data;
      });

      this.axios.get(
        `/administration/bkf_info/service_visits/${this.serialNumber}`,
      ).then((response) => {
        this.visitsTabData = response.data;
      });
    },

    reset() {
      this.carwashStatusData = null;
      this.contactsAndNotesData = null;
      this.visitsTabData = null;
    },
  },
};
</script>
