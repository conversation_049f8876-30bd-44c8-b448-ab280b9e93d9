<template>
  <div
    v-if="carwashStatusData == null || contactsAndNotesData == null || visitsData == null
      || activeAlarms == null"
    class="d-flex align-center justify-center"
  >
    <v-progress-circular
      indeterminate
      class="mt-8 mb-5"
      color="primary"
    />
  </div>
  <div v-else>
    <v-row>
      <v-col
        cols="3"
      >
        <widget
          title="Status myjni"
        >
          <template v-for="item in statusData">
            <v-row
              :key="item.label"
              no-gutters
              class="pt-4"
            >
              <v-col
                cols="5"
              >
                <div class="font-weight-bold">
                  {{ item.label }}
                </div>
              </v-col>
              <v-col>
                {{ item.value ?? '-' }}
                <v-btn
                  v-if="item.copy"
                  icon
                  x-small
                  class="ml-5"
                  @click="copyValueToClipboard(item.value)"
                >
                  <v-icon>mdi-content-copy</v-icon>
                </v-btn>
              </v-col>
            </v-row>
          </template>
        </widget>
      </v-col>
      <v-col>
        <widget
          :title="$t('common_employeesDetails')"
        >
          <v-simple-table v-if="contactsAndNotesData.employees_details?.length">
            <template #default>
              <thead>
                <tr>
                  <th class="text-left">
                    {{ $t('common_formName') }}
                  </th>
                  <th class="text-left">
                    {{ $t('common_surname') }}
                  </th>
                  <th class="text-left">
                    {{ $t('common_phone') }}
                  </th>
                  <th class="text-left">
                    {{ $t('common_email') }}
                  </th>
                  <th class="text-left">
                    {{ $t('common_stand') }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="item in contactsAndNotesData.employees_details"
                  :key="item.gsm"
                >
                  <td>{{ item.firstname }}</td>
                  <td>{{ item.surname }}</td>
                  <td>{{ item.gsm ?? '-' }}</td>
                  <td>{{ item.mail ?? '-' }}</td>
                  <td>{{ item.job ?? '-' }}</td>
                </tr>
              </tbody>
            </template>
          </v-simple-table>
          <p
            v-else
            class="text-center pt-5 grey--text"
          >
            Brak danych
          </p>
        </widget>
      </v-col>
      <v-col>
        <widget
          :title="$t('common_administratorDetails')"
        >
          <v-simple-table v-if="contactsAndNotesData.administrator_details?.length">
            <template #default>
              <thead>
                <tr>
                  <th class="text-left">
                    {{ $t('common_formName') }}
                  </th>
                  <th class="text-left">
                    {{ $t('common_surname') }}
                  </th>
                  <th class="text-left">
                    {{ $t('common_phone') }}
                  </th>
                  <th class="text-left">
                    {{ $t('common_email') }}
                  </th>
                  <th class="text-left">
                    {{ $t('common_stand') }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="item in contactsAndNotesData.administrator_details"
                  :key="item.gsm"
                >
                  <td>{{ item.firstname }}</td>
                  <td>{{ item.surname }}</td>
                  <td>{{ item.gsm ?? '-' }}</td>
                  <td>{{ item.mail ?? '-' }}</td>
                  <td>{{ item.job ?? '-' }}</td>
                </tr>
              </tbody>
            </template>
          </v-simple-table>
          <p
            v-else
            class="text-center pt-5 grey--text"
          >
            Brak danych
          </p>
        </widget>
      </v-col>
    </v-row>
    <v-row>
      <v-col
        cols="3"
      >
        <widget
          :title="$t('common_lastVisit')"
        >
          <v-row class="mt-2">
            <v-col
              cols="5"
            >
              <div class="font-weight-bold">
                {{ $t('common_lastVisit') }}
              </div>
            </v-col>
            <v-col>
              {{ (visitsData.last_completed_visit)
                ? `${visitsData.last_completed_visit.visit_planned_date} -
                  ${visitsData.last_completed_visit.serviceman}` : '-' }}
            </v-col>
          </v-row>
          <v-row>
            <v-col
              cols="5"
            >
              <div class="font-weight-bold">
                {{ $t('common_lastAttendance') }}
              </div>
            </v-col>
            <v-col>
              {{ (visitsData.last_attendance)
                ? `${visitsData.last_attendance.visit_attended_timestamp}` : '-' }}
            </v-col>
          </v-row>
          <v-row>
            <v-col
              cols="5"
            >
              <div class="font-weight-bold">
                {{ $t('common_nextVisit') }}
              </div>
            </v-col>
            <v-col>
              {{ (visitsData.next_planned_visit)
                ? `${visitsData.next_planned_visit.visit_planned_date} -
                  ${visitsData.next_planned_visit.serviceman}` : '-' }}
            </v-col>
          </v-row>
        </widget>
      </v-col>
      <v-col>
        <widget
          :title="$t('common_noteDetails')"
        >
          <v-simple-table v-if="contactsAndNotesData.note_details?.length">
            <template #default>
              <thead>
                <tr>
                  <th class="text-left">
                    {{ $t('common_tableDescription') }}
                  </th>
                  <th class="text-left">
                    {{ $t('common_tableCreateDate') }}
                  </th>
                  <th class="text-left">
                    {{ $t('common_username') }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="item in contactsAndNotesData.note_details"
                  :key="item.timestamp"
                >
                  <td>{{ item.note }}</td>
                  <td>{{ item.timestamp }}</td>
                  <td>{{ item.username }}</td>
                </tr>
              </tbody>
            </template>
          </v-simple-table>
          <p
            v-else
            class="text-center pt-5 grey--text"
            Brak
            danych
          />
        </widget>
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <widget
          title="Aktywne problemy"
        >
          <v-data-table
            :headers="alarmsHeaders"
            :items="activeAlarms"
            :server-length="activeAlarms.length"
            :hide-default-footer="false"
            :items-per-page="10"
          >
            <template #[`item.alarmDefinition.text`]="{ item }">
              <v-tooltip
                v-if="item.alarmDefinition.docUrl"
                right
              >
                <template #activator="{ on, attrs }">
                  <v-btn
                    :key="item.alarmDefinition.id"
                    text
                    elevation="0"
                    v-bind="attrs"
                    :href="item.alarmDefinition.docUrl"
                    target="_blank"
                    small
                    color="primary"
                    v-on="on"
                  >
                    {{ item.alarmDefinition.text }}
                  </v-btn>
                </template>
                <span>{{ $t('actions.show_doc') }}</span>
              </v-tooltip>
              <template v-else>
                {{ item.alarmDefinition.text }}
              </template>
            </template>
            <template #[`item.etime`]="{ item } ">
              {{ item.etime ?? '-' }}
            </template>
            <template #[`item.comment`]="{ item } ">
              {{ item.comment ?? '-' }}
            </template>
            <template #[`item.lastUser`]="{ item } ">
              {{ item.lastUser ?? '-' }}
            </template>
          </v-data-table>
        </widget>
      </v-col>
    </v-row>
  </div>
</template>

<script>
import DurationMixin from '@/components/common/mixins/DurationMixin.vue';
import Widget from '@components/common/Widget.vue';

export default {
  name: 'CarwashStatus',
  components: {
    Widget,
  },
  mixins: [
    DurationMixin,
  ],
  props: {
    serialNumber: {
      type: Number,
      required: true,
    },
  },

  data() {
    return {
      carwashStatusData: null,
      contactsAndNotesData: null,
      visitsData: null,
      activeAlarms: null,
      alarmsHeaders: [
        {
          text: this.$t('common_alarmId'),
          value: 'alarmDefinition.id',
          align: 'sm-start',
          sortable: false,
        },
        {
          text: this.$t('common_tableDescription'),
          value: 'alarmDefinition.text',
          align: 'sm-start',
          sortable: false,
        },
        {
          text: this.$t('common_createDate'),
          value: 'ctime',
          sortable: false,
        },
        {
          text: this.$t('common_lastTime'),
          value: 'lastTime',
          sortable: false,
        },
        {
          text: this.$t('common_endDate'),
          value: 'etime',
          sortable: false,
        },
        {
          text: this.$t('predictiveMaintenance_comment'),
          value: 'comment',
          sortable: false,
        },
        {
          text: this.$t('common_user'),
          value: 'lastUser',
          sortable: false,
        },
        {
          text: this.$t('common_state'),
          value: 'status',
          align: 'sm-start',
          sortable: false,
        },
      ],
    };
  },

  computed: {
    statusData() {
      return [
        {
          label: this.$t('common_carwashSn'),
          value: this.carwashStatusData?.sn,
          copy: true,
        },
        {
          label: this.$t('common_carwashIp'),
          value: this.carwashStatusData.ipVnc,
          copy: true,
        },
        {
          label: this.$t('common_software'),
          value: this.carwashStatusData.software,
        },
        {
          label: this.$t('common_lastAlarm'),
          value: this.carwashStatusData.lastAlarmContact,
        },
        {
          label: this.$t('common_lastSynchro'),
          value: this.carwashStatusData.lastStatsContact,
        },
        {
          label: this.$t('common_lastOnline'),
          value: this.carwashStatusData.lastMobileOnline,
        },
      ];
    },
  },

  mounted() {

  },

  methods: {
    fetchData() {
      this.reset();

      this.axios.get(
        `/cm/alarm/by_serials/${this.serialNumber}`,
      ).then((response) => {
        const [data] = response.data;
        this.carwashStatusData = data;
      });

      this.axios.get(
        `/administration/bkf_info/carwash_contact_details/${this.serialNumber}`,
      ).then((response) => {
        this.contactsAndNotesData = response.data;
      });

      this.axios.get(
        `/administration/bkf_info/service_visits/${this.serialNumber}`,
      ).then((response) => {
        this.visitsData = response.data;
      });

      this.axios.get(
        `/administration/carwashissues/list?serial=${this.serialNumber}&isOpen=true`,
      ).then((response) => {
        this.activeAlarms = response.data.data;
      });
    },
    reset() {
      this.carwashStatusData = null;
      this.contactsAndNotesData = null;
      this.visitsData = null;
      this.activeAlarms = null;
    },
    copyValueToClipboard(value) {
      navigator.clipboard.writeText(value);
    },
  },
};
</script>
