<template>
  <v-card>
    <v-toolbar
      color="secondary"
      dark
      flat
    >
      <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
        <div
          class="d-flex align-center"
        >
          <img
            v-if="data.owner.logo"
            :src="data.owner.logo"
            alt="Logo"
            style="max-height: 35px; width: auto;"
            class="mr-4"
          >
          <v-icon
            v-else
            size="25"
            class="mr-4"
          >
            mdi-help
          </v-icon>
          <div>
            <div>
              {{ data.product }}
              {{ data.sn }}
              -
              {{ data.name }}
              -
              {{ data.owner.name }}
            </div>
            <div class="text-body-2">
              {{ data.address }}
            </div>
          </div>
        </div>
      </v-toolbar-title>
      <v-spacer />
      <v-btn
        icon
        @click="closeDialog"
      >
        <v-icon>mdi-close</v-icon>
      </v-btn>
    </v-toolbar>
    <issues-list
      ref="issuesList"
      class="ma-2"
      :serial-number="data.sn"
      :issues="issues"
      :selected-issue="selectedIssue"
      :issue-groups="issueGroups"
      @issue-selected="onIssueSelected"
      @action-performed="onActionPerformed"
    />

    <v-card
      flat
      class="ma-2"
    >
      <v-tabs
        v-model="currentTabIndex"
        @change="onTabChange"
      >
        <v-tab>Szczegóły problemu</v-tab>
        <v-tab>Status myjni</v-tab>
        <v-tab>Dane procesowe</v-tab>
        <v-tab>Alarmy</v-tab>

        <v-tab-item class="pa-4">
          <issue-details
            :carwash-serial-number="data.sn"
            :issue-id="selectedIssue?.id"
            :no-issues="issues == null ? null : issues.length === 0"
          />
        </v-tab-item>
        <v-tab-item class="pa-4">
          <carwash-status
            ref="carwashStatus"
            :serial-number="data.sn"
          />
        </v-tab-item>
        <v-tab-item class="pa-4">
          <process-data-tab
            ref="processData"
            :serial-number="data.sn"
          />
        </v-tab-item>
        <v-tab-item class="pa-4">
          <alarms-tab
            ref="alarms"
            :serial-number="data.sn"
          />
        </v-tab-item>
      </v-tabs>
    </v-card>
  </v-card>
</template>

<script>
import IssuesList from './IssuesList.vue';

import IssueDetails from './IssueDetails.vue';
import CarwashStatus from './CarwashStatus.vue';
import ProcessDataTab from './ProcessDataTab.vue';
import AlarmsTab from './AlarmsTab.vue';

export default {
  name: 'IssueDetailsPage',
  components: {
    IssuesList,

    IssueDetails,
    CarwashStatus,
    ProcessDataTab,
    AlarmsTab,
  },
  props: {
    data: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    issueGroups: {
      type: Array,
      required: true,
      default: () => ([]),
    },
  },
  data() {
    return {
      currentTabIndex: 0,
      fetchCarwashData: true,
      fetchProcessData: true,
      fetchAlarmsData: true,
      actionPerformed: false,
      selectedIssue: null,
      issues: null,
    };
  },

  mounted() {

  },

  methods: {
    onTabChange(tabNumber) {
      if (tabNumber === 1) {
        setTimeout(async () => {
          if (this.fetchCarwashData) {
            await this.$refs.carwashStatus.fetchData();
            this.fetchCarwashData = false;
          }
        }, 50);
      }
      if (tabNumber === 2) {
        setTimeout(async () => {
          if (this.fetchProcessData) {
            await this.$refs.processData.fetchData();
            this.fetchProcessData = false;
          }
        }, 50);
      }
      if (tabNumber === 3) {
        setTimeout(async () => {
          if (this.fetchAlarmsData) {
            await this.$refs.alarms.fetchData();
            this.fetchAlarmsData = false;
          }
        }, 50);
      }
    },
    async fetchData() {
      this.issues = await this.$refs.issuesList.fetchIssues();
      this.$refs.issuesList.actionIssues = [];

      if (this.issues.length > 0) {
        [this.selectedIssue] = this.issues;
      } else {
        this.issues = [];
        this.selectedIssue = null;
      }
    },
    onIssueSelected(issue) {
      this.selectedIssue = issue;
    },
    onActionPerformed() {
      this.reset();
      this.actionPerformed = true;

      this.fetchData();
    },
    reset() {
      this.currentTabIndex = 0;
      this.selectedIssue = null;
      this.issues = null;
      this.fetchCarwashData = true;
      this.fetchProcessData = true;
      this.fetchAlarmsData = true;
      this.actionPerformed = false;
    },
    closeDialog() {
      this.$emit('close-dialog');
    },
  },
};
</script>
