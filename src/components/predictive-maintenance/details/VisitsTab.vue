<template>
  <div
    v-if="data == null"
    class="d-flex align-center justify-center"
  >
    <v-progress-circular
      indeterminate
      class="mt-8 mb-5"
      color="primary"
    />
  </div>
  <div v-else>
    <v-row no-gutters>
      <v-col class="ma-2">
        <v-row>
          <v-col
            cols="3"
          >
            <div class="font-weight-bold">
              {{ $t('common_lastVisit') }}
            </div>
          </v-col>
          <v-col>
            {{ (data.last_completed_visit)
              ? `${data.last_completed_visit.visit_planned_date} -
                ${data.last_completed_visit.serviceman}` : '-' }}
          </v-col>
        </v-row>
        <v-row>
          <v-col
            cols="3"
          >
            <div class="font-weight-bold">
              {{ $t('common_lastAttendance') }}
            </div>
          </v-col>
          <v-col>
            {{ (data.last_attendance)
              ? `${data.last_attendance.visit_attended_timestamp}` : '-' }}
          </v-col>
        </v-row>
        <v-row>
          <v-col
            cols="3"
          >
            <div class="font-weight-bold">
              {{ $t('common_nextVisit') }}
            </div>
          </v-col>
          <v-col>
            {{ (data.next_planned_visit)
              ? `${data.next_planned_visit.visit_planned_date} -
                ${data.next_planned_visit.serviceman}` : '-' }}
          </v-col>
        </v-row>
      </v-col>
    </v-row>
  </div>
</template>

<script>

export default {
  name: 'VisitsTab',

  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },

  computed: {
    visitsData() {
      return [
        {
          label: 'Ostatnia zakończona wizyta',
          value: this.data.last_completed_visit,
        },
        {
          label: 'Ostatnie potwierdzenie obecności',
          value: this.data.last_attendance?.visit_attended_timestamp?.visit_attended_timestamp,
        },
        {
          label: 'Następna zaplanowana wizyta',
          value: this.data.next_planned_visit,
        },
      ];
    },
  },

  mounted() {

  },

  methods: {

  },
};
</script>
