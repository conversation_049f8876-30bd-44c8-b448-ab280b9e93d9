<template>
  <div
    v-if="data == null"
    class="d-flex align-center justify-center"
  >
    <v-progress-circular
      indeterminate
      class="mt-8 mb-5"
      color="primary"
    />
  </div>
  <div v-else>
    <v-row>
      <v-col>
        <h3>
          {{ $t('common_employeesDetails') }}
        </h3>
        <v-simple-table v-if="data.employees_details?.length">
          <template #default>
            <thead>
              <tr>
                <th class="text-left">
                  {{ $t('common_formName') }}
                </th>
                <th class="text-left">
                  {{ $t('common_surname') }}
                </th>
                <th class="text-left">
                  {{ $t('common_phone') }}
                </th>
                <th class="text-left">
                  {{ $t('common_email') }}
                </th>
                <th class="text-left">
                  {{ $t('common_stand') }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="item in data.employees_details"
                :key="item.gsm"
              >
                <td>{{ item.firstname }}</td>
                <td>{{ item.surname }}</td>
                <td>{{ item.gsm ?? '-' }}</td>
                <td>{{ item.mail ?? '-' }}</td>
                <td>{{ item.job ?? '-' }}</td>
              </tr>
            </tbody>
          </template>
        </v-simple-table>
        <p
          v-else
          class="text-center pt-5 grey--text"
        >
          Brak danych
        </p>
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <h3>
          {{ $t('common_administratorDetails') }}
        </h3>
        <v-simple-table v-if="data.administrator_details?.length">
          <template #default>
            <thead>
              <tr>
                <th class="text-left">
                  {{ $t('common_formName') }}
                </th>
                <th class="text-left">
                  {{ $t('common_surname') }}
                </th>
                <th class="text-left">
                  {{ $t('common_phone') }}
                </th>
                <th class="text-left">
                  {{ $t('common_email') }}
                </th>
                <th class="text-left">
                  {{ $t('common_stand') }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="item in data.administrator_details"
                :key="item.gsm"
              >
                <td>{{ item.firstname }}</td>
                <td>{{ item.surname }}</td>
                <td>{{ item.gsm ?? '-' }}</td>
                <td>{{ item.mail ?? '-' }}</td>
                <td>{{ item.job ?? '-' }}</td>
              </tr>
            </tbody>
          </template>
        </v-simple-table>
        <p
          v-else
          class="text-center pt-2 grey--text"
        >
          Brak danych
        </p>
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <h3>
          {{ $t('common_noteDetails') }}
        </h3>
        <v-simple-table v-if="data.note_details?.length">
          <template #default>
            <thead>
              <tr>
                <th class="text-left">
                  {{ $t('common_tableDescription') }}
                </th>
                <th class="text-left">
                  {{ $t('common_tableCreateDate') }}
                </th>
                <th class="text-left">
                  {{ $t('common_username') }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="item in data.note_details"
                :key="item.timestamp"
              >
                <td>{{ item.note }}</td>
                <td>{{ item.timestamp }}</td>
                <td>{{ item.username }}</td>
              </tr>
            </tbody>
          </template>
        </v-simple-table>
        <p
          v-else
          class="text-center pt-5 grey--text"
        >
          Brak danych
        </p>
      </v-col>
    </v-row>
  </div>
</template>

<script>
export default {
  name: 'ContactsAndNotes',

  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },

  mounted() {

  },

  methods: {

  },
};
</script>
