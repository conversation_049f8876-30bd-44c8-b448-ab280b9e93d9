<template>
  <div
    v-if="loading"
    class="d-flex align-center justify-center"
  >
    <v-progress-circular
      indeterminate
      class="mt-8 mb-5"
      color="primary"
    />
  </div>
  <div
    v-else
  >
    <v-row
      class="d-flex justify-end mr-2"
    >
      <date-range-picker
        key="dateRange"
        ref="dateRange"
        prepend-icon="mdi-calendar-range"
        :show-presets="true"
        :show-custom="true"
        :presets="dateRangePresets"
        :settings-namespace="settingsNamespace"
        @reload-transaction-list="onDateRangeChange"
      />
    </v-row>
    <v-row v-if="Object.keys(parametersToChart).length">
      <v-col>
        <widget
          title-prepend-icon="mdi-chart-line-variant"
          :title="$t('common_chart')"
        >
          <line-chart
            ref="linechart"
            :items="Object.values(charts)"
            :loading="loadingChartData"
          />
        </widget>
      </v-col>
    </v-row>
    <v-row>
      <v-col
        v-for="(item) in categories"
        :key="`widget-${item.id}`"
        cols="12"
        md="6"
        lg="4"
      >
        <widget
          :title="item.name"
          :title-prepend-icon="item.icon"
        >
          <process-category-table
            :items="item.items"
            :loader="!item.items.length"
            :parameters-to-chart="parametersToChart"
            @addParameterToChart="addParameterToChart"
          />
        </widget>
      </v-col>
    </v-row>
  </div>
</template>

<script>
import DateRangePicker from '@components/common/DateRangePicker.vue';
import Widget from '@components/common/Widget.vue';
import ProcessCategoryTable from '@components/process-data/ProcessCategoryTable.vue';
import LineChart from '@components/process-data/ProcessCategoryChart.vue';
import {
  endOfToday,
  endOfYesterday,
  startOfDay, startOfToday,
  startOfYesterday,
  subDays,
} from 'date-fns';

export default {
  name: 'ProcessDataTab',

  components: {
    DateRangePicker,
    Widget,
    ProcessCategoryTable,
    LineChart,
  },

  props: {
    serialNumber: {
      type: Number,
      required: true,
    },
    initialDataFetch: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      items: [],
      icons: {
        100: 'mdi-desktop-classic', // Komputer
        210: 'mdi-coolant-temperature', // Ogrzewanie
        220: 'mdi-water-boiler', // Kocioł
        230: 'mdi-car-wash', // Stanowiska
        240: 'mdi-sync', // Cyrkulacja
        300: 'mdi-water', // Woda
        310: 'mdi-gradient-vertical', // Osmoza
        400: 'mdi-flask-outline', // Chemia
      },
      loading: false,
      loadingChartData: false,
      parametersToChart: [],
      chartParamData: [],
      settingsNamespace: 'process-data:chart:date',
      dates: [],
      dateFrom: '',
      dateTo: '',
      dateRangePresets: [
        {
          value: 'today',
          text: this.$t('common_today'),
          start: startOfToday(),
          end: endOfToday(),
          default: true,
        },
        {
          value: 'yesterday',
          text: this.$t('common_daterangeYesterday'),
          start: startOfYesterday(),
          end: endOfYesterday(),
          default: true,
        },
        {
          value: 'last7Days',
          text: this.$t('common_p7d'),
          start: startOfDay(subDays(new Date(), 6)),
          end: endOfToday(),
        },
        {
          value: 'last14Days',
          text: this.$t('common_p14d'),
          start: startOfDay(subDays(new Date(), 13)),
          end: endOfToday(),
        },
      ],
    };
  },

  computed: {
    categories() {
      const categories = {};
      if (this.items) {
        this.items.forEach((param) => {
          if (!param.category.categoryDescription) {
            return;
          }
          if (!(param.category.categoryDescription in categories)) {
            categories[param.category.categoryDescription] = {
              id: param.category.categoryId,
              name: param.category.categoryDescription,
              description: param.category.categoryDescription,
              icon: this.icons[param.category.categoryId] || 'mdi-clipboard-list-outline',
              items: [],
            };
          }
          categories[param.category.categoryDescription].items.push(param);
        });
      }

      return Object.values(categories);
    },
    charts() {
      const groupedArray = this.chartParamData.reduce((result, item) => {
        const { items, unit, name } = item;
        if (!result[unit]) {
          // eslint-disable-next-line no-param-reassign
          result[unit] = [];
        }
        result[unit].push({ items, name, unit });
        return result;
      }, {});
      return groupedArray;
    },
  },

  mounted() {
    if (this.initialDataFetch) {
      this.fetchData();
    }
  },

  methods: {
    reset() {
      this.loadingChartData = false;
      this.parametersToChart = [];
      this.chartParamData = [];
    },
    async fetchData() {
      this.loading = true;

      const response = await this.axios.get(`/cm_new/param/?sn=${this.serialNumber}`);
      this.items = response.data.data;

      this.loading = false;
    },
    addParameterToChart(param) {
      const searchIndex = this.parametersToChart.findIndex((parameter) => parameter.id
        === param.id);
      if (searchIndex === -1) {
        this.parametersToChart.push({ id: param.id, unit: param.unit, name: param.name });
      } else {
        this.parametersToChart.splice(searchIndex, 1);
      }

      this.fetchParamsHistory();
    },
    onDateRangeChange({
      from,
      to,
    }) {
      const dateFromArray = from.split(' ');
      const dateFrom = dateFromArray[0];
      this.dateFrom = dateFrom;
      const dateToArray = to.split(' ');
      const dateTo = dateToArray[0];
      this.dateTo = dateTo;
      this.dates = [this.dateFrom, this.dateTo];
      this.fetchParamsHistory();
    },
    async fetchParamsHistory() {
      this.loadingChartData = true;
      await this.axios.post(
        `/cm_new/param-history?sn=${this.serialNumber}`,
        {
          date_from: this.dateFrom,
          date_to: this.dateTo,
          params: JSON.stringify(this.parametersToChart.map((a) => a.id)),
        },
      )
        .then(
          (response) => {
            if (response.status === 200) {
              this.chartParamData = [];
              response.data.forEach((paramData, index) => {
                this.chartParamData.push({
                  items: paramData,
                  unit: this.parametersToChart[index].unit,
                  name: this.parametersToChart[index].name,
                });
              });
            }
            this.loadingChartData = false;
          },
        )
        .catch(() => {
        });
    },
  },
};
</script>
