<template>
  <v-row class="ma-1">
    <v-col
      cols="6"
    >
      <issues-list
        ref="issuesList"
        :serial-number="serialNumber"
        :issues="issues"
        :selected-issue="selectedIssue"
        :issue-groups="issueGroups"
        @issue-selected="onIssueSelected"
        @action-performed="onActionPerformed"
      />
      <carwash-details-tabs
        ref="carwashDetailsTab"
        :serial-number="serialNumber"
      />
    </v-col>
    <v-col
      cols="6"
    >
      <issue-details
        :carwash-serial-number="serialNumber"
        :issue-id="selectedIssue?.id"
        :no-issues="issues == null ? false : !issues.length"
      />
    </v-col>
  </v-row>
</template>

<script>
import alarmLevels from '@components/predictive-maintenance/AlarmLevels';
import IssuesList from './IssuesList.vue';
import IssueDetails from './IssueDetails.vue';
import CarwashDetailsTabs from './CarwashDetailsTabs.vue';

export default {
  name: 'CarwashIssuesTab',
  components: {
    IssuesList,
    IssueDetails,
    CarwashDetailsTabs,
  },
  props: {
    serialNumber: {
      type: Number,
      required: true,
    },
    issueGroups: {
      type: Array,
      required: true,
      default: () => ([]),
    },
  },
  data() {
    return {
      alarmLevels,
      selectedIssue: null,
      issues: null,
    };
  },

  mounted() {

  },

  methods: {
    async fetchData() {
      this.$refs.carwashDetailsTab.reset();
      this.$refs.carwashDetailsTab.fetchData();

      this.selectedIssue = null;
      this.issues = null;
      this.issues = await this.$refs.issuesList.fetchIssues();
      this.$refs.issuesList.actionIssues = [];

      [this.selectedIssue] = this.issues;
    },
    onIssueSelected(issue) {
      this.selectedIssue = issue;
    },
    onActionPerformed() {
      this.$emit('action-performed');

      this.fetchData();
    },
    closeDialog() {
      this.$emit('close-dialog');
    },
    reset() {
      this.$refs.carwashDetailsTab.reset();
    },
  },
};
</script>
