<template>
  <v-card
    outlined
    color="blue-grey lighten-5"
  >
    <div
      v-if="loading"
      class="d-flex align-center justify-center"
    >
      <v-progress-circular
        indeterminate
        class="mt-8 mb-5"
        color="primary"
      />
    </div>
    <div
      v-else
    >
      <v-row no-gutters>
        <v-col>
          <div class="ma-4 d-flex justify-space-between">
            <h3 class="ml-2 align-self-center">
              Problemy na myjni
            </h3>
            <div>
              <v-btn
                class="mr-4"
                elevation="0"
                target="_blank"
                color="primary"
                :disabled="!actionIssues.length"
                @click="openDialog('close')"
              >
                <v-icon
                  dark
                  small
                  class="mr-1"
                >
                  mdi-close
                </v-icon>
                {{ $t('actions.close') }}
              </v-btn>
              <v-btn
                class="mr-4"
                elevation="0"
                target="_blank"
                color="secondary"
                :disabled="!actionIssues.length"
                @click="openDialog('postpone')"
              >
                <v-icon
                  dark
                  small
                  class="mr-1"
                >
                  mdi-arrow-right
                </v-icon>
                {{ $t('actions.postpone') }}
              </v-btn>
              <v-btn
                elevation="0"
                target="_blank"
                color="green lighten-2"
                :disabled="!actionIssues.length"
                @click="openDialog('service')"
              >
                <v-icon
                  dark
                  small
                  class="mr-1"
                >
                  mdi-wrench
                </v-icon>
                {{ $t('actions.service') }}
              </v-btn>
            </div>
          </div>
          <v-data-table
            v-if="issues?.length"
            class="mx-4 mb-4 no-hover-highlight"
            :items="issues"
          >
            <template #body="{ items }">
              <tbody>
                <tr>
                  <td class="text-center">
                    {{ $t('common_status') }}
                  </td>
                  <td class="text-center">
                    Data utworzenia
                  </td>
                  <td class="text-center">
                    Ostatnie wystąpienie
                  </td>
                  <td class="text-center">
                    {{ $t('common_tableDescription') }}
                  </td>
                  <td />
                  <td class="text-center">
                    Akcja
                  </td>
                </tr>
                <tr
                  v-for="issue in items"
                  :key="issue.id"
                  :class="{ 'selected-issue': selectedIssue?.id === issue.id }"
                  @click="$emit('issue-selected', issue)"
                >
                  <td class="text-center">
                    <v-tooltip bottom>
                      <template #activator="{ on }">
                        <v-icon
                          class="mr-3"
                          :color="alarmLevels[issue.level.name].color"
                          v-on="on"
                        >
                          {{ alarmLevels[issue.level.name].icon }}
                        </v-icon>
                      </template>
                      <span>{{ issue.level.name }}</span>
                    </v-tooltip>
                    <v-tooltip bottom>
                      <template #activator="{ on }">
                        <v-avatar
                          size="22"
                          class="rounded-full transparent-with-border mr-3"
                          v-on="on"
                        >
                          {{ issue.activeAlarms.length }}
                        </v-avatar>
                      </template>
                      <span>
                        {{ $t('common_quantity') }}:
                        {{ issue.activeAlarms.length }}
                      </span>
                    </v-tooltip>
                    {{ $t('issues-alarm.' + (issue.status).toLowerCase()) }}
                  </td>
                  <td class="no-white-space-wrap text-center">
                    {{
                      translateDateToDaysAgo(parseDate(issue.ctime))
                    }}
                    <br>
                    {{
                      parseHour(issue.ctime)
                    }}
                  </td>
                  <td class="no-white-space-wrap text-center">
                    {{
                      translateDateToDaysAgo(parseDate(issue.lastTime))
                    }}
                    <br>
                    {{
                      parseHour(issue.lastTime)
                    }}
                  </td>
                  <td class="no-white-space-wrap text-center">
                    {{ issue.alarmDefinition.text }}
                  </td>
                  <td>
                    <v-tooltip
                      v-if="issue.stats.similar"
                      right
                    >
                      <template
                        #activator="{ on, attrs }"
                      >
                        <v-icon
                          v-bind="attrs"
                          class="rotate"
                          v-on="on"
                        >
                          mdi-reload
                        </v-icon>
                      </template>
                      <span>Powracające problemy: {{ issue.stats.similar }}</span>
                    </v-tooltip>
                  </td>
                  <td class="d-flex align-center justify-center">
                    <v-checkbox
                      v-model="actionIssues"
                      :value="issue"
                    />
                  </td>
                </tr>
              </tbody>
            </template>
          </v-data-table>
          <div
            v-else
            class="text-center pb-5"
          >
            Brak problemów
          </div>
        </v-col>
        <v-col
          v-if="incidents?.length"
          class="mr-4"
        >
          <v-card
            flat
            outlined
            color="blue-grey lighten-5"
          >
            <h3 class="mt-6 mb-5">
              Zgłoszenia
            </h3>
            <v-data-table
              class="no-hover-highlight mb-4"
              :items="incidents"
            >
              <template #body="{ items }">
                <tbody>
                  <tr>
                    <td
                      class="text-center"
                    >
                      Data utworzenia <!-- TODO: tłumaczenie -->
                    </td>
                    <td
                      class="text-center"
                    >
                      {{ $t('common_status') }}
                    </td>
                    <td
                      class="text-center"
                    >
                      Czas docelowy <!-- TODO: tłumaczenie -->
                    </td>
                    <td
                      class="text-center"
                    >
                      Priorytet <!-- TODO: tłumaczenie -->
                    </td>
                    <td
                      class="text-center"
                    >
                      Tytuł
                    </td>
                    <td
                      class="text-center"
                    >
                      {{ $t('common_tableDescription') }}
                    </td>
                  </tr>
                  <tr
                    v-for="incident in items"
                    :key="incident.id"
                  >
                    <td
                      class="no-white-space-wrap text-center"
                    >
                      {{
                        translateDateToDaysAgo(parseDate(incident.ctime))
                      }}
                      <br>
                      {{
                        parseHour(incident.ctime)
                      }}
                    </td>
                    <td class="text-center">
                      {{ incident.status }}
                    </td>
                    <td
                      class="no-white-space-wrap text-center"
                    >
                      {{
                        translateDateToDaysAgo(parseDate(incident.targetTime))
                      }}
                      <br>
                      {{
                        parseHour(incident.targetTime)
                      }}
                    </td>
                    <td class="text-center">
                      {{ incident.priority.length ? incident.priority ?? '-' : '-' }}
                    </td>
                    <td class="text-center">
                      {{ incident.title }}
                    </td>
                    <td class="text-center">
                      {{ incident.description }}
                    </td>
                  </tr>
                </tbody>
              </template>
            </v-data-table>
          </v-card>
        </v-col>
      </v-row>
    </div>
    <v-dialog
      v-model="dialog"
      width="500"
      content-class="dialogWidth-3"
      persistent
      header-color="primary"
    >
      <v-card>
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">
              {{ $t('common_management') }}
            </h5>
          </span>
        </v-card-title>
        <div
          v-if="dialogLoading"
          class="d-flex align-center justify-center"
        >
          <v-progress-circular
            indeterminate
            class="mt-8 mb-5"
            color="primary"
          />
        </div>
        <div v-else>
          <v-card-text>
            <div
              class="mt-5"
            >
              <div class="mb-1 subtitle-1">
                {{ $t('common_action') }}
                <strong>{{ $t('actions.' + issueAction) }}</strong>
                <br>
                {{ $t('common_refersTo') }}
              </div>
            </div>
            <div
              class="mt-5"
            >
              <v-chip
                v-for="(issue, index) in actionIssues"
                :key="index"
                density="compact"
                class="mr-2"
                small
              >
                {{ issue.alarmDefinition.id }}
              </v-chip>
            </div>
            <v-form
              ref="formUserEdit"
              v-model="form.valid"
              lazy-validation
            >
              <v-menu
                v-if="issueAction === 'postpone'"
                ref="menu"
                v-model="menu"
                :close-on-content-click="false"
                :return-value.sync="date"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template #activator="{ on, attrs }">
                  <v-text-field
                    :label="$t('common_postpone')"
                    prepend-icon="mdi-calendar-clock"
                    name="date"
                    v-bind="attrs"
                    readonly
                    :value="datetime"
                    required
                    :rules="nameRules"
                    :error-messages="nameErrors"
                    v-on="on"
                  />
                  <div
                    v-if="nameErrors.length > 0"
                    class="red--text"
                  >
                    <ul>
                      <li
                        v-for="error in nameErrors"
                        :key="error"
                      >
                        {{ error }}
                      </li>
                    </ul>
                  </div>
                </template>
                <v-card>
                  <v-card-text class="px-0 py-0">
                    <v-row>
                      <v-col class="pr-0">
                        <v-date-picker
                          v-model="date"
                          :min="today"
                        />
                      </v-col>
                      <v-col>
                        <v-time-picker
                          v-model="time"
                          format="24hr"
                        />
                      </v-col>
                    </v-row>
                  </v-card-text>
                  <v-card-actions>
                    <v-spacer />
                    <v-btn
                      color="primary"
                      @click="$refs.menu.save(date)"
                    >
                      OK
                    </v-btn>
                  </v-card-actions>
                </v-card>
              </v-menu>
              <v-select
                v-if="issueAction === 'service'"
                v-model="selectedPriority"
                :items="issuePriority"
                item-value="val"
                item-text="text"
                :label="$t('common_priority')"
                prepend-icon="mdi-priority-high"
                class="mt-6"
                required
                :rules="rules.selectRequired"
              />
              <v-textarea
                ref="explanation"
                v-model="explanation"
                required
                :rules="rules.textRequired"
                :label="$t('common_description')"
                prepend-icon="mdi-text"
                class="mt-2"
              />
            </v-form>
          </v-card-text>
          <v-divider />
          <v-card-actions>
            <v-spacer />
            <v-btn
              v-if="issueAction === 'postpone'"
              color="green lighten-2"
              tile
              @click="postponeForOneHour"
            >
              {{ $t('common_postponeForOneHour') }}
            </v-btn>
            <v-btn
              color="secondary"
              tile
              @click="dialog = false"
            >
              {{ $t('actions.cancel') }}
            </v-btn>
            <v-btn
              color="primary"
              tile
              @click="saveIssue"
            >
              {{ $t('actions.save') }}
            </v-btn>
          </v-card-actions>
        </div>
      </v-card>
    </v-dialog>
  </v-card>
</template>

<script>
import moment from 'moment';
import alarmLevels from '@components/predictive-maintenance/AlarmLevels';

export default {
  name: 'IssuesList',
  props: {
    issues: {
      type: Array,
      default: () => [{}],
    },
    selectedIssue: {
      type: Object,
      default: null,
    },
    serialNumber: {
      type: Number,
      required: true,
    },
    issueGroups: {
      type: Array,
      required: true,
      default: () => ([]),
    },
  },

  data() {
    return {
      alarmLevels,
      actionIssues: [],
      loading: false,
      dialog: false,
      form: {
        valid: false,
      },
      rules: {
        textRequired: [(v) => !!v || this.$t('common_fieldRequired')],
        selectRequired: [(v) => (!!v || v === '') || this.$t('common_fieldRequired')],
      },
      issueAction: 'close',
      date: (new Date(Date.now() - (new Date()).getTimezoneOffset()
        * 60000)).toISOString().substr(0, 10),
      time: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000))
        .toISOString()
        .substr(-13, 8),
      menu: false,
      explanation: '',
      nameRules: [
        (v) => !!v || this.$t('common_fieldRequired'),
      ],
      nameErrors: [],
      issuePriority: [
        { val: '', text: this.$t('common_none') },
        { val: 'AWK', text: this.$t('common_awk') },
        { val: 'AWC', text: this.$t('common_awc') },
        { val: 'AWP', text: this.$t('common_awp') },
      ],
      selectedPriority: '',
      today: new Date().toISOString().substr(0, 10),
      dialogLoading: false,
      incidents: null,
      headers: [
        {
          text: 'Status problemu',
          value: 'status',
          align: 'center',
          sortable: false,
        },
        {
          text: 'Data utworzenia',
          value: 'ctime',
          align: 'center',
          sortable: false,
        },
        {
          text: 'Ostatnie wystąpienie',
          value: 'lastTime',
          align: 'center',
          sortable: false,
        },
        {
          text: this.$t('common_tableDescription'),
          value: 'alarmDefinition.text',
          align: 'center',
          sortable: false,
        },
      ],
    };
  },

  computed: {
    datetime() {
      return `${this.date} ${this.time}`;
    },
  },

  mounted() {

  },

  methods: {
    async fetchIssues() {
      this.incidents = null;
      this.loading = true;

      const response = await this.axios.get(
        '/administration/carwashissues',
        {
          params: {
            groups: this.issueGroups.join(),
            serial: this.serialNumber,
          },
        },
      );

      this.loading = false;

      const data = response.data.data.filter((item) => item.sn === this.serialNumber)[0];
      this.incidents = data.incidents;

      return data.issues;
    },
    parseDate(dateString) {
      if (dateString == null) return null;

      const date = moment(dateString);
      return date.format('YYYY-MM-DD');
    },
    parseHour(dateString) {
      if (dateString == null) return null;

      const date = moment(dateString);
      return date.format('HH:mm:ss');
    },
    translateDateToDaysAgo(dateString) {
      if (dateString == null) return '-';

      const inputMoment = moment(dateString, 'YYYY-MM-DD');
      const currentDate = moment();
      const diffInDays = currentDate.diff(inputMoment, 'days');
      let result = dateString;
      if (diffInDays === 0) {
        result = this.$t('common_now');
      } else if (diffInDays === 1) {
        result = this.$t('common_yesterday');
      }
      return result;
    },
    openDialog(action) {
      this.dialog = true;
      this.issueAction = action;
    },
    postponeForOneHour() {
      const currentMoment = moment();
      this.date = (new Date(Date.now() - (new Date()).getTimezoneOffset()
        * 60000)).toISOString().substr(0, 10);
      this.time = currentMoment.add(1, 'hour').format('HH:mm');
      this.saveIssue();
    },
    async saveIssue() {
      // this.$emit('action-performed');

      this.$validator.validateAll();
      if (this.$refs.formUserEdit.validate()) {
        this.dialogLoading = true;
        try {
          await this.axios.post(
            '/administration/carwashissues/update',
            {
              action: this.issueAction,
              explanation: this.explanation,
              date: this.datetime,
              issues: this.actionIssues.map(
                (item) => ({
                  id: item.id,
                  alarmDefinition: item.alarmDefinition.id,
                  carwashSerialNumber: item.carwash.sn,
                  description: item.alarmDefinition.text,
                  level: item.level.name,
                }),
              ),
              priority: this.selectedPriority,
            },
          );
          this.dialog = false;
          this.issueAction = '';
          this.explanation = '';
          this.actionIssues = [];
          this.date = (new Date(Date.now() - (new Date()).getTimezoneOffset()
          * 60000)).toISOString().substr(0, 10);

          this.$emit('action-performed');
        // eslint-disable-next-line no-empty
        } catch (error) {

        }

        this.dialogLoading = false;
      }
    },
  },
};
</script>

<style scoped>

.transparent-with-border {
  background-color: transparent;
  border: 1px solid #cacaca;
}

.selected-issue {
  box-shadow: 10px 0 0 0 #6e7d96 inset;
}

.no-hover-highlight tbody tr:hover {
  background-color: initial !important;
}

.rotate {
  transform: rotate(-90deg);
}

</style>
