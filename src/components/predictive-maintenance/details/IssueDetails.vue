<template>
  <div
    v-if="noIssues == true"
    class="text-center"
  >
    Brak danych do pokazania
  </div>
  <div
    v-else-if="issueId == null || loading"
    class="d-flex align-center justify-center"
  >
    <v-progress-circular
      indeterminate
      class="mt-8 mb-5"
      color="primary"
    />
  </div>
  <div v-else>
    <v-row>
      <v-col
        cols="6"
      >
        <widget
          title="Alarmy przypisane do tego problemu"
        >
          <template #titleActions>
            <v-btn-toggle
              v-model="alarmsToggleValue"
              mandatory
            >
              <v-btn
                value="active"
                small
              >
                <span>Aktywne</span>
              </v-btn>
              <v-btn
                value="all"
                small
              >
                <span>Wszystkie</span>
              </v-btn>
            </v-btn-toggle>
          </template>
          <v-data-table
            :headers="activeAlarmsHeaders"
            :items="issueAlarms"
            :hide-default-footer="false"
            :items-per-page="10"
          >
            <template #[`item.durationTimeInSec`]="{ item }">
              {{ calculateDuration(item.durationTimeInSec) }}
            </template>
            <template #[`item.alarmDef.text`]="{ item }">
              <v-tooltip
                v-if="item.alarmDef.docUrl"
                right
              >
                <template #activator="{ on, attrs }">
                  <v-btn
                    :key="item.alarmDef.id"
                    text
                    elevation="0"
                    v-bind="attrs"
                    :href="item.alarmDef.docUrl"
                    target="_blank"
                    small
                    color="primary"
                    v-on="on"
                  >
                    {{ item.text }}
                  </v-btn>
                </template>
                <span>{{ $t('actions.show_doc') }}</span>
              </v-tooltip>
              <template v-else>
                {{ item.text }}
              </template>
            </template>
          </v-data-table>
        </widget>
      </v-col>
      <v-col
        cols="6"
      >
        <widget
          :title="$t('common_issueHistory')"
        >
          <v-data-table
            :headers="historyHeaders"
            :sort-by="['ctime']"
            :sort-desc="[true]"
            :items="issueDetails?.history"
            :hide-default-footer="false"
            :items-per-page="10"
          />
        </widget>
      </v-col>
      <v-col
        cols="12"
      >
        <widget
          title="Podobne problemy"
        >
          <v-data-table
            :headers="similarAlarmsHeaders"
            :items="similarIssues?.data"
            :server-length="similarIssues?.total"
            :hide-default-footer="false"
            :items-per-page="10"
          >
            <template #[`item.alarmDefinition.text`]="{ item }">
              <v-tooltip
                v-if="item.alarmDefinition.docUrl"
                right
              >
                <template #activator="{ on, attrs }">
                  <v-btn
                    :key="item.alarmDefinition.id"
                    text
                    elevation="0"
                    v-bind="attrs"
                    :href="item.alarmDefinition.docUrl"
                    target="_blank"
                    small
                    color="primary"
                    v-on="on"
                  >
                    {{ item.alarmDefinition.text }}
                  </v-btn>
                </template>
                <span>{{ $t('actions.show_doc') }}</span>
              </v-tooltip>
              <template v-else>
                {{ item.alarmDefinition.text }}
              </template>
            </template>
            <template #[`item.etime`]="{ item } ">
              {{ item.etime ?? '-' }}
            </template>
            <template #[`item.comment`]="{ item } ">
              {{ item.comment ?? '-' }}
            </template>
            <template #[`item.lastUser`]="{ item } ">
              {{ item.lastUser ?? '-' }}
            </template>
          </v-data-table>
        </widget>
      </v-col>
    </v-row>
  </div>
</template>

<script>
import DurationMixin from '@/components/common/mixins/DurationMixin.vue';
import Widget from '@components/common/Widget.vue';

export default {
  name: 'IssueDetails',

  components: {
    Widget,
  },
  mixins: [
    DurationMixin,
  ],

  props: {
    carwashSerialNumber: {
      type: Number,
      required: true,
    },
    issueId: {
      type: Number,
      default: null,
    },
    noIssues: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      alarmsToggleValue: 'active',
      panels: [0, 1, 2],
      loading: false,
      issueDetails: null,
      similarIssues: null,
      historyHeaders: [
        {
          text: this.$t('common_date'),
          value: 'ctime',
          align: 'sm-start',
          sortable: true,
        },
        {
          text: this.$t('common_comment'),
          value: 'comment',
          align: 'sm-start',
          sortable: false,
        },
        {
          text: this.$t('common_email'),
          value: 'email',
          align: 'sm-start',
          sortable: false,
        },
        {
          text: this.$t('common_state'),
          value: 'status',
          align: 'sm-start',
          sortable: false,
        },
      ],
      activeAlarmsHeaders: [
        {
          text: this.$t('common_alarmId'),
          value: 'alarmDef.id',
          align: 'sm-start',
          sortable: false,
        },
        {
          text: this.$t('common_tableDescription'),
          value: 'alarmDef.text',
          align: 'sm-start',
          sortable: false,
        },
        {
          text: this.$t('common_createDate'),
          value: 'ct',
          sortable: false,
        },
        {
          text: this.$t('common_AlarmDuration'),
          value: 'durationTimeInSec',
          sortable: false,
        },
      ],
      similarAlarmsHeaders: [
        {
          text: this.$t('common_alarmId'),
          value: 'alarmDefinition.id',
          align: 'sm-start',
          sortable: false,
        },
        {
          text: this.$t('common_tableDescription'),
          value: 'alarmDefinition.text',
          align: 'sm-start',
          sortable: false,
        },
        {
          text: this.$t('common_createDate'),
          value: 'ctime',
          sortable: false,
        },
        {
          text: this.$t('common_lastTime'),
          value: 'lastTime',
          sortable: false,
        },
        {
          text: this.$t('common_endDate'),
          value: 'etime',
          sortable: false,
        },
        {
          text: this.$t('predictiveMaintenance_comment'),
          value: 'comment',
          sortable: false,
        },
        {
          text: this.$t('common_user'),
          value: 'lastUser',
          sortable: false,
        },
        {
          text: this.$t('common_state'),
          value: 'status',
          align: 'sm-start',
          sortable: false,
        },
      ],
    };
  },

  computed: {
    issueAlarms() {
      return this.alarmsToggleValue === 'active' ? this.issueDetails?.activeAlarms : this.issueDetails?.alarm;
    },
  },

  watch: {
    issueId(newValue, oldValue) {
      if (newValue !== oldValue) {
        this.fetchData();
      }
    },
  },

  mounted() {

  },

  methods: {
    async fetchData() {
      this.panels = [0, 1, 2];
      this.alarmsToggleValue = 'active';
      this.loading = true;

      if (this.issueId == null) return;

      let response = await this.axios.get(
        `/administration/carwashissue/${this.issueId}`,
      );

      this.issueDetails = response.data;

      response = await this.axios.get(
        `/administration/carwashissue/${this.issueId}/similar`,
      );

      this.similarIssues = response.data;

      this.loading = false;
    },
  },
};
</script>
