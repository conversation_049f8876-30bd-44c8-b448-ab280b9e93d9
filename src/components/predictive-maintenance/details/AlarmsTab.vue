<template>
  <div>
    <v-row
      class="d-flex justify-end mr-2"
    >
      <date-range-picker
        key="dateRange"
        ref="dateRange"
        prepend-icon="mdi-calendar-range"
        :show-presets="true"
        :show-custom="true"
        :presets="dateRangePresets"
        :settings-namespace="settingsNamespace"
        @reload-transaction-list="onDateRangeChange"
      />
    </v-row>
    <v-row>
      <v-col>
        <v-card>
          <v-toolbar
            color="secondary"
            dark
            flat
            height="46"
          >
            <v-btn
              icon
              dark
            >
              <v-icon>mdi-alarm</v-icon>
            </v-btn>
            <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
              {{ $t('common_alarmActive') }}
            </v-toolbar-title>
            <v-spacer />
          </v-toolbar>
          <alarm-active-list
            :alarms="alarms"
            :loading="loading"
          />
        </v-card>
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <v-card>
          <v-toolbar
            color="secondary"
            class="mt-5"
            dark
            flat
            height="46"
          >
            <v-btn
              icon
              dark
            >
              <v-icon>mdi-alarm</v-icon>
            </v-btn>
            <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
              {{ $t('common_alarmHistory') }}
            </v-toolbar-title>
            <v-spacer />
          </v-toolbar>
          <alarm-history-list
            ref="historyList"
            :selected-carwash="serialNumber"
            :dates="dates"
          />
        </v-card>
      </v-col>
    </v-row>
  </div>
</template>

<script>
import DateRangePicker from '@components/common/DateRangePicker.vue';
import AlarmActiveList from '@components/alarm-active/AlarmActiveList.vue';
import AlarmHistoryList from '@components/alarm-history/AlarmHistoryList.vue';
import {
  endOfToday,
  endOfYesterday,
  startOfDay, startOfToday,
  startOfYesterday,
  subDays,
} from 'date-fns';

export default {
  name: 'AlarmsTab',

  components: {
    DateRangePicker,
    AlarmActiveList,
    AlarmHistoryList,
  },

  props: {
    serialNumber: {
      type: Number,
      required: true,
    },
    initialDataFetch: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      loading: false,
      alarms: [],
      settingsNamespace: 'process-data:chart:date',
      dates: [],
      dateFrom: '',
      dateTo: '',
      dateRangePresets: [
        {
          value: 'today',
          text: this.$t('common_today'),
          start: startOfToday(),
          end: endOfToday(),
          default: true,
        },
        {
          value: 'yesterday',
          text: this.$t('common_daterangeYesterday'),
          start: startOfYesterday(),
          end: endOfYesterday(),
          default: true,
        },
        {
          value: 'last7Days',
          text: this.$t('common_p7d'),
          start: startOfDay(subDays(new Date(), 6)),
          end: endOfToday(),
        },
        {
          value: 'last14Days',
          text: this.$t('common_p14d'),
          start: startOfDay(subDays(new Date(), 13)),
          end: endOfToday(),
        },
      ],
    };
  },

  mounted() {
    if (this.initialDataFetch) {
      this.fetchData();
    }
  },

  methods: {
    async fetchData() {
      this.$refs.historyList.getData();
      this.loading = true;

      const response = await this.axios.get(`/cm/alarm/by_serials/${this.serialNumber}`);
      this.alarms = response.data[0].alarms;

      this.loading = false;
    },
    onDateRangeChange({
      from,
      to,
    }) {
      const dateFromArray = from.split(' ');
      const dateFrom = dateFromArray[0];
      this.dateFrom = dateFrom;
      const dateToArray = to.split(' ');
      const dateTo = dateToArray[0];
      this.dateTo = dateTo;
      this.dates = [this.dateFrom, this.dateTo];
    },
  },
};
</script>
