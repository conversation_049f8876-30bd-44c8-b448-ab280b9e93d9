<template>
  <widget
    v-show="show"
    :loading="loader"
    :title="$t('dashboard_yourCarwash')"
    title-class="title title-widget pr-0 pl-2"
    class="mb-3 mr-0"
  >
    <v-data-table
      v-resize="onResize"
      class="grey--text text--darken-1"
      :headers="dataTable.headers"
      :items="items"
      item-key="serialNumber"
      :loading="loader"
      :footer-props="dataTable.footerProps"
      :hide-default-footer="true"
      mobile-breakpoint="0"
      :single-expand="true"
      :disable-pagination="true"
    >
      <template #progress>
        <div class="text-center">
          <v-progress-circular
            class="loader"
            indeterminate
            color="primary"
          />
        </div>
      </template>

      <template #item="{ item, expand: expandSub, isExpanded }">
        <template v-if="!loader">
          <tr @click="expandSub(!isExpanded)">
            <td class="text-start">
              {{ item.name }}
              <span
                v-if="item.address"
                class="grey--text text--darken-3"
              ><br>{{ item.address }}</span>
            </td>
            <td class="text-left">
              <v-tooltip bottom>
                <template #activator="{ on }">
                  <span
                    :key="item.sn"
                    v-on="on"
                  >
                    <blurred-formatter :value="item.ip" />
                  </span>
                </template>
                <span>{{ $t('dashboard_lastActualizaction') }} {{
                  item.lastContact !== null
                    ? dateInTimezone(item.lastContact)
                    : '-'
                }}</span>
              </v-tooltip>
            </td>
            <td class="text-start hidden-md-and-down">
              {{ item.software !== null ? item.software : '-' }}
            </td>
            <td class="text-center">
              <v-tooltip bottom>
                <template #activator="{ on }">
                  <v-icon
                    :key="item.alarmId"
                    :color="getCarwashAlertColor(item.alarmsHighestLevel)"
                    v-on="on"
                  >
                    {{ getCarwashAlertIcon(item.alarmsHighestLevel) }}
                  </v-icon>
                </template>
                <span>{{ carwashAlarmsTooltip(item) }}</span>
              </v-tooltip>
            </td>
          </tr>
        </template>
      </template>

      <template #expanded-item="{ item }">
        <template v-if="!loader">
          <tr
            v-if="carwashHasAlarms(item)"
            style="border-bottom: 1px solid"
          >
            <td
              class="pl-0 pr-0"
              colspan="4"
            >
              <v-card flat>
                <v-simple-table
                  v-if="carwashHasAlarms(item)"
                  class="expand-table"
                  dense
                >
                  <template #default>
                    <tbody>
                      <tr>
                        <td>{{ $t('common_tableDate') }}</td>
                        <td>{{ $t('common_alarmId') }}</td>
                        <td>{{ $t('common_alarms') }}</td>
                        <td>{{ $t('common_alarmLevel') }}</td>
                      </tr>
                      <tr
                        v-for="alarm in item.alarmsInfo"
                        :key="alarm.alarmId"
                      >
                        <td class="no-white-space-wrap">
                          {{ alarm.created }}
                        </td>
                        <td class="text-sm-end">
                          {{ alarm.alarmId }}
                        </td>
                        <td>
                          <v-tooltip
                            v-if="alarm.docUrl"
                            bottom
                          >
                            <template #activator="{ on }">
                              <div v-on="on">
                                <v-btn
                                  :key="alarm.alarmId"
                                  class="pl-0 wrap-btn btn-tooltip"
                                  text
                                  elevation="0"
                                  :href="alarm.docUrl"
                                  target="_blank"
                                  small
                                  color="primary"
                                >
                                  {{ alarm.description }}
                                </v-btn>
                              </div>
                            </template>
                            <span>{{ $t('actions.show_doc') }}</span>
                          </v-tooltip>
                          <template v-else>
                            {{ alarm.description }}
                          </template>
                        </td>
                        <td class="text-sm-center">
                          <v-tooltip bottom>
                            <template #activator="{ on }">
                              <v-icon
                                :key="alarm.alarmId"
                                :color="getCarwashAlertColor(alarm.level)"
                                v-on="on"
                              >
                                {{ getCarwashAlertIcon(alarm.level) }}
                              </v-icon>
                            </template>
                            <span>{{ alarm.level }}</span>
                          </v-tooltip>
                        </td>
                      </tr>
                    </tbody>
                  </template>
                </v-simple-table>
              </v-card>
            </td>
          </tr>
        </template>
      </template>
    </v-data-table>
  </widget>
</template>

<script>

import { mapGetters } from 'vuex';
// import WidgetPeriodSelect from '@components/common/filters/WidgetPeriodSelect.vue';
import Widget from '@components/common/Widget.vue';
import CarwashAlertTypeMixin
  from '@components/common/badge/icon-text-mixin/CarwashAlertTypeMixin.vue';
import BlurredFormatter from '@components/common/formatters/BlurredFormatter.vue';

export default {
  components: {
    BlurredFormatter,
    Widget,
  },
  mixins: [
    CarwashAlertTypeMixin,
  ],
  data() {
    return {
      carwashes: [],
      expand: false,
      clickable: false,
      loader: false,
      dataTable: {
        headers: [
          {
            text: this.$t('common_name'),
            value: 'name',
            showInRowExpand: false,
            sortable: false,
          },
          {
            text: 'IP',
            value: 'ip',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_software'),
            value: 'software',
            class: 'hidden-md-and-down',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_alarms'),
            value: 'alarmsStatus',
            align: 'center',
            showInRowExpand: true,
            sortable: false,
          },
        ],
        items: [],
      },
    };
  },
  computed: {
    show() {
      return this.hasRole('ROLE_CM_ALARMS_AND_TECHNICAL_DATA');
    },
    items() {
      // TODO: fix this hack
      return Object.values(this.carwashes);
    },
    ...mapGetters({
      hasRole: 'auth/hasRole',
      timezone: 'auth/userTimezone',
    }),
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      this.loader = true;
      this.axios.get('/cm/dashboard-widgets/carwashes')
        .then((resp) => {
          if (resp.data !== undefined) {
            this.carwashes = resp.data.data;
          }
          this.loader = false;
        });
    },
    onResize() {
      this.windowWidth = window.innerWidth;
    },
    carwashHasAlarms: (carwash) => carwash.alarmsStatus === 'warning',
    carwashAlarmsTooltip(carwash) {
      if (!this.carwashHasAlarms(carwash)) {
        return this.$t('common_noAlarms');
      }

      return this.$t('actions.click_to_show_more_details');
    },
    dateInTimezone(utcDateString) {
      const utcDate = new Date(`${utcDateString}Z`);

      return utcDate.toLocaleString('pl', { timeZone: this.timezone });
    },
    expandRow(carwash) {
      if (this.carwashHasAlarms(carwash)) {
        // eslint-disable-next-line no-param-reassign
        carwash.expand = !carwash.expand;
      }
    },
  },
};
</script>

<style lang="css" scoped>

.expand-table {
  width: 100%
}

.no-white-space-wrap {
  white-space: nowrap;
}

.wrap-btn {
  display: contents;
  white-space: normal;
}
</style>
