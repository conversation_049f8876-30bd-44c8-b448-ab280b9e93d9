<template>
  <widget
    v-show="show"
    :loading="loader"
    :title="$t('common_title')"
    title-class="title title-widget pr-0 pl-2"
    class="mb-3 mr-0"
  >
    <template #titleActions>
      <widget-period-select
        v-model="filtering.dates"
      />
    </template>

    <programs-usage-bar-chart
      title=""
      :enable-export="false"
      :values="items"
      :loader="loader"
      :filtering="filtering"
    />
  </widget>
</template>

<script>
import { endOfToday, startOfDay, subDays } from 'date-fns';
import { mapGetters } from 'vuex';
import Widget from '@components/common/Widget.vue';
import DataFetchMixin from '@components/common/mixins/DataFetchMixin.vue';
import FiltersHandlingMixin from '@components/common/mixins/FiltersHandlingMixin.vue';
import ProgramsUsageBarChart from '@components/finance/program-usage/ProgramsUsageBarChart.vue';
import WidgetPeriodSelect from '@components/common/filters/WidgetPeriodSelect.vue';
import SettingsMixin from '@components/common/mixins/SettingsMixin.vue';
import DateOptionsMixins from '@components/common/mixins/DateOptionsMixins.vue';

export default {
  name: 'DashboardCarwashProgramsUsage',
  components: { WidgetPeriodSelect, ProgramsUsageBarChart, Widget },
  mixins: [
    SettingsMixin,
    DataFetchMixin,
    FiltersHandlingMixin,
    DateOptionsMixins,
  ],
  data() {
    return {
      first: true,
      settingsNamespace: 'dashboard:program-usage:dates',
      dataUrl: '/api/reports/data',
      filtering: {
        dates: {
          value: 'last7Days',
          text: this.$t('common_p7d'),
          from: startOfDay(subDays(new Date(), 6)),
          to: endOfToday(),
        },
      },
      defaultDates: {
        value: 'last7Days',
        text: this.$t('common_p7d'),
        from: startOfDay(subDays(new Date(), 6)),
        to: endOfToday(),
      },
    };
  },
  computed: {
    show() {
      return this.hasRole('ROLE_CM_FINANCE') && (
        this.hasRole('ROLE_SUBSCRIPTION_BASIC')
        || this.hasRole('ROLE_SUBSCRIPTION_PREMIUM')
      );
    },
    ...mapGetters({
      hasRole: 'auth/hasRole',
    }),
  },
  mounted() {
    this.getData();
  },
  methods: {
    parseApiResponseData(data) {
      this.items = data;
    },
    getParams() {
      if (this.first) {
        const settings = this.getSetts(this.settingsNamespace, this.defaultDates.value);
        this.filtering.dates = this.getDateItemByValue(settings);

        this.first = false;
      }

      this.setSetts(this.settingsNamespace, this.filtering.dates.value);

      return {
        params: {
          startDate: this.$options.filters.formatDateDay(this.filtering.dates.from),
          endDate: this.$options.filters.formatDateDay(this.filtering.dates.to),
          report: 'v2\\FinanceProgramsUsageDaily',
        },
      };
    },
  },
};
</script>
