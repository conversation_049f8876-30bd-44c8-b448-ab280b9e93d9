<template>
  <widget
    v-show="show"
    :loading="loader"
    :title="$t('dashboard_standsTurnover')"
    title-class="title title-widget pr-0 pl-2"
    class="mb-3 mr-0"
  >
    <template #titleActions>
      <widget-period-select
        v-model="period"
        :date-items="dateItems"
      />
    </template>
    <div
      v-show="noData"
      class="text-sm-center py-10"
    >
      <h4>{{ $t('common_noData') }}</h4>
    </div>
    <income-stand-candle-chart
      v-show="!noData"
      :items="dataTable.items"
      :currency-sym="currencySymbol"
    />
  </widget>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import SettingsMixin from '@components/common/mixins/SettingsMixin.vue';
import WidgetPeriodSelect from '@components/common/filters/WidgetPeriodSelect.vue';
import Widget from '@components/common/Widget.vue';
import IncomeStandCandleChart
  from '@components/dashboard/widgets/IncomeStandRace/IncomeStandCandleChart.vue';

export default {
  components: {
    IncomeStandCandleChart,
    Widget,
    WidgetPeriodSelect,
  },
  mixins: [
    SettingsMixin,
  ],
  data() {
    return {
      noData: true,
      currencySymbol: '',
      loader: true,
      dataTable: {
        headers: [
          {
            text: '',
            value: 'startDate',
            class: 'text-start',
            sortable: false,
          },
          {
            text: this.$t('dashboard_value'),
            value: 'endDate',
            class: 'text-end',
            sortable: false,
          },
        ],
        items: [
          [
            'sn',
            'minimum',
            'average',
            'maximum',
            'name',
          ],
          [
            0,
            0,
            0,
            0,
            '-',
          ],
        ],
      },
      dateItems: [
        {
          text: this.$t('common_last'),
          value: 'last',
        },
        {
          text: this.$t('common_now'),
          value: 'now',
        },
        {
          text: this.$t('common_yesterday'),
          value: 'yesterday',
        },
        {
          text: this.$t('common_p7d'),
          value: 'P7D',
        },
        {
          text: this.$t('common_p14d'),
          value: 'P14D',
        },
        {
          text: this.$t('common_sinceMonthStart'),
          value: 'since_month_start',
        },
        {
          text: this.$t('dashboard_previousMonth'),
          value: 'previous_month',
        },
      ],
      loading: false,
    };
  },
  computed: {
    show() {
      return this.hasRole('ROLE_SUBSCRIPTION_BASIC') && this.hasRole('ROLE_CM_FINANCE');
    },
    period: {
      get() {
        const value = this.getSetts('dashboard:incomestandrace:period', 'P7D');
        return this.dateItems.find((item) => item.value === value);
      },
      set(newValue) {
        this.setSetts('dashboard:incomestandrace:period', newValue.value);
        this.getData(newValue);
        return newValue;
      },
    },
    ...mapGetters({
      hasRole: 'auth/hasRole',
      getSettings: 'settings/findByNamespace',
    }),
  },
  mounted() {
    this.loader = true;
    if (this.hasRole('ROLE_SUBSCRIPTION_BASIC') && this.hasRole('ROLE_CM_FINANCE')) {
      this.getData(this.period);
    }
  },
  methods: {
    ...mapActions({
      modifySettings: 'settings/modifySettings',
    }),
    onResize() {
      this.windowWidth = window.innerWidth;
    },
    toChartData(data) {
      const keys = Object.keys(data[0]);
      const result = data.map((x) => Object.values(x));

      result.unshift(keys);
      return result;
    },
    getData(period) {
      this.loader = true;
      this.axios.get(
        '/api/bay/stats',
        {
          params: {
            period: period.value,
          },
        },
      )
        .then((response) => {
          if (response.data.data.length > 0) {
            this.noData = false;
            this.dataTable.items = this.toChartData(response.data.data);
          } else {
            this.noData = true;
            this.dataTable.items = [
              [
                'sn',
                'minimum',
                'average',
                'maximum',
                'name',
              ],
              [
                0,
                0,
                0,
                0,
                '-',
              ],
            ];
          }
          this.currencySymbol = response.data.currencySymbol;
          this.loader = false;
        });
    },
  },
};
</script>
