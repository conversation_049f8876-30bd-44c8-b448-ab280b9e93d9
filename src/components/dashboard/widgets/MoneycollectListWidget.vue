<template>
  <widget
    v-show="show"
    :loading="loader"
    :title="$t('dashboard.moneycollect.title')"
    title-class="title pr-0 pl-2"
    class="mb-3 mr-0"
  >
    <v-data-table
      key="dashboard-moneycollect"
      v-resize="onResize"
      class="grey--text text--darken-1"
      :headers="dataTable.headers"
      :items="tableItems"
      item-key="id"
      :loading="loader"
      :footer-props="dataTable.footerProps"
      :hide-default-footer="true"
      mobile-breakpoint="0"
    >
      <template #progress>
        <div class="text-center">
          <v-progress-circular
            class="loading"
            indeterminate
            color="primary"
          />
        </div>
      </template>

      <template #item="{ item }">
        <template v-if="!loader">
          <tr>
            <td class="text-start">
              {{ item.at }}
            </td>
            <td class="text-start">
              {{ item.carwashName }}
            </td>
            <td class="text-start">
              {{ item.type }}
            </td>
            <td class="text-end">
              {{ item.sum }}
            </td>
          </tr>
        </template>
      </template>
    </v-data-table>
  </widget>
</template>

<script>
import { mapGetters } from 'vuex';
import Widget from '@components/common/Widget.vue';

export default {
  components: {
    Widget,
  },
  data() {
    return {
      period: 'P7D',
      loader: false,
      results: [],
      trans: {
        turnover_tooltip: 'help.turnover-since-last-money-collect',
      },
      dataTable: {
        footerProps: {
          'items-per-page-options': [100],
          'items-per-page-text': this.$t('common_rowsPerPage'),
        },
        headers: [
          {
            text: this.$t('common_date'),
            value: 'data',
            align: 'left',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('dashboard.moneycollect.carwash'),
            value: 'name',
            align: 'left',
            showInRowExpand: false,
            sortable: false,
          },
          {
            text: this.$t('common_type'),
            value: 'type',
            align: 'left',
            showInRowExpand: false,
            sortable: false,
          },
          {
            text: this.$t('dashboard_sum'),
            value: 'sums',
            align: 'right',
            showInRowExpand: true,
            sortable: false,
          },
        ],
        items: [],
      },
    };
  },
  computed: {
    show() {
      return this.hasRole('ROLE_CM_FINANCE') && (
        this.hasRole('ROLE_SUBSCRIPTION_BASIC') || this.hasRole('ROLE_SUBSCRIPTION_PREMIUM'));
    },
    ...mapGetters({
      hasRole: 'auth/hasRole',
      currency: 'auth/userCurrencySymbol',
    }),
    resultIsEmpty() {
      return this.results.length > 0;
    },
    tableItems() {
      return this.dataTable.items.map((item) => ({
        type: this.$t(`dashboard.moneycollect.${item.source}`),
        sum: item.sum ? `${item.sum.toFixed(2)} ${this.currency}` : '-',
        carwashName: item.carwashName,
        at: item.at ? this.$options.filters.formatDateDayTime(item.at) : '-',
      }));
    },
  },
  mounted() {
    this.loader = true;
    this.getData();
  },
  methods: {
    onResize() {
      this.windowWidth = window.innerWidth;
    },
    getData() {
      if (!this.show) {
        return;
      }

      this.loader = true;
      this.axios.get(
        '/cm/dashboard-widgets/money_collect',
      )
        .then((response) => {
          this.loader = false;
          this.dataTable.items = Object.values(response.data.data);
        });
    },
  },
};
</script>
