<template>
  <widget
    v-show="show"
    :loading="loader"
    :title="$t('fiscal_transactions.heading')"
    title-class="title title-widget pr-0 pl-2"
    class="mb-3 mr-0"
  >
    <template #titleActions>
      <widget-period-select
        v-model="period"
        :date-items="dateItems"
      />
    </template>
    <div
      v-if="!loader"
    >
      <v-data-table
        :headers="dataTable.headers"
        :items="dataTable.items"
        item-key="number"
        :no-data-text="$t('common_noData')"
        class="text-uppercase"
        :hide-default-footer="true"
        mobile-breakpoint="0"
      >
        <template #item="{ item}">
          <template v-if="!loader">
            <tr
              v-for="(row, index) in item"
              :key="index"
            >
              <td class="text-start">
                {{ index }}
              </td>
              <td class="text-end">
                {{ row }}
              </td>
            </tr>
          </template>
        </template>
      </v-data-table>
    </div>
    <div
      v-if="!loader"
      class="pt-2 pb-3 text-right"
    >
      <h2>{{ $t('dashboard_summary') }}: {{ sum }}</h2>
    </div>
  </widget>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import SettingsMixin from '@components/common/mixins/SettingsMixin.vue';
import Widget from '@components/common/Widget.vue';
import WidgetPeriodSelect from '@components/common/filters/WidgetPeriodSelect.vue';

export default {
  components: {
    WidgetPeriodSelect,
    Widget,
  },
  mixins: [
    SettingsMixin,
  ],
  data() {
    return {
      dataTable: {
        headers: [
          {
            text: this.$t('fiscal_transactions.table.fiscal'),
            value: 'startDate',
            class: 'text-start',
            sortable: false,
          },
          {
            text: this.$t('fiscal_transactions.table.value'),
            value: 'endDate',
            class: 'text-end',
            sortable: false,
          },
        ],
        items: [],
      },
      dateItems: [
        {
          text: this.$t('common_now'),
          value: 'now',
        },
        {
          text: this.$t('common_yesterday'),
          value: 'yesterday',
        },
        {
          text: this.$t('common_p7d'),
          value: 'P7D',
        },
        {
          text: this.$t('common_p14d'),
          value: 'P14D',
        },
        {
          text: this.$t('common_sinceMonthStart'),
          value: 'since_month_start',
        },
        {
          text: this.$t('dashboard_previousMonth'),
          value: 'previous_month',
        },
      ],
      loader: false,
      sum: null,
    };
  },
  computed: {
    show() {
      return this.hasRole('ROLE_CM_FINANCE') && this.hasRole('ROLE_SUBSCRIPTION_PREMIUM');
    },
    period: {
      get() {
        const value = this.getSetts('dashboard:fiscal:period', 'P7D');
        return this.dateItems.find((item) => item.value === value);
      },
      set(newValue) {
        this.setSetts('dashboard:fiscal:period', newValue.value);
        this.getData(newValue);
        return newValue;
      },
    },
    ...mapGetters({
      hasRole: 'auth/hasRole',
      getSettings: 'settings/findByNamespace',
    }),
  },
  mounted() {
    this.loader = true;
    if (this.hasRole('ROLE_SUBSCRIPTION_PREMIUM') && this.hasRole('ROLE_CM_FINANCE')) {
      this.getData(this.period);
    }
  },
  methods: {
    ...mapActions({
      modifySettings: 'settings/modifySettings',
    }),
    getData(period) {
      this.loader = true;
      this.axios.get(
        '/cm/dashboard-widgets/fiscal',
        {
          params: {
            period: period.value,
          },
        },
      )
        .then((response) => {
          this.dataTable.items[0] = response.data.data;
          this.sum = response.data.sum;
          this.loader = false;
        });
    },
  },
};
</script>
