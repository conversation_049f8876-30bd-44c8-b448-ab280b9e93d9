<template>
  <widget
    v-show="show"
    :loading="loader"
    :title="$t('dashboard_header')"
    title-class="title title-widget pr-0 pl-2"
    class="mb-3 mr-0"
  >
    <template #titleActions>
      <widget-period-select
        v-model="filtering.dates"
      />
    </template>

    <payments-share-pie-chart
      :loader="loader"
      :items="pieChartsItems"
      :currency-symbol="currency"
      title=""
    />
  </widget>
</template>

<script>
import { endOfToday, startOfDay, subDays } from 'date-fns';
import WidgetPeriodSelect from '@components/common/filters/WidgetPeriodSelect.vue';
import Widget from '@components/common/Widget.vue';
import { mapGetters } from 'vuex';
import FiltersHandlingMixin from '@components/common/mixins/FiltersHandlingMixin.vue';
import PaymentsSharePieChart from '@components/finance/turnover/monthly/PaymentsSharePieChart.vue';
import WidgetDataFetchMixin from '@components/finance/turnover/mixins/WidgetDataFetchMixin.vue';

export default {
  name: 'DashboardPaymentsShareChart',
  components: { PaymentsSharePieChart, Widget, WidgetPeriodSelect },
  mixins: [
    WidgetDataFetchMixin,
    FiltersHandlingMixin,
  ],
  data() {
    return {
      lock: true,
      settingsNamespace: 'finance:dates',
      dataUrl: '/cm/dashboard-widgets/carwashes/payments-share',
      filtering: {
        dataSource: {
          value: '',
          text: '',
          dataUrl: '/api/reports/data',
          lastCollectionUrl: '',
          dateFormatFunc: this.$options.filters.formatDateDay,
          exportOptions: [],
        },
        dates: {
          value: 'last7Days',
          text: this.$t('common_p7d'),
          from: startOfDay(subDays(new Date(), 6)),
          to: endOfToday(),
        },
      },
    };
  },
  computed: {
    show() {
      return this.hasRole('ROLE_CM_FINANCE') && (
        this.hasRole('ROLE_SUBSCRIPTION_BASIC') || this.hasRole('ROLE_SUBSCRIPTION_PREMIUM'));
    },
    ...mapGetters({
      currency: 'auth/userCurrencySymbol',
      hasRole: 'auth/hasRole',
    }),
    pieChartsItems() {
      return this.items[0];
    },
  },
  mounted() {
    this.lock = !this.show;
    if (this.show) {
      this.getData();
    }
  },
  methods: {
    getParams() {
      // if (
      //   this.settingsNamespace !== undefined
      //   && this.filtering.dates.value !== undefined
      //   && this.filtering.dates.value !== 'custom'
      // ) {
      //   let settings = this.filtering.dates.value;
      //   if (this.first && this.filtering.dates.value !== undefined) {
      //     settings = this.getSetts(this.settingsNamespace, this.filtering.dates.value);
      //     this.filtering.dates = this.getDateItemByValue(settings);
      //     this.first = false;
      //   }
      //
      //   this.$set(this.filtering.dates, 'value', settings);
      //
      //   this.setSetts(this.settingsNamespace, this.filtering.dates.value);
      // }

      return {
        params: {
          startDate: this.$options.filters.formatDateDay(this.filtering.dates.from),
          endDate: this.$options.filters.formatDateDay(this.filtering.dates.to),
        },
      };
    },
  },
};
</script>
