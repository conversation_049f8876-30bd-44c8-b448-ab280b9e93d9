<template>
  <widget
    v-show="show"
    :loading="loader"
    :title="$t('common_financeTurnover')"
    title-class="title title-widget pr-0 pl-2"
    class="mb-3 mr-0"
  >
    <template #titleActions>
      <widget-period-select
        v-model="period"
        :date-items="dateItems"
      />
    </template>

    <div
      v-if="!loader"
      class="summaryBorder pt-4 pb-3 mb-3 text-right"
    >
      <h2>{{ $t('dashboard_summary') }}: {{ sum }}</h2>
    </div>
    <div
      class="wrapperClass"
      :style="{
        'height': `${computeHeight}px`
      }"
    >
      <v-chart
        v-if="!loader"
        ref="chart"
        :option="echart"
        :autoresize="true"
      />
    </div>
  </widget>
</template>

<script>
import { BarChart } from 'echarts/charts';
import { use } from 'echarts/core';
import { CanvasRenderer } from 'echarts/renderers';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent,
} from 'echarts/components';
import VChart from 'vue-echarts';
import { mapGetters, mapActions } from 'vuex';
import SettingsMixin from '@components/common/mixins/SettingsMixin.vue';
import Widget from '@components/common/Widget.vue';
import WidgetPeriodSelect from '@components/common/filters/WidgetPeriodSelect.vue';

use([
  CanvasRenderer,
  GridComponent,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
]);

export default {
  components: {
    WidgetPeriodSelect,
    Widget,
    VChart,
  },
  mixins: [
    SettingsMixin,
  ],
  data() {
    return {
      dateItems: [
        {
          text: this.$t('common_last'),
          value: 'last',
        },
        {
          text: this.$t('common_now'),
          value: 'now',
        },
        {
          text: this.$t('common_yesterday'),
          value: 'yesterday',
        },
        {
          text: this.$t('common_p7d'),
          value: 'P7D',
        },
        {
          text: this.$t('common_p14d'),
          value: 'P14D',
        },
        {
          text: this.$t('common_sinceMonthStart'),
          value: 'since_month_start',
        },
        {
          text: this.$t('dashboard_previousMonth'),
          value: 'previous_month',
        },
      ],
      loader: true,
      carwashCount: 0,
      trans: {
        turnover_tooltip: 'help.turnover-since-last-money-collect',
      },
      sum: null,
      echart: {
        grid: {
          left: '2%',
          right: '2%',
          bottom: 0,
          top: 0,
          containLabel: true,
        },
        tooltip: {
          show: false,
        },
        xAxis: [
          {
            show: false,
            type: 'value',
            max: 'dataMax',
          },
        ],
        yAxis: {
          triggerEvent: true,
          type: 'category',
          data: [],
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
        },
        series: [],
        animationDuration: 300,
        legend: {
          show: false,
        },
      },
    };
  },
  computed: {
    computeHeight() {
      return this.carwashCount * 50;
    },
    show() {
      return this.hasRole('ROLE_CM_FINANCE') && (
        this.hasRole('ROLE_SUBSCRIPTION_BASIC')
        || this.hasRole('ROLE_SUBSCRIPTION_PREMIUM')
      );
    },
    period: {
      get() {
        const value = this.getSetts('dashboard:turnover:period', 'P7D');
        return this.dateItems.find((item) => item.value === value);
      },
      set(newValue) {
        this.setSetts('dashboard:turnover:period', newValue.value);
        this.getData(newValue);
        return newValue;
      },
    },
    ...mapGetters({
      hasRole: 'auth/hasRole',
      getSettings: 'settings/findByNamespace',
    }),
  },
  mounted() {
    this.loader = true;
    this.getData(this.period);
  },
  methods: {
    ...mapActions({
      modifySettings: 'settings/modifySettings',
    }),
    onResize() {
      this.windowWidth = window.innerWidth;
    },
    getData(period) {
      if (!this.show) {
        return;
      }

      this.loader = true;
      this.axios.get(
        '/cm/dashboard-widgets/carwashes/turnover',
        {
          params: {
            period: period.value,
          },
        },
      )
        .then((response) => {
          this.sum = response.data.sums.all;
          const carwashes = response.data.data.sort(
            (a, b) => -1 * (b.turnoverValue - a.turnoverValue),
          );

          const series = {
            color: '#3B81E3',
            type: 'bar',
            data: [],
            barGap: 5,
            label: {
              show: true,
              position: 'insideLeft',
              formatter: (param) => param.data.label,
            },
            markLine: 1,
          };

          this.echart.yAxis.data = [];

          this.carwashCount = carwashes.length;

          carwashes.map((carwash) => {
            const {
              turnover, turnoverValue, name,
            } = carwash;

            series.data.push(
              {
                label: turnover,
                value: turnoverValue,
              },
            );

            let legendName = name;
            if (legendName.length > 30) {
              legendName = `${name.slice(0, 30)} ...`;
            }

            this.echart.yAxis.data.push(legendName);

            return true;
          });

          this.echart.series = [series];

          this.loader = false;
        });
    },
  },
};
</script>

<style lang="css" scoped>
.wrapperClass {
  min-height: 175px;
}

.summaryBorder {
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 2px;
}

</style>
