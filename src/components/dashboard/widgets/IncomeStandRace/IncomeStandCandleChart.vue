<template>
  <div
    :class="wrapperClass"
    :style="{
      'height': `${computeHeight}px`
    }"
  >
    <v-chart
      ref="chart"
      class="chart"
      :option="option"
      autoresize
    />
  </div>
</template>
<script>
import VChart from 'vue-echarts';

import { registerTransform, use } from 'echarts/core';
import * as ecSimpleTransform from 'echarts-simple-transform';

import {
  DatasetComponent,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  DataZoomComponent,
  TransformComponent,
} from 'echarts/components';
import { Boxplot<PERSON><PERSON>, ScatterChart } from 'echarts/charts';
import { UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';
import * as Sentry from '@sentry/vue';

use([
  DatasetComponent,
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  DataZoomComponent,
  TransformComponent,
  Boxplot<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CanvasRenderer,
  UniversalTransition,
]);

registerTransform(ecSimpleTransform.aggregate);

export default {
  name: 'IncomeStandCandleChart',
  components: {
    VChart,
  },
  props: {
    title: {
      type: String,
      default() {
        return this.$t('common_paymenttypesharepieTitle');
      },
    },
    wrapperClass: {
      type: [String, Object],
      default: '',
    },
    loader: {
      type: Boolean,
      default: false,
    },
    currencySym: {
      type: String,
      default: 'C',
    },
    items: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  computed: {
    computeHeight() {
      if (this.items == null) {
        return 30;
      }
      if (this.items.length === 0) {
        return 400;
      }
      return this.items.length * 35;
    },
    option() {
      return {
        dataset: [
          {
            id: 'raw',
            source: this.items,
          },
          {
            id: 'income_bays_aggregate',
            fromDatasetId: 'raw',
            transform: [
              {
                type: 'ecSimpleTransform:aggregate',
                config: {
                  resultDimensions: [
                    { name: 'min', from: 'minimum' },
                    { name: 'q1', from: 'minimum' },
                    { name: 'average', from: 'average' },
                    { name: 'q3', from: 'maximum' },
                    { name: 'max', from: 'maximum' },
                    { name: 'sn', from: 'sn' },
                    { name: 'name', from: 'name' },
                  ],
                  groupBy: 'sn',
                },
              },
              {
                type: 'sort',
                config: {
                  dimension: 'average',
                  order: 'asc',
                },
              },
            ],
          },
        ],
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            let message = '';
            message += `<strong>${params[0].axisValue}</strong>`;

            message += `<br/>${params[0].marker}${params[0].seriesName}:`;

            message += `<br/>${this.$t('dashboard_min')}: ${this.currencySymbol(params[0].value[0], this.currencySym)}`;
            message += `<br/>${this.$t('dashboard_average')}: ${this.currencySymbol(params[0].value[2], this.currencySym)}`;
            message += `<br/>${this.$t('dashboard_max')}: ${this.currencySymbol(params[0].value[4], this.currencySym)}`;

            return message;
          },
        },
        xAxis: {
          name: this.$t('dashboard_standsTurnover'),
          nameLocation: 'middle',
          nameGap: 30,
          scale: true,
          axisLabel: {
            formatter: (value) => `${this.formatNumber(value)}`,
          },
        },
        yAxis: {
          type: 'category',
          nameGap: 130,
          nameLocation: 'left',
          scale: true,
        },
        grid: {
          left: 230,
          bottom: 50,
          top: 20,
        },
        series: [
          {
            name: this.$t('dashboard_standsTurnover'),
            type: 'boxplot',
            datasetId: 'income_bays_aggregate',
            itemStyle: {
              color: '#2176db',
              borderColor: '#164281',
              borderWidth: 2,
            },
            encode: {
              x: [this.$t('dashboard_standsTurnover')],
              y: 'name',
              itemName: ['name'],
              tooltip: ['min', 'average', 'max'],
            },
          },
        ],
      };
    },
  },
  methods: {
    currencySymbol(value, currency) {
      if (currency === undefined || currency === null) {
        try {
          throw new Error('Currency symbol undefined in currencySymbol filter');
        } catch (err) {
          Sentry.captureException(err);
        }

        return `${Number(value).toFixed(2)} undefined`;
      }

      if (value === null) {
        return '-';
      }

      return `${Number(value).toFixed(2)} ${currency}`;
    },
    formatNumber(value) {
      const formatter = new Intl.NumberFormat('pl', {
        style: 'decimal',
      });

      return formatter.format(value);
    },
  },
};
</script>
