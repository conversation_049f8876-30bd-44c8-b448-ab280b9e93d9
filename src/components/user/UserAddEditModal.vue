<template>
  <v-dialog
    v-model="dialog"
    scrollable
    content-class="dialog-3"
  >
    <v-card>
      <v-card-title class="title">
        <span class="headline">
          <h5 class="text-uppercase">
            <template v-if="modalAction == 'edit'">
              {{ $t('actions.edit_user') }} {{ initiationEmail }}
            </template>
            <template v-else>
              {{ $t('actions.add_user') }}
            </template>
          </h5>
        </span>
      </v-card-title>
      <v-progress-linear
        v-if="loaders.site"
        :indeterminate="true"
        class="mt-0"
      />
      <v-card-text class="pt-6">
        <div class="text-center">
          <v-progress-circular
            v-if="loaders.site"
            class="circleProgress"
            :size="90"
            :width="7"
            color="primary"
            indeterminate
          />
        </div>
        <template v-if="!loaders.site">
          <v-container grid-list-md>
            <v-alert
              v-show="userExists"
              :value="true"
              type="error"
              border="left"
              :color="'red'"
              icon="mdi-alert"
            >
              <v-layout wrap>
                <v-col
                  cols="12"
                  sm="12"
                  md="12"
                >
                  {{ $t('common_userEmailAlreadyExist') }}
                </v-col>
              </v-layout>
            </v-alert>
            <v-form
              ref="formUserEdit"
              v-model="form.valid"
              lazy-validation
            >
              <v-layout wrap>
                <v-col
                  sm="12"
                  class="pt-0 pb-0"
                >
                  <v-text-field
                    v-model="user.email"
                    v-validate="'required|email|max:64|min:6'"
                    :label="$t('common_email')"
                    prepend-icon="mdi-email"
                    :data-vv-as="`${$t('common_email')}`"
                    :counter="64"
                    name="email"
                    :disabled="modalAction == 'edit'"
                    :error-messages="errors.collect('email')"
                    required
                    @change="setLoadingAndCheckUserExists"
                  />
                </v-col>
                <v-col
                  sm="12"
                  class="pt-0 pb-0"
                >
                  <v-autocomplete
                    id="carwashes"
                    v-model="user.carwashes"
                    v-validate="'required|min:1'"
                    item-value="id"
                    item-text="longName"
                    :label="$t('common_carwashes')"
                    :items="carwashes"
                    :autocomplete="true"
                    prepend-icon="mdi-car-wash"
                    multiple
                    chips
                    name="carwashes"
                    :data-vv-as="`${$t('common_carwashes')}`"
                    :error-messages="errors.collect('carwashes')"
                    required
                    :rules="rules.selectRequired2"
                  >
                    <template #selection="{ item, index }">
                      <v-chip v-if="index < 2">
                        <span>{{ item.longName }}</span>
                      </v-chip>
                      <span
                        v-if="index === 3"
                        class="grey--text caption"
                      >
                        (+{{ user.carwashes.length - 2 }} {{ $t('user_others') }})
                      </span>
                    </template>
                  </v-autocomplete>
                </v-col>
                <v-col
                  sm="12"
                  class="pt-0 pb-0"
                >
                  <v-autocomplete
                    id="roles"
                    v-model="user.roles"
                    v-validate="'required|min:1'"
                    item-value="id"
                    item-text="text"
                    :label="$t('common_roles')"
                    :items="rolesOptions"
                    :autocomplete="true"
                    prepend-icon="mdi-account-key"
                    multiple
                    chips
                    name="roles"
                    :data-vv-as="`${$t('common_roles')}`"
                    :error-messages="errors.collect('roles')"
                    required
                    :rules="rules.selectRequired2"
                  >
                    <template #selection="{ item, index }">
                      <v-chip v-if="index < 4">
                        <span>{{ item.text }}</span>
                      </v-chip>
                      <span
                        v-if="index === 5"
                        class="grey--text caption"
                      >
                        (+ {{ $t('user_others') }})
                      </span>
                    </template>
                  </v-autocomplete>
                </v-col>
                <v-col
                  sm="12"
                  class="pt-0 pb-0"
                >
                  <v-text-field
                    v-model="user.firstname"
                    v-validate="'required|alpha_spaces|max:32'"
                    :label="$t('common_firstName')"
                    prepend-icon="mdi-account-details"
                    name="firstname"
                    :data-vv-as="`${$t('common_firstName')}`"
                    :error-messages="errors.collect('firstname')"
                    required
                  />
                </v-col>
                <v-col
                  sm="12"
                  class="pt-0 pb-0"
                >
                  <v-text-field
                    v-model="user.lastname"
                    v-validate="'required|alpha_spaces|max:64'"
                    :label="$t('common_lastName')"
                    prepend-icon="mdi-account-details"
                    name="lastname"
                    :data-vv-as="`${$t('common_lastName')}`"
                    :error-messages="errors.collect('lastname')"
                    required
                  />
                </v-col>
                <v-col
                  sm="12"
                  class="pt-0 pb-0"
                >
                  <v-text-field
                    v-model="user.phone"
                    :label="$t('common_phone')"
                    prepend-icon="mdi-phone"
                    name="phone"
                    :data-vv-as="`${$t('common_phone')}`"
                    :error-messages="errors.collect('phone')"
                  />
                </v-col>
                <v-col
                  sm="12"
                  class="pt-0 pb-0"
                >
                  <v-autocomplete
                    v-model="user.language.locale"
                    v-validate="'required|min:1'"
                    item-value="locale"
                    item-text="name"
                    :label="$t('common_language')"
                    :items="languagesOptions"
                    :autocomplete="true"
                    prepend-icon="mdi-web"
                    name="language"
                    :data-vv-as="`${$t('common_language')}`"
                    :error-messages="errors.collect('language')"
                    :rules="rules.selectRequired"
                    required
                  />
                </v-col>
                <v-col
                  sm="12"
                  class="pt-0 pb-0"
                >
                  <v-autocomplete
                    v-model="user.timezone.id"
                    v-validate="'required|min:1'"
                    item-value="id"
                    item-text="location"
                    :label="$t('common_timezone')"
                    :items="timezonesOptions"
                    :autocomplete="true"
                    prepend-icon="mdi-map-clock"
                    name="timezone"
                    :data-vv-as="`${$t('common_timezone')}`"
                    :error-messages="errors.collect('timezone')"
                    :rules="rules.selectRequired"
                    required
                  />
                </v-col>
              </v-layout>
            </v-form>
          </v-container>
        </template>
      </v-card-text>
      <v-card-actions v-if="!loaders.site">
        <v-spacer />
        <v-btn
          color="gray"
          text
          @click.native="closeDialog"
        >
          {{ $t('actions.return_to_list') }}
        </v-btn>
        <v-btn
          color="primary darken-1"
          :loading="loaders.actualize"
          :disabled="!form.valid || userExists"
          @click.native="submit"
        >
          <template v-if="modalAction == 'edit'">
            {{ $t('actions.save') }}
          </template>
          <template v-else>
            {{ $t('actions.add_user') }}
          </template>
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  props: {
    userNumber: {
      type: Number,
      default: null,
    },
    action: {
      type: String,
      default: 'add',
    },
    email: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      initiationEmail: this.email,
      currencies: [],
      roles: [],
      userExists: false,
      currencySymbol: 'PLN',
      modalAction: this.action,
      loaders: {
        site: true,
        actualize: false,
      },
      user: {
        number: this.userNumber,
        name: '',
        email: '',
        carwashes: [],
        firstname: '',
        lastname: '',
        roles: [],
        timezone: { id: 0 },
        language: { locale: 'en' },
        phnoe: '',
      },
      dialog: false,
      rolesOptions: [],
      currenciesOptions: [],
      carwashesOptions: [],
      languagesOptions: [],
      timezonesOptions: [],
      form: {
        cancelRefill: {
          valid: true,
        },
        validateOnBlur: true,
        valid: false,
      },
      rules: {
        selectRequired: [(v) => !!v || this.$t('common_fieldRequired')],
        selectRequired2: [(v) => v.length > 0 || this.$t('common_fieldRequired')],
      },
    };
  },
  computed: {
    ...mapGetters({
      carwashes: 'carwashes/carwashes',
    }),
  },
  watch: {
    dialog(val) {
      // get user data only when dialog shows up
      if (val) {
        this.loaders.actualize = false;
        this.resetValidationErrorsAndClearFields();
        this.getLanguges();
        this.getTimezones();
        this.getRoles();

        if (this.modalAction === 'edit') {
          this.getData();
        }
      }
    },
  },
  methods: {
    rolesToMultiselectFormat() {
      this.rolesOptions = this.multiselectHelper.toSelect(this.roles);
    },
    validateAll() {
      this.$refs.formUserEdit.validate();
    },
    resetValidationErrorsAndClearFields() {
      this.clearFormData();
      if (this.$refs.formUserEdit) {
        this.$refs.formUserEdit.reset();
        this.$refs.formUserEdit.resetValidation();
        this.$validator.reset();
      }
    },
    setLoadingAndCheckUserExists() {
      this.loaders.actualize = true;
      this.checkUserExists();
    },
    checkUserExists() {
      this.checkEmail(this.user.email);
    },
    getLanguges() {
      this.axios.get(
        '/api/lists/languages',
      )
        .then((response) => {
          this.languagesOptions = response.data.data;
        });
    },
    getTimezones() {
      this.axios.get(
        '/api/lists/timezones',
        {
          params: {
            perPage: 1000,
          },
        },
      )
        .then((response) => {
          this.timezonesOptions = response.data.data;
        });
    },
    getRoles() {
      this.axios.get(
        '/api/users/roles',
      )
        .then((response) => {
          this.roles = response.data;
          this.rolesToMultiselectFormat();
          this.loaders.site = false;
        });
    },
    checkEmail(userEmail) {
      if (userEmail.length <= 0) {
        return true;
      }
      this.loaders.actualize = true;

      this.axios.get(
        `/api/user/exists/${userEmail}/`,
        {
          config: {
            validateStatus: (status) => status >= 200 && status < 500,
          },
        },
      )
        .then(
          () => {
            this.userExists = true;
            this.loaders.actualize = false;
          },
          () => {
            this.userExists = false;
            this.loaders.actualize = false;
          },
        );
      return false;
    },
    getData() {
      this.loaders.site = true;
      this.loaders.actualize = false;
      this.axios.get(
        `/api/user/${this.user.number}`,
      )
        .then(
          (response) => {
            if ((response.status === 200) && response.data) {
              const carwashesArray = Object.values(response.data.carwashes).map(
                (obj) => obj.id,
              );

              this.user.email = response.data.email;
              this.user.firstname = response.data.firstname;
              this.user.lastname = response.data.lastname;
              this.user.carwashes = carwashesArray;
              this.user.phone = response.data.phone;
              this.user.roles = response.data.roles;
              this.user.timezone.id = response.data.timezone.id;
              this.user.language.locale = response.data.language.locale;
            }
            this.loaders.site = false;
          },
          () => {
            this.onError();
          },
        );
    },
    onError() {
      // on error
      this.closeDialog();
    },
    editUser() {
      this.$validator.validateAll();
      if (this.$refs.formUserEdit.validate()) {
        this.loaders.site = true;
        this.loaders.actualize = true;

        const parameters = {
          email: this.user.email,
          firstname: this.user.firstname,
          lastname: this.user.lastname,
          assignedCarwashes: this.user.carwashes,
          roles: this.user.roles,
          phone: this.user.phone,
          timezone: { id: this.user.timezone.id },
          language: { locale: this.user.language.locale },
        };

        const url = `/api/user/${this.user.number}`;

        this.axios.patch(
          url,
          parameters,
        )
          .then(
            (response) => {
              if (response.status === 200) {
                this.propagateUserUpdate();
                this.closeDialog();
              }
            },
            () => {
              // on error
              this.loaders.site = false;
              this.loaders.actualize = false;
            },
          );
      }
    },
    addUser() {
      this.$validator.validateAll();
      if (this.$refs.formUserEdit.validate()) {
        this.loaders.site = true;
        this.loaders.actualize = true;

        const parameters = {
          email: this.user.email,
          firstname: this.user.firstname,
          lastname: this.user.lastname,
          assignedCarwashes: this.user.carwashes,
          roles: this.user.roles,
          phone: this.user.phone,
          timezone: { id: this.user.timezone.id },
          language: { locale: this.user.language.locale },
        };

        this.axios.post(
          '/api/user',
          parameters,
        )
          .then(
            (response) => {
              if (response.status === 200) {
                this.propagateUserUpdate();
                this.closeDialog();
              }
            },
            () => {
              // on error
              this.loaders.site = false;
              this.loaders.actualize = false;
            },
          );
      }
    },
    submit() {
      if (this.modalAction === 'add') {
        this.addUser();
      } else {
        this.editUser();
      }
    },
    clearFormData() {
      this.user.email = '';
      this.user.carwashes = [];
      this.user.roles = [];
      this.user.firstname = '';
      this.user.lastname = '';
      this.user.phone = '';
      this.user.language.locale = 0;
      this.user.timezone.id = 0;
    },
    propagateUserUpdate() {
      this.$emit('update-user-list');
    },
    closeDialog() {
      this.resetValidationErrorsAndClearFields();
      this.dialog = false;
    },
  },
};
</script>

<style>
.dialog-3 {
  max-width: 700px;
}
</style>
