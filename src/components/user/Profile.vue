<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
  >
    <v-row>
      <loading-overlay v-if="loaders.site" />
      <v-col
        cols="12"
        class="text-sm-start"
      >
        <h2>
          <span>{{ $t('user_baseData') }}</span>
        </h2>
      </v-col>

      <v-form
        ref="formProfileSettings"
        v-model="form.valid"
        lazy-validation
      >
        <v-layout wrap>
          <v-col
            cols="12"
            sm="6"
            class="pt-0 pb-0"
          >
            <v-text-field
              v-model="profileData.email"
              v-validate="'required|email|max:64|min:6'"
              :label="$t('common_email')"
              prepend-icon="mdi-email"
              :data-vv-as="`${$t('common_email')}`"
              :counter="64"
              name="email"
              disabled
              readonly
              :error-messages="errors.collect('email')"
              required
            />
          </v-col>
          <v-col
            cols="12"
            sm="6"
            class="pt-0 pb-0"
          >
            <v-text-field
              v-model="profileData.phone"
              v-validate="''"
              :label="$t('common_phone')"
              prepend-icon="mdi-phone"
              name="phone"
              :data-vv-as="`${$t('common_phone')}`"
              :error-messages="errors.collect('phone')"
            />
          </v-col>
          <v-col
            cols="12"
            sm="6"
            class="pt-0 pb-0"
          >
            <v-text-field
              v-model="profileData.firstname"
              v-validate="'alpha_spaces|max:32'"
              :label="$t('common_firstName')"
              prepend-icon="mdi-account"
              name="firstName"
              :data-vv-as="`${$t('common_firstName')}`"
              :error-messages="errors.collect('firstName')"
            />
          </v-col>
          <v-col
            cols="12"
            sm="6"
            class="pt-0 pb-0 pl-4"
          >
            <v-text-field
              v-model="profileData.lastname"
              v-validate="'alpha_spaces|max:64'"
              :label="$t('common_lastName')"
              prepend-icon="mdi-account"
              name="lastName"
              :data-vv-as="`${$t('common_lastName')}`"
              :error-messages="errors.collect('lastName')"
            />
          </v-col>
          <v-col
            cols="12"
            class="text-sm-start"
          >
            <h2>
              <span>{{ $t('user_regionalSettings') }}</span>
            </h2>
          </v-col>
          <v-col
            cols="12"
            sm="6"
            class="pt-0 pb-0"
          >
            <v-autocomplete
              v-model="profileData.language.locale"
              v-validate="'required|min:1'"
              item-value="locale"
              item-text="name"
              :label="$t('common_language')"
              :items="languagesOptions"
              :autocomplete="true"
              prepend-icon="mdi-web"
              name="language"
              :data-vv-as="`${$t('common_language')}`"
              :error-messages="errors.collect('language')"
              required
            />
          </v-col>
          <v-col
            cols="12"
            sm="6"
            class="pt-0 pb-0"
          >
            <v-autocomplete
              v-model="profileData.timezone.id"
              v-validate="'required|min:1'"
              item-value="id"
              item-text="location"
              :label="$t('common_timezone')"
              :items="timezonesOptions"
              :autocomplete="true"
              prepend-icon="mdi-clock-outline"
              name="timezone"
              :data-vv-as="`${$t('common_timezone')}`"
              :error-messages="errors.collect('timezone')"
              required
            />
          </v-col>
          <v-col
            cols="12"
            align="right"
            class="pt-0"
          >
            <v-btn
              color="primary darken-1"
              :loading="loaders.update"
              :disabled="!form.valid"
              @click.native="submit"
            >
              {{ $t('actions.update') }}
            </v-btn>
          </v-col>
        </v-layout>
      </v-form>
    </v-row>
  </v-container>
</template>

<script>
import LoadingOverlay from '@components/common/LoadingOverlay.vue';
import { mapGetters, mapActions } from 'vuex';

export default {
  name: 'UserProfile',
  components: {
    LoadingOverlay,
  },
  data() {
    return {
      languagesOptions: [],
      timezonesOptions: [],
      loaders: {
        site: false,
        update: false,
      },
      form: {
        cancelRefill: {
          valid: true,
        },
        validateOnBlur: true,
        valid: false,
      },
      profileData: {
        phone: '',
        firstName: '',
        lastName: '',
        email: '',
        timezone: {
          id: null,
        },
        language: {
          locale: null,
        },
      },
    };
  },
  computed: {
    ...mapGetters({
      user: 'auth/getUser',
    }),
  },
  mounted() {
    // todo: get from store
    this.getLanguages();
    this.getTimezones();

    this.getData();
  },
  methods: {
    ...mapActions({
      changeLocale: 'auth/changeLanguage',
      initUser: 'auth/initUser',
    }),
    getLanguages() {
      this.axios.get(
        '/api/lists/languages',
      )
        .then((response) => {
          this.languagesOptions = response.data.data;
        });
    },
    getTimezones() {
      this.axios.get(
        '/api/lists/timezones',
        {
          params: {
            perPage: 1000,
          },
        },
      )
        .then((response) => {
          this.timezonesOptions = response.data.data;
        });
    },
    getData() {
      this.profileData = {
        phone: this.user.phone,
        firstname: this.user.firstname,
        lastname: this.user.lastname,
        email: this.user.email,
        timezone: {
          id: this.user.timezone.id,
        },
        language: {
          locale: this.user.language.locale,
        },
      };
    },
    async submit() {
      this.loaders.actualize = true;
      this.loaders.site = true;
      await this.axios.patch(
        '/api/profile',
        {
          ...this.profileData,
        },
      );

      await this.initUser();

      this.loaders.actualize = false;
      this.loaders.site = false;
    },
  },
};
</script>
