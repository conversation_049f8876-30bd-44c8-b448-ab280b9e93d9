<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
  >
    <v-row>
      <loading-overlay v-if="loaders.site" />
      <v-form
        ref="formReportsSettings"
        v-model="form.valid"
        lazy-validation
      >
        <v-layout wrap>
          <v-col
            cols="12"
            class="text-sm-start pb-0"
          >
            <v-alert
              text
              border="left"
              type="info"
            >
              {{ $t('user_notificationNotice') }}
            </v-alert>
          </v-col>
          <v-col
            cols="12"
            class="text-sm-start"
          >
            <h2>
              <span>{{ $t('user_notificationsSettings') }}</span>
            </h2>
          </v-col>
          <v-col
            cols="12"
            sm="6"
            class="pt-0 pb-0"
          >
            <v-autocomplete
              v-model="reportsSettings.alarmsSettings"
              item-value="id"
              :label="$t('user_sendAlarmNotificationEmail')"
              :items="notificationsOptions"
              :autocomplete="true"
              prepend-icon="mdi-email-newsletter"
              name="emailAlarm"
              :data-vv-as="$t('user_sendAlarmNotificationEmail')"
              :error-messages="errors.collect('emailAlarm')"
            />
          </v-col>
          <v-col
            cols="12"
            sm="6"
            class="pt-0 pb-0"
          >
            <v-autocomplete
              v-model="reportsSettings.alarmsMobileSettings"
              item-value="id"
              :label="$t('user_sendAlarmNotificationMobile')"
              :items="notificationsOptions"
              :autocomplete="true"
              prepend-icon="mdi-cellphone-message"
              name="mobileAlarm"
              :data-vv-as="$t('user_sendAlarmNotificationMobile')"
              :error-messages="errors.collect('mobileAlarm')"
            />
          </v-col>
          <v-col
            cols="12"
            sm="6"
            class="pt-0 pb-0"
          >
            <v-autocomplete
              v-model="reportsSettings.moneyCollectAlarms"
              item-value="id"
              :label="$t('user_moneyCollectEmail')"
              :items="notificationsOptionsBoolean"
              :autocomplete="true"
              prepend-icon="mdi-account-cash"
              name="moneyCollectEmail"
              :data-vv-as="$t('user_moneyCollectEmail')"
              :error-messages="errors.collect('moneyCollectEmail')"
            />
          </v-col>
          <v-col
            cols="12"
            align="right"
            class="pt-0"
          >
            <v-btn
              color="primary darken-1"
              :disabled="!form.valid"
              @click.native="submit"
            >
              {{ $t('actions.update') }}
            </v-btn>
          </v-col>
        </v-layout>
      </v-form>
    </v-row>
  </v-container>
</template>

<script>
import LoadingOverlay from '@components/common/LoadingOverlay.vue';

export default {
  components: {
    LoadingOverlay,
  },
  data() {
    return {
      notificationsOptions: [
        {
          id: null,
          text: this.$t('user_dontSend'),
        },
        {
          id: 'information',
          text: this.$t('user_info'),
        },
        {
          id: 'warning',
          text: this.$t('user_warning'),
        },
        {
          id: 'error',
          text: this.$t('user_error'),
        },
      ],
      notificationsOptionsBoolean: [
        {
          id: false,
          text: this.$t('user_dontSend'),
        },
        {
          id: true,
          text: this.$t('user_send'),
        },
      ],
      loaders: {
        site: false,
      },
      form: {
        cancelRefill: {
          valid: true,
        },
        validateOnBlur: true,
        valid: false,
      },
      reportsSettings: {
        alarmsSettings: null,
        alarmsMobileSettings: null,
        moneyCollectAlarms: false,
      },
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      this.loaders.site = true;
      this.axios.get(
        '/api/profile/reports',
      )
        .then(
          (response) => {
            this.reportsSettings = response.data;

            this.loaders.site = false;
          },
        );
    },
    submit() {
      this.loaders.site = true;
      this.axios.patch(
        '/api/profile/reports',
        {
          ...this.reportsSettings,
        },
      )
        .then(
          () => {
            this.loaders.site = false;
          },
        );
    },
  },
};
</script>
