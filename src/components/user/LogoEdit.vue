<template>
  <v-container
    fluid
  >
    <v-row>
      <v-col
        sm="6"
        offset-sm="2"
        class="pr-0 pt-6"
      >
        <v-form
          ref="formUserEdit"
          v-model="form.valid"
          :disabled="!isOwner || !isPremiumSubscription"
          lazy-validation
        >
          <v-file-input
            class="pt-0"
            :rules="rules.fileSize"
            accept="image/png, image/jpeg, image/bmp"
            prepend-icon="mdi-camera"
            :label="$t('common_invoice_logo')"
            @change="uploadFile"
          />
          <img
            v-if="logo"
            :src="logo"
            height="150px"
          >
        </v-form>
      </v-col>
      <v-col
        cols="12"
        align="right"
        class="pt-0 pb-0"
      >
        <v-btn
          color="primary darken-1"
          :loading="loaders.actualize"
          :disabled="!form.valid"
          @click.native="submit"
        >
          {{ $t('actions.save') }}
        </v-btn>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>

import { mapGetters } from 'vuex';

export default {
  components: {
  },
  props: {
    dataUrl: {
      type: String,
      default: '/api/subscriber',
    },
    subscriberId: {
      type: Number,
      default: null,
    },
    showAllFields: {
      type: Boolean,
      default: true,
    },
    showNotice: {
      type: Boolean,
      default: true,
    },
    isEditable: {
      type: Boolean,
      default: true,
    },
    onSuccessSubmit: {
      type: Function,
      default: () => {},
    },
    subscriber: {
      type: [Array, Object],
      default: null,
    },
  },
  data() {
    return {
      rules: {
        fileSize: [(value) => !value || value.size < 500000 || 'Avatar size should be less than 0,5 MB!'],
      },
      dealer: null,
      loaders: {
        site: true,
        actualize: false,
      },
      form: {
        cancelRefill: {
          valid: true,
        },
        validateOnBlur: true,
        valid: false,
      },
      logo: null,
    };
  },
  computed: {
    ...mapGetters({
      isOwner: 'auth/isOwner',
      hasRole: 'auth/hasRole',
    }),
    isPremiumSubscription() {
      return this.hasRole('ROLE_SUBSCRIPTION_PREMIUM');
    },
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      let url = `${this.dataUrl}`;

      // if (this.$route.params.id && this.subscriberId === undefined) {
      //   url += `/${this.$route.params.id}`;
      //
      //   this.subId = this.$route.params.id;
      // }

      if (this.subscriberId) {
        url += `/${this.subscriberId}`;
      }

      this.loaders.site = true;
      this.axios.get(
        url,
      )
        .then(
          (response) => {
            if ((response.status === 200) && response.data) {
              this.logo = response.data.logo;
            }
            this.loaders.site = false;
          },
          () => {
            this.alertType = 'error';
            this.dataNotFilled = true;
          },
        );
      this.loaders.site = false;
    },
    submit() {
      if (this.isEditable) {
        this.loaders.actualize = true;
        this.loaders.site = true;
        let url = `${this.dataUrl}`;

        if (this.subscriberId) {
          url += '/';
          url += this.subscriberId;
        }

        const params = {
          logo: this.logo,
        };

        this.axios.patch(
          url,
          params,
        )
          .then(
            (response) => {
              if ((response.status === 200) && response.data) {
                this.loaders.actualize = false;
                this.loaders.site = false;
                this.onSuccessSubmit();
              }
            },
            () => {
              this.loaders.actualize = false;
              this.loaders.site = false;
            },
          );
      }
    },
    uploadFile(file) {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        this.logo = reader.result;
      };
    },
  },
};
</script>
