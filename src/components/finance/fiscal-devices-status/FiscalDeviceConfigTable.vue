<template>
  <v-container
    fluid
    class="pa-0"
  >
    <v-card
      flat
      outlined
      color="blue-grey lighten-5"
      class="pa-3"
    >
      <div class="w-100">
        <v-card
          outlined
          elevation="2"
        >
          <v-card-title class="py-2 d-flex">
            <span>{{ $t('fiscal_transactions_configDetails') }}</span>
            <v-spacer />
            <v-btn
              color="primary"
              small
              class="ml-2"
              @click="openUploadModal"
            >
              <v-icon
                left
                small
              >
                mdi-upload
              </v-icon>
              {{ $t('fiscal_transactions_certificate_uploadBtn') }}
            </v-btn>
          </v-card-title>
          <v-divider />
          <template v-if="!configData">
            <v-card-text class="py-3 text-center">
              <i>{{ $t('fiscal_transactions_config_noData') }}</i>
            </v-card-text>
          </template>
          <v-simple-table
            v-else
            dense
          >
            <template #default>
              <tbody>
                <tr
                  v-if="configData.oib"
                  class="striped-row"
                >
                  <td class="py-1 px-3 font-weight-medium">
                    {{ $t('fiscal_transactions_config_oib') }}
                  </td>
                  <td class="py-1 px-3">
                    {{ configData.oib }}
                  </td>
                </tr>
                <tr
                  v-if="configData.subject"
                  class="striped-row"
                >
                  <td class="py-1 px-3 font-weight-medium">
                    {{ $t('fiscal_transactions_config_subject') }}
                  </td>
                  <td class="py-1 px-3">
                    {{ configData.subject }}
                  </td>
                </tr>
                <tr
                  v-if="configData.errorCounter !== undefined"
                  class="striped-row"
                >
                  <td class="py-1 px-3 font-weight-medium">
                    {{ $t('fiscal_transactions_config_errorCounter') }}
                  </td>
                  <td class="py-1 px-3">
                    {{ configData.errorCounter }}
                  </td>
                </tr>
                <tr
                  v-if="configData.isError !== undefined"
                  class="striped-row"
                >
                  <td class="py-1 px-3 font-weight-medium">
                    {{ $t('common_state') }}
                  </td>
                  <td class="py-1 px-3">
                    <v-icon
                      :color="configData.isError ? 'error' : 'success'"
                      small
                    >
                      {{ configData.isError ? 'mdi-alert-circle' : 'mdi-check-circle' }}
                    </v-icon>
                  </td>
                </tr>
                <tr
                  v-if="configData.lastTime"
                  class="striped-row"
                >
                  <td class="py-1 px-3 font-weight-medium">
                    {{ $t('common_updateTime') }}
                  </td>
                  <td class="py-1 px-3">
                    {{ configData.lastTime | formatDateDayTime }}
                  </td>
                </tr>
                <tr
                  v-if="configData.validFrom"
                  class="striped-row"
                >
                  <td class="py-1 px-3 font-weight-medium">
                    {{ $t('fiscal_transactions_config_validFrom') }}
                  </td>
                  <td class="py-1 px-3">
                    {{ configData.validFrom | formatDateDay }}
                  </td>
                </tr>
                <tr
                  v-if="configData.expiration"
                  class="striped-row"
                >
                  <td class="py-1 px-3 font-weight-medium">
                    {{ $t('fiscal_transactions_config_expiration') }}
                  </td>
                  <td class="py-1 px-3">
                    {{ configData.expiration | formatDateDay }}
                  </td>
                </tr>
              </tbody>
            </template>
          </v-simple-table>
        </v-card>
      </div>
    </v-card>
    <upload-certificate-modal
      ref="uploadModal"
      :device-sn="deviceSn"
      @certificate-uploaded="$emit('refresh-data')"
    />
  </v-container>
</template>

<script>
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import UploadCertificateModal from './UploadCertificateModal.vue';

export default {
  name: 'FiscalDeviceConfigTable',
  components: {
    UploadCertificateModal,
  },
  mixins: [
    FilterMixin,
  ],
  props: {
    configData: {
      type: Object,
      default: null,
    },
    deviceSn: {
      type: [String, Number],
      default: '',
    },
    deviceName: {
      type: String,
      default: '',
    },
  },
  methods: {
    openUploadModal() {
      this.$refs.uploadModal.openDialog();
    },
  },
};
</script>
