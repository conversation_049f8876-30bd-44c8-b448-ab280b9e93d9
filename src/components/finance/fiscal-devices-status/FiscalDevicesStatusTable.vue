<template>
  <v-data-table
    key="fiscal-devices-status-table"
    dense
    item-key="id"
    mobile-breakpoint="0"
    :headers="headers"
    :items="tableItems"
    :single-expand="true"
    :expanded.sync="expanded"
    :loading="loader"
    :options="options"
    :server-items-length="itemsTotal"
    :footer-props="footerProps"
    hide-default-footer
    @update:options="onOptionsChange"
  >
    <!--Loader-->
    <template #progress>
      <div class="text-center mx-n4">
        <v-progress-linear
          class="loader"
          indeterminate
          color="primary"
        />
      </div>
    </template>

    <!--Item-->
    <template #item="{ item, expand, isExpanded }">
      <template v-if="!loader">
        <tr
          class="text-sm-start  cursor-pointer"
          @click="onRowClick({item, expand, isExpanded})"
        >
          <td class="text-start tabcell-carwash">
            {{ item.carwashName }}
          </td>
          <td class="text-center">
            {{ item.lastContact|formatDateDayTime }}
          </td>
          <td
            class="text-end"
          >
            <v-icon v-if="isExpanded">
              mdi-chevron-up
            </v-icon>
            <v-icon v-else>
              mdi-chevron-down
            </v-icon>
          </td>
        </tr>
      </template>
    </template>

    <!--Expanded item-->
    <template #expanded-item="{ headers: _headers, item }">
      <td
        v-if="!loader"
        :colspan="_headers.length"
        class="elevation-3"
      >
        <v-row class="ma-0">
          <v-col
            cols="12"
            class="pa-2"
          >
            <fiscal-device-config-table
              v-if="showFiscalConfig"
              :config-data="fiscalConfig[item.serialNumber]"
              :device-sn="item.serialNumber"
              :device-name="item.carwashName"
              @refresh-data="$emit('refresh-data')"
            />
          </v-col>
        </v-row>
        <v-row class="ma-0">
          <v-col
            v-if="'standInfo' in item"
            cols="12"
            class="pa-2"
          >
            <stand-info-table
              :stands-info="item.standInfo"
            />
          </v-col>
        </v-row>
      </td>
    </template>
  </v-data-table>
</template>

<script>
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import StandInfoTable from './StandInfoTable.vue';
import FiscalDeviceConfigTable from './FiscalDeviceConfigTable.vue';

export default {
  name: 'FiscalDevicesStatusTable',
  components: {
    StandInfoTable,
    FiscalDeviceConfigTable,
  },
  mixins: [
    FilterMixin,
  ],
  props: {
    items: {
      type: Array,
      default() {
        return [];
      },
    },
    showFiscalConfig: {
      type: Boolean,
      default: false,
    },
    fiscalConfig: {
      type: Object,
      default() {
        return {};
      },
    },
    options: {
      type: Object,
      default() {
        return {};
      },
    },
    itemsTotal: {
      type: Number,
      default: -1,
    },
    loader: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      expanded: [],
      filtering: {
        options: {},
      },
      typeIcons: {
        CASH: 'mdi-cash',
        CASHLESS: 'mdi-credit-card-outline',
      },
      footerProps: {
        'items-per-page-options': [25, 50, 100],
        'items-per-page-text': this.$t('common_rowsPerPage'),
      },
      headers: [
        {
          value: 'carwashName',
          text: this.$t('fiscal_transactions.table.carwash'),
          sortable: false,
        },
        {
          value: 'lastContact',
          text: this.$t('fiscal_transactions.lastReceipt'),
          align: 'center',
          sortable: false,
        },
        {
          value: 'expand',
          sortable: false,
          text: '',
          align: 'end',
        },
      ],
    };
  },
  computed: {
    tableItems() {
      if (this.loader) {
        return [];
      }

      return this.items;
    },
  },
  methods: {
    onOptionsChange(options) {
      this.$set(this.filtering, 'options', options);
    },
    onRowClick({ expand, isExpanded }) {
      expand(!isExpanded);
    },
  },
};
</script>
