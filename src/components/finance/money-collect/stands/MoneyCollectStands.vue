<template>
  <div>
    <v-col
      cols="12"
      class="mt-5"
    >
      <v-layout
        row
        wrap
      >
        <v-col
          cols="12"
          sm="8"
          class="text-sm-start"
        >
          <heading
            :dates="filtering.dates"
          />
        </v-col>
        <v-col
          cols="12"
          sm="4"
          class="d-flex justify-end"
        >
          <btn-refresh
            class="mr-2"
            :disabled="loader"
            @click="getData"
          />
          <report-create-modal
            btn-class="ml-2"
            :params="exportAsyncParams"
            :disabled="loader"
            :preset="filtering.dates.value"
          />
        </v-col>
      </v-layout>
    </v-col>
    <v-col cols="12">
      <money-collect-stands-table
        :items="items"
        :loader="loader"
        :items-total="totalItems"
        :page-sums="pageSums.CAR_WASH"
        :options="filtering.options"
        :currency-symbol="currencySymbol"
        @change="onFiltersChange"
      />
    </v-col>
  </div>
</template>

<script>
import DataFetchMixin from '@components/common/mixins/DataFetchMixin.vue';
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import FiltersHandlingMixin from '@components/common/mixins/FiltersHandlingMixin.vue';
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import ReportCreateModal from '@components/reports/ReportCreateModal.vue';
import { mapGetters } from 'vuex';
import MoneyCollectStandsTable from './MoneyCollectStandsTable.vue';
import Heading from './Heading.vue';

export default {
  name: 'MoneyCollectStands',
  components: {
    BtnRefresh,
    Heading,
    MoneyCollectStandsTable,
    ReportCreateModal,
  },
  mixins: [
    DataFetchMixin,
    FilterMixin,
    FiltersHandlingMixin,
  ],
  props: {
    dates: {
      type: Object,
      default: () => ({}),
    },
    carwash: {
      type: [Object, Number],
      default: null,
      nullable: true,
    },
  },
  data() {
    return {
      settingsNamespace: 'finance:dates',
      dataUrl: '/api/reports/data',
      filtering: {
        options: {
          sortBy: ['date'],
          sortDesc: [true],
          page: 1,
          itemsPerPage: 10,
        },
      },
    };
  },
  computed: {
    ...mapGetters({
      getCarwashBySerial: 'carwashes/getCarwashBySerial',
    }),
    exportAsyncParams() {
      return {
        serial: this.filtering.carwash ?? null,
        startDate: this.$options.filters.formatDateDay(this.filtering.dates.from),
        endDate: this.$options.filters.formatDateDay(this.filtering.dates.to),
        sources: 'CAR_WASH',
        report: 'v2\\FinanceMoneyCollects',
      };
    },
    currencySymbol() {
      return this.currencyObject.symbol;
    },
  },
  watch: {
    dates(val) {
      this.$set(this.filtering, 'dates', val);
      this.onFiltersChangePageReset();
      this.emitChangeEvent();
    },
    carwash(val) {
      this.$set(this.filtering, 'carwash', val);
      this.onFiltersChangePageReset();
      this.emitChangeEvent();
    },
  },
  mounted() {
    if (this.dates !== null) {
      this.$set(this.filtering, 'carwash', this.carwash);
      this.$set(this.filtering, 'dates', this.dates);
    }
  },
  methods: {
    onFiltersChangePageReset(filters) {
      this.filtering.options.page = 1;
      this.onFiltersChange(filters);
    },
    parseApiResponseData(data) {
      const rows = Object.values(data);
      this.items = rows;
    },
    getParams() {
      return {
        params: {
          serial: this.filtering.carwash,
          startDate: this.$options.filters.formatDateDay(this.filtering.dates.from),
          endDate: this.$options.filters.formatDateDay(this.filtering.dates.to),
          sources: 'CAR_WASH',
          page: this.filtering.options.page || null,
          perPage: this.filtering.options.itemsPerPage || null,
          report: 'v2\\FinanceMoneyCollects',
        },
      };
    },
  },
};
</script>
