<template>
  <v-data-table
    item-key="id"
    :headers="headers"
    :items="tableItems"
    :loading="loader"
    :options="options"
    :server-items-length="itemsTotal"
    :footer-props="footerProps"
    :single-expand="true"
    :expanded.sync="expanded"
    mobile-breakpoint="0"
    @update:options="onOptionsChange"
  >
    <!--Loader-->
    <template #progress>
      <div class="text-center mx-n4">
        <v-progress-linear
          class="loader"
          indeterminate
          color="primary"
        />
      </div>
    </template>

    <!--Item-->
    <template #item="{ item, expand, isExpanded }">
      <template v-if="!loader">
        <tr
          class="text-sm-start  cursor-pointer"
          @click="onRowClick({item, expand, isExpanded})"
        >
          <td class="text-start tabcell-carwash font-weight-bold">
            {{ item.at|formatDateDayTime }}
          </td>
          <td class="text-start tabcell-carwash font-weight-bold">
            {{ item.carwash }}
          </td>
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="item.sums.coins"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="item.sums.bill"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="item.sums.tokens"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="item.sums.all"
            weight="bold"
          />
          <td class="text-right">
            <v-icon v-if="isExpanded">
              mdi-chevron-up
            </v-icon>
            <v-icon v-else>
              mdi-chevron-down
            </v-icon>
          </td>
        </tr>
      </template>
    </template>

    <!--Expanded item-->
    <template #expanded-item="{ headers: _headers, item }">
      <td
        v-if="!loader"
        :colspan="_headers.length"
        class="elevation-3"
      >
        <money-collect-stands-details-table
          :parent="item"
          :sums="item.sums"
          :currency-symbol="currencySymbol"
        />
      </td>
    </template>

    <!--Summary-->
    <template #[`body.append`]>
      <template v-if="!loader">
        <tr class="table-summary">
          <td
            colspan="2"
            class="text-start font-weight-bold"
          >
            {{ $t('common_totalOnPage') }}
          </td>
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="pageSums['coins']"
            weight="bold"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="pageSums['bills']"
            weight="bold"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="pageSums['tokens']"
            weight="bold"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="pageSums['all']"
            weight="bold"
          />
          <td />
        </tr>
      </template>
    </template>
  </v-data-table>
</template>

<script>
import CustomCurrencySymbolCell from '@components/common/CustomCurrencySymbolCell.vue';
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import MoneyCollectStandsDetailsTable from './MoneyCollectStandsDetailsTable.vue';

export default {
  name: 'MoneyCollectStandsTable',
  components: {
    CustomCurrencySymbolCell,
    MoneyCollectStandsDetailsTable,
  },
  mixins: [
    FilterMixin,
  ],
  props: {
    itemsTotal: {
      type: Number,
      default: -1,
    },
    options: {
      type: Object,
      default() {
        return {};
      },
    },
    items: {
      type: Array,
      default() {
        return [];
      },
    },
    currencySymbol: {
      type: String,
      default: undefined,
    },
    pageSums: {
      type: Object,
      default() {
        return {};
      },
    },
    loader: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      filtering: {
        options: {},
      },
      footerProps: {
        'items-per-page-options': [10, 25],
        'items-per-page-text': this.$t('common_rowsPerPage'),
      },
      sortBy: 'time',
      sortDesc: true,
      expanded: [],
      headers: [
        {
          value: 'at',
          text: this.$t('turnover.table.date'),
        },
        {
          value: 'carwash',
          text: this.$t('turnover.table.carwash'),
        },
        {
          value: 'coins',
          text: this.$t('turnover.table.coins'),
          align: 'end',
        },
        {
          value: 'bills',
          text: this.$t('turnover.table.banknotes'),
          align: 'end',
        },
        {
          value: 'tokens',
          text: this.$t('turnover.table.tokens'),
          align: 'end',
        },
        {
          value: 'sums',
          text: this.$t('turnover.table.sum'),
          align: 'end',
        },
        {
          value: 'expand',
          sortable: false,
          text: '',
        },
      ],
    };
  },
  computed: {
    tableItems() {
      if (this.loader) {
        return [];
      }
      return this.items;
    },
  },
  watch: {
    items() {
      const items = [...this.items];
      this.expanded.pop();
      if (this.items.length > 0) {
        items.sort((a, b) => b[this.sortBy] - a[this.sortBy]);
        this.$set(this.expanded, 0, items[0]);
      }
    },
  },
  methods: {
    onOptionsChange(options) {
      this.$set(this.filtering, 'options', options);
    },
    onRowClick({
      expand,
      isExpanded,
    }) {
      expand(!isExpanded);
    },
  },
};
</script>
