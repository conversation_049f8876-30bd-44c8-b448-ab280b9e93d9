<template>
  <h2>
    <span>{{ $t('finance_exchangerMoneyCollections') }}</span>
    <span v-if="dateFrom !== dateTo">
      {{ $t('finance_moneycollectFrom') }}
      {{ dateFrom }} {{ $t('finance_moneycollectTo') }} {{ dateTo }}
    </span>
    <span v-else>
      {{ $t('finance_moneycollectFor') }} {{ dateFrom }}
    </span>
  </h2>
</template>

<script>

export default {
  name: 'MoneyChangerHeading',
  props: {
    dates: {
      type: Object,
      required: true,
    },
  },
  computed: {
    dateFrom() {
      return this.$options.filters.formatDateDay(this.dates.from);
    },
    dateTo() {
      return this.$options.filters.formatDateDay(this.dates.to);
    },
  },
};
</script>
