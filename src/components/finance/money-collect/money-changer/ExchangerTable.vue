<template>
  <v-card
    v-if="items.length"
    outlined
    color="blue-grey lighten-5"
  >
    <v-card-title class="px-2 py-2">
      <v-layout
        row
        wrap
      >
        <v-col
          cols="6"
          class="text-sm-start"
        >
          {{ $t('turnover.table.exchanger') }}
        </v-col>
        <v-col
          cols="6"
          class="d-flex justify-end"
        >
          <report-create-modal
            btn-class="ml-2"
            :params="exportAsyncParams"
            :show-dates="false"
          />
        </v-col>
      </v-layout>
    </v-card-title>
    <v-card-text>
      <v-data-table
        dense
        :headers="headers"
        :items="items"
        hide-default-footer
        class="elevation-2"
        mobile-breakpoint="0"
        disable-sort
      >
        <template #item="{ item: exchanger }">
          <tr>
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="exchanger.content[0].bill"
              align="center"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="exchanger.content[0].coin"
              align="center"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="exchanger.content[0].bankCard"
              align="center"
              additional-class="border-right"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="exchanger.content[0].hopperA"
              align="center"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="exchanger.content[0].hopperB"
              align="center"
              additional-class="border-right"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="exchanger.content[0].bkfCardRevalue"
              align="center"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="exchanger.content[0].bkfCardSoldValue"
              :append="`(${
                exchanger.content[0].bkfCardSoldCount} ${$t('finance_units')})`"
              align="center"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="exchanger.content[0].carwashRecharge"
              align="center"
              additional-class="border-right"
            />
          </tr>
          <tr class="text-center">
            <td
              colspan="3"
              class="border-right"
            >
              <span>{{ $t('turnover.table.paid') }}: </span>
              <strong>{{ exchanger.sums.income|currencySymbol(currencySymbol) }}</strong>
            </td>
            <td
              colspan="5"
              class="border-right"
            >
              <span>{{ $t('turnover.table.exchanges') }}: </span>
              <strong>{{ exchanger.sums.outcome|currencySymbol(currencySymbol) }}</strong>
            </td>
          </tr>
          <tr class="table-summary">
            <td
              :colspan="8"
              class="border-right"
            >
              <span>
                {{ $t('turnover.table.balance') }}:
              </span>
              <strong
                v-if="exchanger.sums.balance !== 0"
                class="deep-orange--text text--darken-3"
              >
                {{ exchanger.sums.balance|currencySymbol(currencySymbol) }}
              </strong>
              <strong v-else>
                {{ exchanger.sums.balance|currencySymbol(currencySymbol) }}
              </strong>
            </td>
          </tr>
        </template>
      </v-data-table>
    </v-card-text>
  </v-card>
</template>

<script>
import CustomCurrencySymbolCell from '@components/common/CustomCurrencySymbolCell.vue';
import ReportCreateModal from '@components/reports/ReportCreateModal.vue';

export default {
  name: 'ExchangerTable',
  components: {
    ReportCreateModal,
    CustomCurrencySymbolCell,
  },
  props: {
    parent: {
      type: Object,
      required: true,
    },
    currencySymbol: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      headers: [
        {
          value: 'banknotes',
          text: this.$t('turnover.table.banknotes'),
          align: 'center',
        },
        {
          value: 'coin',
          text: this.$t('turnover.table.coins'),
          align: 'center',
        },
        {
          value: 'bankCards',
          text: this.$t('turnover.table.bankCards'),
          align: 'center',
        },
        {
          value: 'hopperA',
          text: this.$t('turnover.table.hopperA'),
          align: 'center',
        },
        {
          value: 'hopperB',
          text: this.$t('turnover.table.hopperB'),
          align: 'center',
        },
        {
          value: 'bkfKeyRecharge',
          text: this.$t('turnover.table.bkfKeyRecharge'),
          align: 'center',
        },
        {
          value: 'bkfCardSale',
          text: this.$t('turnover.table.bkfCardSale'),
          align: 'center',
        },
        {
          value: 'carwashRecharge',
          text: this.$t('turnover.table.carwashRecharge'),
          align: 'center',
        },
      ],
    };
  },
  computed: {
    items() {
      return [this.parent];
    },
    exportAsyncParams() {
      return {
        id: this.parent.id,
        sources: 'MONEY_CHANGER,TERMINAL',
        report: 'v2\\FinanceMoneyCollects',
      };
    },
  },
};
</script>
