<template>
  <v-data-table
    item-key="id"
    :headers="headers"
    :items="tableItems"
    :loading="loader"
    :options="options"
    :expanded.sync="expanded"
    :server-items-length="itemsTotal"
    :footer-props="footerProps"
    :single-expand="true"
    mobile-breakpoint="0"
    :sort-by="sortBy"
    :sort-desc="sortDesc"
    @update:options="onOptionsChange"
  >
    <!--Loader-->
    <template #progress>
      <div class="text-center mx-n4">
        <v-progress-linear
          class="loader"
          indeterminate
          color="primary"
        />
      </div>
    </template>

    <!--Item-->
    <template #item="{ item, expand, isExpanded }">
      <template v-if="!loader">
        <tr
          class="text-sm-start  cursor-pointer"
          @click="onRowClick({item, expand, isExpanded})"
        >
          <td class="text-start tabcell-carwash font-weight-bold">
            {{ item.date|formatDateDayTime }}
          </td>
          <td class="text-start tabcell-carwash font-weight-bold">
            {{ item.carwashName }}
          </td>
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="item.sumSucked"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="item.sumSorted"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="item.balance"
          />
          <td class="text-right">
            <v-icon v-if="isExpanded">
              mdi-chevron-up
            </v-icon>
            <v-icon v-else>
              mdi-chevron-down
            </v-icon>
          </td>
        </tr>
      </template>
    </template>

    <!--Expanded item-->
    <template #expanded-item="{ headers: _headers, item }">
      <td
        v-if="!loader"
        :colspan="_headers.length"
        class="elevation-3"
      >
        <money-collect-yeti-details-table
          :parent="item"
          :currency-symbol="currencySymbol"
        />
      </td>
    </template>

    <!--Summary-->
    <template #[`body.append`]>
      <template v-if="!loader">
        <tr class="table-summary">
          <td
            colspan="2"
            class="text-start font-weight-bold"
          >
            {{ $t('turnover.table.total') }}
          </td>
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="items|sumProperty('sumSucked')"
            weight="bold"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="items|sumProperty('sumSorted')"
            weight="bold"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="items|sumProperty('balance')"
            weight="bold"
          />
          <td />
        </tr>
      </template>
    </template>
  </v-data-table>
</template>

<script>
import CustomCurrencySymbolCell from '@components/common/CustomCurrencySymbolCell.vue';
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import MoneyCollectYetiDetailsTable from './MoneyCollectYetiDetailsTable.vue';

export default {
  name: 'MoneyCollectYetiTable',
  components: {
    MoneyCollectYetiDetailsTable,
    CustomCurrencySymbolCell,
  },
  mixins: [
    FilterMixin,
  ],
  props: {
    currencySymbol: {
      type: String,
      default: undefined,
    },
    itemsTotal: {
      type: Number,
      default: -1,
    },
    options: {
      type: Object,
      default() {
        return {};
      },
    },
    items: {
      type: Array,
      default() {
        return [];
      },
    },
    pageSums: {
      type: Object,
      default() {
        return {};
      },
    },
    loader: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      sortBy: 'date',
      sortDesc: true,
      expanded: [],
      filtering: {
        options: {},
      },
      footerProps: {
        'items-per-page-options': [10, 25],
        'items-per-page-text': this.$t('common_rowsPerPage'),
      },
      headers: [
        {
          value: 'date',
          text: this.$t('turnover.table.date'),
        },
        {
          value: 'carwash',
          text: this.$t('turnover.table.carwash'),
        },
        {
          value: 'sumSucked',
          text: this.$t('finance_sucked'),
          align: 'end',
        },
        {
          value: 'sumSorted',
          text: this.$t('finance_sorted'),
          align: 'end',
        },
        {
          value: 'unrecognized',
          text: this.$t('finance_balance'),
          align: 'end',
        },
        {
          value: 'expand',
          sortable: false,
          text: '',
        },
      ],
    };
  },
  computed: {
    tableItems() {
      if (this.loader) {
        return [];
      }
      return this.items;
    },
  },
  watch: {
    items() {
      const items = [...this.items];
      this.expanded.pop();
      if (this.items.length > 0) {
        items.sort((a, b) => b[this.sortBy] - a[this.sortBy]);
        this.$set(this.expanded, 0, items[0]);
      }
    },
  },
  methods: {
    onOptionsChange(options) {
      this.$set(this.filtering, 'options', options);
    },
    onRowClick({
      expand,
      isExpanded,
    }) {
      expand(!isExpanded);
    },
  },
};
</script>
