<script>

import DataFetchMixin from '@components/common/mixins/DataFetchMixin.vue';
import SettingsMixin from '@components/common/mixins/SettingsMixin.vue';
import DateOptionsMixins from '@components/common/mixins/DateOptionsMixins.vue';

export default {
  name: 'DataFetchMixin',
  mixins: [
    DataFetchMixin,
    SettingsMixin,
    DateOptionsMixins,
  ],
  data() {
    return {
      first: true,
      filtering: {
        dataSource: {
          lastCollectionUrl: '',
          dataUrl: '',
        },
      },
    };
  },
  methods: {
    parseApiResponseData(data) {
      const rows = Object.values(data);
      this.items = rows.map((row, index) => ({
        id: index,
        ...row,
        date: new Date(Date.parse(row.date)),
        dates: this.filtering.dates,
      }));
    },
    getUrl() {
      if (this.filtering.dates.isLastCollection) {
        return this.filtering.dataSource.lastCollectionUrl;
      }
      return this.filtering.dataSource.dataUrl;
    },
    getParams() {
      if (
        this.settingsNamespace !== undefined
        && this.filtering.dates.value !== undefined
        && this.filtering.dates.value !== 'custom'
      ) {
        let settings = this.filtering.dates.value;
        if (this.first && this.filtering.dates.value !== undefined) {
          settings = this.getSetts(this.settingsNamespace, this.filtering.dates.value);
          this.filtering.dates = this.getDateItemByValue(settings);
          this.first = false;
        }

        this.$set(this.filtering.dates, 'value', settings);

        this.setSetts(this.settingsNamespace, this.filtering.dates.value);
      }

      if (this.filtering.dates.isLastCollection) {
        return {
          params: {
            sn: this.filtering.carwash,
          },
        };
      }

      return {
        params: {
          sn: this.filtering.carwash,
          date_from: this.$options.filters.formatDateDay(this.filtering.dates.from),
          date_to: this.$options.filters.formatDateDay(this.filtering.dates.to),
        },
      };
    },
  },
};

</script>
