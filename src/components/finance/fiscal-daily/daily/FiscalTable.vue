<template>
  <v-data-table
    :headers="headers"
    :items="tableItems"
    item-key="id"
    :loading="loader"
    hide-default-footer
    disable-pagination
    mobile-breakpoint="0"
    :sort-by="sortBy"
    :sort-desc="sortDesc"
    dense
  >
    <!--Loader-->
    <template #progress>
      <div class="text-center mx-n4">
        <v-progress-linear
          class="loader"
          indeterminate
          color="primary"
        />
      </div>
    </template>

    <!--Item-->
    <template #item="{ item }">
      <tr>
        <td class="text-start tabcell-date font-weight-bold">
          {{ dateFormatFunc(item.time) }}
        </td>
        <td class="text-start">
          {{ item.carwashName }}
        </td>
        <td class="text-start">
          {{ $t(`fiscal_transactions.type.${item.method}`) }}
        </td>
        <td class="text-start">
          {{ item.fiscal }}
        </td>
        <custom-currency-symbol-cell
          :currency="currencySymbol"
          :value="item.payments"
        />
      </tr>
    </template>

    <!--Summary-->
    <template #[`body.append`]>
      <template v-if="!loader">
        <tr class="table-summary">
          <td
            colspan="4"
            class="text-start font-weight-bold"
          >
            {{ $t('turnover.table.total') }}
          </td>
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="items|sumProperty('payments')"
            weight="bold"
          />
        </tr>
      </template>
    </template>
  </v-data-table>
</template>

<script>
import CustomCurrencySymbolCell from '@components/common/CustomCurrencySymbolCell.vue';

export default {
  name: 'FiscalTable',
  components: {
    CustomCurrencySymbolCell,
  },
  props: {
    currencySymbol: {
      type: String,
      default: '',
    },
    items: {
      type: Array,
      default() {
        return [];
      },
    },
    loader: {
      type: Boolean,
      default: false,
    },
    dateFormatFunc: {
      type: Function,
      required: true,
    },
  },
  data() {
    return {
      graphSelectedColumns: [],
      headers: [
        {
          value: 'time',
          text: this.$t('fiscal_transactions.table.date'),
        },
        {
          value: 'carwashName',
          text: this.$t('finance_name'),
          align: 'start',
          sortable: false,
        },
        {
          value: 'method',
          text: this.$t('fiscal_transactions.table.type'),
          align: 'start',
          sortable: false,
        },
        {
          value: 'fiscal',
          text: this.$t('fiscal_transactions.table.fiscal'),
          align: 'start',
          sortable: false,
        },
        {
          value: 'payments',
          text: this.$t('fiscal_transactions.table.value'),
          align: 'end',
          sortable: false,
        },
      ],
      sortBy: 'date',
      sortDesc: true,
    };
  },
  computed: {
    tableItems() {
      if (this.loader) {
        return [];
      }
      return this.items;
    },
  },
};
</script>

<style>
.v-application--is-ltr .v-input--selection-controls__input {
  margin-right: 0;
}
</style>
