<template>
  <v-card
    v-if="items.length"
    outlined
    color="blue-grey lighten-5"
  >
    <v-card-title class="px-2 py-2">
      <v-layout
        row
        wrap
      >
        <v-col
          cols="6"
          class="text-sm-start"
        >
          <span>
            {{ $t('turnover.table.detailsRollover') }}
          </span>
        </v-col>
      </v-layout>
    </v-card-title>

    <v-card-text>
      <v-data-table
        dense
        :items="items"
        :headers="headers"
        :hide-default-footer="true"
        class="elevation-2"
      >
        <template #item="{ item }">
          <tr>
            <td class="font-weight-bold">
              {{ $t('programsusage.table.program') }} {{ item.program }}
            </td>
            <td class="text-end">
              {{ item.counter }}
            </td>
            <custom-currency-symbol-cell
              additional-class="text-end"
              :value="item.value"
              :currency="currencySymbol"
            />
          </tr>
        </template>

        <template #[`body.append`]>
          <tr
            v-if="items.length"
            class="table-summary"
          >
            <td class="text-start font-weight-bold">
              {{ $t('turnover.table.total') }}
            </td>
            <td class="text-end font-weight-bold">
              {{ items|sumProperty('counter') }}
            </td>
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="items|sumProperty('value')"
              additional-class="text-end font-weight-bold"
            />
          </tr>
        </template>
      </v-data-table>
    </v-card-text>
  </v-card>
</template>

<script>
import CustomCurrencySymbolCell from '@components/common/CustomCurrencySymbolCell.vue';

export default {
  name: 'RolloverTable',
  components: { CustomCurrencySymbolCell },
  props: {
    parent: {
      type: Object,
      required: true,
    },
    currencySymbol: {
      type: String,
      default: undefined,
    },
  },
  computed: {
    items() {
      if (typeof this.parent.portal === 'undefined' || !this.parent.portal) {
        return [];
      }

      return this.parent.portal;
    },
    headers() {
      return [
        {
          value: 'program',
          text: this.$t('programsusage.table.program'),
        },
        {
          value: 'counter',
          text: this.$t('turnover.table.counter'),
          align: 'end',
        },
        {
          value: 'value',
          text: this.$t('turnover.table.saleValue'),
          align: 'end',
        },
      ];
    },
  },
};
</script>
