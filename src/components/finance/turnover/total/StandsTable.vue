<template>
  <v-card
    v-if="items.length"
    outlined
    color="blue-grey lighten-5"
  >
    <v-card-title class="px-2 py-2">
      <v-layout
        row
        wrap
      >
        <v-col
          cols="6"
          class="text-sm-start"
        >
          {{ $t('turnover.table.details') }}
        </v-col>
      </v-layout>
    </v-card-title>

    <v-card-text>
      <v-data-table
        dense
        :headers="headers"
        :items="items"
        hide-default-footer
        class="elevation-2"
        item-key="name"
        mobile-breakpoint="0"
        :sort-by="['source', 'stand']"
        :items-per-page="-1"
      >
        <template #item="{ item }">
          <tr>
            <td class="font-weight-bold text-sm-start border-right">
              <v-icon
                small
                color="grey lighten-1"
                class="d-inline-block mr-1"
              >
                {{ item.icon }}
              </v-icon>
              {{ item.name }}
            </td>
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="item.coin"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="item.bill"
              additional-class="border-right"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="item.bank_card"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="item.mobile"
              additional-class="border-right"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="item.sum"
              weight="bold"
              additional-class="border-right"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="item.bkf_card"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="item.token"
              additional-class="border-right"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="item.changer_revalue"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="item.promotion"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="item.service"
            />
            <td class="text-right">
              {{ item.client }}
            </td>
          </tr>
        </template>

        <!-- Summary row -->
        <template #[`body.append`]>
          <tr class="table-summary">
            <td
              class="text-start font-weight-bold border-right"
              rowspan="2"
            >
              {{ $t('turnover.table.total') }}
            </td>
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="valueByPath.byPath(parent,'standsSum.coin')"
              weight="bold"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="valueByPath.byPath(parent,'standsSum.bill')"
              weight="bold"
              additional-class="border-right"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="valueByPath.byPath(parent,'standsSum.bank_card')"
              weight="bold"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="valueByPath.byPath(parent,'standsSum.mobile')"
              weight="bold"
              additional-class="border-right"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="valueByPath.byPath(parent,'standsSum.sum')"
              weight="bold"
              rowspan="2"
              additional-class="border-right"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="valueByPath.byPath(parent,'standsSum.bkf_card')"
              weight="bold"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="valueByPath.byPath(parent,'standsSum.token')"
              weight="bold"
              additional-class="border-right"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="valueByPath.byPath(parent,'standsSum.changer_revalue')"
              weight="bold"
              rowspan="2"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="valueByPath.byPath(parent,'standsSum.promotion')"
              weight="bold"
              rowspan="2"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="valueByPath.byPath(parent,'standsSum.service')"
              weight="bold"
              rowspan="2"
            />
            <td
              class="text-right font-weight-bold"
              rowspan="2"
            >
              {{ valueByPath.byPath(parent, 'standsSum.client') }}
            </td>
          </tr>
          <tr class="text-center">
            <td
              colspan="2"
              class="border-right"
            >
              <span>{{ $t('turnover.table.cash') }}: </span>
              <strong>{{ valueByPath.byPath(parent, 'standsSum.cash')
                |currencySymbol(currencySymbol) }}
              </strong>
            </td>
            <td
              colspan="2"
              class="border-right"
            >
              <span>{{ $t('turnover.table.cashless') }}: </span>
              <strong>{{ valueByPath.byPath(parent,'standsSum.cashless')
                |currencySymbol(currencySymbol) }}
              </strong>
            </td>
            <td
              colspan="2"
              class="border-right"
            >
              <span>{{ $t('turnover.table.prepaid') }}: </span>
              <strong>{{ valueByPath.byPath(parent,'standsSum.prepaid')
                |currencySymbol(currencySymbol) }}
              </strong>
            </td>
          </tr>
        </template>
      </v-data-table>
    </v-card-text>
  </v-card>
</template>

<script>
import CustomCurrencySymbolCell from '@components/common/CustomCurrencySymbolCell.vue';

export default {
  name: 'StandsTable',
  components: {
    CustomCurrencySymbolCell,
  },
  props: {
    parent: {
      type: Object,
      required: true,
    },
    currencySymbol: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      headers: [
        {
          value: 'name',
          text: this.$t('turnover.table.name'),
        },
        {
          value: 'coin',
          text: this.$t('turnover.table.coins'),
          align: 'end',
        },
        {
          value: 'bill',
          text: this.$t('turnover.table.banknotes'),
          align: 'end',
        },
        {
          value: 'bank_card',
          text: this.$t('turnover.table.bankCards'),
          align: 'end',
        },
        {
          value: 'mobile',
          text: this.$t('turnover.table.mobilePayments'),
          align: 'end',
        },
        {
          value: 'sum',
          text: this.$t('turnover.table.sum'),
          align: 'end',
        },
        {
          value: 'bkf_card',
          text: this.$t('turnover.table.bkfKey'),
          align: 'end',
        },
        {
          value: 'token',
          text: this.$t('turnover.table.tokens'),
          align: 'end',
        },
        {
          value: 'changer_revalue',
          text: this.$t('turnover.table.post'),
          align: 'end',
        },
        {
          value: 'promotion',
          text: this.$t('turnover.table.promotion'),
          align: 'end',
        },
        {
          value: 'service',
          text: this.$t('turnover.table.service'),
          align: 'end',
        },
        {
          value: 'client',
          text: this.$t('turnover.table.clients'),
          align: 'end',
        },
      ],
      deviceIcons: {
        CAR_WASH: 'mdi-car-wash',
        VACUUM_CLEANER: 'mdi-auto-fix',
        DISTRIBUTOR: 'mdi-cup-water',
      },
    };
  },
  computed: {
    items() {
      if (typeof this.parent.stands === 'undefined' || !this.parent.stands) {
        return [];
      }

      return this.parent.stands.map((stand) => ({
        name: stand.deviceName,
        // sum: stand.coin + stand.bill + stand.bkf_card + stand.bank_card + stand.token
        //   + stand.mobile + stand.changer_revalue,
        icon: this.getIconForDeviceType(stand.source),
        ...stand,
      }));
    },
  },
  methods: {
    getIconForDeviceType(deviceType) {
      if (deviceType in this.deviceIcons) {
        return this.deviceIcons[deviceType];
      }
      return 'mdi-help';
    },
  },
};
</script>
