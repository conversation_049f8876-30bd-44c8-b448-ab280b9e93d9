<template>
  <div>
    <v-col
      ols="12"
      class="mt-5"
    >
      <v-layout
        row
        wrap
      >
        <v-col
          cols="12"
          sm="8"
          class="text-sm-start"
        >
          <heading
            :dates="filtering.dates"
          />
        </v-col>
        <v-col
          cols="12"
          sm="4"
          class="d-flex justify-end"
        >
          <btn-refresh
            class="mr-2"
            :disabled="loader"
            @click="getData"
          />
          <report-create-modal
            btn-class="ml-2"
            :params="exportAsyncParams"
            :disabled="loader"
            :preset="filtering.dates.value"
          />
        </v-col>
      </v-layout>
    </v-col>
    <v-col
      cols="12"
      class="pt-0"
    >
      <turnover-table
        :loader="loader"
        :items="itemsFiltered"
        :summary="summary"
        :currency-symbol="currencySymbol"
      />
    </v-col>
  </div>
</template>

<script>
import Heading from '@components/finance/turnover/total/Heading.vue';
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import TurnoverTable from '@components/finance/turnover/total/TurnoverTable.vue';
import FiltersHandlingMixin from '@components/common/mixins/FiltersHandlingMixin.vue';
import DataFetchMixin from '@components/finance/turnover/mixins/DataFetchMixin.vue';
import ReportCreateModal from '@components/reports/ReportCreateModal.vue';
import BtnRefresh from '@components/common/button/BtnRefresh.vue';

export default {
  name: 'TurnoverTotal',
  components: {
    BtnRefresh,
    ReportCreateModal,
    TurnoverTable,
    Heading,
  },
  mixins: [
    FilterMixin,
    FiltersHandlingMixin,
    DataFetchMixin,
  ],
  props: {
    dates: {
      type: Object,
      default: () => ({}),
    },
    carwash: {
      type: [Object, Number],
      default: null,
      nullable: true,
    },
  },
  data() {
    return {
      settingsNamespace: 'finance:dates',
      filtering: {
        dataSource: {
          dataUrl: '/api/reports/data',
          lastCollectionUrl: '/api/reports/data',
        },
      },
    };
  },
  computed: {
    summary() {
      return this.pageSums;
    },
    currencySymbol() {
      return this.currencyObject.symbol;
    },
    itemsFiltered() {
      return this.items;
    },
    exportAsyncParams() {
      return {
        serial: this.filtering.carwash || null,
        startDate: this.$options.filters.formatDateDayNull(this.filtering.dates.from),
        endDate: this.$options.filters.formatDateDayNull(this.filtering.dates.to),
        report: 'v2\\FinanceTurnoverTotal',
      };
    },
  },
  watch: {
    dates(val) {
      this.$set(this.filtering, 'dates', val);
      this.onFiltersChange();
      this.emitChangeEvent();
    },
    carwash(val) {
      this.$set(this.filtering, 'carwash', val);
      this.onFiltersChange();
      this.emitChangeEvent();
    },
  },
  mounted() {
    if (this.dates !== null) {
      this.$set(this.filtering, 'carwash', this.carwash);
      this.$set(this.filtering, 'dates', this.dates);
    }
  },
  methods: {
    parseApiResponseData(data) {
      const carwashes = Object.values(data);

      this.items = carwashes.map((carwash, index) => ({
        id: index,
        carwash: carwash.serialNumber,
        carwashName: carwash.carwashName,
        exchanger: carwash.exchanger,
        stands: carwash.stands,
        terminal: carwash.terminal,
        portal: carwash.portal,
        standsSum: carwash.standsSum,
        summary: carwash.summary,
        dates: this.filtering.dates,
        isRollover: typeof carwash.portal !== 'undefined',
        // ...carwash,
      }));
    },
    getParams() {
      // if (this.first) {
      //   const settings = this.getSetts('finance:fiscal-summary:status', '');
      //   this.filtering.fiscal = settings;
      //   this.first = false;
      // }

      return {
        params: {
          serial: this.filtering.carwash || null,
          startDate: this.$options.filters.formatDateDayNull(this.filtering.dates.from),
          endDate: this.$options.filters.formatDateDayNull(this.filtering.dates.to),
          report: 'v2\\FinanceTurnoverTotal',
        },
      };
    },
  },
};
</script>
