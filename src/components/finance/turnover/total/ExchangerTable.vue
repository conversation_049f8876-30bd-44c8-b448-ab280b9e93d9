<template>
  <v-card
    v-if="items.length"
    outlined
    color="blue-grey lighten-5"
  >
    <v-card-title class="px-2 py-2">
      <v-layout
        row
        wrap
      >
        <v-col
          cols="6"
          class="text-sm-start"
        >
          <span>{{ $t('turnover.table.exchanger') }}</span>
        </v-col>
      </v-layout>
    </v-card-title>
    <v-card-text>
      <v-data-table
        dense
        :headers="tableHeaders"
        :items="items"
        hide-default-footer
        class="elevation-2"
        mobile-breakpoint="0"
        disable-sort
        :items-per-page="-1"
      >
        <template #item="{ item: exchanger }">
          <tr>
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="exchanger.note"
              align="center"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="exchanger.coin"
              align="center"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="exchanger.bank_card"
              align="center"
              additional-class="border-right"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="exchanger.hopper_a"
              align="center"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="exchanger.hopper_b"
              align="center"
              additional-class="border-right"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="exchanger.bkf_card_revalue"
              align="center"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="exchanger.bkf_card_sold"
              align="center"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="exchanger.bay_charge"
              align="center"
              additional-class="border-right"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="exchanger.promotion"
              align="center"
              rowspan="3"
            />
          </tr>
          <tr class="text-center">
            <td
              :colspan="paidCellColspan"
              class="border-right"
            >
              <span>{{ $t('turnover.table.paid') }}: </span>
              <strong>{{ exchanger.income|currencySymbol(currencySymbol) }}</strong>
            </td>
            <td
              colspan="2"
              class="border-right"
            >
              <span>{{ $t('turnover.table.exchanges') }}: </span>
              <strong>{{ exchanger.outcome|currencySymbol(currencySymbol) }}</strong>
            </td>
            <td
              colspan="3"
              class="border-right"
            >
              <span>{{ $t('turnover.table.sell') }}: </span>
              <strong>{{ exchanger.selling|currencySymbol(currencySymbol) }}</strong>
            </td>
          </tr>
          <tr class="table-summary">
            <td
              :colspan="balanceCellColspan"
              class="border-right"
            >
              <span>
                {{ $t('turnover.table.balance') }}:
              </span>
              <strong
                v-if="exchanger.balance !== 0"
                class="deep-orange--text text--darken-3"
              >
                {{ exchanger.balance|currencySymbol(currencySymbol) }}
              </strong>
              <strong v-else>
                {{ exchanger.balance|currencySymbol(currencySymbol) }}
              </strong>
            </td>
          </tr>
        </template>
      </v-data-table>
    </v-card-text>
  </v-card>
</template>

<script>
import CustomCurrencySymbolCell from '@components/common/CustomCurrencySymbolCell.vue';

export default {
  name: 'ExchangerTable',
  components: {
    CustomCurrencySymbolCell,
  },
  props: {
    parent: {
      type: Object,
      required: true,
    },
    currencySymbol: {
      type: String,
      default: undefined,
    },
  },
  data() {
    return {
      headers: [
        {
          value: 'note',
          text: this.$t('turnover.table.banknotes'),
          align: 'center',
        },
        {
          value: 'coin',
          text: this.$t('turnover.table.coins'),
          align: 'center',
        },
        {
          value: 'bank_card',
          text: this.$t('turnover.table.bankCards'),
          align: 'center',
        },
        {
          value: 'hopper_a',
          text: this.$t('turnover.table.hopperA'),
          align: 'center',
        },
        {
          value: 'hopper_b',
          text: this.$t('turnover.table.hopperB'),
          align: 'center',
        },
        {
          value: 'bkf_card_revalue',
          text: this.$t('turnover.table.bkfKeyRecharge'),
          align: 'center',
        },
        {
          value: 'bkf_card_sold',
          text: this.$t('turnover.table.bkfCardSale'),
          align: 'center',
        },
        {
          value: 'carwashRecharge',
          text: this.$t('turnover.table.carwashRecharge'),
          align: 'center',
        },
        {
          value: 'promotion',
          text: this.$t('turnover.table.promotion'),
          align: 'center',
        },
      ],
    };
  },
  computed: {
    items() {
      if (typeof this.parent.exchanger === 'undefined' || !this.parent.exchanger) {
        return [];
      }

      return [this.parent.exchanger];
    },
    tableHeaders() {
      return this.headers.filter((h) => h.value !== 'bkfKeyRecharge' && h.value !== 'bkfCardSale').map((h) => h);
    },
    paidCellColspan() {
      return 3;
    },
    balanceCellColspan() {
      return 8;
    },
  },
};
</script>
