<template>
  <h2>
    <span>{{ $t('finance_tabsTotal') }} {{ $t('finance_turnover') }}</span>

    <template v-if="dates.isLastCollection">
      <span>{{ ` ${$t('finance_fromLastCollection')}` }}</span>
    </template>

    <template v-else>
      <span v-if="dateFrom !== dateTo">
        {{ $t('finance_turnoverFrom') }} {{ dateFrom }} {{ $t('finance_turnoverTo') }} {{ dateTo }}
      </span>
      <span v-else>
        {{ $t('finance_turnoverFor') }} {{ dateFrom }}
      </span>
    </template>
  </h2>
</template>

<script>

export default {
  name: 'HeadingTurnOver',
  props: {
    dates: {
      type: Object,
      required: true,
    },
  },
  computed: {
    dateFrom() {
      return this.$options.filters.formatDateDay(this.dates.from);
    },
    dateTo() {
      return this.$options.filters.formatDateDay(this.dates.to);
    },
  },
};
</script>
