<template>
  <v-card
    v-if="terminal"
    outlined
    color="blue-grey lighten-5"
  >
    <v-card-title class="px-2 py-2">
      <v-layout
        row
        wrap
      >
        <v-col
          cols="6"
          class="text-sm-start"
        >
          <span>{{ $t('turnover.table.terminal') }}</span>
        </v-col>
      </v-layout>
    </v-card-title>

    <v-card-text>
      <v-simple-table
        class="elevation-2 text-center"
        dense
      >
        <thead>
          <tr>
            <th class="text-center">
              {{ $t('turnover.table.banknotes') }}
            </th>
            <th class="text-center">
              {{ $t('turnover.table.coins') }}
            </th>
            <th class="text-center">
              {{ $t('turnover.table.bankCards') }}
            </th>
            <th class="text-center">
              {{ $t('turnover.table.bkfKey') }}
            </th>
            <th class="text-center">
              {{ $t('fiscal_transactions.type.MOBILE') }}
            </th>
            <th class="text-center">
              {{ $t('turnover.table.service') }}
            </th>
            <th class="text-center">
              {{ $t('turnover.table.hopperA') }}
            </th>
            <th class="text-center">
              {{ $t('turnover.table.hopperB') }}
            </th>
            <th class="text-center">
              {{ $t('turnover.table.programsSale') }}
            </th>
          </tr>
        </thead>

        <tbody>
          <tr>
            <td>
              <currency-formatter
                :symbol="currencySymbol"
                :value="terminal.note"
              />
            </td>
            <td>
              <currency-formatter
                :symbol="currencySymbol"
                :value="terminal.coin"
              />
            </td>
            <td>
              <currency-formatter
                :symbol="currencySymbol"
                :value="terminal.bank_card"
              />
            </td>
            <td>
              <currency-formatter
                :symbol="currencySymbol"
                :value="terminal.bkf_card_pay"
              />
            </td>
            <td class="border-right">
              <currency-formatter
                :symbol="currencySymbol"
                :value="terminal.mobile"
              />
            </td>
            <td class="border-right">
              <currency-formatter
                :symbol="currencySymbol"
                :value="terminal.service"
              />
            </td>
            <td>
              <currency-formatter
                :symbol="currencySymbol"
                :value="terminal.hopper_a"
              />
            </td>
            <td class="border-right">
              <currency-formatter
                :symbol="currencySymbol"
                :value="terminal.hopper_b"
              />
            </td>
            <td>
              <currency-formatter
                :symbol="currencySymbol"
                :value="terminal.selling"
              />
            </td>
          </tr>

          <tr>
            <td
              :colspan="5"
              class="border-right"
            >
              <span>{{ $t('turnover.table.paid') }}: </span>
              <strong><currency-formatter
                :symbol="currencySymbol"
                :value="terminal.income"
              /></strong>
            </td>
            <td
              class="border-right"
            >
              <strong><currency-formatter
                :symbol="currencySymbol"
                :value="terminal.service"
              /></strong>
            </td>
            <td
              :colspan="2"
              class="border-right"
            >
              <span>{{ $t('turnover.table.exchanges') }}: </span>
              <strong>
                <currency-formatter
                  :symbol="currencySymbol"
                  :value="terminal.outcome"
                />
              </strong>
            </td>
            <td class="border-right">
              <span>{{ $t('turnover.table.sell') }}: </span>
              <strong>
                <currency-formatter
                  :symbol="currencySymbol"
                  :value="terminal.selling"
                />
              </strong>
            </td>
          </tr>

          <tr class="table-summary">
            <td
              :colspan="8"
              class="border-right"
            >
              <span>{{ $t('turnover.table.balance') }}:</span>
              <strong
                :class="terminal.balance !== 0 ? 'deep-orange--text text--darken-3' : ''"
              >
                <currency-formatter
                  :symbol="currencySymbol"
                  :value="terminal.balance"
                />
              </strong>
            </td>
          </tr>
        </tbody>
      </v-simple-table>
    </v-card-text>
  </v-card>
</template>

<script>
import CurrencyFormatter from '@components/common/formatters/CurrencyFormatter.vue';

export default {
  components: {
    CurrencyFormatter,
  },
  props: {
    terminal: {
      type: Object,
      required: true,
    },
    currencySymbol: {
      type: String,
      default: undefined,
    },
  },
};
</script>
