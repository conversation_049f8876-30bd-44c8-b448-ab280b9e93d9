<template>
  <v-data-table
    :headers="headers"
    :items="tableItems"
    item-key="id"
    :loading="loader"
    :single-expand="true"
    :expanded.sync="expanded"
    hide-default-footer
    disable-pagination
    mobile-breakpoint="0"
    :sort-by="sortBy"
    :sort-desc="sortDesc"
  >
    <!--Loader-->
    <template #progress>
      <div class="text-center mx-n4">
        <v-progress-linear
          class="loader"
          indeterminate
          color="primary"
        />
      </div>
    </template>

    <!--Item-->
    <template #item="{ item, expand, isExpanded }">
      <tr
        v-if="!loader"
        :key="item.serialNumber"
        class="text-sm-start  cursor-pointer"
        @click="onRowClick({item, expand, isExpanded})"
      >
        <td class="text-start tabcell-carwash font-weight-bold">
          <rollover-badge v-if="item.isRollover" />
          <self-service-badge v-else />
          <span>{{ item.carwashName }}</span>
        </td>
        <custom-currency-symbol-cell
          :value="item.summary.cash"
          :currency="currencySymbol"
        />
        <custom-currency-symbol-cell
          :value="item.summary.cashless"
          :currency="currencySymbol"
          additional-class="border-right"
        />
        <custom-currency-symbol-cell
          :value="item.summary.selling"
          :currency="currencySymbol"
          additional-class="border-right"
        />
        <custom-currency-symbol-cell
          :value="item.summary.sum"
          weight="bold"
          additional-class="border-right"
          :currency="currencySymbol"
        />
        <custom-currency-symbol-cell
          :value="item.summary.prepaid"
          :currency="currencySymbol"
        />
        <custom-currency-symbol-cell
          :value="item.summary.exchanging"
          :currency="currencySymbol"
        />
        <custom-currency-symbol-cell
          :value="item.summary.promotion"
          :currency="currencySymbol"
        />
        <td class="text-right">
          <span>{{ item.summary.client }}</span>
        </td>
        <td class="text-right">
          <v-icon v-if="isExpanded">
            mdi-chevron-up
          </v-icon>
          <v-icon v-else>
            mdi-chevron-down
          </v-icon>
        </td>
      </tr>
    </template>

    <!--Summary-->
    <template #[`body.append`]>
      <template v-if="!loader">
        <tr class="table-summary">
          <td class="text-start font-weight-bold">
            {{ $t('turnover.table.total') }}
          </td>
          <custom-currency-symbol-cell
            weight="bold"
            :value="summary.cash"
            :currency="currencySymbol"
          />
          <custom-currency-symbol-cell
            weight="bold"
            :value="summary.cashless"
            :currency="currencySymbol"
            additional-class="border-right"
          />
          <custom-currency-symbol-cell
            weight="bold"
            :value="summary.selling"
            :currency="currencySymbol"
            additional-class="border-right"
          />
          <custom-currency-symbol-cell
            weight="bold"
            :value="summary.sum"
            :currency="currencySymbol"
            additional-class="border-right"
          />
          <custom-currency-symbol-cell
            weight="bold"
            :value="summary.prepaid"
            :currency="currencySymbol"
          />
          <custom-currency-symbol-cell
            weight="bold"
            :value="summary.exchanging"
            :currency="currencySymbol"
          />
          <custom-currency-symbol-cell
            weight="bold"
            :value="summary.promotion"
            :currency="currencySymbol"
          />
          <td class="text-end font-weight-bold">
            {{ summary.client }}
          </td>
          <td />
        </tr>
      </template>
    </template>

    <!--Expanded item-->
    <template #expanded-item="{ headers: _headers, item }">
      <td
        v-if="!loader"
        :colspan="_headers.length"
        class="elevation-3"
      >
        <stands-table
          v-if="'stands' in item"
          :key="`stands-table-${item.serialNumber}`"
          :parent="item"
          :sums="item.standsSum"
          :currency-symbol="currencySymbol"
        />
        <exchanger-table
          v-if="'exchanger' in item"
          :key="`exchanger-table-${item.serialNumber}`"
          :parent="item"
          :currency-symbol="currencySymbol"
        />
        <terminal-table
          :terminal="item.terminal"
          :currency-symbol="currencySymbol"
        />
        <portal-table
          v-if="'portal' in item"
          :key="`portal-table-${item.serialNumber}`"
          :parent="item"
          :currency-symbol="currencySymbol"
        />
      </td>
    </template>
  </v-data-table>
</template>

<script>
import ExchangerTable from '@components/finance/turnover/total/ExchangerTable.vue';
import PortalTable from '@components/finance/turnover/total/PortalTable.vue';
import StandsTable from '@components/finance/turnover/total/StandsTable.vue';
import TerminalTable from '@components/finance/turnover/total/TerminalTable.vue';
import SelfServiceBadge from '@components/common/badge/SelfServiceBadge.vue';
import RolloverBadge from '@components/common/badge/RolloverBadge.vue';
import CustomCurrencySymbolCell from '@components/common/CustomCurrencySymbolCell.vue';

export default {
  name: 'TurnoverTable',
  components: {
    CustomCurrencySymbolCell,
    SelfServiceBadge,
    RolloverBadge,
    ExchangerTable,
    StandsTable,
    PortalTable,
    TerminalTable,
  },
  props: {
    items: {
      type: Array,
      default() {
        return [];
      },
    },
    summary: {
      type: Object,
      default() {
        return {};
      },
    },
    currencySymbol: {
      type: String,
      default: undefined,
    },
    loader: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      sortBy: null,
      sortDesc: false,
      expanded: [],
      headers: [
        {
          value: 'carwashName',
          text: this.$t('turnover.table.carwash'),
        },
        {
          value: 'summary.cash',
          text: this.$t('turnover.table.cash'),
          align: 'end',
        },
        {
          value: 'summary.cashless',
          text: this.$t('turnover.table.cashless'),
          align: 'end',
        },
        {
          value: 'summary.selling',
          text: this.$t('turnover.table.exchangerSale'),
          align: 'end',
        },
        {
          value: 'summary.sum',
          text: this.$t('turnover.table.carwashEarnings'),
          align: 'end',
        },
        {
          value: 'summary.prepaid',
          text: this.$t('turnover.table.prepaid'),
          align: 'end',
        },
        {
          value: 'summary.exchanging',
          text: this.$t('turnover.table.exchangerHoppers'),
          align: 'end',
        },
        {
          value: 'summary.promotion',
          text: this.$t('turnover.table.promotion'),
          align: 'end',
        },
        {
          value: 'summary.client',
          text: this.$t('turnover.table.clients'),
          align: 'end',
        },
        {
          value: 'expand',
          sortable: false,
          text: '',
        },
      ],
    };
  },
  computed: {
    tableItems() {
      if (!this.items.length || this.loader) {
        return [];
      }

      return this.items;
    },
  },
  watch: {
    items() {
      this.expanded = [];
      if (this.items.length > 0) {
        this.$set(this.expanded, 0, this.items[0]);
      }
    },
  },
  methods: {
    onRowClick({
      expand,
      isExpanded,
    }) {
      expand(!isExpanded);
    },
  },
};
</script>
