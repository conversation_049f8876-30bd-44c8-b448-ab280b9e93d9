<template>
  <div>
    <v-col v-if="showLastCollectionError">
      <v-alert>
        <bkf-alert
          :message="$t('finance_periodError')"
          :dismissible="false"
          type="warning"
        />
      </v-alert>
    </v-col>
    <template v-else>
      <v-col
        v-show="!hasErrors"
        cols="12"
      >
        <v-layout
          row
          wrap
          class="d-flex justify-start"
        >
          <v-col
            cols="12"
            md="4"
          >
            <type-filter
              :disabled="loader"
              @change="onFiltersChange"
            />
          </v-col>
        </v-layout>
        <v-layout
          row
          wrap
        >
          <v-col
            cols="12"
            sm="8"
            class="text-sm-start"
          >
            <heading
              :dates="filtering.dates"
              :carwash="filtering.carwash"
            />
          </v-col>
          <v-col
            cols="12"
            sm="4"
            class="d-flex justify-end"
          >
            <btn-refresh
              class="mr-2"
              :disabled="loader"
              @click="getAllData"
            />
            <report-create-modal
              btn-class="ml-2"
              :params="exportAsyncParams"
              :disabled="loader"
              :preset="filtering.dates.value"
            />
          </v-col>
        </v-layout>
      </v-col>
      <div
        v-if="!hasErrors"
      >
        <v-row>
          <v-col
            cols="12"
            lg="8"
          >
            <div
              class="d-flex flex-column"
            >
              <stack-bar-with-line-chart
                class="px-2"
                :title="$t('finance_linechartTitle')"
                :legend="chartLegend"
                legend-align="left"
                :x-data="chartRanges"
                :bar-series-values="barChartSeriesValues"
                :line-series-values="lineChartSeriesValues"
                :loader="loader"
                :unit="currencySymbol"
                bar-max-width="90"
                legend-vertical-align="0"
                :selected-legend-options="selectedLegendOptionsOriginal"
                @legendselectchanged="legendSelectChanged"
              />
              <v-checkbox
                v-model="compareToPreviousYear"
                :label="$t('finance_compareWithPreviousYear')"
                class="align-self-end mr-14"
                @change="onCompareToPreviousYearChange"
              />
            </div>
          </v-col>
          <v-col
            cols="12"
            lg="4"
          >
            <payments-share-pie-chart
              :loader="loader"
              :items="pieChartsItems"
              :currency-symbol="currencySymbol"
              wrapper-class="px-2"
            />
          </v-col>
        </v-row>
      </div>
      <div
        v-if="!hasErrors"
      >
        <v-col
          cols="12"
          class="pt-3"
        >
          <turnover-table
            :loader="loader"
            :items="items"
            :date-format-func="formatDate"
            :currency-symbol="currencySymbol"
          />
        </v-col>
      </div>
    </template>
  </div>
</template>

<script>
import Heading from '@components/finance/turnover/monthly/Heading.vue';
import TurnoverTable from '@components/finance/turnover/monthly/TurnoverTable.vue';
import FiltersHandlingMixin from '@components/common/mixins/FiltersHandlingMixin.vue';
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import DataFetchMixin from '@components/finance/turnover/mixins/DataFetchMixin.vue';
import ExportMixin from '@components/finance/turnover/mixins/ExportMixin.vue';
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import TypeFilter from '@components/common/filters/TypeFilter.vue';
import PaymentsSharePieChart from '@components/finance/turnover/monthly/PaymentsSharePieChart.vue';
import StackBarWithLineChart from '@components/common/charts/StackBarWithLineChart.vue';
import ReportCreateModal from '@components/reports/ReportCreateModal.vue';
import BkfAlert from '@components/common/BkfAlert.vue';

export default {
  name: 'TurnoverMonthly',
  components: {
    BkfAlert,
    ReportCreateModal,
    PaymentsSharePieChart,
    StackBarWithLineChart,
    TypeFilter,
    BtnRefresh,
    TurnoverTable,
    Heading,
  },
  mixins: [
    FilterMixin,
    FiltersHandlingMixin,
    DataFetchMixin,
    ExportMixin,
  ],
  props: {
    dates: {
      type: Object,
      default: () => ({}),
    },
    carwash: {
      type: [Object, Number],
      default: null,
      nullable: true,
    },
  },
  data() {
    return {
      settingsNamespace: 'finance:dates',
      filtering: {
        dataSource: {
          value: '',
          text: '',
          dataUrl: '/api/reports/data',
          dateFormatFunc: this.$options.filters.formatDateDay,
          // exportOptions: [],
        },
      },
      chartColumns: [
        {
          name: this.$t('turnover.table.coins'),
          field: 'coin',
          pieChart: true,
          lineChart: true,
          color: '#3B81E3',
        },
        {
          name: this.$t('turnover.table.banknotes'),
          field: 'bill',
          pieChart: true,
          lineChart: true,
          color: '#2A5DA3',
        },
        {
          name: this.$t('turnover.table.bankCards'),
          field: 'bank_card',
          pieChart: true,
          lineChart: true,
          color: '#5E7DA8',
        },
        {
          name: this.$t('turnover.table.mobilePayments'),
          field: 'mobile',
          pieChart: true,
          lineChart: true,
          color: '#81A9E6',
        },
        {
          name: this.$t('turnover.table.bkfKey'),
          field: 'bkf_card',
          pieChart: true,
          lineChart: true,
          color: '#9EA8B8',
        },
        {
          name: this.$t('turnover.table.tokens'),
          field: 'token',
          pieChart: true,
          lineChart: true,
          color: '#585D66',
        },
        {
          name: this.$t('turnover.table.post'),
          field: 'changer_revalue',
          pieChart: true,
          lineChart: true,
          color: '#10233D',
        },
      ],
      compareToPreviousYear: false,
      previousYearItems: [],
      lineChartSeriesValues: [],
      selectedLegendOptions: [],
      selectedLegendOptionsOriginal: {},
    };
  },
  computed: {
    currencySymbol() {
      return this.currencyObject.symbol;
    },
    pieChartsItems() {
      return this.pageSums;
    },
    showLastCollectionError() {
      return this.dates.isLastCollection;
    },
    chartRanges() {
      return this.items.map((item) => this.filtering.dataSource.dateFormatFunc(item.date))
        .sort((a, b) => a - b);
    },
    barChartSeriesValues() {
      return this.chartColumns
        .map((column) => ({
          data: this.items.sort((a, b) => a.date - b.date).map((item) => item[column.field]),
          name: column.name,
          color: column.color,
        }));
    },
    chartLegend() {
      return this.chartColumns.filter((c) => c.lineChart);
    },
    exportAsyncParams() {
      return this.getParams().params;
    },
  },
  watch: {
    dates(val) {
      if (!val.isLastCollection) {
        this.$set(this.filtering, 'dates', val);
        this.onFiltersChange();
        this.emitChangeEvent();
      }
    },
    carwash(val) {
      this.$set(this.filtering, 'carwash', val);
      this.onFiltersChange();
      this.emitChangeEvent();
    },
    filtering: {
      handler(newValue, oldValue) {
        if (
          newValue.dates.from !== oldValue.dates.from
          || newValue.carwash !== oldValue.carwash
        ) {
          this.getData();
        }

        this.onCompareToPreviousYearChange(this.compareToPreviousYear);
      },
      deep: true,
    },
  },
  mounted() {
    if (this.dates !== null && !this.dates.isLastCollection) {
      this.$set(this.filtering, 'carwash', this.carwash);
      this.$set(this.filtering, 'dates', this.dates);
    }
    this.selectedLegendOptions = this.chartColumns.map(
      (item) => ({ name: item.name, field: item.field }),
    );
    this.selectedLegendOptionsOriginal = this.chartColumns.reduce((acc, item) => {
      acc[item.name] = true;
      return acc;
    }, {});
    this.getAllData();
  },
  methods: {
    formatDate(data) {
      if (this.filtering.grouping === 'monthly') {
        return this.$options.filters.formatDateMonth(data);
      }

      return this.$options.filters.formatDateDay(data);
    },
    getAllData() {
      if (!this.dates.isLastCollection) {
        this.getData();
        this.onCompareToPreviousYearChange(this.compareToPreviousYear);
      }
    },
    async onCompareToPreviousYearChange(compare) {
      if (compare) {
        const dateFrom = new Date(this.filtering.dates.from);
        const dateFromYearAgo = new Date(dateFrom);
        dateFromYearAgo.setFullYear(dateFrom.getFullYear() - 1);

        const dateTo = new Date(this.filtering.dates.to);
        const dateToYearAgo = new Date(dateTo);
        dateToYearAgo.setFullYear(dateTo.getFullYear() - 1);

        this.loader = true;

        const response = await this.axios.get(
          this.filtering.dataSource.dataUrl,
          {
            params: {
              serial: this.filtering.carwash,
              startDate: this.$options.filters.formatDateDay(dateFromYearAgo),
              endDate: this.$options.filters.formatDateDay(dateToYearAgo),
              report: 'v2\\FinanceTurnoverDetailed',
              type: this.filtering.grouping || 'daily',
            },
          },
        );

        this.loader = false;

        const { data } = response.data;
        this.previousYearItems = data;

        this.lineChartSeriesValues = [{
          data: data.map(
            (item) => this.selectedLegendOptions.reduce(
              (accumulator, { field }) => accumulator + (field !== 'summary' ? (item[field] || 0) : 0),
              0,
            ),
          ),
          name: this.$t('common_previousYear'),
          color: 'red',
        }];
      } else {
        this.lineChartSeriesValues = [];
        this.previousYearItems = [];
      }
    },
    legendSelectChanged(selectedLegendOption) {
      this.selectedLegendOptionsOriginal = selectedLegendOption.selected;
      this.selectedLegendOptions = Object.entries(selectedLegendOption.selected)
        .filter(([, value]) => value)
        .map(
          ([key]) => {
            const index = this.chartColumns.findIndex((element) => element.name === key);
            return ({ name: key, field: this.chartColumns[index].field });
          },
        );

      if (this.compareToPreviousYear) {
        const dataToLineChart = this.previousYearItems.map(
          (item) => {
            const reduced = this.selectedLegendOptions.reduce(
              (accumulator, { field }) => accumulator + (item[field] || 0),
              0,
            );

            return reduced;
          },
        );
        this.lineChartSeriesValues = [{
          data: dataToLineChart,
          name: this.$t('common_previousYear'),
          color: 'red',
        }];
      }
    },
    getParams() {
      if (
        this.settingsNamespace !== undefined
        && this.filtering.dates.value !== undefined
        && this.filtering.dates.value !== 'custom'
      ) {
        let settings = this.filtering.dates.value;
        if (this.first && this.filtering.dates.value !== undefined) {
          settings = this.getSetts(this.settingsNamespace, this.filtering.dates.value);
          this.filtering.dates = this.getDateItemByValue(settings);
          this.first = false;
        }

        this.$set(this.filtering.dates, 'value', settings);

        this.setSetts(this.settingsNamespace, this.filtering.dates.value);
      }

      return {
        params: {
          serial: this.filtering.carwash || null,
          startDate: this.$options.filters.formatDateDay(this.filtering.dates.from),
          endDate: this.$options.filters.formatDateDay(this.filtering.dates.to),
          report: 'v2\\FinanceTurnoverDetailed',
          type: this.filtering.grouping || 'daily',
        },
      };
    },
  },
};
</script>
