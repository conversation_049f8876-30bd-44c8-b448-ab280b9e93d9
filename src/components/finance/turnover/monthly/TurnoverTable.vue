<template>
  <v-data-table
    :headers="headers"
    :items="tableItems"
    item-key="id"
    :loading="loader"
    hide-default-footer
    disable-pagination
    mobile-breakpoint="0"
    :sort-by="sortBy"
    :sort-desc="sortDesc"
  >
    <!--Loader-->
    <template #progress>
      <div class="text-center mx-n4">
        <v-progress-linear
          class="loader"
          indeterminate
          color="primary"
        />
      </div>
    </template>

    <!--Item-->
    <template #item="{ item }">
      <tr>
        <td class="text-start tabcell-date font-weight-bold">
          {{ dateFormatFunc(item.date) }}
        </td>
        <custom-currency-symbol-cell
          :currency="currencySymbol"
          :value="item.coin"
        />
        <custom-currency-symbol-cell
          :currency="currencySymbol"
          :value="item.bill"
        />
        <custom-currency-symbol-cell
          :currency="currencySymbol"
          :value="item.bank_card"
        />
        <custom-currency-symbol-cell
          :currency="currencySymbol"
          :value="item.mobile"
        />
        <custom-currency-symbol-cell
          :currency="currencySymbol"
          :value="item.summary"
          weight="bold"
          additional-class="border-right"
        />
        <custom-currency-symbol-cell
          :currency="currencySymbol"
          :value="item.bkf_card"
        />
        <custom-currency-symbol-cell
          :currency="currencySymbol"
          :value="item.token"
        />
        <custom-currency-symbol-cell
          :currency="currencySymbol"
          :value="item.changer_revalue"
        />
        <custom-currency-symbol-cell
          :currency="currencySymbol"
          :value="item.promotion"
        />
        <custom-currency-symbol-cell
          :currency="currencySymbol"
          :value="item.service"
        />
        <td class="text-right">
          {{ item.client }}
        </td>
      </tr>
    </template>

    <!--Summary-->
    <template #[`body.append`]>
      <template v-if="!loader">
        <tr class="table-summary">
          <td class="text-start font-weight-bold">
            {{ $t('turnover.table.total') }}
          </td>
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="items|sumProperty('coin')"
            weight="bold"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="items|sumProperty('bill')"
            weight="bold"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="items|sumProperty('bank_card')"
            weight="bold"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="items|sumProperty('mobile')"
            weight="bold"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="items|sumProperty('summary')"
            additional-class="border-right"
            weight="bold"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="items|sumProperty('bkf_card')"
            weight="bold"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="items|sumProperty('token')"
            weight="bold"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="items|sumProperty('changer_revalue')"
            weight="bold"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="items|sumProperty('promotion')"
            weight="bold"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="items|sumProperty('service')"
            weight="bold"
          />
          <td class="text-right font-weight-bold">
            {{ items|sumProperty('client') }}
          </td>
        </tr>
      </template>
    </template>
  </v-data-table>
</template>

<script>
import CustomCurrencySymbolCell from '@components/common/CustomCurrencySymbolCell.vue';

export default {
  name: 'TurnoverTable',
  components: {
    CustomCurrencySymbolCell,
  },
  props: {
    items: {
      type: Array,
      default() {
        return [];
      },
    },
    loader: {
      type: Boolean,
      default: false,
    },
    currencySymbol: {
      type: String,
      default: undefined,
    },
    dateFormatFunc: {
      type: Function,
      required: true,
    },
  },
  data() {
    return {
      graphSelectedColumns: [],
      headers: [
        {
          value: 'date',
          text: this.$t('turnover.table.date'),
        },
        {
          value: 'coins',
          text: this.$t('turnover.table.coins'),
          align: 'end',
          sortable: false,
        },
        {
          value: 'banknotes',
          text: this.$t('turnover.table.banknotes'),
          align: 'end',
          sortable: false,
        },
        {
          value: 'bankCards',
          text: this.$t('turnover.table.bankCards'),
          align: 'end',
          sortable: false,
        },
        {
          value: 'mobilePayments',
          text: this.$t('turnover.table.mobilePayments'),
          align: 'end',
          sortable: false,
        },
        {
          value: 'sum',
          text: this.$t('turnover.table.sum'),
          align: 'end',
          sortable: false,
        },
        {
          value: 'bkfKey',
          text: this.$t('turnover.table.bkfKey'),
          align: 'end',
          sortable: false,
        },
        {
          value: 'tokens',
          text: this.$t('turnover.table.tokens'),
          align: 'end',
          sortable: false,
        },
        {
          value: 'post',
          text: this.$t('turnover.table.post'),
          align: 'end',
          sortable: false,
        },
        {
          value: 'promotion',
          text: this.$t('turnover.table.promotion'),
          align: 'end',
          sortable: false,
        },
        {
          value: 'service',
          text: this.$t('turnover.table.service'),
          align: 'end',
          sortable: false,
        },
        {
          value: 'clients',
          text: this.$t('turnover.table.clients'),
          align: 'end',
          sortable: false,
        },
      ],
      sortBy: 'date',
      sortDesc: true,
    };
  },
  computed: {
    tableItems() {
      if (this.loader) {
        return [];
      }
      return this.items;
    },
  },
};
</script>

<style>
.v-application--is-ltr .v-input--selection-controls__input {
  margin-right: 0;
}
</style>
