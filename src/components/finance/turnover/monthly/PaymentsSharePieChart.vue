<template>
  <div :class="wrapperClass">
    <pie-chart
      :title="title"
      :name="$t('finance_paymenttypesharepieName')"
      :values="values"
      :loading="loader"
      :unit="currencySymbol"
    />
  </div>
</template>
<script>
import PieChart from '@components/common/charts/PieChart.vue';

export default {
  name: 'PaymentsSharePieChart',
  components: { PieChart },
  props: {
    title: {
      type: String,
      default() {
        return this.$t('common_paymenttypesharepieTitle');
      },
    },
    currencySymbol: {
      type: String,
      default: undefined,
    },
    wrapperClass: {
      type: [String, Object],
      default: '',
    },
    loader: {
      type: Boolean,
      default: false,
    },
    items: {
      type: [Array, Object],
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      chartColumns: [
        {
          name: 'coins',
          field: 'coin',
          pieChart: true,
          lineChart: true,
          color: '#3B81E3',
        },
        {
          name: 'banknotes',
          field: 'bill',
          pieChart: true,
          lineChart: true,
          color: '#2A5DA3',
        },
        {
          name: 'bankCards',
          field: 'bank_card',
          pieChart: true,
          lineChart: true,
          color: '#5E7DA8',
        },
        {
          name: 'mobilePayments',
          field: 'mobile',
          pieChart: true,
          lineChart: true,
          color: '#81A9E6',
        },
        {
          name: 'bkfKey',
          field: 'bkf_card',
          pieChart: true,
          lineChart: true,
          color: '#9EA8B8',
        },
        {
          name: 'tokens',
          field: 'token',
          pieChart: true,
          lineChart: true,
          color: '#585D66',
        },
        {
          name: 'post',
          field: 'changer_revalue',
          pieChart: true,
          lineChart: true,
          color: '#10233D',
        },
      ],
    };
  },
  computed: {
    values() {
      return this.chartColumns
        .filter((item) => this.items[item.field] > 0)
        .map((item) => ({
          name: this.$t(`turnover.table.${item.name}`),
          value: this.items[item.field],
          itemStyle: {
            color: item.color,
          },
        }));
    },
  },
};
</script>
