<template>
  <div>
    <h2>
      <span>{{ $t('finance_detailed') }} {{ $t('finance_turnover') }}</span>

      <template v-if="dates.isLastCollection">
        <span>{{ ` ${$t('finance_fromLastCollection')}` }}</span>
      </template>

      <template v-else>
        <span v-if="dateFrom !== dateTo">
          {{ $t('finance_turnoverFrom') }}
          {{ dateFrom }} {{ $t('finance_turnoverTo') }} {{ dateTo }}
        </span>
        <span v-else>
          {{ $t('finance_turnoverFor') }} {{ dateFrom }}
        </span>
      </template>
    </h2>
    <span
      v-if="carwash"
      class="text-subtitle-1"
    >
      {{ $t('finance_ofCarwash') }}{{ carwash }}
    </span>
    <span
      v-else
      class="text-subtitle-1"
    >
      {{ $t('finance_ofAllCarwashes') }}
    </span>
  </div>
</template>

<script>

export default {
  name: 'HeadingMonthly',
  props: {
    dates: {
      type: Object,
      required: true,
    },
    carwash: {
      type: Number,
      default: null,
    },
  },
  computed: {
    dateFrom() {
      return this.$options.filters.formatDateDay(this.dates.from);
    },
    dateTo() {
      return this.$options.filters.formatDateDay(this.dates.to);
    },
  },
};
</script>
