<template>
  <v-card
    class="px-3 py-3"
    outlined
    color="blue-grey lighten-5"
  >
    <h3 class="mb-3">
      {{ $t('fiscal_transactions.details.heading') }}
    </h3>
    <v-simple-table
      v-if="items.length"
      dense
      class="elevation-3"
    >
      <template #default>
        <tbody>
          <tr
            v-for="[key, value] in items"
            :key="`item-${key}`"
          >
            <td :key="`itemName-${key}`">
              {{ key }}
            </td>
            <td :key="`itemValue-${key}`">
              {{ value }}
            </td>
          </tr>
        </tbody>
      </template>
    </v-simple-table>
    <v-card-text v-else>
      <v-icon small>
        mdi-alert-circle-outline
      </v-icon>
      <i class="ml-2">{{ $t('common_noData') }}</i>
    </v-card-text>
  </v-card>
</template>

<script>
export default {
  name: 'DetailsTable',
  props: {
    parent: {
      type: Object,
      required: true,
    },
  },
  computed: {
    items() {
      if (typeof this.parent.details === 'undefined' || !this.parent.details) {
        return [];
      }

      return Object.entries(this.parent.details);
    },
  },
};
</script>
