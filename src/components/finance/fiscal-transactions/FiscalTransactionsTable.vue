<template>
  <v-data-table
    dense
    item-key="id"
    mobile-breakpoint="0"
    :headers="headers"
    :items="tableItems"
    :single-expand="true"
    :expanded.sync="expanded"
    :loading="loader"
    :options="options"
    :server-items-length="itemsTotal"
    :footer-props="footerProps"
    @update:options="onOptionsChange"
  >
    <!--Loader-->
    <template #progress>
      <div class="text-center mx-n4">
        <v-progress-linear
          class="loader"
          indeterminate
          color="primary"
        />
      </div>
    </template>

    <!--Item-->
    <template #item="{ item, expand, isExpanded }">
      <template v-if="!loader">
        <tr
          class="text-sm-start  cursor-pointer"
          @click="onRowClick({item, expand, isExpanded})"
        >
          <td class="text-start tabcell-carwash font-weight-bold">
            {{ item.date|formatDateDayTime }}
          </td>
          <td class="text-start tabcell-carwash">
            {{ item.carwashName }}
          </td>
          <td class="text-start">
            <device-type-badge
              :source="item.source"
              :stand-id="item.bayId"
            />
          </td>
          <td class="text-start">
            <v-icon
              small
              color="grey lighten-1"
              class="d-inline-block mr-1"
            >
              {{ typeIcons[item.type] }}
            </v-icon>
            {{ $t(`fiscal_transactions.type.${item.type}`) }}
          </td>
          <td class="text-start">
            {{ item.fiscal }}
          </td>
          <template v-if="item.net !== null">
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="item.net"
            />
          </template>
          <template v-else>
            <td class="text-center">
              -
            </td>
          </template>
          <template v-if="item.vat !== null">
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="item.vat"
            />
          </template>
          <template v-else>
            <td class="text-center">
              -
            </td>
          </template>
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="item.value"
          />
          <td
            class="text-end"
          >
            <v-icon v-if="isExpanded">
              mdi-chevron-up
            </v-icon>
            <v-icon v-else>
              mdi-chevron-down
            </v-icon>
          </td>
        </tr>
      </template>
    </template>

    <!--Summary-->
    <template #[`body.append`]>
      <template v-if="!loader">
        <tr class="table-summary">
          <td
            colspan="5"
            class="text-start"
          >
            {{ $t('common_totalOnPage') }}
          </td>
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="pageSums.net"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="pageSums.vat"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="pageSums.value"
          />
        </tr>
      </template>
    </template>

    <!--Expanded item-->
    <template #expanded-item="{ headers: _headers, item: _item }">
      <td
        v-if="!loader && 'details' in _item"
        :colspan="_headers.length"
        class="elevation-3"
      >
        <details-table
          :parent="_item"
        />
      </td>
    </template>
  </v-data-table>
</template>

<script>
import CustomCurrencySymbolCell from '@components/common/CustomCurrencySymbolCell.vue';
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import DeviceTypeBadge from '@components/libs/standard-types/badge/DeviceTypeBadge.vue';
import DetailsTable from './DetailsTable.vue';

export default {
  name: 'FiscalTransactionsTable',
  components: {
    CustomCurrencySymbolCell,
    DetailsTable,
    DeviceTypeBadge,
  },
  mixins: [
    FilterMixin,
  ],
  props: {
    items: {
      type: Array,
      default() {
        return [];
      },
    },
    options: {
      type: Object,
      default() {
        return {};
      },
    },
    pageSums: {
      type: Object,
      required: true,
    },
    currencySymbol: {
      type: String,
      default: undefined,
    },
    itemsTotal: {
      type: Number,
      default: -1,
    },
    loader: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      expanded: [],
      filtering: {
        options: {},
      },
      typeIcons: {
        CASH: 'mdi-cash',
        CASHLESS: 'mdi-credit-card-outline',
      },
      footerProps: {
        'items-per-page-options': [25, 50, 100],
        'items-per-page-text': this.$t('common_rowsPerPage'),
      },
      headers: [
        {
          value: 'date',
          text: this.$t('fiscal_transactions.table.date'),
          sortable: false,
        },
        {
          value: 'carwash',
          text: this.$t('fiscal_transactions.table.carwash'),
          sortable: false,
        },
        {
          value: 'device',
          text: this.$t('turnover.table.name'),
          sortable: false,
        },
        {
          value: 'type',
          text: this.$t('fiscal_transactions.table.type'),
          sortable: false,
        },
        {
          value: 'fiscalStatus',
          text: this.$t('fiscal_transactions.table.fiscal'),
          sortable: false,
        },
        {
          value: 'net',
          text: this.$t('fiscal_transactions.table.net'),
          sortable: false,
          align: 'end',
        },
        {
          value: 'vat',
          text: this.$t('fiscal_transactions.table.vat'),
          sortable: false,
          align: 'end',
        },
        {
          value: 'value',
          text: this.$t('fiscal_transactions.table.value'),
          sortable: false,
          align: 'end',
        },
        {
          value: 'expand',
          sortable: false,
          text: '',
          align: 'end',
        },
      ],
    };
  },
  computed: {
    tableItems() {
      if (this.loader) {
        return [];
      }
      return this.items;
    },
  },
  methods: {
    onOptionsChange(options) {
      this.$set(this.filtering, 'options', options);
    },
    onRowClick({ expand, isExpanded }) {
      expand(!isExpanded);
    },
  },
};
</script>
