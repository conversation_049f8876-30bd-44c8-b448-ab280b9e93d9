<template>
  <v-row>
    <v-col
      cols="12"
    >
      <v-row>
        <v-col
          cols="12"
        >
          <text-search
            :disabled="loader"
            @change="onFiltersChangePageReset"
          />
        </v-col>
      </v-row>
    </v-col>
    <v-col cols="12">
      <v-layout
        row
        wrap
      >
        <v-col
          cols="12"
          sm="8"
          class="text-sm-start"
        >
          <heading
            :dates="filtering.dates"
          />
        </v-col>
        <v-col
          cols="12"
          sm="4"
          class="d-flex justify-end"
        >
          <fiscal-add-modal
            v-if="userCountry === 'HR'"
            @submit="onFiltersChangePageReset"
          />
          <btn-refresh
            class="ml-2"
            @click="getData"
          />
          <report-create-modal
            btn-class="ml-2"
            :params="exportAsyncParams"
            :disabled="loader"
            :preset="filtering.dates.value"
          />
        </v-col>
      </v-layout>
    </v-col>
    <v-col
      cols="12"
      class="pt-0"
    >
      <fiscal-transactions-table
        :loader="loader"
        :items="items"
        :items-total="totalItems"
        :page-sums="pageSums"
        :total-sums="totalSums"
        :options="filtering.options"
        :currency-symbol="currencySymbol"
        @change="onFiltersChange"
      />
    </v-col>
  </v-row>
</template>

<script>
import Heading from '@components/finance/fiscal-transactions/Heading.vue';
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import FiscalTransactionsTable from '@components/finance/fiscal-transactions/FiscalTransactionsTable.vue';
import DataFetchMixin from '@components/common/mixins/DataFetchMixin.vue';
import FiltersHandlingMixin from '@components/common/mixins/FiltersHandlingMixin.vue';
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import DateOptionsMixins from '@components/common/mixins/DateOptionsMixins.vue';
import ReportCreateModal from '@components/reports/ReportCreateModal.vue';
import SettingsMixin from '@components/common/mixins/SettingsMixin.vue';
import TextSearch from '@components/common/filters/TextSearch.vue';
import FiscalAddModal from '@components/finance/fiscal-transactions/FiscalAddModal.vue';
import { mapGetters } from 'vuex';
import { endOfToday, startOfDay, subDays } from 'date-fns';

export default {
  name: 'FiscalTransactions',
  components: {
    ReportCreateModal,
    FiscalAddModal,
    TextSearch,
    Heading,
    FiscalTransactionsTable,
    BtnRefresh,
  },
  mixins: [
    DateOptionsMixins,
    SettingsMixin,
    DataFetchMixin,
    FiltersHandlingMixin,
    FilterMixin,
  ],
  props: {
    dates: {
      type: Object,
      default: () => ({}),
    },
    carwash: {
      type: [Object, Number],
      default: null,
      nullable: true,
    },
    fiscalStatus: {
      type: String,
      default: null,
      nullable: true,
    },
  },
  data() {
    return {
      curentDate: 'last7Days',
      first: true,
      defaultDates: {
        value: 'last7Days',
        text: this.$t('common_p7d'),
        from: startOfDay(subDays(new Date(), 6)),
        to: endOfToday(),
      },
      dataUrl: '/api/reports/data',
      filtering: {
        search: null,
        fiscal: null,
        options: {
          sortBy: ['date'],
          sortDesc: [true],
          itemsPerPage: 25,
        },
      },
      fiscalOptions: [
        'none',
        'transaction',
        'fiscalized',
        'test',
        'not_fiscalized',
        'error',
      ],
    };
  },
  computed: {
    ...mapGetters({
      userCountry: 'auth/userCountry',
    }),
    currentDate: {
      get() {
        return this.curentDate;
      },
      set(newValue) {
        this.curentDate = newValue;
      },
    },
    exportAsyncParams() {
      const carwash = this.filtering.carwash || null;
      return {
        serial: carwash,
        startDate: this.$options.filters.formatDateDay(this.filtering.dates.from),
        endDate: this.$options.filters.formatDateDay(this.filtering.dates.to),
        report: 'v2\\FinanceFiscalTransactions',
        search: this.filtering.search,
        fiscal: this.filtering.fiscal,
      };
    },
    currencySymbol() {
      return this.currencyObject.symbol;
    },
  },
  watch: {
    dates(val) {
      this.$set(this.filtering, 'dates', val);
      this.onFiltersChange();
      this.emitChangeEvent();
    },
    carwash(val) {
      this.$set(this.filtering, 'carwash', val);
      this.onFiltersChange();
      this.emitChangeEvent();
    },
    fiscalStatus(val) {
      this.$set(this.filtering, 'fiscal', val);
      this.onFiltersChange();
      this.emitChangeEvent();
    },
  },
  mounted() {
    if (this.dates !== null) {
      this.$set(this.filtering, 'carwash', this.carwash);
      this.$set(this.filtering, 'dates', this.dates);
      this.$set(this.filtering, 'fiscal', this.fiscalStatus);
      this.onFiltersChangePageReset();
    }
  },
  methods: {
    onFiltersChangePageReset(filters) {
      this.filtering.options.page = 1;
      this.onFiltersChange(filters);
      // debouncing and close all lists
    },
    parseApiResponseData(data) {
      const rows = Object.values(data);
      this.items = rows.map((row, index) => ({
        ...row,
        id: index,
        carwashName: row.carwashName,
        date: this.$options.filters.formatDateDayTime(row.time),
        dates: this.filtering.dates,
      }));
    },
    // afterItemsUpdate(data) {
    //   if ('possibleFiscalStatuses' in data) {
    //     this.fiscalOptions = data.possibleFiscalStatuses;
    //   }
    // },
    getParams() {
      return {
        params: {
          serial: this.filtering.carwash || null,
          startDate: this.$options.filters.formatDateDay(this.filtering.dates.from),
          endDate: this.$options.filters.formatDateDay(this.filtering.dates.to),
          order: this.filtering.options.sortDesc[0] ? 'DESC' : 'ASC',
          page: this.filtering.options.page || null,
          perPage: this.filtering.options.itemsPerPage || null,
          search: this.filtering.search,
          fiscal: this.filtering.fiscal,
          report: 'v2\\FinanceFiscalTransactions',
        },
      };
    },
  },
};
</script>
