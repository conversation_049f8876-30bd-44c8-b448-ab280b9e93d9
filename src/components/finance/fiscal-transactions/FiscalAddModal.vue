<template>
  <div>
    <btn-add
      @click="show = true"
    />
    <form-modal
      v-model="show"
      :title="$t('fiscal_transactions.modal_add.heading')"
      :url="url"
      @submit="$emit('submit')"
    >
      <v-container class="px-0">
        <v-row>
          <v-col>
            <v-select
              v-model="carwash"
              auto-select-first
              :items="carwashes"
              item-value="serialNumber"
              item-text="name"
              prepend-icon="mdi-car-wash"
              :label="$t('common_filtersCarwash')"
              required
              :rules="[rules.required]"
              name="sn"
            />
          </v-col>
        </v-row>
        <v-row>
          <v-col
            cols="10"
          >
            <v-select
              v-model="source"
              :items="sources"
              prepend-icon="mdi-point-of-sale"
              :label="$t('common_deviceType')"
              name="source"
              required
              :rules="[rules.required]"
              auto-select-first
            />
          </v-col>
          <v-col
            cols="2"
          >
            <v-text-field
              prepend-icon="mdi-music-accidental-sharp mdi-flip-h"
              type="number"
              name="bayId"
              min="1"
              step="1"
              :required="requiresBayId"
              :disabled="!requiresBayId"
              :rules="[requiresBayId ? rules.required : true]"
            />
          </v-col>
        </v-row>
        <v-row>
          <v-col>
            <v-text-field
              :label="$t('common_value')"
              prepend-icon="mdi-currency-usd"
              type="number"
              min="0"
              step="0.01"
              name="value"
              required
              :rules="[rules.required]"
            />
          </v-col>
          <v-col>
            <v-menu
              v-model="datetimePicker"
              :close-on-content-click="false"
            >
              <template #activator="{ on, attrs }">
                <v-text-field
                  :label="$t('common_paymentDate')"
                  prepend-icon="mdi-calendar-clock"
                  name="date"
                  v-bind="attrs"
                  readonly
                  :value="datetime"
                  required
                  :rules="[rules.required]"
                  v-on="on"
                />
              </template>
              <v-card>
                <v-card-text class="px-0 py-0">
                  <v-row>
                    <v-col class="pr-0">
                      <v-date-picker
                        v-model="date"
                      />
                    </v-col>
                    <v-col>
                      <v-time-picker
                        v-model="time"
                        use-seconds
                        format="24hr"
                      />
                    </v-col>
                  </v-row>
                </v-card-text>
                <v-card-actions>
                  <v-spacer />
                  <v-btn
                    color="primary"
                    @click="datetimePicker = false"
                  >
                    OK
                  </v-btn>
                </v-card-actions>
              </v-card>
            </v-menu>
          </v-col>
        </v-row>
        <v-row>
          <v-col>
            <v-text-field
              :label="$t('fiscal_transactions.modal_add.password')"
              prepend-icon="mdi-key"
              name="pw"
              required
              :rules="[rules.required]"
            />
          </v-col>
        </v-row>
        <v-row>
          <v-col>
            <v-text-field
              type="number"
              name="isu"
              :label="$t('fiscal_transactions.modal_add.isu')"
              step="1"
              required
              :rules="[rules.required, rules.nowhitespaces]"
              prepend-icon="mdi-barcode"
            />
          </v-col>
          <v-col>
            <v-text-field
              name="location"
              :label="$t('fiscal_transactions.modal_add.location')"
              prepend-icon="mdi-map-marker"
              :rules="[rules.nowhitespaces]"
            />
          </v-col>
        </v-row>
      </v-container>
    </form-modal>
  </div>
</template>

<script>

import FormModal from '@components/common/FormModal.vue';
import BtnAdd from '@components/common/button/BtnAdd.vue';
import { mapGetters } from 'vuex';

export default {
  name: 'FiscalAddModal',
  components: { FormModal, BtnAdd },
  data() {
    return {
      show: false,
      url: '/api/finance/fiscal/transactions',
      rules: {
        required: (field) => !!field || this.$t('common_fieldRequired'),
        nowhitespaces: (v) => /(^\S+.*\S+$)/u.test(v) || this.$t('finance_noWhitespacesAtBeginEnd'),
      },
      carwash: null,
      source: 'CAR_WASH',
      date: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000))
        .toISOString()
        .substr(0, 10),
      time: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000))
        .toISOString()
        .substr(-13, 8),
      datetimePicker: false,
      sources: [
        {
          text: this.$t('fiscal_transactions.source.CAR_WASH'),
          value: 'CAR_WASH',
        },
        {
          text: this.$t('fiscal_transactions.source.VACUUM_CLEANER'),
          value: 'VACUUM_CLEANER',
        },
        {
          text: this.$t('fiscal_transactions.source.DISTRIBUTOR'),
          value: 'DISTRIBUTOR',
        },
        {
          text: this.$t('fiscal_transactions.source.MONEY_CHANGER'),
          value: 'MONEY_CHANGER',
        },
      ],
    };
  },
  computed: {
    ...mapGetters({
      carwashes: 'carwashes/carwashes',
    }),
    datetime() {
      return `${this.date} ${this.time}`;
    },
    requiresBayId() {
      return this.source === 'CAR_WASH'
        || this.source === 'VACUUM_CLEANER'
        || this.source === 'DISTRIBUTOR';
    },
  },
  mounted() {
    this.carwash = null;
    if (this.carwashes.length > 0) {
      this.carwash = this.carwashes[0].serialNumber;
    }
  },
};
</script>
