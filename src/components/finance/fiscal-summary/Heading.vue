<template>
  <h2>
    <span>{{ $t('finance_financeFiscalSummary') }}</span>
    <span v-if="dateFrom !== dateTo">
      {{ $t('fiscal_transactions.from') }} {{ dateFrom }} {{ $t('fiscal_transactions.to') }} {{
        dateTo }}
    </span>
    <span v-else>
      {{ $t('fiscal_transactions.for') }} {{ dateFrom }}
    </span>
  </h2>
</template>

<script>

export default {
  name: 'FiscalHeading',
  props: {
    dates: {
      type: Object,
      required: true,
    },
  },
  computed: {
    dateFrom() {
      return this.$options.filters.formatDateDay(this.dates.from);
    },
    dateTo() {
      return this.$options.filters.formatDateDay(this.dates.to);
    },
  },
};
</script>
