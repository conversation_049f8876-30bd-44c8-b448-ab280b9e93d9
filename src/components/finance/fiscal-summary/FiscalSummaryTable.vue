<template>
  <v-data-table
    key="fiscal-summary-table"
    dense
    item-key="id"
    mobile-breakpoint="0"
    :headers="headers"
    :items="tableItems"
    :single-expand="true"
    :expanded.sync="expanded"
    :loading="loader"
    :options="options"
    :server-items-length="itemsTotal"
    :footer-props="footerProps"
    hide-default-footer
    @update:options="onOptionsChange"
  >
    <!--Loader-->
    <template #progress>
      <div class="text-center mx-n4">
        <v-progress-linear
          class="loader"
          indeterminate
          color="primary"
        />
      </div>
    </template>

    <!--Item-->
    <template #item="{ item, expand, isExpanded }">
      <template v-if="!loader">
        <tr
          class="text-sm-start  cursor-pointer"
          @click="onRowClick({item, expand, isExpanded})"
        >
          <td class="text-start tabcell-carwash">
            {{ item.carwashName }}
          </td>
          <template v-if="item.net !== null">
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="item.net"
            />
          </template>
          <template v-else>
            <td class="text-center">
              -
            </td>
          </template>
          <template v-if="item.vat !== null">
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="item.vat"
            />
          </template>
          <template v-else>
            <td class="text-center">
              -
            </td>
          </template>
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="item.value"
          />
          <td
            class="text-end"
          >
            <v-icon v-if="isExpanded">
              mdi-chevron-up
            </v-icon>
            <v-icon v-else>
              mdi-chevron-down
            </v-icon>
          </td>
        </tr>
      </template>
    </template>

    <!--Summary-->
    <template #[`body.append`]>
      <template v-if="!loader">
        <tr class="table-summary">
          <td
            colspan="1"
            class="text-start"
          >
            {{ $t('common_totalOnPage') }}
          </td>
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="pageSums.net"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="pageSums.vat"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="pageSums.value"
          />
        </tr>
      </template>
    </template>

    <!--Expanded item-->
    <template #expanded-item="{ headers: _headers, item }">
      <td
        v-if="!loader && 'stands' in item"
        :colspan="_headers.length"
        class="elevation-3"
      >
        <stands-details-table
          :parent="item"
          :currency-symbol="currencySymbol"
        />
      </td>
    </template>
  </v-data-table>
</template>

<script>
import CustomCurrencySymbolCell from '@components/common/CustomCurrencySymbolCell.vue';
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import StandsDetailsTable from './StandsDetailsTable.vue';

export default {
  name: 'FiscalSummaryTable',
  components: {
    CustomCurrencySymbolCell,
    StandsDetailsTable,
  },
  mixins: [
    FilterMixin,
  ],
  props: {
    currencySymbol: {
      type: String,
      default: undefined,
    },
    items: {
      type: Array,
      default() {
        return [];
      },
    },
    options: {
      type: Object,
      default() {
        return {};
      },
    },
    pageSums: {
      type: Object,
      required: true,
    },
    itemsTotal: {
      type: Number,
      default: -1,
    },
    loader: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      expanded: [],
      filtering: {
        options: {},
      },
      typeIcons: {
        CASH: 'mdi-cash',
        CASHLESS: 'mdi-credit-card-outline',
      },
      footerProps: {
        'items-per-page-options': [25, 50, 100],
        'items-per-page-text': this.$t('common_rowsPerPage'),
      },
      headers: [
        {
          value: 'carwash',
          text: this.$t('fiscal_transactions.table.carwash'),
          sortable: false,
        },
        {
          value: 'value',
          text: this.$t('fiscal_transactions.table.net'),
          sortable: false,
          align: 'end',
        },
        {
          value: 'value',
          text: this.$t('fiscal_transactions.table.vat'),
          sortable: false,
          align: 'end',
        },
        {
          value: 'value',
          text: this.$t('fiscal_transactions.table.value'),
          sortable: false,
          align: 'end',
        },
        {
          value: 'expand',
          sortable: false,
          text: '',
          align: 'end',
        },
      ],
    };
  },
  computed: {
    tableItems() {
      if (this.loader) {
        return [];
      }
      return this.items;
    },
  },
  methods: {
    onOptionsChange(options) {
      this.$set(this.filtering, 'options', options);
    },
    onRowClick({ expand, isExpanded }) {
      expand(!isExpanded);
    },
  },
};
</script>
