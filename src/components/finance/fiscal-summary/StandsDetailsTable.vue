<template>
  <v-card
    class="px-3 py-3"
    outlined
    color="blue-grey lighten-5"
  >
    <h3 class="mb-3">
      {{ $t('fiscal_transactions.details.heading') }}
    </h3>
    <v-simple-table
      v-if="items.length"
      dense
      class="elevation-3"
    >
      <template #default>
        <thead>
          <tr>
            <th class="text-left">
              {{ $t('turnover.table.name') }}
            </th>
            <th class="text-right">
              {{ $t('fiscal_transactions.table.date') }}
            </th>
            <th class="text-right">
              {{ $t('fiscal_transactions.table.fiscal_device') }}
            </th>
            <th class="text-right">
              {{ $t('fiscal_transactions.table.net') }}
            </th>
            <th class="text-right">
              {{ $t('fiscal_transactions.table.vat') }}
            </th>
            <th class="text-right">
              {{ $t('fiscal_transactions.table.value') }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="[key, item] in items"
            :key="key"
          >
            <td>
              <device-type-badge
                :source="item.source"
                :stand-id="item.bayId"
              />
            </td>
            <td class="text-right">
              {{ item.last ?? '-' }}
            </td>
            <td class="text-right">
              {{ item.fiscalDevice }}
            </td>
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="item.net"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="item.vat"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="item.value"
            />
          </tr>
        </tbody>
      </template>
    </v-simple-table>
    <v-card-text v-else>
      <v-icon small>
        mdi-alert-circle-outline
      </v-icon>
      <i class="ml-2">{{ $t('common_noData') }}</i>
    </v-card-text>
  </v-card>
</template>

<script>
import CustomCurrencySymbolCell from '@components/common/CustomCurrencySymbolCell.vue';
import DeviceTypeBadge from '@components/libs/standard-types/badge/DeviceTypeBadge.vue';

export default {
  name: 'DetailsTable',
  components: {
    CustomCurrencySymbolCell,
    DeviceTypeBadge,
  },
  props: {
    currencySymbol: {
      type: String,
      default: undefined,
    },
    parent: {
      type: Object,
      required: true,
    },
  },
  computed: {
    items() {
      if (typeof this.parent.stands === 'undefined' || !this.parent.stands) {
        return [];
      }

      return Object.entries(this.parent.stands);
    },
  },
};
</script>
