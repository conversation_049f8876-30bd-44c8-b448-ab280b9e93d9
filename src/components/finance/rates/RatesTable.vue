<template>
  <v-data-table
    :items="rates"
    :headers="headers"
    class="text--darken-1"
    :hide-default-footer="true"
    :loading="loader"
    :items-per-page="-1"
  >
    <template #progress>
      <div class="text-center mx-n4">
        <v-progress-linear
          class="loader"
          indeterminate
          color="primary"
        />
      </div>
    </template>
    <template #item="{ item }">
      <template v-if="!loader">
        <tr>
          <td>
            <strong>{{ item.carwashName }}</strong>
          </td>
          <td class="text-center">
            {{ formatValue(item.prewash) }}
          </td>
          <td class="text-center">
            {{ formatValue(item.mainwash) }}
          </td>
          <td class="text-center">
            {{ formatValue(item.rinsing) }}
          </td>
          <td class="text-center">
            {{ formatValue(item.wasxing) }}
          </td>
          <td class="text-center">
            {{ formatValue(item.glossing) }}
          </td>
          <td class="text-center">
            {{ formatValue(item.rims) }}
          </td>
          <td class="text-center">
            {{ formatValue(item.brush) }}
          </td>
          <td class="text-center">
            {{ formatValue(item.foam) }}
          </td>
          <td class="text-center">
            {{ formatValue(item.degreser) }}
          </td>
          <td class="text-center">
            {{ formatValue(item.pause) }}
          </td>
          <td class="text-center">
            {{ formatValue(item.cleaner) }}
          </td>
          <td class="text-center">
            {{ formatValue(item.distributor) }}
          </td>
          <td class="text-center">
            <v-tooltip
              right
            >
              <template #activator="{ on, attrs }">
                <span
                  v-bind="attrs"
                  v-on="on"
                >
                  <v-icon
                    small
                    class="ml-1"
                    :color="item.syncWarning ? 'warning' : 'success'"
                  >
                    {{ item.syncWarning ? 'mdi-alert-circle-outline' : 'mdi-check-circle-outline' }}
                  </v-icon>
                </span>
              </template>
              <div class="text-center">
                <div>{{ $t('common_updateTime') }}: {{ item.syncTime }}</div>
                <div
                  v-if="item.syncWarning"
                  class="warning white--text"
                >
                  {{ $t('common_itemOld') }}
                </div>
              </div>
            </v-tooltip>
          </td>
        </tr>
      </template>
    </template>
  </v-data-table>
</template>

<script>
export default {
  name: 'RatesTable',
  props: {
    rates: {
      type: Array,
      default: () => [],
    },
    timeUnit: {
      type: Boolean,
      default: false,
    },
    loader: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      headers: [
        { text: this.$t('finance_name'), value: 'carwashName', align: 'start' },
        { text: this.$t('programs.prewash'), value: 'prewash.value', align: 'center' },
        { text: this.$t('programs.mainwash'), value: 'mainwash.value', align: 'center' },
        { text: this.$t('programs.rinsing'), value: 'rinsing.value', align: 'center' },
        { text: this.$t('programs.wasxing'), value: 'wasxing.value', align: 'center' },
        { text: this.$t('programs.glossing'), value: 'glossing.value', align: 'center' },
        { text: this.$t('programs.rims'), value: 'rims.value', align: 'center' },
        { text: this.$t('programs.brush'), value: 'brush.value', align: 'center' },
        { text: this.$t('programs.foam'), value: 'foam.value', align: 'center' },
        { text: this.$t('programs.degreaser'), value: 'degreser.value', align: 'center' },
        { text: this.$t('finance_pause'), value: 'pause.value', align: 'center' },
        { text: this.$t('fiscal.source.VACUUM_CLEANER'), value: 'cleaner.value', align: 'center' },
        { text: this.$t('fiscal.source.DISTRIBUTOR'), value: 'distributor.value', align: 'center' },
        { text: this.$t('finance_carwashRates_synchronization'), value: 'syncTime', align: 'center' },
      ],
    };
  },
  computed: {
  },
  methods: {
    formatValue(item) {
      if (item.value === null) {
        return '-';
      }

      return `${item.value?.toFixed(2)} ${item.unit}`;
    },
  },
};
</script>
