<template>
  <div :class="wrapperClass">
    <report-create-modal
      v-if="enableExport"
      btn-class="float-right"
      :params="exportAsyncParams"
      :disabled="loader"
      :preset="filtering.dates.value"
    />
    <stack-bar-chart
      class="px-2 align-self-stretch"
      :title="title"
      unit="%"
      :loader="loader"
      :series-values="getSeriesValues"
      :legend="getWeekdays"
      legend-align="left"
      legend-vertical-align="0"
      :x-data="getHourRanges"
      grid-bottom="50"
      grid-left="0"
      grid-right="0"
      y-axis-formatter=""
      emphasis-focus="none"
      series-tooltip-disabled
      chart-tooltip-disabled
    />
  </div>
</template>

<script>
import StackBarChart from '@components/common/charts/StackBarChart.vue';
import ReportCreateModal from '@components/reports/ReportCreateModal.vue';

export default {
  name: 'ProgramsUsageBarChart',
  components: {
    ReportCreateModal,
    StackBarChart,
  },
  props: {
    title: {
      type: String,
      default() {
        return this.$t('common_title');
      },
    },
    enableExport: {
      type: Boolean,
      default: true,
    },
    wrapperClass: {
      type: [String, Object],
      default: '',
    },
    loader: {
      type: Boolean,
      default: false,
    },
    values: {
      type: Array,
      default() {
        return [];
      },
    },
    filtering: {
      type: Object,
      required: true,
    },
    reportType: {
      type: String,
      default: 'v2\\FinanceProgramsUsageDaily',
    },
  },
  data() {
    return {
      legendSelected: [],
    };
  },
  computed: {
    exportAsyncParams() {
      return {
        report: this.reportType,
        serial: this.filtering.carwash,
        startDate: this.$options.filters.formatDateDay(this.filtering.dates.from),
        endDate: this.$options.filters.formatDateDay(this.filtering.dates.to),
      };
    },
    getHourRanges() {
      const ranges = [];
      for (let i = 0; i < 24; i += 1) {
        ranges.push(`${i}:00\n—\n${i + 1}:00`);
      }
      return ranges;
    },
    getWeekdays() {
      return [
        this.$t('finance_1'),
        this.$t('finance_2'),
        this.$t('finance_3'),
        this.$t('finance_4'),
        this.$t('finance_5'),
        this.$t('finance_6'),
        this.$t('finance_0'),
      ];
    },
    getValues() {
      return Object.values(this.values.map((hour) => ({
        monday: this.getHourValue(hour, 1, 'monday'),
        tuesday: this.getHourValue(hour, 2, 'tuesday'),
        wednesday: this.getHourValue(hour, 3, 'wednesday'),
        thursday: this.getHourValue(hour, 4, 'thursday'),
        friday: this.getHourValue(hour, 5, 'friday'),
        saturday: this.getHourValue(hour, 6, 'saturday'),
        sunday: this.getHourValue(hour, 0, 'sunday'),
      })));
    },
    getSeriesValues() {
      const valuesArray = this.getValues;
      return [
        {
          data: valuesArray.map((hour) => hour.sunday),
          name: this.$t('finance_0'),
          color: '#10233D',
        },
        {
          data: valuesArray.map((hour) => hour.monday),
          name: this.$t('finance_1'),
          color: '#3B81E3',
        },
        {
          data: valuesArray.map((hour) => hour.tuesday),
          name: this.$t('finance_2'),
          color: '#2A5DA3',
        },
        {
          data: valuesArray.map((hour) => hour.wednesday),
          name: this.$t('finance_3'),
          color: '#a4bfe8',
        },
        {
          data: valuesArray.map((hour) => hour.thursday),
          name: this.$t('finance_4'),
          color: '#5E7DA8',
        },
        {
          data: valuesArray.map((hour) => hour.friday),
          name: this.$t('finance_5'),
          color: '#81A9E6',
        },
        {
          data: valuesArray.map((hour) => hour.saturday),
          name: this.$t('finance_6'),
          color: '#585D66',
        },
      ];
    },
  },
  methods: {
    getHourValue(hour, weekday, weekdayName) {
      return (this.$t(`finance_${weekday}`) in this.legendSelected
      && this.legendSelected[this.$t(`finance_${weekday}`)])
      || !Object.keys(this.legendSelected).length
        ? hour[weekdayName]
        : 0;
    },
  },
};
</script>
