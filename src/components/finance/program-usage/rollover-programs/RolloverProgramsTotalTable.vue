<template>
  <div>
    <v-row>
      <v-col
        cols="12"
        sm="8"
      >
        <h3
          style="color: #464646;
                font-size: 1.47em;"
        >
          {{ $t('finance_programs') }}
        </h3>
      </v-col>
    </v-row>
    <v-data-table
      :headers="headers"
      :items="tableItems"
      item-key="id"
      :loading="loader"
      hide-default-footer
      disable-pagination
      mobile-breakpoint="0"
      :sort-by="sortBy"
      :sort-desc="sortDesc"
    >
      <!--Loader-->
      <template #progress>
        <div class="text-center mx-n4">
          <v-progress-linear
            class="loader"
            indeterminate
            color="primary"
          />
        </div>
      </template>

      <!--Item-->
      <template #item="{ item }">
        <tr>
          <td class="text-start tabcell-carwash font-weight-bold">
            {{ item.carwashName }}
          </td>
          <td class="text-end">
            {{ item.programs.p1 }} {{ $t('programsusage.table.wash') }}
          </td>
          <td class="text-end">
            {{ item.programs.p2 }} {{ $t('programsusage.table.wash') }}
          </td>
          <td class="text-end">
            {{ item.programs.p3 }} {{ $t('programsusage.table.wash') }}
          </td>
          <td class="text-end">
            {{ item.programs.p4 }} {{ $t('programsusage.table.wash') }}
          </td>
          <td class="text-end">
            {{ item.programs.p5 }} {{ $t('programsusage.table.wash') }}
          </td>
          <td class="text-end font-weight-bold">
            {{ item.total }} {{ $t('programsusage.table.wash') }}
          </td>
        </tr>
      </template>

      <!--Summary-->
      <template #[`body.append`]>
        <template v-if="!loader && Object.keys(sums).length">
          <tr class="table-summary">
            <td class="text-start font-weight-bold" />
            <td class="text-end font-weight-bold" />
            <td class="text-end font-weight-bold" />
            <td class="text-end font-weight-bold" />
            <td class="text-end font-weight-bold" />
            <td class="text-end font-weight-bold" />
            <td class="text-end font-weight-bold" />
          </tr>
        </template>
      </template>
    </v-data-table>
  </div>
</template>

<script>
import ExportMixin from '@components/common/mixins/ExportMixin.vue';

export default {
  name: 'ProgramsUsageTotalTable',
  mixins: [ExportMixin],
  props: {
    items: {
      type: Array,
      default() {
        return [];
      },
    },
    sums: {
      type: Object,
      default() {
        return {};
      },
    },
    loader: {
      type: Boolean,
      default: false,
    },
    filtering: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      graphSelectedColumns: [],
      headers: [
        {
          value: 'carwash',
          text: this.$t('turnover.table.carwash'),
          sortable: false,
        },
        {
          value: 'prewash',
          text: `${this.$t('programsusage.table.program')} 1`,
          align: 'end',
          sortable: false,
        },
        {
          value: 'mainwash',
          text: `${this.$t('programsusage.table.program')} 2`,
          align: 'end',
          sortable: false,
        },
        {
          value: 'rinsing',
          text: `${this.$t('programsusage.table.program')} 3`,
          align: 'end',
          sortable: false,
        },
        {
          value: 'wasxing',
          text: `${this.$t('programsusage.table.program')} 4`,
          align: 'end',
          sortable: false,
        },
        {
          value: 'glossing',
          text: `${this.$t('programsusage.table.program')} 5`,
          align: 'end',
          sortable: false,
        },
        {
          text: this.$t('programsusage.table.sum'),
          value: 'sum',
          align: 'end',
          sortable: false,
        },
      ],
      sortBy: 'date',
      sortDesc: true,
    };
  },
  computed: {
    tableItems() {
      if (this.loader) {
        return [];
      }

      return this.items;
    },
  },
};
</script>
