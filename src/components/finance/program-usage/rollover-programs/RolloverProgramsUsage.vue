<template>
  <v-container
    fluid
    class="p-0"
  >
    <v-row>
      <type-carwash-and-date-range-filter
        :carwashes="filteringOptions.carwash.rollovers"
        :disabled="loader"
        settings-namespace="finance:dates"
        carwash-settings-namespace="finance:carwashes"
        @change="onFiltersChange"
      />
    </v-row>
    <v-row>
      <v-col
        cols="12"
        sm="8"
        class="text-sm-start"
      >
        <heading
          :dates="filtering.dates"
        />
      </v-col>
      <v-col
        cols="12"
        sm="4"
        class="d-flex justify-end"
      >
        <btn-refresh
          class="mr-2"
          :disabled="loader"
          @click="getData"
        />
      </v-col>
    </v-row>
    <v-row class="mb-5">
      <v-col
        cols="12"
        lg="8"
      >
        <programs-usage-bar-chart
          :values="itemsDaily"
          :loader="loader"
          :filtering="filtering"
          report-type="v2\FinancePortalProgramsUsageDaily"
        />
      </v-col>
      <v-col
        cols="12"
        lg="4"
      >
        <rollover-programs-usage-pie-chart
          :values="itemsTotal"
          :loader="loader"
          :filtering="filtering"
        />
      </v-col>
    </v-row>
    <v-row class="mb-5">
      <v-col cols="12">
        <programs-usage-over-time-bar-chart
          :carwashes="filteringOptions.carwash.rollovers"
          :loader="loader"
          :filtering="filtering"
        />
      </v-col>
    </v-row>
    <v-row class="mb-5">
      <v-col
        cols="12"
      >
        <rollover-programs-total-table
          :items="itemsTotal"
          :sums="sums"
          :loader="loader"
          :filtering="filtering"
        />
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import DataFetchMixin from '@components/common/mixins/DataFetchMixin.vue';
import FiltersHandlingMixin from '@components/common/mixins/FiltersHandlingMixin.vue';
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import { mapGetters } from 'vuex';
import RolloverProgramsTotalTable from '@components/finance/program-usage/rollover-programs/RolloverProgramsTotalTable.vue';
import TypeCarwashAndDateRangeFilter from '../TypeCarwashAndDateRangeFilter.vue';
import ProgramsUsageBarChart from '../ProgramsUsageBarChart.vue';
import RolloverProgramsUsagePieChart from './RolloverProgramsUsagePieChart.vue';
import Heading from '../Heading.vue';
import ProgramsUsageOverTimeBarChart from '../program-usage/ProgramsUsageOverTimeBarChart.vue';

export default {
  name: 'RolloverProgramsUsage',
  components: {
    RolloverProgramsTotalTable,
    ProgramsUsageBarChart,
    RolloverProgramsUsagePieChart,
    TypeCarwashAndDateRangeFilter,
    Heading,
    BtnRefresh,
    ProgramsUsageOverTimeBarChart,
  },
  mixins: [
    DataFetchMixin,
    FiltersHandlingMixin,
  ],
  data() {
    return {
      settingsNamespace: 'finance:dates',
      dataUrl: '/api/reports/data',
      itemsDaily: [],
      itemsTotal: [],
    };
  },
  computed: {
    ...mapGetters({
      getCarwashBySerial: 'carwashes/getCarwashBySerial',
    }),
  },
  methods: {
    parseApiResponseData(data) {
      this.itemsTotal = data.total.map((item) => ({
        carwashName: this.getCarwashBySerial(item.sn).name,
        ...item,
      }));
      this.itemsDaily = data.daily;
    },
    getParams() {
      return {
        params: {
          serial: this.filtering.carwash,
          startDate: this.$options.filters.formatDateDay(this.filtering.dates.from),
          endDate: this.$options.filters.formatDateDay(this.filtering.dates.to),
          report: 'v2\\FinancePortalProgramsUsageDaily',
        },
      };
    },
  },
};
</script>
