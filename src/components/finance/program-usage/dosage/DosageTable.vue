<template>
  <v-data-table
    :headers="[
      { text: $t('finance_carwash'), value: 'carwashName' },
      { text: $t('programs.prewash'),
        value: 'prewash',
        align: 'center',
      },
      { text: $t('programs.mainwash'),
        value: 'mainwash', align: 'center', },
      { text: $t('programs.foam'),
        value: 'foam', align: 'center', },
      { text: $t('programs.wasxing'),
        value: 'wasxing', align: 'center', },
      { text: $t('programs.glossing'),
        value: 'glossing', align: 'center', },
      { text: $t('programs.rims'), value: 'rims', align: 'center', },
      { text: $t('programs.brush'), value: 'brush', align: 'center', },
    ]"
    :items="items"
    item-key="carwash"
    :loading="loader"
    hide-default-footer
    disable-pagination
  >
    <template #item="{ item }">
      <tr>
        <td class="text-start">
          <b>{{ item.carwashName }}</b>
        </td>
        <td class="text-center">
          {{ item.prewash }}{{ item.unit }}
        </td>
        <td class="text-center">
          {{ item.mainwash }}{{ item.unit }}
        </td>
        <td class="text-center">
          {{ item.foam }}{{ item.unit }}
        </td>
        <td class="text-center">
          {{ item.wasxing }}{{ item.unit }}
        </td>
        <td class="text-center">
          {{ item.glossing }}{{ item.unit }}
        </td>
        <td class="text-center">
          {{ item.rims }}{{ item.unit }}
        </td>
        <td class="text-center">
          {{ item.brush }}{{ item.unit }}
        </td>
      </tr>
    </template>
  </v-data-table>
</template>

<script>

export default {
  name: 'DosageTable',

  props: {
    items: {
      type: Array,
      default: () => [],
      required: true,
    },
    loader: {
      type: Boolean,
      default: false,
    },
  },
};
</script>
