<template>
  <v-card-text class="px-0 pb-0">
    <v-layout
      row
      wrap
      justify-end
    >
      <v-col
        md="4"
        sm="4"
        cols="12"
        class="py-0"
      >
        <multiselect
          v-model="filtering.status"
          class="d-flex"
          :items="statusOptions"
          :label="$t('financeRollover_status')"
          prepend-icon="mdi-clipboard-list-outline"
          :disabled="disabled"
          unified
          allow-null
          @change="onStatusChange"
        />
      </v-col>
      <v-col
        md="4"
        sm="4"
        cols="12"
        class="py-0"
      >
        <v-autocomplete
          v-if="showCarwashFilter"
          :disabled="disabled"
          :items="carwashesOptions"
          item-value="serialNumber"
          item-text="longName"
          prepend-icon="mdi-car-wash"
          :label="$t('common_filtersCarwash')"
          :value="null"
          @change="onCarwashChange"
        />
      </v-col>
      <v-col
        md="4"
        sm="4"
        cols="12"
        class="py-0"
      >
        <date-range-picker
          key="dateRange"
          ref="dateRange"
          prepend-icon="mdi-calendar-range"
          :show-presets="true"
          :show-custom="true"
          :show-last-collection="showLastCollection"
          :disabled="disabled"
          :start-preset="startDatePreset"
          :settings-namespace="settingsNamespace"
          @reload-transaction-list="onDateRangeChange"
        />
      </v-col>
    </v-layout>
  </v-card-text>
</template>

<script>
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import DateRangePicker from '@components/common/DateRangePicker.vue';
import Multiselect from '@components/common/filters/Multiselect.vue';

export default {
  name: 'TypeCarwashAndDateRangeFilter2',
  components: {
    Multiselect,
    DateRangePicker,
  },
  mixins: [
    FilterMixin,
  ],
  props: {
    startDatePreset: {
      type: String,
      default: 'last7Days',
    },
    showCarwashFilter: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    carwashes: {
      type: Array,
      default() {
        return [];
      },
    },
    showLastCollection: {
      type: Boolean,
      default: false,
    },
    settingsNamespace: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      filtering: {
        carwash: null,
        dates: {
          from: null,
          to: null,
        },
        status: 'success',
      },
    };
  },
  computed: {
    statusOptions() {
      return [
        {
          value: 'success',
          text: this.$t('common_success'),
        },
        {
          value: 'reset',
          text: this.$t('common_reset'),
        },
      ];
    },
    carwashesOptions() {
      const all = {
        serialNumber: null,
        longName: this.$t('common_all'),
      };
      return [all, ...this.carwashes];
    },
  },
  methods: {
    onDateRangeChange(dates) {
      this.$set(this.filtering, 'dates', dates);
    },
    onCarwashChange(carwash) {
      this.$set(this.filtering, 'carwash', carwash);
    },
    onStatusChange(status) {
      this.$set(this.filtering, 'status', status);
    },
  },
};
</script>
