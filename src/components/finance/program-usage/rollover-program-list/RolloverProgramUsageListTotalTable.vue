<template>
  <div>
    <v-row>
      <v-col
        cols="12"
        sm="8"
      >
        <h3
          style="color: #464646; font-size: 1.47em;"
        >
          {{ $t('finance_programs') }}
        </h3>
      </v-col>
    </v-row>
    <v-data-table
      dense
      item-key="id"
      mobile-breakpoint="0"
      :headers="headers"
      :items="tableItems"
      :loading="loader"
      :sort-by="sortBy"
      :sort-desc="sortDesc"
      :footer-props="footerProps"
      show-expand
      :single-expand="true"
      :server-items-length="itemsTotal"
      :expanded.sync="expanded"
      @update:options="onOptionsChange"
    >
      <!--Loader-->
      <template #progress>
        <div class="text-center mx-n4">
          <v-progress-linear
            class="loader"
            indeterminate
            color="primary"
          />
        </div>
      </template>

      <!--Item-->
      <template #item="{ item, expand, isExpanded }">
        <template v-if="!loader">
          <tr
            class="text-sm-start  cursor-pointer"
            @click="onRowClick({item, expand, isExpanded})"
          >
            <td class="text-start">
              {{ item.id }}
            </td>
            <td class="text-start">
              <b>{{ item.time|formatDateDayTime }}</b>
            </td>
            <td class="text-start">
              {{ item.carwashName }}
            </td>
            <td class="text-end">
              {{ item.programName }}
            </td>
            <td class="text-end">
              {{ item.duration }} s
            </td>
            <td class="text-end">
              <v-tooltip left>
                <template #activator="{ on }">
                  <v-icon
                    v-if="item.status === 'success'"
                    color="green darken-2"
                    v-on="on"
                  >
                    mdi-check-circle-outline
                  </v-icon>
                  <v-icon
                    v-else
                    color="error"
                    v-on="on"
                  >
                    mdi-close-circle-outline
                  </v-icon>
                </template>
                <span>
                  {{ $t(`common_${item.status}`) }}
                </span>
              </v-tooltip>
            </td>
            <td
              class="text-end"
            >
              <v-icon v-if="isExpanded">
                mdi-chevron-up
              </v-icon>
              <v-icon v-else>
                mdi-chevron-down
              </v-icon>
            </td>
          </tr>
        </template>
      </template>

      <!--Expanded item-->
      <template #expanded-item="{ headers: _headers, item: _item }">
        <td
          v-if="!loader && 'ai' in _item"
          :colspan="_headers.length"
          class="elevation-3"
        >
          <details-table
            :parent="_item"
          />

          <raides-table
            :parent="_item"
          />
        </td>
      </template>
    </v-data-table>
  </div>
</template>

<script>
import ExportMixin from '@components/common/mixins/ExportMixin.vue';
import DetailsTable from './DetailsTable.vue';
import RaidesTable from './RaidesTable.vue';

export default {
  name: 'ProgramsUsageTotalTable',
  components: {
    RaidesTable,
    DetailsTable,
  },
  mixins: [ExportMixin],
  props: {
    items: {
      type: Array,
      default() {
        return [];
      },
    },
    sums: {
      type: Object,
      default() {
        return {};
      },
    },
    loader: {
      type: Boolean,
      default: false,
    },
    filtering: {
      type: Object,
      required: true,
    },
    itemsTotal: {
      type: Number,
      default: -1,
    },
    options: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      expanded: [],
      graphSelectedColumns: [],
      footerProps: {
        'items-per-page-options': [25, 50],
        'items-per-page-text': this.$t('common_rowsPerPage'),
      },
      headers: [
        {
          value: 'id',
          text: this.$t('common_id'),
          sortable: false,
        },
        {
          value: 'time',
          text: this.$t('common_date'),
          align: 'start',
          sortable: false,
        },
        {
          value: 'carwashName',
          text: this.$t('turnover.table.carwash'),
          sortable: false,
        },
        {
          value: 'programName',
          text: this.$t('programsusage.table.program'),
          align: 'end',
          sortable: false,
        },
        {
          value: 'duration',
          text: this.$t('common_timeDuration'),
          align: 'end',
          sortable: false,
        },
        {
          value: 'status',
          text: this.$t('financeRollover_status'),
          align: 'end',
          sortable: false,
        },
        {
          text: '',
          value: 'data-table-expand',
        },
      ],
      sortBy: 'date',
      sortDesc: true,
    };
  },
  computed: {
    tableItems() {
      if (this.loader) {
        return [];
      }

      return this.items;
    },
  },
  methods: {
    onOptionsChange(options) {
      this.$set(this.filtering, 'options', options);
      setTimeout(() => {
        this.$emit('reload-list', {});
      }, 200);
    },
    onRowClick({ expand, isExpanded }) {
      expand(!isExpanded);
    },
  },
};
</script>
