<template>
  <h2>
    <span>{{ $t('common_total') }}</span>
    <span v-if="dateFrom !== dateTo">
      {{ $t('finance_programsusageFrom') }}
      {{ dateFrom }} {{ $t('finance_programsusageTo') }} {{ dateTo }}
    </span>
    <span v-else>
      {{ $t('finance_programsusageFor') }} {{ dateFrom }}
    </span>
  </h2>
</template>

<script>

export default {
  name: 'ProgramUsageHeading',
  props: {
    dates: {
      type: Object,
      required: true,
    },
  },
  computed: {
    dateFrom() {
      return this.$options.filters.formatDateDay(this.dates.from);
    },
    dateTo() {
      return this.$options.filters.formatDateDay(this.dates.to);
    },
  },
};
</script>
