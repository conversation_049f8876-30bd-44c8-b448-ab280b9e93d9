<template>
  <v-card-text class="px-0 pb-0">
    <v-layout
      row
      wrap
      justify-end
    >
      <v-col
        md="4"
        sm="4"
        cols="12"
        class="py-0"
      >
        <v-select
          v-model="filtering.dataSource"
          prepend-icon="mdi-clipboard-list-outline"
          :items="dataSources"
          :label="$t('common_type')"
          :disabled="disabled"
          class="d-flex"
          @change="onDataSourceChange"
        />
      </v-col>
      <v-col
        md="4"
        sm="4"
        cols="12"
        class="py-0"
      >
        <v-autocomplete
          v-if="showCarwashFilter"
          :disabled="disabled"
          :items="carwashesOptions"
          item-value="serialNumber"
          item-text="longName"
          prepend-icon="mdi-car-wash"
          :label="$t('common_filtersCarwash')"
          :value="null"
          @change="onCarwashChange"
        />
      </v-col>
      <v-col
        md="4"
        sm="4"
        cols="12"
        class="py-0"
      >
        <date-range-picker
          key="dateRange"
          ref="dateRange"
          prepend-icon="mdi-calendar-range"
          :show-presets="true"
          :show-custom="true"
          :show-last-collection="showLastCollection"
          :disabled="disabled"
          :start-preset="startDatePreset"
          :settings-namespace="settingsNamespace"
          @reload-transaction-list="onDateRangeChange"
        />
      </v-col>
    </v-layout>
  </v-card-text>
</template>

<script>
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import DateRangePicker from '@components/common/DateRangePicker.vue';

export default {
  name: 'TypeCarwashAndDateRangeFilter',
  components: {
    DateRangePicker,
  },
  mixins: [
    FilterMixin,
  ],
  props: {
    startDatePreset: {
      type: String,
      default: 'last7Days',
    },
    showCarwashFilter: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    carwashes: {
      type: Array,
      default() {
        return [];
      },
    },
    showLastCollection: {
      type: Boolean,
      default: false,
    },
    settingsNamespace: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      filtering: {
        carwash: null,
        dates: {
          from: null,
          to: null,
        },
        dataSource: null,
      },
    };
  },
  computed: {
    dataSources() {
      return [
        {
          value: 'daily',
          text: this.$t('common_daily'),
          dataUrl: '/cm_new/portal/programsusage/over_time/daily',
          dateFormatFunc: this.$options.filters.formatDateDay,
        },
        {
          value: 'monthly',
          text: this.$t('common_monthly'),
          dataUrl: '/cm_new/portal/programsusage/over_time/monthly',
          dateFormatFunc: this.$options.filters.formatDateMonth,
        },
      ];
    },
    carwashesOptions() {
      const all = {
        serialNumber: null,
        longName: this.$t('common_all'),
      };
      return [all, ...this.carwashes];
    },
  },
  mounted() {
    const [dataSource] = this.dataSources;
    this.$set(this.filtering, 'dataSource', dataSource);
  },
  methods: {
    onDateRangeChange(dates) {
      this.$set(this.filtering, 'dates', dates);
    },
    onCarwashChange(carwash) {
      this.$set(this.filtering, 'carwash', carwash);
    },
    onDataSourceChange(dataSource) {
      const [targetDataSource] = this.dataSources.filter((s) => s.value === dataSource);
      this.$set(this.filtering, 'dataSource', targetDataSource);
    },
  },
};
</script>
