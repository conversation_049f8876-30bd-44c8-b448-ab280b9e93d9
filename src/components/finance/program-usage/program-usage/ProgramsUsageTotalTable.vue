<template>
  <div>
    <v-row>
      <v-col
        cols="12"
        sm="8"
      >
        <h3
          style="color: #464646;
                font-size: 1.47em;"
        >
          {{ $t('finance_programs') }}
        </h3>
      </v-col>
      <v-col
        cols="12"
        sm="4"
        class="d-flex justify-end"
      />
    </v-row>
    <v-data-table
      :headers="headers"
      :items="tableItems"
      item-key="id"
      :loading="loader"
      hide-default-footer
      disable-pagination
      mobile-breakpoint="0"
      :sort-by="sortBy"
      :sort-desc="sortDesc"
    >
      <!--Loader-->
      <template #progress>
        <div class="text-center mx-n4">
          <v-progress-linear
            class="loader"
            indeterminate
            color="primary"
          />
        </div>
      </template>

      <!--Item-->
      <template
        #item="{ item }"
      >
        <tr>
          <td class="text-start tabcell-carwash font-weight-bold">
            {{ item.carwashName }}
          </td>
          <td class="text-end">
            {{ parseProgramTime(item.prewash) }}
          </td>
          <td class="text-end">
            {{ parseProgramTime(item.mainwash) }}
          </td>
          <td class="text-end">
            {{ parseProgramTime(item.rinsing) }}
          </td>
          <td class="text-end">
            {{ parseProgramTime(item.wasxing) }}
          </td>
          <td class="text-end">
            {{ parseProgramTime(item.glossing) }}
          </td>
          <td class="text-end">
            {{ parseProgramTime(item.rims) }}
          </td>
          <td class="text-end">
            {{ parseProgramTime(item.brush) }}
          </td>
          <td class="text-end">
            {{ parseProgramTime(item.foam) }}
          </td>
          <td class="text-end">
            {{ parseProgramTime(item.degreaser) }}
          </td>
          <td
            class="text-end font-weight-bold"
          >
            {{ parseProgramTime(item.total) }}
          </td>
          <td class="text-end font-weight-bold">
            {{ parseWaterTotalUsage(item.water) }}
          </td>
          <td class="text-end font-weight-bold">
            {{ parseWaterAwerageUsage(item.water_avg) }}
          </td>
          <td class="text-end">
            {{ parseAvailability(item.availability) }}
          </td>
        </tr>
      </template>

      <!--Summary-->
      <template #[`body.append`]>
        <template v-if="!loader && Object.keys(sums).length">
          <tr class="table-summary">
            <td class="text-start font-weight-bold">
              {{ $t('turnover.table.total') }}
            </td>
            <td
              class="text-end font-weight-bold"
            >
              {{ parseProgramTime(sums.prewash) }}
            </td>
            <td
              class="text-end font-weight-bold"
            >
              {{ parseProgramTime(sums.mainwash) }}
            </td>
            <td
              class="text-end font-weight-bold"
            >
              {{ parseProgramTime(sums.rinsing) }}
            </td>
            <td
              class="text-end font-weight-bold"
            >
              {{ parseProgramTime(sums.wasxing) }}
            </td>
            <td
              class="text-end font-weight-bold"
            >
              {{ parseProgramTime(sums.glossing) }}
            </td>
            <td
              class="text-end font-weight-bold"
            >
              {{ parseProgramTime(sums.rims) }}
            </td>
            <td
              class="text-end font-weight-bold"
            >
              {{ parseProgramTime(sums.brush) }}
            </td>
            <td
              class="text-end font-weight-bold"
            >
              {{ parseProgramTime(sums.foam) }}
            </td>
            <td
              class="text-end font-weight-bold"
            >
              {{ parseProgramTime(sums.degreaser) }}
            </td>
            <td
              class="text-end font-weight-bold"
            >
              {{ parseProgramTime(sums.total) }}
            </td>
            <td class="text-end font-weight-bold">
              {{ parseWaterTotalUsage(sums.water) }}
            </td>
            <td class="text-end font-weight-bold">
              {{ parseWaterAwerageUsage(sums.water_avg) }}
            </td>
            <td class="text-end">
              {{ parseAvailability(sums.availability) }}
            </td>
          </tr>
        </template>
      </template>
    </v-data-table>
  </div>
</template>

<script>

export default {
  name: 'ProgramsUsageTotalTable',
  components: {
  },
  props: {
    items: {
      type: Array,
      default() {
        return [];
      },
    },
    sums: {
      type: Object,
      default() {
        return {};
      },
    },
    loader: {
      type: Boolean,
      default: false,
    },
    filtering: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      headers: [
        {
          value: 'carwash',
          text: this.$t('turnover.table.carwash'),
          sortable: false,
        },
        {
          value: 'prewash',
          text: this.$t('programs.prewash'),
          align: 'end',
          sortable: false,
        },
        {
          value: 'mainwash',
          text: this.$t('programs.mainwash'),
          align: 'end',
          sortable: false,
        },
        {
          value: 'rinsing',
          text: this.$t('programs.rinsing'),
          align: 'end',
          sortable: false,
        },
        {
          value: 'wasxing',
          text: this.$t('programs.wasxing'),
          align: 'end',
          sortable: false,
        },
        {
          value: 'glossing',
          text: this.$t('programs.glossing'),
          align: 'end',
          sortable: false,
        },
        {
          value: 'rims',
          text: this.$t('programs.rims'),
          align: 'end',
          sortable: false,
        },
        {
          value: 'brush',
          text: this.$t('programs.brush'),
          align: 'end',
          sortable: false,
        },
        {
          value: 'foam',
          text: this.$t('programs.foam'),
          align: 'end',
          sortable: false,
        },
        {
          value: 'degreaser',
          text: this.$t('programs.degreaser'),
          align: 'end',
          sortable: false,
        },
        {
          text: this.$t('programsusage.table.sum'),
          value: 'total',
          align: 'end',
          sortable: true,
        },
        {
          value: 'water',
          text: this.$t('programsusage.table.water-usage'),
          align: 'end',
          sortable: true,
        },
        {
          value: 'water_avg',
          text: this.$t('programsusage.table.water-average'),
          align: 'end',
          sortable: true,
        },
        {
          value: 'availability',
          text: this.$t('programsusage.table.availability'),
          align: 'end',
          sortable: true,
        },
      ],
      sortBy: 'date',
      sortDesc: true,
    };
  },
  computed: {
    tableItems() {
      return this.items;
    },
  },
  methods: {
    parseProgramTime(program) {
      return `${program} ${this.$t('finance_minutes')}`;
    },
    parseWaterAwerageUsage(waterAvg) {
      if (waterAvg === null) {
        return '-';
      }
      return `${(waterAvg).toFixed(2)} ${this.$t('finance_litres')}/${this.$t('finance_minutes')}`;
    },
    parseWaterTotalUsage(water) {
      if (water === null) {
        return '-';
      }
      return `${(water).toFixed(2)} ${this.$t('finance_cubicMeters')}`;
    },

    parseAvailability(availability) {
      if (availability === null) {
        return '-';
      }
      return `${(availability).toFixed(0)} %`;
    },

  },
};
</script>
