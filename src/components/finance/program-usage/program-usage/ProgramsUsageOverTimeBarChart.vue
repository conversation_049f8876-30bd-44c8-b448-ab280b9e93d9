<template>
  <stack-bar-chart
    class="px-2"
    :title="$t('finance_overTimeTitle')"
    :legend="lineChartLegend"
    :x-data="lineChartRanges"
    :series-values="seriesValues"
    legend-align="left"
    :loader="loader"
    bar-max-width="90"
    legend-vertical-align="0"
  />
</template>

<script>
import DataFetchMixin from '@components/common/mixins/DataFetchMixin.vue';
import StackBarChart from '@components/common/charts/StackBarChart.vue';

export default {
  name: 'ProgramsUsageOverTimeBarChart',
  components: {
    StackBarChart,
  },
  mixins: [
    DataFetchMixin,
  ],
  props: {
    filtering: {
      type: Object,
      required: true,
    },
    carwashes: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      dataUrl: '',
    };
  },
  computed: {
    seriesValues() {
      return [
        {
          data: this.items.map((item) => item.p1),
          name: `${this.$t('programsusage.table.program')} 1`,
          color: '#3B81E3',
        },
        {
          data: this.items.map((item) => item.p2),
          name: `${this.$t('programsusage.table.program')} 2`,
          color: '#2A5DA3',
        },
        {
          data: this.items.map((item) => item.p3),
          name: `${this.$t('programsusage.table.program')} 3`,
          color: '#a4bfe8',
        },
        {
          data: this.items.map((item) => item.p4),
          name: `${this.$t('programsusage.table.program')} 4`,
          color: '#5E7DA8',
        },
        {
          data: this.items.map((item) => item.p5),
          name: `${this.$t('programsusage.table.program')} 5`,
          color: '#81A9E6',
        },
      ];
    },
    lineChartLegend() {
      return Array.from({ length: 5 }, (_, index) => `${this.$t('programsusage.table.program')} ${index + 1}`);
    },
    lineChartRanges() {
      return this.items.map((item) => item.date);
    },
  },
  watch: {
    filtering(value) {
      this.dataUrl = value.dataSource.dataUrl;
    },
  },
  methods: {
    parseApiResponseData(data) {
      this.items = data.data;
    },
    getParams() {
      const params = {
        params: {
          serial_numbers: this.filtering.carwash != null ? this.filtering.carwash : this.carwashes.map((item) => item.serialNumber).join(','),
          dateFrom: this.$options.filters.formatDateDay(this.filtering.dates.from),
          dateTo: this.$options.filters.formatDateDay(this.filtering.dates.to),
        },
      };
      return params;
    },
  },
};
</script>
