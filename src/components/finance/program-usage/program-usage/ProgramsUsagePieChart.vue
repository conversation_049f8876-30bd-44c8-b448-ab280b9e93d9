<template>
  <div :class="wrapperClass">
    <pie-chart
      class="px-2"
      :title="$t('finance_title')"
      :values="getValues"
      :loader="loader"
      :unit="$t('finance_minutes')"
    />
  </div>
</template>

<script>
import <PERSON><PERSON><PERSON> from '@components/common/charts/PieChart.vue';

export default {
  name: 'ProgramsUsagePieChart',
  components: {
    PieChart,
  },
  props: {
    wrapperClass: {
      type: [String, Object],
      default: '',
    },
    loader: {
      type: Boolean,
      default: false,
    },
    programs: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      colors: [
        '#3B81E3',
        '#2A5DA3',
        '#a4bfe8',
        '#5E7DA8',
        '#81A9E6',
        '#133c77',
        '#9EA8B8',
        '#585D66',
        '#10233D',
      ],
    };
  },
  computed: {
    getValues() {
      const allowedKeys = [
        'prewash',
        'mainwash',
        'rinsing',
        'wasxing',
        'glossing',
        'rims',
        'brush',
        'foam',
        'degreser',
      ];

      if (this.programs === undefined) {
        return [];
      }
      const values = Object.entries(this.programs)
        .filter(([key]) => allowedKeys.includes(key))
        .map(([key, value], index) => ({
          value,
          name: this.$t(`programs.${key}`),
          itemStyle: {
            color: this.colors[index],
          },
        }));
      return values;
    },
  },
};
</script>
