<template>
  <v-data-table
    dense
    item-key="id"
    mobile-breakpoint="0"
    :headers="headers"
    :items="tableItems"
    :loading="loader"
    sort-by="issuanceDate"
    :sort-desc="true"
    :footer-props="footerProps"
  >
    <!--Loader-->
    <template #progress>
      <div class="text-center mx-n4">
        <v-progress-linear
          class="loader"
          indeterminate
          color="primary"
        />
      </div>
    </template>

    <!--Item-->
    <template #item="{ item }">
      <template v-if="!loader">
        <tr
          :key="item.id"
          class="text-sm-start"
        >
          <td class="text-start tabcell-carwash font-weight-bold">
            {{ item.issuanceDate|formatDateDayTime }}
          </td>
          <td class="text-start tabcell-carwash">
            {{ item.period }}
          </td>
          <td class="text-start">
            {{ item.number }}
          </td>
          <custom-currency-symbol-cell
            :value="parseFloat(item.value)"
            :currency="item.currency"
          />
          <td class="text-center">
            <btn-confirm
              x-small
              :confirmed="item.confirmationDate !== null"
              :confirm-date="item.confirmationDate"
              :confirm-user="item.confirmedByUser"
              :confirm-hint-text="$t('common_confirmHint')"
              @confirm="confirmInvoice(item.id)"
            />
          </td>
          <td class="text-end">
            <btn-download
              x-small
              @click="invoiceDownload(item)"
            />
          </td>
        </tr>
      </template>
    </template>
  </v-data-table>
</template>

<script>
import CustomCurrencySymbolCell from '@components/common/CustomCurrencySymbolCell.vue';
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import ExportMixin from '@components/common/mixins/ExportMixin.vue';
import BtnDownload from '@components/common/BtnDownload.vue';
import BtnConfirm from '@components/common/BtnConfirm.vue';

export default {
  name: 'InvoicesTable',
  components: {
    CustomCurrencySymbolCell,
    BtnDownload,
    BtnConfirm,
  },
  mixins: [
    FilterMixin,
    ExportMixin,
  ],
  props: {
    items: {
      type: Array,
      default() {
        return [];
      },
    },
    sums: {
      type: Object,
      required: true,
    },
    itemsTotal: {
      type: Number,
      default: -1,
    },
    loader: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      filtering: {
        options: {},
      },
      footerProps: {
        'items-per-page-options': [12, 24, 36],
        'items-per-page-text': this.$t('common_rowsPerPage'),
      },
      headers: [
        {
          value: 'issuanceDate',
          text: this.$t('common_mobilePaymentInvoicesDate'),
        },
        {
          value: 'period',
          text: this.$t('common_period'),
          sortable: false,
        },
        {
          value: 'number',
          text: this.$t('common_invoiceNumber'),
          sortable: false,
          align: 'start',
        },
        {
          value: 'value',
          text: this.$t('common_valueGross'),
          sortable: false,
          align: 'end',
        },
        {
          value: 'confirmationDate',
          text: this.$t('common_confirmation'),
          sortable: false,
          align: 'center',
          width: '130px',
        },
        {
          value: 'downloadUrl',
          text: ' ',
          sortable: false,
          align: 'end',
          width: '130px',
        },
      ],
    };
  },
  computed: {
    tableItems() {
      if (this.loader) {
        return [];
      }
      return this.items;
    },
  },
  methods: {
    invoiceDownload(item) {
      const url = item.downloadUrl;
      const filename = `${item.number}.pdf`;
      this.onExport({
        url,
        filetype: 'application/pdf',
        filename,
      });
    },
    confirmInvoice(invoiceId) {
      this.axios.post(
        '/cm_new/selfInvocice/confirm',
        {
          invoiceId,
        },
      )
        .then((response) => {
          if (response.data) {
            this.$parent.getData();
          }
        });
    },
  },
};
</script>
