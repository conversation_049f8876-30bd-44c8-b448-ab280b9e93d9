<template>
  <div>
    <v-layout
      row
      wrap
    >
      <v-col cols="12" />
      <v-col cols="12">
        <v-layout
          row
          wrap
        >
          <v-col
            cols="12"
            sm="8"
            class="text-sm-start"
          >
            <heading
              :dates="filtering.dates"
            />
          </v-col>
          <v-col
            cols="12"
            sm="4"
            class="d-flex justify-end"
          >
            <btn-refresh
              class="mr-2"
              :disabled="loader"
              @click="getData"
            />
          </v-col>
        </v-layout>
      </v-col>
      <v-col
        cols="12"
        class="pt-0"
      >
        <invoices-table
          :loader="loader"
          :items="items"
          :items-total="totalItems"
          :sums="sums"
          :options="filtering.options"
          @change="onFiltersChange"
        />
      </v-col>
    </v-layout>
  </div>
</template>

<script>
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import DataFetchMixin from '@components/common/mixins/DataFetchMixin.vue';
import FiltersHandlingMixin from '@components/common/mixins/FiltersHandlingMixin.vue';
import Heading from './Heading.vue';
import InvoicesTable from './InvoicesTable.vue';

export default {
  name: 'InvoicesList',
  components: {
    InvoicesTable,
    Heading,
    BtnRefresh,
  },
  mixins: [
    FiltersHandlingMixin,
    DataFetchMixin,
  ],
  data() {
    return {
      dataUrl: '/cm_new/selfInvocice/list',
      filtering: {
        options: {
          sortBy: ['date'],
          sortDesc: [true],
          itemsPerPage: 25,
        },
      },
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    onFiltersChangePageReset(filters) {
      this.filtering.options.page = 1;
      this.onFiltersChange(filters);
    },
    parseApiResponseData(data) {
      const rows = Object.values(data);
      this.items = rows.map((row) => ({
        id: row.id,
        issuanceDate: row.issuanceDate,
        value: row.price,
        period: row.period,
        downloadUrl: row.downloadUrl,
        number: row.number,
        confirmationDate: row.confirmationDate,
        confirmedByUser: row.confirmedByUser,
        currency: row.currency,
      }));
    },
    getParams() {
      return {
        params: {
          sn: this.filtering.carwash || null,
          dateFrom: this.$options.filters.formatDateDay(this.filtering.dates.from),
          dateTo: this.$options.filters.formatDateDay(this.filtering.dates.to),
          order: this.filtering.options.sortDesc[0] ? 'DESC' : 'ASC',
          page: this.filtering.options.page || null,
          limit: this.filtering.options.itemsPerPage || null,
        },
      };
    },
  },
};
</script>
