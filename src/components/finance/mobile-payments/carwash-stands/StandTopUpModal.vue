<template>
  <v-layout
    row
    justify-center
  >
    <v-dialog
      v-model="dialog"
      scrollable
      persisten
      content-class="dialogWidth-3"
      style="z-index: 1200"
    >
      <v-card>
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">
              {{ $t('finance_topupStand') }} {{ stand.number }}
            </h5>
          </span>
        </v-card-title>
        <v-progress-linear
          v-if="loaders.site"
          :indeterminate="true"
          class="mt-0"
        />
        <v-card-text class="pt-6">
          <v-progress-circular
            v-if="loaders.site"
            class="circleProgress"
            :size="90"
            :width="7"
            color="primary"
            indeterminate
          />
          <template v-if="!loaders.site">
            <v-form
              ref="formStandRefill"
              v-model="forms.refill.valid"
              lazy-validation
            >
              <v-text-field
                v-model="stand.refillValue"
                :label="`${$t('finance_refillFor')} (${currencySymbol})`"
                :rules="[
                  forms.validationRules.positiveNumber,
                  forms.validationRules.topupValue
                ]"
              />
            </v-form>
          </template>
        </v-card-text>
        <v-card-actions v-if="!loaders.site">
          <v-spacer />
          <v-btn
            color="gray"
            text
            @click.native="closeDialog"
          >
            {{ $t('actions.return_to_list') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            :loading="loaders.cardRefill"
            :disabled="!buttons.cardRefill"
            @click.native="refillCard()"
          >
            {{ $t('actions.refill') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-layout>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  props: {
    standNumber: {
      type: String,
      default: null,
    },
    user: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      loaders: {
        site: false,
        cardRefill: false,
      },
      buttons: {
        cardRefill: true,
      },
      form: {
        invoice: {
          invoice_address: null,
          invoice_city: null,
          invoice_country: null,
          invoice_currency: null,
          invoice_post_code: null,
          nip: null,
        },
      },
      stand: {
        number: this.standNumber,
        refillValue: 0,
      },
      isCardChanged: false,
      dialog: false,
      clients: [],
      clientsOptions: [],
      forms: {
        refill: {
          valid: true,
        },
        edit: {
          valid: true,
        },
        validationRules: {
          positiveNumber: (v) => Number(v) > 0 || this.$t(
            'loyaltyCards_topUpPositiveNumberOnly',
          ),
          nonNegativeNumber: (v) => Number(v) >= 0 || this.$t(
            'loyaltyCards_topUpPositiveNumberOnly',
          ),
          topupValue: (v) => Number(v) < 10000000000 || this.$t(
            'form.validation.top_up_value_to_big',
          ),
        },
      },
    };
  },
  computed: {
    ...mapGetters({
      currencySymbol: 'currency/getSymbol',
    }),
  },
  watch: {
  },
  methods: {
    refillCard() {
      if (this.$refs.formStandRefill.validate()) {
        this.axios.post(
          '/api/mobile_topup',
          {
            standCode: this.stand.number,
            value: parseFloat(this.stand.refillValue),
          },
        )
          .then(
            (response) => {
              this.closeDialog();
              if (response.status === 200) {
                this.snackbar.showMessage(
                  'success',
                  this.$t('common_topupSent'),
                );
              } else {
                this.snackbar.showMessage(
                  'error',
                  this.$t('finance_error'),
                );
              }
            },
          )
          .catch(() => {
            this.closeDialog();
            this.snackbar.showMessage(
              'error',
              this.$t('finance_error'),
            );
          });
      }
    },
    closeDialog() {
      this.dialog = false;
    },
  },
};
</script>
