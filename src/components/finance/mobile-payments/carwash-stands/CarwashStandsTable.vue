<template>
  <v-row>
    <v-col
      col="12"
    >
      <v-row>
        <v-col
          col="6"
        >
          <v-autocomplete
            v-model="filtering.carwash"
            :disabled="loader"
            :items="carwashes"
            item-value="serialNumber"
            item-text="longName"
            prepend-icon="mdi-car-wash"
            :label="$t('common_filtersCarwash')"
          />
        </v-col>
      </v-row>
      <v-row>
        <v-col
          col="12"
        >
          <v-data-table
            dense
            :loading="loader"
            item-key="standCode"
            :headers="headers"
            :items="itemsData"
            :items-per-page="-1"
            :footer-props="footerProps"
          >
            <!--Loader-->
            <template #progress>
              <div class="text-center mx-n4">
                <v-progress-linear
                  class="loader"
                  indeterminate
                  color="primary"
                />
              </div>
            </template>

            <!--Item-->
            <template #item="{ item }">
              <template v-if="!loader">
                <tr class="text-sm-start">
                  <td class="text-start">
                    <v-icon>
                      {{ getIconForDeviceType(item.standType) }}
                    </v-icon>
                    {{ getTextForDeviceType(item.standType) }}
                    {{ `#${item.bayId}` }}
                  </td>
                  <td class="text-sm-end">
                    {{ item.standCode }}
                  </td>
                  <td
                    class="text-sm-end"
                  >
                    <v-tooltip
                      :key="item.standCode"
                      bottom
                    >
                      <template #activator="{ on, attrs }">
                        <div
                          v-bind="attrs"
                          v-on="on"
                        >
                          <v-btn
                            :key="item.standCode"
                            :disabled="topUpsDisabled(item.mobileAvailable)"
                            class="card-button"
                            elevation="1"
                            x-small
                            color="primary"
                            @click.stop
                            @click.native="openModal(
                              `topUpStandDialog${item.standCode}`,
                              {standNumber: item.standCode}
                            )"
                          >
                            <v-icon>mdi-trending-up</v-icon>
                            {{ $t('finance_topup') }}
                          </v-btn>
                        </div>
                      </template>
                      <span>
                        <template v-if="topUpsDisabled(item.mobileAvailable)">
                          {{ $t('finance_topupDisabled') }}
                        </template>
                        <template v-else>
                          {{ $t('finance_topup') }}
                        </template>
                      </span>
                    </v-tooltip>

                    <stand-top-up-modal
                      :key="`topUpStandDialog${item.standCode}`"
                      :ref="`topUpStandDialog${item.standCode}`"
                      style="display: none"
                      :stand-number="item.standCode"
                      user="<EMAIL>"
                    />
                  </td>
                </tr>
              </template>
            </template>
          </v-data-table>
        </v-col>
      </v-row>
    </v-col>
  </v-row>
</template>

<script>
import { mapGetters } from 'vuex';
import StandTopUpModal from './StandTopUpModal.vue';

export default {
  name: 'CarwashStandsTable',
  components: {
    StandTopUpModal,
  },
  data() {
    return {
      deviceIcons: {
        CAR_WASH: 'mdi-car-wash',
        VACUUM_CLEANER: 'mdi-auto-fix',
        DISTRIBUTOR: 'mdi-cup-water',
        MONEY_CHANGER: 'mdi-sync',
      },
      deviceText: {
        CAR_WASH: this.$t('fiscal_transactions.source.CAR_WASH'),
        VACUUM_CLEANER: this.$t('fiscal_transactions.source.VACUUM_CLEANER'),
        DISTRIBUTOR: this.$t('fiscal_transactions.source.DISTRIBUTOR'),
        MONEY_CHANGER: this.$t('fiscal_transactions.source.MONEY_CHANGER'),
      },
      filtering: {
        carwash: null,
      },
      itemsPerPage: 25,
      loader: false,
      footerProps: {
        'items-per-page-options': [25],
        'items-per-page-text': this.$t('common_rowsPerPage'),
      },
      itemsData: [],
    };
  },
  computed: {
    headers() {
      return [
        {
          class: 'd-none',
          value: 'carwashSn',
          text: this.$t('common_carwashSn'),
          filter: (value) => {
            if (this.filtering.carwash === null) {
              return true;
            }

            if (this.filtering.carwash !== value) {
              return false;
            }

            return true;
          },
        },
        {
          value: 'standType',
          class: 'text-start',
          text: this.$t('turnover.table.name'),
        },
        {
          value: 'standCode',
          class: 'text-sm-end',
          text: this.$t('common_standCode'),
        },
        {
          value: 'topUp',
          class: 'text-sm-end',
          text: this.$t('finance_topup'),
        },
      ];
    },
    ...mapGetters({
      canAccess: 'auth/canAccess',
      carwashes: 'carwashes/carwashes',
    }),
  },
  mounted() {
    this.filtering.carwash = null;
    if (this.carwashes.length > 0) {
      this.filtering.carwash = this.carwashes[0].serialNumber;
    }
    this.getData();
  },
  methods: {
    topUpsDisabled(mobileAvailable) {
      return !(this.canAccess('extras', 'standsTopUps') && mobileAvailable);
    },
    openModal(modal) {
      this.$refs[modal].dialog = true;
    },
    getIconForDeviceType(deviceType) {
      if (deviceType in this.deviceIcons) {
        return this.deviceIcons[deviceType];
      }
      return 'mdi-help';
    },
    getTextForDeviceType(deviceType) {
      if (deviceType in this.deviceText) {
        return this.deviceText[deviceType];
      }
      return 'mdi-help';
    },
    getData() {
      this.loader = true;
      this.axios.get(
        '/api/carwash_stands',
      )
        .then((response) => {
          this.itemsData = response.data;
          this.loader = false;
        });
    },
  },
};
</script>
