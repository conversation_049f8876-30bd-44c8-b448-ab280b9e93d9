<template>
  <div>
    <v-layout
      row
      wrap
    >
      <v-col cols="12">
        <v-layout
          row
          wrap
        >
          <v-col
            cols="12"
            sm="8"
            class="text-sm-start"
          >
            <heading />
          </v-col>
          <v-col
            cols="12"
            sm="4"
            class="d-flex justify-end"
          />
        </v-layout>
      </v-col>
      <v-col
        cols="12"
        class="pt-0"
      >
        <carwash-stands-table />
      </v-col>
    </v-layout>
  </div>
</template>

<script>
import Heading from './Heading.vue';
import CarwashStandsTable from './CarwashStandsTable.vue';

export default {
  name: 'CarwashStands',
  components: {
    CarwashStandsTable,
    Heading,
  },
};
</script>
