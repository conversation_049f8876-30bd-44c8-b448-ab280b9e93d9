<template>
  <h2>
    <span>{{ $t('finance_heading') }}</span>
    <span v-if="dateFrom !== dateTo">
      {{ $t('finance_from') }} {{ dateFrom }} {{ $t('finance_to') }} {{ dateTo }}
    </span>
    <span v-else>
      {{ $t('finance_for') }} {{ dateFrom }}
    </span>
  </h2>
</template>

<script>

export default {
  name: 'TransactionsHeading',
  props: {
    dates: {
      type: Object,
      required: true,
    },
  },
  computed: {
    dateFrom() {
      return this.$options.filters.formatDateDay(this.dates.from);
    },
    dateTo() {
      return this.$options.filters.formatDateDay(this.dates.to);
    },
  },
};
</script>
