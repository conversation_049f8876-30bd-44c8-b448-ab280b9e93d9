<template>
  <div>
    <v-layout
      row
      wrap
    >
      <v-col cols="8">
        <carwash-and-date-range-filter
          :carwashes="filteringOptions.carwash.carwashes"
          :disabled="loader"
          settings-namespace="finance:dates"
          carwash-settings-namespace="finance:carwashes"
          @change="onFiltersChangePageReset"
        />
      </v-col>
      <v-col cols="4">
        <multiselect
          v-model="status"
          class="pt-4"
          :items="statusesDisabled"
          :label="filters.statuses.text"
          :prepend-icon="filters.statuses.icon"
          :return-array="true"
          :disabled="loader"
          unified
          dense
          allow-null
        />
      </v-col>
      <v-col cols="12">
        <v-layout
          row
          wrap
        >
          <v-col
            cols="12"
            sm="8"
            class="text-sm-start"
          >
            <heading
              :dates="filtering.dates"
            />
          </v-col>
          <v-col
            cols="12"
            sm="4"
            class="d-flex justify-end"
          >
            <btn-refresh
              class="mr-2"
              :disabled="loader"
              @click="getData"
            />
            <report-create-modal
              btn-class="ml-2"
              :params="exportAsyncParams"
              :disabled="loader"
              :preset="filtering.dates.value"
            />
          </v-col>
        </v-layout>
      </v-col>
      <v-col
        cols="12"
        class="pt-0"
      >
        <transactions-table
          :loader="loader"
          :items="items"
          :items-total="totalItems"
          :page-sum="pageSums"
          :total-sum="totalSums"
          :options="filtering.options"
          :currency-symbol="currencySymbol"
          @change="onFiltersChange"
        />
      </v-col>
    </v-layout>
  </div>
</template>

<script>
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import CarwashAndDateRangeFilter from '@components/common/filters/CarwashAndDateRangeFilter.vue';
import DataFetchMixin from '@components/common/mixins/DataFetchMixin.vue';
import ReportCreateModal from '@components/reports/ReportCreateModal.vue';
import FiltersHandlingMixin from '@components/common/mixins/FiltersHandlingMixin.vue';
import { mapGetters } from 'vuex';
import Multiselect from '@components/common/filters/Multiselect.vue';
import Heading from './Heading.vue';
import TransactionsTable from './TransactionsTable.vue';

export default {
  name: 'TransactionList',
  components: {
    Multiselect,
    TransactionsTable,
    Heading,
    CarwashAndDateRangeFilter,
    BtnRefresh,
    ReportCreateModal,
  },
  mixins: [
    FiltersHandlingMixin,
    DataFetchMixin,
  ],
  data() {
    return {
      status: [
      ],
      possibleStatuses: [
      ],
      dataUrl: '/api/reports/data',
      filtering: {
        options: {
          sortBy: ['date'],
          sortDesc: [true],
          itemsPerPage: 25,
        },
      },
      filters: {
        statuses: {
          icons: {
            success: 'mdi-check-circle-outline',
            refused: 'mdi-alert-outline',
            initiated: 'mdi-cached',
          },
          colors: {
            success: 'green darken-2',
            refused: 'error',
            initiated: 'progress',
          },
          texts: {
            success: this.$t('finance_success'),
            refused: this.$t('finance_refused'),
            initiated: this.$t('finance_initiated'),
          },
          text: this.$t('common_state'),
          icon: 'mdi-list-status',
        },
      },
    };
  },
  computed: {
    ...mapGetters({
      getCarwashBySerial: 'carwashes/getCarwashBySerial',
    }),
    currencySymbol() {
      return this.currencyObject.symbol;
    },
    statusesDisabled() {
      const statuses = this.possibleStatuses.map(
        (row) => ({
          text: this.filters.statuses.texts[row] ?? row,
          value: row,
          disabled: false,
          icon: this.filters.statuses.icons[row] ?? '',
        }),
      );

      return statuses;
    },
    exportAsyncParams() {
      const carwash = this.filtering.carwash || null;

      return {
        serial: carwash,
        startDate: this.$options.filters.formatDateDay(this.filtering.dates.from),
        endDate: this.$options.filters.formatDateDay(this.filtering.dates.to),
        report: 'v2\\FinanceMobileTransactions',
        status: this.getToggleStatusFiltering(),
      };
    },
  },
  watch: {
    status() {
      this.filtering.options.page = 1;
      this.getData();
    },
  },
  methods: {
    getToggleStatusFiltering() {
      return this.status.length ? this.status.join(',') : '';
    },
    onFiltersChangePageReset(filters) {
      this.filtering.options.page = 1;
      this.onFiltersChange(filters);
    },
    afterItemsUpdate(response) {
      if (response !== undefined && 'filters' in response) {
        this.possibleStatuses = response.filters.status;
      }
    },
    parseApiResponseData(data) {
      const rows = Object.values(data);
      this.items = rows.map((row, index) => ({
        id: index,
        carwashName: row.carwashName,
        date: this.$options.filters.formatDateDayTime(row.time),
        dates: this.filtering.dates,
        value: row.value,
        ...row,
        issuer: row.app,
      }));
    },
    getParams() {
      return {
        params: {
          serial: this.filtering.carwash || null,
          startDate: this.$options.filters.formatDateDay(this.filtering.dates.from),
          endDate: this.$options.filters.formatDateDay(this.filtering.dates.to),
          // order: this.filtering.options.sortDesc[0] ? 'DESC' : 'ASC',
          page: this.filtering.options.page || null,
          perPage: this.filtering.options.itemsPerPage || null,
          report: 'v2\\FinanceMobileTransactions',
          status: this.getToggleStatusFiltering(),
        },
      };
    },
  },
};
</script>
