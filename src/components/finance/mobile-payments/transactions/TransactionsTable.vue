<template>
  <v-data-table
    dense
    item-key="id"
    mobile-breakpoint="0"
    :headers="headers"
    :items="tableItems"
    :loading="loader"
    :options="options"
    :server-items-length="itemsTotal"
    :footer-props="footerProps"
    @update:options="onOptionsChange"
  >
    <!--Loader-->
    <template #progress>
      <div class="text-center mx-n4">
        <v-progress-linear
          class="loader"
          indeterminate
          color="primary"
        />
      </div>
    </template>

    <!--Item-->
    <template #item="{ item }">
      <template v-if="!loader">
        <tr class="text-sm-start">
          <td class="text-start tabcell-carwash font-weight-bold">
            <div class="flex-inline-start">
              <v-tooltip bottom>
                <template #activator="{ on, attrs }">
                  <v-icon
                    :color="filters.statuses.colors[item.status]"
                    v-bind="attrs"
                    v-on="on"
                  >
                    {{ filters.statuses.icons[item.status] }}
                  </v-icon>
                </template>
                <span> {{ filters.statuses.texts[item.status] }}</span>
              </v-tooltip>
              {{ item.date|formatDateDayTime }}
            </div>
          </td>
          <td class="text-start tabcell-carwash">
            {{ item.carwashName }}
          </td>
          <td class="text-start">
            <device-type-badge
              :source="item.source"
              :stand-id="item.bayId"
            />
          </td>
          <td class="text-start">
            {{ item.standCode }}
          </td>
          <td class="text-start">
            {{ item.issuer }}
          </td>
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="item.value"
          />
        </tr>
      </template>
    </template>

    <!--Summary-->
    <template #[`body.append`]>
      <template v-if="!loader">
        <tr class="table-summary">
          <td
            colspan="5"
            class="text-start"
          >
            {{ $t('common_totalOnPage') }}
          </td>
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="pageSum.value"
          />
        </tr>
        <tr>
          <td
            colspan="5"
            class="text-start font-weight-bold"
          >
            {{ $t('finance_total') }}
          </td>
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="totalSum.value"
            weight="bold"
          />
        </tr>
      </template>
    </template>
  </v-data-table>
</template>

<script>
import CustomCurrencySymbolCell from '@components/common/CustomCurrencySymbolCell.vue';
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import DeviceTypeBadge from '@components/libs/standard-types/badge/DeviceTypeBadge.vue';

export default {
  name: 'TransactionsTable',
  components: {
    CustomCurrencySymbolCell,
    DeviceTypeBadge,
  },
  mixins: [
    FilterMixin,
  ],
  props: {
    currencySymbol: {
      type: String,
      default: undefined,
    },
    items: {
      type: Array,
      default() {
        return [];
      },
    },
    options: {
      type: Object,
      default() {
        return {};
      },
    },
    pageSum: {
      type: Object,
      required: true,
    },
    totalSum: {
      type: Object,
      required: true,
    },
    itemsTotal: {
      type: Number,
      default: -1,
    },
    loader: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      filtering: {
        options: {},
      },
      footerProps: {
        'items-per-page-options': [25, 50, 100],
        'items-per-page-text': this.$t('common_rowsPerPage'),
      },
      filters: {
        statuses: {
          icons: {
            success: 'mdi-check-circle-outline',
            refused: 'mdi-alert-outline',
            initiated: 'mdi-cached',
          },
          colors: {
            success: 'green darken-2',
            refused: 'error',
            initiated: 'progress',
          },
          texts: {
            success: this.$t('finance_success'),
            refused: this.$t('finance_refused'),
            initiated: this.$t('finance_initiated'),
          },
          text: this.$t('common_state'),
          icon: 'mdi-list-status',
        },
      },
      headers: [
        {
          value: 'date',
          text: this.$t('finance_date'),
          sortable: false,
        },
        {
          value: 'carwash',
          text: this.$t('finance_carwash'),
          sortable: false,
        },
        {
          value: 'name',
          text: this.$t('turnover.table.name'),
        },
        {
          value: 'standCode',
          text: this.$t('finance_standCode'),
          sortable: false,
        },
        {
          value: 'paymentType',
          text: this.$t('finance_paymentType'),
          sortable: false,
        },
        {
          value: 'value',
          text: this.$t('finance_value'),
          sortable: false,
          align: 'end',
        },
      ],
    };
  },
  computed: {
    tableItems() {
      if (this.loader) {
        return [];
      }
      return this.items;
    },
  },
  methods: {
    onOptionsChange(options) {
      this.$set(this.filtering, 'options', options);
    },
  },
};
</script>
