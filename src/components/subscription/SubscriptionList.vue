<template>
  <report-data-table
    ref="table"
    :headers="headers"
    :url="url"
    :filters="filters"
  >
    <template #table-actions>
      <slot name="table-actions" />
    </template>
    <template #[`item.ctime`]="{ item }">
      <date-time-formatter
        :value="item.ctime"
        format="YYYY-MM-DD HH:mm:ss"
      />
    </template>
    <template #[`item.ownerEmail`]="{ item }">
      {{ item.subscriber.name ? item.subscriber.name : '-' }}
    </template>
    <template #[`item.grossValue`]="{ item }">
      <currency-formatter
        :value="item.grossValue"
        :symbol="item.currencySymbol"
      />
    </template>
    <template #[`item.startDate`]="{ item }">
      <date-time-formatter
        :value="item.startDate"
        format="YYYY-MM-DD"
      />
    </template>
    <template #[`item.endDate`]="{ item }">
      <date-time-formatter
        :value="item.endDate"
        format="YYYY-MM-DD"
      />
    </template>
    <template #[`item.type`]="{ item }">
      <subscription-code-badge
        :value="item.type"
      />
    </template>
    <template #[`item.status`]="{ item }">
      <subscription-status-badge :status="item.status" />
    </template>
    <template #[`item.vatTax`]="{ item }">
      <currency-formatter
        :value="item.taxValue"
        symbol="%"
      />
    </template>
    <template #[`item.actions`]="{ item }">
      <v-container
        class="d-flex"
      >
        <subscription-preview-modal
          :subscription-id="item.id"
        />
        <subscription-confirm-modal
          :base-url="`/administration/subscription/${item.id}`"
          :disabled="!item.confirmable"
          @success="refresh"
        />
        <delete-modal
          :disabled="!item.cancelable"
          :text="$t('subscriptions.delete-question')"
          :url="`/administration/subscription/${item.id}`"
          @success="refresh"
        />
      </v-container>
    </template>
  </report-data-table>
</template>

<script>
import ReportDataTable from '@components/reports/ReportDataTable.vue';
import SubscriptionConfirmModal from '@components/subscription/modals/SubscriptionConfirmModal.vue';
import SubscriptionPreviewModal from '@components/subscription/modals/SubscriptionPreviewModal.vue';
import CurrencyFormatter from '@components/common/formatters/CurrencyFormatter.vue';
import DateTimeFormatter from '@components/common/formatters/DateTimeFormatter.vue';
import SubscriptionCodeBadge from '@components/subscription/badge/SubscriptionCodeBadge.vue';
import DeleteModal from '@components/common/modal/DeleteModal.vue';
import SubscriptionStatusBadge from './badge/SubscriptionStatusBadge.vue';

export default {
  components: {
    SubscriptionStatusBadge,
    DeleteModal,
    SubscriptionCodeBadge,
    DateTimeFormatter,
    CurrencyFormatter,
    SubscriptionConfirmModal,
    SubscriptionPreviewModal,
    ReportDataTable,
  },
  props: {
    onFiltersChange: {
      type: Function,
      default: () => {},
    },
    onLoading: {
      type: Function,
      default: () => {},
    },
    url: {
      type: String,
      required: true,
    },
    filters: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      headers: [
        {
          text: this.$t('common_id'),
          value: 'id',
          sortable: false,
          width: '50',
        },
        {
          text: this.$t('admin_added'),
          value: 'ctime',
          sortable: false,
          align: 'start',
          width: '150',
        },
        {
          text: this.$t('admin_subscriber'),
          value: 'ownerEmail',
          sortable: false,
          width: '200',
        },
        {
          text: this.$t('common_startDate'),
          value: 'startDate',
          width: '100',
          sortable: false,
        },
        {
          text: this.$t('common_subscriptionsEnddate'),
          value: 'endDate',
          width: '100',
          sortable: false,
        },
        {
          text: this.$t('common_type'),
          value: 'type',
          sortable: false,
          width: '100',
        },
        {
          text: this.$t('common_state'),
          value: 'status',
          sortable: false,
          width: '50',
        },
        {
          text: this.$t('common_price'),
          value: 'grossValue',
          sortable: false,
          align: 'right',
          width: '100',
        },
        {
          text: this.$t('admin_vat'),
          value: 'vatTax',
          align: 'right',
          sortable: false,
          width: '80',
        },
        {
          text: this.$t('admin_whoAdded'),
          value: 'whoAddedEmail',
          sortable: false,
          width: '150',
        },
        {
          text: this.$t('actions.actions'),
          class: 'text-sm-end',
          value: 'actions',
          align: 'right',
          sortable: false,
          width: '190',
        },
      ],
    };
  },
  methods: {
    fetchData() {
      this.$refs.table.fetchData();
    },
  },
};
</script>
