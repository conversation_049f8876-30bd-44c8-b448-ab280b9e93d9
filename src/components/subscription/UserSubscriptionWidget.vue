<template>
  <v-card>
    <v-toolbar
      color="secondary"
      dark
      flat
    >
      <v-btn
        icon
        dark
      >
        <v-icon>mdi-credit-card</v-icon>
      </v-btn>
      <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
        {{ $t('subscription_yourSubscription') }}
      </v-toolbar-title>
      <v-spacer />
    </v-toolbar>
    <v-card-text>
      <v-layout
        row
        wrap
      >
        <v-col cols="12">
          <v-card-text
            dark
            class="subscription-card"
          >
            <div
              v-if="loader"
              class="loader-background"
            >
              <v-progress-circular
                size="50"
                class="loader"
                indeterminate
                color="primary"
              />
            </div>

            <v-row
              v-else
              align="center"
              justify="space-between"
            >
              <!-- Lewa kolumna: nagłówek + badge -->
              <v-col>
                <h1>
                  {{ $t('subscriptions.subscription') }}

                  <subscription-code-badge
                    :value="type"
                    class="ml-2"
                  />
                </h1>
              </v-col>

              <!-- Prawa kolumna: modal lub przycisk -->
              <v-col
                cols="auto"
              >
                <subscription-payment-modal ref="subscriptionPayDialog" />
              </v-col>
            </v-row>
          </v-card-text>
        </v-col>
      </v-layout>
    </v-card-text>
  </v-card>
</template>

<script>
import subscriptionPaymentModal from '@components/subscription/modals/SubscriptionPaymentModal.vue';
import SubscriptionCodeBadge from '@components/subscription/badge/SubscriptionCodeBadge.vue';

export default {
  components: {
    SubscriptionCodeBadge,
    subscriptionPaymentModal,
  },
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loader: true,
      type: 'free',
    };
  },
  mounted() {
    this.getData();

    if (this.$route.params.paymentId !== undefined) {
      this.$refs.subscriptionPayDialog.dialog = true;
    }
  },
  methods: {
    getData() {
      this.loader = true;
      this.axios.get('/cm/subscription/manager/subscription_data')
        .then((response) => {
          if (response.data) {
            this.type = response.data.type;
            this.loader = false;
          }
          this.loader = false;
        })
        .catch(() => {
          this.loader = false;
        });
    },
  },
};
</script>
