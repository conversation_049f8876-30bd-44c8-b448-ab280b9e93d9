<template>
  <v-layout
    row
    justify-center
  >
    <div class="pt-4 pb-4">
      <v-btn
        :disabled="disabled"
        color="primary"
        @click.native="dialog=true"
      >
        <v-icon class="mr-2">
          mdi-credit-card
        </v-icon>
        {{ $t('subscriptions.actions.chose-and-pay') }}
      </v-btn>
    </div>
    <generic-modal
      v-model="dialog"
      :fullscreen="false"
      :show-actions="false"
      :max-width="900"
      :title="`${$t('subscription_subscription')} - ${$t(StepTitles[step])}`"
    >
      <v-stepper
        v-model="step"
        class="elevation-0"
      >
        <v-stepper-header class="hidden-sm-and-down">
          <v-stepper-step
            :complete="step > 1"
            step="1"
          >
            {{ $t(StepTitles[1]) }}
          </v-stepper-step>
          <v-divider />
          <v-stepper-step
            :complete="step > 2"
            step="2"
          >
            {{ $t(StepTitles[2]) }}
          </v-stepper-step>
          <v-divider />
          <v-stepper-step
            :complete="step > 3"
            step="3"
          >
            {{ $t(StepTitles[3]) }}
          </v-stepper-step>
          <v-divider />
          <v-stepper-step
            :complete="step > 4"
            step="4"
          >
            {{ $t(StepTitles[4]) }}
          </v-stepper-step>
        </v-stepper-header>
        <v-stepper-items class="text-sm-center">
          <v-stepper-content step="1">
            <subscription-types-selector v-model="subscription" />
            <div class="mb-2">
              <v-btn
                color="primary"
                :disabled="subscription === 'free'"
                @click="stepNext()"
              >
                {{ $t('subscription_goToAbonamentLength') }}
              </v-btn>
            </div>
          </v-stepper-content>

          <v-stepper-content step="2">
            <subscription-period-selector
              v-model="planId"
              :subscription="subscription"
            />
            <subscription-price-summary-modal
              :plan-id="planId"
            />
            <div class="mb-2">
              <v-btn
                color="primary"
                @click="stepNext()"
              >
                {{ $t('subscription_checkInvoiceData') }}
              </v-btn>
            </div>
            <div class="mb-2">
              <v-btn
                text
                @click.native="stepBack()"
              >
                <h4>{{ $t('subscription_back') }}</h4>
              </v-btn>
            </div>
          </v-stepper-content>
          <v-stepper-content step="3">
            <v-alert
              text
              :value="!isDataCompleted"
              border="left"
              type="error"
              class="my-0"
            >
              {{ $t('subscription_subscriptionBuyMissingData') }}
              <a
                color="error"
                href="mailto:<EMAIL>"
              ><EMAIL></a>
            </v-alert>
            <subscriber-summary
              ref="subscriber"
              url="/api/subscriber"
              :collapsed="false"
            />
            <div class="mb-2">
              <v-btn
                color="primary"
                :disabled="!isDataCompleted"
                @click.native="stepPayment()"
              >
                {{ $t('subscription_orderWithPaymentObligation') }}
              </v-btn>
            </div>
            <div class="mb-2">
              <v-btn
                text
                @click.native="stepBack()"
              >
                <h4>{{ $t('subscription_back') }}</h4>
              </v-btn>
            </div>
          </v-stepper-content>
          <v-stepper-content step="4">
            <subscription-payment-status
              v-if="paymentId"
              :payment-id="paymentId"
            />
            <subscription-payment
              v-else
              ref="payment"
              :plan-id="planId"
            />

            <v-btn
              color="primary"
              @click.native="dialog = false"
            >
              {{ $t('actions.close') }}
            </v-btn>
          </v-stepper-content>
        </v-stepper-items>
      </v-stepper>
    </generic-modal>
  </v-layout>
</template>

<script>
import subscriptionPriceSummaryModal from '@components/subscription/modals/SubscriptionPriceSummaryModal.vue';
import SubscriberSummary from '@components/subscription/subscriber/SubscriberSummary.vue';
import SubscriptionPayment
  from '@components/subscription/modals/createUser/SubscriptionPayment.vue';
import SubscriptionTypesSelector
  from '@components/subscription/modals/createUser/SubscriptionTypesSelector.vue';
import SubscriptionPaymentStatus
  from '@components/subscription/modals/createUser/SubscriptionPaymentStatus.vue';
import GenericModal from '@components/common/GenericModal.vue';
import SubscriptionPeriodSelector
  from './createUser/SubscriptionPeriodSelector.vue';
import { StepTitles } from './createUser/types';

export default {
  components: {
    GenericModal,
    SubscriptionPaymentStatus,
    SubscriptionTypesSelector,
    SubscriptionPayment,
    SubscriberSummary,
    subscriptionPriceSummaryModal,
    SubscriptionPeriodSelector,

  },
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      subscription: 'free',
      step: 1,
      dialog: false,
      planId: null,
      paymentId: this.$route.params.paymentId ? Number(this.$route.params.paymentId) : null,
    };
  },
  computed: {
    StepTitles() {
      return StepTitles;
    },
  },
  watch: {
    dialog(newVal) {
      if (!newVal) {
        this.reset();
      }
    },
  },
  created() {
    if (this.paymentId) {
      this.step = 4;
    }
  },
  methods: {
    isDataCompleted() {
      return this.$refs.subscriber?.valid;
    },

    stepNext() {
      this.step += 1;
    },
    stepPayment() {
      this.step = 4;
      this.$refs.payment.initPayment();
    },
    stepBack() {
      this.step -= 1;
    },
    reset() {
      this.subscription = 'free';
      this.planId = null;
      this.step = 1;
      this.paymentId = null;
    },
    openModal(modal) {
      this.$refs[modal].dialog = true;
    },
  },
};
</script>

<style lang="stylus">
.container-active
  border 2px solid rgb(72, 167, 242) !important

  .v-icon
    background-color rgba(72, 167, 242, 0.65) !important
    font-size 13px !important

.container-inactive
  background rgba(173, 165, 165, 0.161) !important
  border 2px solid rgba(173, 165, 165, 0.01) !important

  .v-icon
    background-color rgba(244, 67, 54, 0.65) !important
    font-size 13px !important
</style>
