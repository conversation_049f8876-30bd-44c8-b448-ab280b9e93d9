<template>
  <div>
    <calculation-badge :url="url" />

    <v-btn
      text
      :disabled="!url"
      @click.native="dialog = true"
    >
      <h4>{{ $t('subscription_whyThisPrice') }}</h4>
    </v-btn>
    <v-dialog
      v-model="dialog"
      content-class="dialogWidth-3"
      style="z-index: 1200"
    >
      <v-card>
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">
              {{ $t('subscriptions.subscription') }} - {{ $t('subscription_whyThisPrice') }}
            </h5>
          </span>
          <v-spacer />
          <v-btn
            small
            fab
            dark
            @click.native="dialog = false"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text>
          <v-container grid-list-md>
            <h3 class="mb-4">
              {{ $t('subscription_whyThisPriceModalHint') }}
            </h3>
            <subscription-calculation
              :url="url"
              @updated="summary = $event"
            />
          </v-container>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="gray"
            :disabled="loaders.submit"
            text
            @click.native="closeDialog"
          >
            {{ $t('actions.return_to_list') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>

import SubscriptionCalculation from '@components/subscription/SubscriptionCalculation.vue';
import CalculationBadge from '@components/subscription/badge/CalculationBadge.vue';

export default {
  components: { CalculationBadge, SubscriptionCalculation },
  props: {
    planId: {
      type: Number,
      default: null,
      required: false,
    },
  },
  data() {
    return {
      dialog: false,
      summary: null,
      loaders: {
        submit: false,
      },
    };
  },
  computed: {
    url() {
      if (!this.planId) {
        return null;
      }

      return `/api/subscriptions/package/${this.planId}/calculate`;
    },
  },
  methods: {

    closeDialog() {
      this.loaders.submit = false;
      this.dialog = false;
    },
  },
};
</script>
