<template>
  <div>
    <btn-confirm
      :disabled="disabled"
      :text="$t('admin_confirm')"
      @click="openDialog()"
    />
    <generic-modal
      v-model="dialog"
      :title="$t('admin_confirm')"
    >
      <v-textarea
        v-model="comment"
        prepend-icon="mdi-comment"
        counter="250"
        required
        :rules="rules.selectRequired"
        :label="$t('admin_comment')"
      />
      <template #actions>
        <v-spacer />
        <v-btn
          color="gray"
          text
          @click.native="closeDialog"
        >
          {{ $t('actions.return_to_list') }}
        </v-btn>
        <v-btn
          color="primary darken-1"
          @click.native="confirm()"
        >
          {{ $t('admin_confirm') }}
        </v-btn>
      </template>
    </generic-modal>
  </div>
</template>

<script>

import GenericModal from '@components/common/GenericModal.vue';
import BtnConfirm from '@components/common/button/BtnConfirm.vue';

export default {
  components: {
    BtnConfirm, GenericModal,
  },
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      comment: null,
      dialog: false,
      rules: {
        selectRequired: [(v) => !!v || this.$t('common_fieldRequired')],
      },
    };
  },
  computed: {
    url() {
      return `${this.baseUrl}`;
    },
  },
  methods: {
    onError() {
      this.closeDialog();
    },
    openDialog() {
      this.dialog = true;
    },
    closeDialog() {
      this.dialog = false;
    },
    confirm() {
      this.axios.post(
        `${this.baseUrl}/confirm`,
        {
          comment: this.comment,
        },
      )
        .then(() => {
          this.onSuccess();
          this.$emit('success');
          this.closeDialog();
        });
    },
  },
};
</script>
