<template>
  <div
    class="text-sm-center"
  >
    <v-layout
      cols="12"
      row
      wrap
    >
      <v-col
        cols="12"
      >
        <h2>{{ $t('subscription_chosePaymentPeriod') }}</h2>
        <h3 class="mb-2">
          {{ $t('subscription_logerPeriodBetterPrice') }}
        </h3>
      </v-col>
      <v-col
        v-for="(plan)
          in plans"
        :key="plan.id"
        class="mb-2 clickable"
        cols="12"
        :sm="columnsWidth"
      >
        <v-card
          class="elevation-0 ml-1 mr-1 pt-2 pb-2 pl-2 pr-2"
          :class="{
            'container-active': selectedId === plan.id,
            'container-inactive': !(selectedId === plan.id)
          }"
          @click.native="toggleSubscriptionId(plan.id)"
        >
          <h2>{{ $t(`date.length.${plan.monthsLength}m`) }}</h2>
          <div>
            <h2>
              <currency-formatter
                :value="plan.value"
                :symbol="plan.currencySymbol"
                :digits="0"
              />
            </h2>
          </div>
          <div class="mt-2 mb-2" />
          <h5>
            {{ $t('subscription_priceForCarwashForMonth') }}
          </h5>
        </v-card>
      </v-col>
    </v-layout>
  </div>
</template>
<script>
import { SubscriptionCodeType } from '@components/subscription/types';
import CurrencyFormatter from '@components/common/formatters/CurrencyFormatter.vue';

export default {
  components: { CurrencyFormatter },
  model: {
    prop: 'value',
    event: 'update:value',
  },
  props: {
    subscription: {
      type: String,
      required: true,
    },
    value: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      selectedId: this.value ?? null,
      plans: null,
    };
  },
  computed: {
    SubscriptionCodeType() {
      return SubscriptionCodeType;
    },
    columnsWidth() {
      // max 4 columns in one row if less fit to full width
      const lengthCount = Object.keys(this.plans).length;

      if (lengthCount > 4) {
        return 3;
      }

      return 12 / lengthCount;
    },
  },
  watch: {
    subscription() {
      this.getPlans();
    },
  },
  mounted() {
    this.getPlans();
  },
  methods: {
    toggleSubscriptionId(key) {
      this.selectedId = key;
      this.$emit('update:value', key);
    },

    getPlans() {
      this.axios.get('/api/subscriptions/plans2', { params: { code: this.subscription } })
        .then((response) => {
          this.plans = response.data;
        })
        .catch(() => {
        });
    },
  },
};
</script>
