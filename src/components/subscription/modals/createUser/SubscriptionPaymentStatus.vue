<template>
  <div
    class="text-sm-center mb-12"
    color="grey lighten-1"
    height="200px"
  >
    <div>
      <template v-if="paymentData?.status === 'confirmed'">
        <v-icon
          style="font-size: 76px"
          color="green darken-2"
        >
          mdi-check-circle-outline
        </v-icon>
        <h1>{{ $t('common_paid') }}</h1>
      </template>

      <template v-else-if="paymentData?.status === 'canceled'">
        <v-icon
          style="font-size: 76px"
          color="error"
        >
          mdi-alert
        </v-icon>
        <h1>{{ $t('common_canceled') }}</h1>
      </template>

      <template v-else>
        <v-icon
          style="font-size: 76px"
          color="progress"
        >
          mdi-cached
        </v-icon>
        <h1>{{ $t('common_processing') }}</h1>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    paymentId: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      loading: true,
      paymentData: null,
      interval: null,
    };
  },
  mounted() {
    if (this.paymentId) {
      this.fetchPaymentData();
      this.interval = setInterval(() => {
        this.fetchPaymentData();
      }, 5000); // odpytywanie co 5 sekund
    }
  },
  beforeDestroy() {
    this.clearInterval();
  },
  methods: {
    fetchPaymentData() {
      if (!this.paymentId) return;

      this.axios
        .get(`/api/subscriptions/payment/${this.paymentId}`)
        .then((response) => {
          if (response.data) {
            this.paymentData = response.data;
            if (
              response.data.status === 'paid' || response.data.status === 'canceled'
            ) {
              this.clearInterval();
            }
          }
        });
    },
    clearInterval() {
      if (this.interval) {
        clearInterval(this.interval);
        this.interval = null;
      }
    },
  },
};
</script>
