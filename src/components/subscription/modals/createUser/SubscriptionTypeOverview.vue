<template>
  <v-card
    class="elevation-0 ml-1 mr-1 pt-2 pb-2 pl-2 pr-2"
    :class="{
      'container-active': selected,
      'container-inactive': !selected
    }"
  >
    <div
      v-for="(content, key) in subscriptionContent"
      v-show="content.show"
      :key="key"
      class="subscription-content-option"
    >
      <v-icon
        v-if="selected"
        color="white"
      >
        mdi-check
      </v-icon>
      <v-icon
        v-else
        color="white"
      >
        mdi-close
      </v-icon>
      {{ $t(content.text) }}
    </div>
  </v-card>
</template>

<script>
import { SubscriptionContents } from '@components/subscription/modals/createUser/types';

export default {
  props: {
    subscription: {
      type: String,
      required: true,
    },
    selected: {
      type: Boolean,
      required: true,
    },
  },
  computed: {
    subscriptionContent() {
      return SubscriptionContents[this.subscription] || [];
    },
  },
};
</script>

<style lang="stylus">
.subscription-content-option
  display flex
  align-items center
  border-bottom 1px solid rgba(0, 0, 0, 0.12)
  border-bottom-right-radius 0px !important
  border-bottom-left-radius 0px !important
  text-align left

  .v-icon
    background-repeat no-repeat
    border-bottom-left-radius 50%
    border-bottom-right-radius 50%
    border-top-left-radius 50%
    border-top-right-radius 50%
    font-size 16px
    font-weight bold
    margin-right 10px
    margin-top 4px
    margin-bottom 4px
</style>
