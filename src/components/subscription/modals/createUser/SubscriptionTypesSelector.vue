<template>
  <div
    v-if="plans"
    class="text-sm-center"
  >
    <h2 class="mb-2">
      {{ $t('subscription_chose2') }}
    </h2>
    <v-layout
      cols="12"
      row
      wrap
    >
      <v-col
        v-for="(subscriptionCode, subCode) in SubscriptionCodeType"
        :key="subCode"
        class="mb-2 clickable"
        cols="12"
        sm="4"
      >
        <v-card
          class="elevation-0 ml-1 mr-1 pt-2 pb-2 pl-2 pr-2"
          :class="{
            'container-active': (subCode === code),
            'container-inactive': !(subCode === code)
          }"
          @click.native="toggleSubscriptionType(subCode)"
        >
          <h2> <subscription-code-badge :value="subCode" /> </h2>
          <h2>
            <currency-formatter
              :value="getPlan(subCode).value"
              :symbol="getPlan(subCode).currencySymbol"
              :digits="0"
            />
          </h2>
          <h5 class="pt-2">
            {{ $t('subscription_priceForCarwashForMonth') }}
          </h5>
        </v-card>
      </v-col>
    </v-layout>

    <h2 class="mb-2">
      {{ $t('subscription_subscriptionContent') }}
    </h2>

    <v-layout
      cols="12"
      row
      wrap
    >
      <v-col
        v-for="(sub, subCode) in SubscriptionCodeType"
        :key="subCode"
        class="mb-2"
        cols="12"
        sm="4"
      >
        <subscription-type-overview
          :selected="SubscriptionCodeType[code].order >= SubscriptionCodeType[subCode].order"
          :subscription="subCode"
        />
      </v-col>
    </v-layout>
  </div>
</template>
<script>
import { SubscriptionCodeType } from '@components/subscription/types';
import SubscriptionTypeOverview
  from '@components/subscription/modals/createUser/SubscriptionTypeOverview.vue';
import CurrencyFormatter from '@components/common/formatters/CurrencyFormatter.vue';
import SubscriptionCodeBadge from '@components/subscription/badge/SubscriptionCodeBadge.vue';

export default {
  components: { SubscriptionCodeBadge, CurrencyFormatter, SubscriptionTypeOverview },
  model: {
    prop: 'value',
    event: 'update:value',
  },
  props: {
    value: {
      type: String,
      default: 'free',
    },
  },
  data() {
    return {
      code: this.value ?? 'free',
      plans: null,
    };
  },
  computed: {
    SubscriptionCodeType() {
      return SubscriptionCodeType;
    },
  },
  watch: {
  },
  mounted() {
    this.getPlans();
  },
  methods: {
    toggleSubscriptionType(key) {
      this.code = key;
      this.$emit('update:value', key);
    },
    getPlan(value) {
      if (this.plans) {
        return this.plans.find((plan) => plan.code === value);
      }
      return null;
    },
    getPlans() {
      this.plans = null;
      this.axios.get('/api/subscriptions/plans1')
        .then((response) => {
          this.plans = response.data;
        })
        .catch(() => {
        });
    },
  },
};
</script>
