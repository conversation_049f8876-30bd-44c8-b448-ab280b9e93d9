<template>
  <div
    class="text-sm-center mb-12"
    color="grey lighten-1"
    height="200px"
  >
    <v-alert
      v-show="errorMessage"
      class="py-6 my-4"
      border="left"
      icon="mdi-information"
      color="warning"
    >
      <div class="white--text">
        {{ errorMessage }}
      </div>
    </v-alert>
  </div>
</template>

<script>

export default {
  props: {
    planId: {
      type: Number,
      default: null,
      required: false,
    },
  },
  data() {
    return {
      loading: true,
      paymentData: null,
      errorMessage: null,
    };
  },
  methods: {
    initPayment() {
      this.axios.post(
        `/api/subscriptions/package/${this.planId}/order`,
      )
        .then((response) => {
          window.location = `${response.data.externalPayment.redirectUrl}`;
        }).catch((data) => {
          const { response } = data;
          if (response.status === 400) {
            this.errorMessage = response.data.message;
          }
        });
    },
  },
};
</script>
