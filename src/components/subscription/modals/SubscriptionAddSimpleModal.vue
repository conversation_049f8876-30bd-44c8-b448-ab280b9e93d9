<template>
  <div>
    <v-btn
      class="ml-2"
      color="primary"
      x-small
      fab
      elevation="1"
      @click.stop
      @click.native="dialog = true"
    >
      <v-icon>
        mdi-plus
      </v-icon>
    </v-btn>
    <generic-modal
      v-model="dialog"
      :title="$t('admin_add')"
    >
      <subscriber-summary :url="baseUrl" />
      <subscription-add-filter-modal
        ref="filter"
        v-model="filters"
        :base-url="baseUrl"
      />
      <subscription-calculation
        v-if="filters.valid"
        :url="`${baseUrl}/subscriptions/calculate`"
        :params="filters"
      />

      <subscription-add-comment
        ref="commentInput"
        v-model="comment"
      />
      <template #actions>
        <v-spacer />
        <v-btn
          color="gray"
          text

          @click.native="closeDialog"
        >
          {{ $t('actions.return_to_list') }}
        </v-btn>
        <v-btn
          v-if="canAccess('administration', 'admin')"
          color="secondary"
          :disabled="!isValid()"
          @click.native="save"
        >
          {{ $t('admin_save') }}
        </v-btn>
        <v-btn
          color="primary darken-1"
          :disabled="!isValid()"
          @click.native="add"
        >
          {{ $t('admin_add') }}
        </v-btn>
      </template>
    </generic-modal>
  </div>
</template>

<script>
import SubscriptionCalculation from '@components/subscription/SubscriptionCalculation.vue';
import { mapGetters } from 'vuex';
import SubscriberSummary from '@components/subscription/subscriber/SubscriberSummary.vue';
import GenericModal from '@components/common/GenericModal.vue';
import SubscriptionAddComment from './createAdmin/SubscriptionAddComment.vue';
import SubscriptionAddFilterModal from './createAdmin/SubscriptionAddFilter.vue';

export default {
  components: {
    SubscriptionAddComment,
    SubscriptionAddFilterModal,
    GenericModal,
    SubscriberSummary,
    SubscriptionCalculation,
  },
  props: {
    onSuccess: {
      type: Function,
      default: () => {},
    },
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      subscriptionOptions: null,
      subModel: null,
      comment: null,
      dialog: false,
      filters: {},
      rules: {
        selectRequired: [(v) => !!v || this.$t('common_fieldRequired')],
      },
    };
  },
  computed: {
    ...mapGetters({
      canAccess: 'auth/canAccess',
    }),
  },
  watch: {
    dialog(val) {
      // get data only when dialog shows up
      if (val) {
        this.reset();
      }
    },
  },
  methods: {
    reset() {
      this.filters = {};
      this.comment = null;
    },
    onError() {
      // on error
      this.closeDialog();
    },
    isValid() {
      return this.$refs.commentInput?.isValid;
    },
    save() {
      this.axios.post(
        `${this.baseUrl}/subscriptions/save`,
        {
          comment: this.comment,
        },
        {
          params: this.filters,
        },
      )
        .then(
          (response) => {
            if (response.status === 200) {
              this.onSuccess();
              this.closeDialog();
            }
          },
          () => {
          },
        );
    },
    add() {
      this.axios.post(
        `${this.baseUrl}/subscriptions/add`,
        {
          comment: this.comment,
        },
        {
          params: this.filters,
        },
      )
        .then(
          (response) => {
            if (response.status === 200) {
              this.onSuccess();
              this.closeDialog();
            }
          },
          () => {
          },
        );
    },
    closeDialog() {
      this.reset();
      this.dialog = false;
    },
  },
};
</script>
