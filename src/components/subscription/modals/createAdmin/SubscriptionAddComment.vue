<!-- components/NewComment.vue -->
<template>
  <v-textarea
    v-model="localValue"
    prepend-icon="mdi-comment"
    counter="250"
    :rules="[validate]"
    :error="touched && !isValid"
    :label="$t('admin_comment')"
    required
    @blur="touched = true"
  />
</template>

<script>
export default {
  props: {
    value: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      localValue: this.value,
      touched: false,
    };
  },
  computed: {
    isValid() {
      return !!this.localValue?.trim();
    },
  },
  watch: {
    localValue(val) {
      this.$emit('input', val);
    },
    value(val) {
      if (val !== this.localValue) {
        this.localValue = val;
      }
    },
  },
  methods: {
    validate(value) {
      return !!value?.trim() || this.$t('common_fieldRequired');
    },
  },
};
</script>
