<template>
  <div>
    <v-row dense>
      <v-col
        cols="12"
        sm="6"
      >
        <single-select
          v-model="code"
          :items="codes"
          prepend-icon="mdi-file-outline"
          :label="$t('admin_subscription')"
        />
      </v-col>

      <v-col
        cols="12"
        sm="6"
      >
        <v-switch
          v-if="canAccess('administration', 'admin')"
          v-model="advanced"
          :label="$t('subscription.advanced')"
          :inset="true"
          :false-value="0"
          :true-value="1"
          :append-icon="advanced ? 'mdi-cog' : 'mdi-lightbulb-outline'"
          class="mb-4"
        />
      </v-col>
    </v-row>

    <!-- Prosta konfiguracja -->
    <template v-if="!advanced">
      <v-col
        sm="12"
        class="pl-0 pr-0"
      >
        <single-select
          v-model="config.plan"
          :items="plans"
          prepend-icon="mdi-file-outline"
          :label="$t('common_subscription')"
          allow-null
        />
      </v-col>
    </template>

    <!-- <PERSON><PERSON><PERSON><PERSON><PERSON> konfiguracja -->
    <template v-else>
      <v-row dense>
        <v-col
          cols="12"
          sm="6"
        >
          <date-range v-model="config.dateRange" />
        </v-col>
        <v-col
          cols="12"
          sm="6"
        >
          <single-select
            v-model="config.payer"
            :items="payers"
            prepend-icon="mdi-file-outline"
            :label="$t('admin_whoPays')"
          />
        </v-col>
      </v-row>
      <v-row
        v-if="documentShow"
        dense
      >
        <v-col>
          <v-checkbox
            v-model="config.document"
            :label="$t('subscription.document.issue')"
            :false-value="false"
            :true-value="true"
          />
        </v-col>
        <v-col>
          <v-checkbox
            v-if="config.document"
            v-model="config.send"
            :label="$t('subscription.document.send')"
            :false-value="false"
            :true-value="true"
          />
        </v-col>
      </v-row>
    </template>
  </div>
</template>

<script>

import { SubscriptionCodeType, SubscriptionPayerType } from '@components/subscription/types';
import DateRange from '@components/reports/filters/DateRange.vue';
import { mapGetters } from 'vuex';
import SingleSelect from '@components/reports/filters/SingleSelect.vue';

export default {
  components: { SingleSelect, DateRange },
  model: {
    prop: 'param',
    event: 'update:param',
  },
  props: {
    param: {
      type: Object,
      default: () => ({}),
    },
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      advanced: 0,
      code: 'premium',
      config: {
        plan: this.param?.plan ?? null,
        payer: this.param?.payer ?? 'client',
        document: this.param?.document ?? false,
        send: this.param?.send ?? false,
        dateRange: [
          this.param?.from ?? null,
          this.param?.to ?? null,
        ],
      },
      plans: [],
    };
  },
  computed: {
    ...mapGetters({
      canAccess: 'auth/canAccess',
    }),
    codes() {
      return Object.entries(SubscriptionCodeType).filter(([key]) => key !== 'free').map(([key, value]) => ({
        value: key,
        text: this.$t(value.text),
      }));
    },
    payers() {
      return Object.entries(SubscriptionPayerType).map(([key, value]) => ({
        value: key,
        text: value.text,
      }));
    },
    documentShow() {
      return this.config.payer === 'dealer';
    },
    internalParam() {
      const base = {
        valid: this.isValid,
        advanced: this.advanced,
      };

      if (this.advanced) {
        return {
          ...base,
          from: this.config.dateRange[0],
          to: this.config.dateRange[1],
          code: this.code,
          payer: this.config.payer,
          document: this.documentShow && this.config.document,
          send: this.documentShow && this.config.send,
        };
      }
      return {
        ...base,
        plan: this.config.plan,
      };
    },
    isValid() {
      return this.advanced
        ? !!this.config.dateRange[0] && !!this.config.dateRange[1] && !!this.code
        : !!this.config.plan;
    },
  },
  watch: {
    internalParam: {
      handler(value) {
        this.$emit('update:param', value);
      },
      immediate: true,
      deep: true,
    },
    code() {
      this.getSubscriptionsList();
    },
  },
  created() {
    this.getSubscriptionsList();
  },
  methods: {
    getSubscriptionsList() {
      this.axios.get(`${this.baseUrl}/subscriptions/plans`, { params: { code: this.code } })
        .then((response) => {
          this.plans = response.data
            .sort((a, b) => a.monthsLength - b.monthsLength)
            .map((item) => ({
              value: item.id,
              text: `${item.code} - ${item.monthsLength} months - ${item.value} ${item.currencySymbol}`,
            }));
        });
    },
  },
};
</script>
