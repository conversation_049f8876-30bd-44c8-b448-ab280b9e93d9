<template>
  <div>
    <btn-info
      :text="$t('admin_information')"
      @click="dialog = true"
    />
    <generic-modal
      v-model="dialog"
      :title="$t('admin_information')"
    >
      <subscription-calculation
        :url="`/administration/subscription/${subscriptionId}`"
      />
      <comments-list :url="`/administration/subscription/${subscriptionId}/comments`" />
      <template #actions>
        <v-spacer />
        <v-btn
          color="gray"
          text
          @click.native="closeDialog"
        >
          {{ $t('actions.return_to_list') }}
        </v-btn>
        <template />
      </template>
    </generic-modal>
  </div>
</template>

<script>
import SubscriptionCalculation from '@components/subscription/SubscriptionCalculation.vue';
import CommentsList from '@components/libs/standard-types/comments/CommentsList.vue';
import GenericModal from '@components/common/GenericModal.vue';
import BtnInfo from '@components/common/button/BtnInfo.vue';

export default {
  components: {
    BtnInfo, GenericModal, CommentsList, SubscriptionCalculation,
  },
  props: {
    subscriptionId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      subscription: {},
      loaders: true,
      dialog: false,
    };
  },
  watch: {
    dialog(val) {
      // get user data only when dialog shows up
      if (val) {
        this.loaders = true;
        this.clearFormData();
        this.getData();
      }
    },
  },
  methods: {
    onError() {
      this.closeDialog();
    },
    getData() {
      this.loaders = true;
      this.axios.get(
        `/administration/subscription/${this.subscriptionId}`,
      )
        .then(
          (response) => {
            if (response.status === 200) {
              this.subscription = response.data;
              this.loaders = false;
            }
          },
          () => {
            // on error
            this.loaders = false;
          },
        );
    },
    clearFormData() {
      this.subscription = {};
    },
    closeDialog() {
      this.dialog = false;
    },
  },
};
</script>
