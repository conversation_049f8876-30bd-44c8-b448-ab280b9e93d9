export const StatusesType = {
  paid: {
    text: 'administration.subscription.status.paid',
    icon: 'mdi-check-circle-outline',
    color: 'green darken-2',
  },
  initiated_proforma: {
    text: 'administration.subscription.status.initiated_proforma',
    icon: 'mdi-clock-outline',
    color: 'progress',
  },
  manually_canceled: {
    text: 'administration.subscription.status.manually_canceled',
    icon: 'mdi-close-circle-outline',
    color: 'error',
  },
  error: {
    text: 'administration.subscription.status.error',
    icon: 'mdi-alert-outline',
    color: 'error',
  },
  canceled: {
    text: 'administration.subscription.status.canceled',
    icon: 'mdi-close-circle-outline',
    color: 'error',
  },
  initiated: {
    text: 'administration.subscription.status.initiated',
    icon: 'mdi-cached',
    color: 'progress',
  },
};

export const SubscriptionCarwashType = {
  UNSUBSCRIBED: {
    text: 'subscription.carwash-type.UNSUBSCRIBED',
  },
  WARRANTY: {
    text: 'subscription.carwash-type.WARRANTY',
  },
  STANDARD: {
    text: 'subscription.carwash-type.STANDARD',
  },
};

export const SubscriptionCodeType = {
  free: {
    text: 'subscriptions.types.free',
    color: 'orange',
    order: 0,
  },
  basic: {
    text: 'subscriptions.types.basic',
    color: 'green',
    order: 1,
  },
  premium: {
    text: 'subscriptions.types.premium',
    color: 'blue',
    order: 2,
  },
};

export const SubscriptionPayerType = {
  dealer: {
    text: 'client/dealer',
    color: 'green',
  },
  bkf: {
    text: 'bkf',
    color: 'red',
  },
  i2m: {
    text: 'i2m',
    color: 'blue',
  },
};
