<template>
  <v-tooltip
    bottom
  >
    <template #activator="{ on, attrs }">
      <v-icon
        :color="statusInfo.color"
        v-bind="attrs"
        v-on="on"
      >
        {{ statusInfo.icon }}
      </v-icon>
    </template>
    <span> {{ statusInfo.text }}</span>
  </v-tooltip>
</template>

<script>

import { StatusesType } from '@components/subscription/types';

/**
 * ikonka statusu subskrypcji
 * zgodnie z https://gitlab.bkf.pl/bkf/ebkf/carwashmanager/cm-api/-/blob/development/src/Entity/Enum/SubscriptionStatus.php
 */
export default {
  props: {
    status: {
      type: String,
      required: true,
    },
  },
  computed: {
    statusInfo() {
      const status = StatusesType[this.status];
      if (status) {
        return {
          icon: status.icon,
          color: status.color,
          text: this.$t(`${status.text}`),
        };
      }

      return {
        icon: 'mdi-help-circle',
        color: 'grey',
        text: this.$t('invoice.status.unknown'),
      };
    },
  },
};
</script>
