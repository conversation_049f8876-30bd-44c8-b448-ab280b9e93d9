<template>
  <div>
    <h2>{{ $t('subscription_summary') }}</h2>

    <div v-if="summary !== null">
      <br>
      <h1>
        <currency-formatter
          :symbol="summary.currencySymbol"
          :value="summary.grossValue"
        />
      </h1>
      <p>
        <currency-formatter
          :symbol="summary.currencySymbol"
          :value="summary.netValue"
        />
        {{ $t('fiscal_transactions.table.net') }}
      </p>
    </div>

    <div v-else>
      <h1>-</h1>
    </div>
  </div>
</template>

<script>

import CurrencyFormatter from '@components/common/formatters/CurrencyFormatter.vue';

export default {
  components: { CurrencyFormatter },
  props: {
    url: {
      type: String,
      required: false,
      default: null,
    },
  },
  data() {
    return {
      summary: null,
    };
  },
  watch: {
    url() {
      this.getCalculateSubscription();
    },
  },
  methods: {
    getCalculateSubscription() {
      if (this.url === null) {
        this.summary = null;
        return;
      }
      this.axios.get(
        this.url,
      )
        .then((response) => {
          this.summary = response.data;
        });
    },
  },
};

</script>
