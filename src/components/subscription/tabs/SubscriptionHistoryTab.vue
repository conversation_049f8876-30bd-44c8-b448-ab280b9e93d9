<template>
  <div>
    <v-card>
      <v-toolbar
        dark
        flat
        color="secondary"
      >
        <v-btn
          icon
          dark
        >
          <v-icon>mdi-list-box</v-icon>
        </v-btn>
        <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
          {{ $t('subscription_historyList') }}
        </v-toolbar-title>
        <v-spacer />
      </v-toolbar>
      <v-card-text>
        <v-layout
          row
          wrap
        >
          <v-col cols="12">
            <v-card-text dark>
              <v-data-table
                :headers="dataTable.headers"
                :items="dataTable.items"
                item-key="number"
                :loading="loader"
                :options.sync="pagination"
                :no-data-text="$t('common_noData')"
                :footer-props="dataTable.footerProps"
              >
                <template #progress>
                  <div class="text-center">
                    <v-progress-circular
                      class="loader"
                      indeterminate
                      color="primary"
                    />
                  </div>
                </template>
                <template #[`item.startDate`]="{ item }">
                  <date-time-formatter
                    :value="item.startDate"
                    format="YYYY-MM-DD"
                  />
                </template>
                <template #[`item.endDate`]="{ item }">
                  <date-time-formatter
                    :value="item.endDate"
                    format="YYYY-MM-DD"
                  />
                </template>

                <template #[`item.status`]="{ item }">
                  <subscription-status-badge :status="item.status" />
                </template>
                <template #[`item.type`]="{ item }">
                  <subscription-code-badge :value="item.type" />
                </template>

                <template #[`item.grossValue`]="{ item }">
                  <div class="text-right">
                    <currency-formatter
                      :value="item.grossValue"
                      :symbol="item.currencySymbol"
                    />
                  </div>
                </template>
              </v-data-table>
            </v-card-text>
          </v-col>
        </v-layout>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import CurrencyFormatter from '@components/common/formatters/CurrencyFormatter.vue';
import DateTimeFormatter from '@components/common/formatters/DateTimeFormatter.vue';
import SubscriptionCodeBadge from '@components/subscription/badge/SubscriptionCodeBadge.vue';
import SubscriptionStatusBadge from '../badge/SubscriptionStatusBadge.vue';

export default {
  components: {
    SubscriptionStatusBadge,
    SubscriptionCodeBadge,
    DateTimeFormatter,
    CurrencyFormatter,
  },
  data() {
    return {
      loader: true,
      pagination: {
        page: 1,
        itemsPerPage: 10,
        sortBy: ['startDate'],
        sortDesc: [true],
      },
      dataTable: {
        headers: [
          {
            text: this.$t('common_startDate'),
            value: 'startDate',
            class: 'text-sm-start',
          },
          {
            text: this.$t('common_subscriptionsEnddate'),
            value: 'endDate',
            class: 'text-sm-start',
          },
          {
            text: this.$t('subscription_state'),
            value: 'status',
            class: 'text-sm-start',
          },
          {
            text: this.$t('subscriptions.subscription'),
            value: 'type',
            class: 'text-sm-start',
          },
          {
            text: this.$t('common_price'),
            value: 'grossValue',
            class: 'text-end',
          },
        ],
        items: [],
        totalItems: 0,
        footerProps: {
          'items-per-page-options': [5, 10, 25],
          'items-per-page-text': this.$t('common_rowsPerPage'),
        },
      },
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      this.loader = true;
      this.axios.get('/api/subscriptions')
        .then((response) => {
          if (response.data) {
            this.dataTable.items = response.data.data;
            this.dataTable.totalItems = response.data.total;
            this.loader = false;
          }
          this.loader = false;
        })
        .catch(() => {
          this.loader = false;
        });
    },
  },
};
</script>
