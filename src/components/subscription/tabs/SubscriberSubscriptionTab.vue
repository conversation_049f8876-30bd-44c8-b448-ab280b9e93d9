<template>
  <v-container
    grid-list-md
    fluid
  >
    <subscription-list
      ref="list"
      :url="`administration/subscriber/${subscriberId}/subscriptions`"
      :filters="filters"
    >
      <template #table-actions>
        <subscription-add-simple-modal
          :subscriber-id="subscriberId"
          :base-url="`/administration/subscriber/${subscriberId}`"
          :on-success="refresh"
        />
      </template>
    </subscription-list>
  </v-container>
</template>

<script>
import SubscriptionList from '@components/subscription/SubscriptionList.vue';
import SubscriptionAddSimpleModal from '../modals/SubscriptionAddSimpleModal.vue';

export default {
  components: {
    SubscriptionList,
    SubscriptionAddSimpleModal,
  },
  props: {
    subscriberId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      filters: {},
      loading: false,
    };
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    fetchData() {
      this.filters = {
        ...this.filters,
      };
      this.$refs.list.fetchData();
    },
  },
};
</script>
