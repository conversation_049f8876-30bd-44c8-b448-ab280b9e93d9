<template>
  <div>
    <div
      v-if="!subscriptionCalculation"
      class="text-center"
    >
      <v-progress-circular
        class="circleProgress"
        :size="90"
        :width="7"
        color="primary"
        indeterminate
      />
    </div>
    <div v-else>
      <table class="pt-2 my-4 summary-table">
        <tr class="font-weight-bold">
          <td>
            {{ $t('common_subscription') }}
          </td>
          <td>
            {{ $t('common_startDate') }}
          </td>
          <td>
            {{ $t('common_subscriptionsEnddate') }}
          </td>
        </tr>
        <tr>
          <td>
            <subscription-code-badge
              :value="subscriptionCalculation.type"
            />
          </td>
          <td>
            <date-time-formatter
              :value="subscriptionCalculation.startDate"
              format="YYYY-MM-DD"
            />
          </td>
          <td>
            <date-time-formatter
              :value="subscriptionCalculation.endDate"
              format="YYYY-MM-DD"
            />
          </td>
        </tr>
      </table>
      <table class="pt-2 summary-table">
        <tr class="font-weight-bold">
          <td>
            {{ $t('subscription.table.position') }}
          </td>
          <td>
            {{ $t('subscription.table.type') }}
          </td>
          <td class="text-end">
            {{ $t('common_document') }}
          </td>
          <td class="text-end">
            {{ $t('subscription.table.price-before-discount') }}
          </td>
        </tr>
        <tr
          v-for="(it, index) in subscriptionCalculation.item"
          :key="index"
        >
          <td>
            {{ it.position }}
          </td>
          <td>
            <carwash-type-badge :value="it.type" />
          </td>
          <td class="text-end">
            {{ it.invoice ?? '-' }}
          </td>
          <td class="text-end">
            <currency-formatter
              :value="it.totalPrice"
              :symbol="subscriptionCalculation.currencySymbol"
            />
          </td>
        </tr>
        <tr class="font-weight-bold">
          <td />
          <td />
          <td>
            {{ $t('subscription.table.sum') }}
          </td>
          <td class="text-end">
            <currency-formatter
              :symbol="subscriptionCalculation.currencySymbol"
              :value="subscriptionCalculation.baseValue"
            />
          </td>
        </tr>
      </table>

      <invoice-info
        :item="subscriptionCalculation.invoice"
      />

      <div class="font-weight-bold">
        {{ $t('subscription.table.summary') }}
      </div>
      <table class="pt-2 summary-table">
        <tr class="font-weight-bold">
          <td>
            {{ $t('subscription.table.type') }}
          </td>
          <td>
            {{ $t('subscription.table.price-before-discount') }}
          </td>
          <td>
            {{ $t('subscription.table.discount') }}
          </td>
          <td class="text-end">
            {{ $t('subscription.table.price-after-discount') }}
          </td>
        </tr>
        <tr
          v-for="(summary, index) in subscriptionCalculation.summary"
          :key="index"
        >
          <td>
            <carwash-type-badge :value="summary.type" />
          </td>
          <td class="text-end">
            <currency-formatter
              :value="summary.baseValue"
              :symbol="subscriptionCalculation.currencySymbol"
            />
          </td>
          <td class="text-end">
            {{ summary.discount }}%
          </td>
          <td class="font-weight-bold text-end">
            <currency-formatter
              :value="summary.valueAfterDiscount"
              :symbol="subscriptionCalculation.currencySymbol"
            />
          </td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script>
import CurrencyFormatter from '@components/common/formatters/CurrencyFormatter.vue';
import DateTimeFormatter from '@components/common/formatters/DateTimeFormatter.vue';
import CarwashTypeBadge from '@components/subscription/badge/CarwashTypeBadge.vue';
import SubscriptionCodeBadge from '@components/subscription/badge/SubscriptionCodeBadge.vue';
import InvoiceInfo from '@components/libs/invoices/badge/InvoiceInfo.vue';

export default {
  components: {
    InvoiceInfo,
    SubscriptionCodeBadge,
    CarwashTypeBadge,
    CurrencyFormatter,
    DateTimeFormatter,
  },
  props: {
    url: {
      type: String,
      required: false,
      default: null,
    },
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      subscriptionCalculation: null,
    };
  },
  watch: {
    url() {
      this.subscriptionCalculation = null;
      this.getCalculateSubscription();
    },
    params() {
      this.subscriptionCalculation = null;
      this.getCalculateSubscription();
    },
    subscriptionCalculation: {
      handler(newVal) {
        this.$emit('updated', {
          netValue: newVal?.netValue,
          grossValue: newVal?.grossValue,
          currencySymbol: newVal?.currencySymbol,
        });
      },
      deep: true,
    },
  },
  mounted() {
    this.getCalculateSubscription();
  },
  methods: {
    getCalculateSubscription() {
      if (this.url === null) {
        this.subscriptionCalculation = null;
        return;
      }
      this.axios.get(
        this.url,
        {
          params: this.params,
        },
      )
        .then((response) => {
          this.subscriptionCalculation = response.data;
        })
        .catch(() => {
          this.snackbar.showMessage(
            'error',
            this.$i18n.t('common_errorHeader'),
          );
        })
        .finally(() => {
        });
    },
  },
};
</script>

<style scoped>
.summary-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px black solid;
}

.summary-table td {
  padding: 5px;
  border: 1px black solid;
}

.card-text-wrap p {
  font-size: 18px;
  text-align: center;
}

</style>
