<template>
  <v-container
    v-if="company"
    fluid
  >
    <v-row>
      <v-col
        cols="12"
        class="d-flex justify-space-between align-center"
      >
        <div class="d-flex flex-column">
          <h1>
            <v-icon
              left
              small
              class="mr-2"
            >
              mdi-domain
            </v-icon>
            {{ company.name }}
          </h1>

          <div
            v-if="company.dealer"
            class="text-caption text--secondary mt-1 ml-6"
          >
            ({{ company.dealer?.name }})
          </div>
        </div>

        <v-btn
          icon
          @click="isCollapsed = !isCollapsed"
        >
          <v-icon>{{ isCollapsed ? 'mdi-chevron-down' : 'mdi-chevron-up' }}</v-icon>
        </v-btn>
      </v-col>

      <v-col
        v-show="!isCollapsed"
        cols="12"
      >
        <v-row>
          <v-col
            cols="12"
            sm="6"
            class="pt-0 pb-2"
          >
            <v-text-field
              :value="company.name"
              :label="$t('common_invoiceCompanySettingsName')"
              prepend-icon="mdi-rename-box"
              readonly
              disabled
            />
          </v-col>

          <v-col
            cols="12"
            sm="6"
            class="pt-0 pb-2"
          >
            <v-text-field
              :value="company.taxNumber"
              :label="$t('common_taxNumber')"
              prepend-icon="mdi-numeric"
              readonly
              disabled
            />
          </v-col>

          <v-col
            cols="12"
            sm="6"
            class="pt-0 pb-2"
          >
            <v-text-field
              :value="company.country"
              :label="$t('common_country')"
              prepend-icon="mdi-earth"
              readonly
              disabled
            />
          </v-col>

          <v-col
            cols="12"
            sm="6"
            class="pt-0 pb-2"
          >
            <v-text-field
              :value="company.dealer?.name || '-' "
              :label="$t('common_dealer')"
              prepend-icon="mdi-account-outline"
              readonly
              disabled
            />
          </v-col>

          <v-col
            cols="12"
            sm="4"
            class="pt-0 pb-2"
          >
            <v-text-field
              :value="company.city"
              :label="$t('common_city')"
              prepend-icon="mdi-city-variant-outline"
              readonly
              disabled
            />
          </v-col>

          <v-col
            cols="12"
            sm="2"
            class="pt-0 pb-2"
          >
            <v-text-field
              :value="company.postCode"
              :label="$t('common_postCode')"
              prepend-icon="mdi-home-city-outline"
              readonly
              disabled
            />
          </v-col>

          <v-col
            cols="12"
            sm="6"
            class="pt-0 pb-2"
          >
            <v-text-field
              :value="company.address"
              :label="$t('common_formAddress')"
              prepend-icon="mdi-map-marker-outline"
              readonly
              disabled
            />
          </v-col>
        </v-row>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
export default {
  props: {
    url: {
      type: String,
      required: true,
    },
    collapsed: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      company: {},
      invoiceDataFilled: false,
      isCollapsed: this.collapsed,
    };
  },
  computed: {
    valid() {
      return this.invoiceDataFilled;
    },
  },
  mounted() {
    this.fetchCompanyData();
  },
  methods: {
    async fetchCompanyData() {
      try {
        const { data } = await this.axios.get(this.url);
        this.company = data;
        this.invoiceDataFilled = this.company.invoiceDataFilled;
      } catch (e) {
        this.snackbar.showMessage(
          'error',
          this.$i18n.t('fetch_error'),
        );
      }
    },
  },
};
</script>
