<script>

import intersection from 'lodash/intersection';

export default {
  name: 'MultiselectValueMixin',
  data() {
    return {
      reportName: 'export',
    };
  },
  methods: {
    getMultiselectValue(possibleArray, chosenArray) {
      const possible = possibleArray.map(
        (row) => row.name,
      );

      let chosen = chosenArray;
      if (typeof chosenArray === 'string') {
        chosen = [chosenArray];
      }
      if (chosen === null) {
        return null;
      }

      const data = intersection(possible, chosen);

      if (chosen == null || possible == null) {
        return null;
      }

      if (possible.length === chosen.length) {
        return null;
      }

      return data.length ? data.join(',') : null;
    },
    getMultiselectFlatValue(possibleArray, chosenArray) {
      const possible = possibleArray.map(
        (row) => row,
      );

      let chosen = chosenArray;
      if (typeof chosenArray === 'string') {
        chosen = [chosenArray];
      }
      if (chosen === null) {
        return null;
      }

      const data = intersection(possible, chosen);

      if (chosen == null || possible == null) {
        return null;
      }

      if (possible.length === chosen.length) {
        return null;
      }

      return data.length ? data.join(',') : null;
    },
  },
};
</script>
