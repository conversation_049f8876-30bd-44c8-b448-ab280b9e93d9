<template>
  <v-radio-group
    :value="value"
    :label="$t('common_typeType')"
    row
    @change="$emit('input', $event)"
  >
    <v-radio
      :label="$t('actions.export_xlsx')"
      value="xlsx"
    />
    <v-radio
      :label="$t('actions.export_csv')"
      value="csv"
    />
    <v-radio
      :label="$t('actions.export_pdf')"
      value="pdf"
    />
  </v-radio-group>
</template>

<script>
export default {
  model: {
    prop: 'value',
    event: 'input',
  },
  props: {
    value: {
      type: String,
      default: 'xlsx', // domyślny typ eksportu
    },
  },
};
</script>
