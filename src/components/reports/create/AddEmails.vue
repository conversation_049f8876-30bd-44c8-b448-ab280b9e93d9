<template>
  <v-combobox
    v-model="internalValue"
    prepend-icon="mdi-email-multiple"
    hide-selected
    :label="$t('common_email')"
    multiple
    small-chips
    deletable-chips
    :rules="rules.comboboxEmail"
  >
    <template #no-data>
      <v-list-item>
        <v-list-item-content>
          <v-list-item-title>
            {{ $t('common_pressEnterToAddNew') }}
          </v-list-item-title>
        </v-list-item-content>
      </v-list-item>
    </template>
  </v-combobox>
</template>

<script>
export default {
  model: {
    prop: 'value',
    event: 'input',
  },
  props: {
    value: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      internalValue: this.value,
      rules: {
        comboboxEmail: [
          (v) => (Array.isArray(v) && v.every((email) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email))) || this.$t('common_emailMustBeValid'),
        ],
      },
    };
  },
  watch: {
    internalValue(val) {
      this.$emit('input', val);
    },
    value(val) {
      this.internalValue = val;
    },
  },
};
</script>
