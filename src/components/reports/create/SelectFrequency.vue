<template>
  <v-radio-group
    :value="value"
    :label="$t('common_frequency')"
    row
    @change="$emit('input', $event)"
  >
    <v-radio
      :label="$t('common_frequency_daily')"
      value="daily"
    />
    <v-radio
      :label="$t('common_frequency_weekly')"
      value="weekly"
    />
    <v-radio
      :label="$t('common_frequency_monthly')"
      value="monthly"
    />
  </v-radio-group>
</template>

<script>
export default {
  model: {
    prop: 'value',
    event: 'input',
  },
  props: {
    value: {
      type: String,
      default: 'daily',
    },
  },
};
</script>
