<template>
  <div>
    <v-btn
      color="primary"
      class="ml-2 white--text"
      :disabled="disabled"
      @click="open"
    >
      {{ $t('common_export') }}
    </v-btn>
    <v-dialog
      v-model="show"
      max-width="800"
    >
      <v-card>
        <v-card-title class="title">
          <slot name="title">
            <span class="headline text-uppercase text-h5">{{ $t('common_exportAsyncTitle') }}</span>
            <v-spacer />
            <btn-close @click="close()" />
          </slot>
        </v-card-title>
        <v-card-text>
          <div
            v-if="!loading"
            class="mt-7"
          >
            <template
              v-if="errorText !== null"
            >
              <v-alert
                border="left"
                class="mt-5"
                text
                type="error"
              >
                {{ errorText }}
              </v-alert>
            </template>
            <template
              v-if="!resourceId"
            >
              <new-report-config
                :params="params"
                :report="report"
                :base-url="baseUrl"
                @report-added="cyclicReportAdded"
                @init-report-generate="initReportGenerate"
              />
            </template>
            <div
              v-else
              class="text-center py-4"
            >
              <h3 class="py-4">
                {{ $t('common_reportReady') }}
              </h3>
              <v-btn
                class="mx-2"
                @click.native="clearReportGenerator"
              >
                {{ $t('common_clearReport') }}
              </v-btn>
              <act-download
                :url="`${baseUrl}/report/${resourceId}/download`"
                :type="'button'"
              />
            </div>
          </div>
          <div
            v-else
            class="text-center py-4"
          >
            <report-file-waiting
              :id="resourceId"
            />
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import ActDownload from '@components/common/Action/ActDownload.vue';
import BtnClose from '@components/common/button/BtnClose.vue';
import NewReportConfig from '@components/reports/create/NewReportConfig.vue';
import ReportFileWaiting from '../file/ReportFileWaiting.vue';

export default {
  components: {
    NewReportConfig,
    BtnClose,
    ReportFileWaiting,
    ActDownload,
  },
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
    report: {
      type: String,
      default: null,
    },
    params: {
      type: Object,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      errorText: null,
      resourceId: null,
      resourceStatus: null,
      show: false,
    };
  },
  computed: {
    ...mapGetters({
      user: 'auth/getUser',
    }),
  },
  methods: {
    cyclicReportAdded() {
      this.close();
    },
    initReportGenerate(report) {
      this.loading = true;
      this.resourceId = report.id;
    },
    clearReportGenerator() {
      this.resourceId = null;
      this.errorText = null;
      this.loading = false;
    },
    close() {
      this.show = false;
      this.clearReportGenerator();
    },
    open() {
      this.show = true;
      this.clearReportGenerator();
    },
  },
};
</script>
