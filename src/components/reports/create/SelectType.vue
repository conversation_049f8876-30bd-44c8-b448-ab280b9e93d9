<template>
  <v-radio-group
    :label="$t('common_typeType')"
    row
    :value="value"
    @change="$emit('input', $event)"
  >
    <v-radio
      :label="$t('common_onetime')"
      value="onetime"
    />
    <v-radio
      :label="$t('common_cyclic')"
      value="cyclic"
    />
  </v-radio-group>
</template>

<script>
export default {
  model: {
    prop: 'value',
    event: 'input',
  },
  props: {
    value: {
      type: String,
      default: 'onetime',
    },
  },
};
</script>
