<template>
  <div>
    <v-row>
      <select-type v-model="exportType" />
    </v-row>
    <v-row>
      <select-frequency
        v-if="exportType === 'cyclic'"
        v-model="period"
      />
      <!-- poka<PERSON><PERSON> tylko wtedy gdy interval jest w parametrach-->
      <date-select
        v-else
        v-model="date"
      />
    </v-row>
    <v-row>
      <v-text-field
        v-model="title"
        :label="$t('common_userTitle')"
        autofocus
        prepend-icon="mdi-mail"
        required
      />
    </v-row>
    <v-row>
      <add-emails
        v-model="reportEmail"
      />
    </v-row>
    <v-row>
      <select-ext
        v-model="ext"
      />
    </v-row>
    <div
      class="text-center py-4"
    >
      <v-btn
        color="primary"
        @click="newReport"
      >
        <v-icon>
          mdi-cached
        </v-icon>
        {{ $t('common_exportAsyncTitle') }}
      </v-btn>
    </div>
  </div>
</template>
<script>
import AddEmails from './AddEmails.vue';
import DateSelect from '../filters/DateSelect.vue';
import SelectExt from './SelectExt.vue';
import SelectFrequency from './SelectFrequency.vue';
import SelectType from './SelectType.vue';

export default {
  components: {
    AddEmails, DateSelect, SelectExt, SelectFrequency, SelectType,
  },
  props: {
    report: {
      type: String,
      default: null,
    },
    params: {
      type: Object,
      default: () => ({}),
    },
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      ext: 'xlsx',
      reportEmail: [],
      title: null,
      exportType: 'onetime',
      period: 'daily',
      date: null,
    };
  },
  computed: {
    isCyclic() {
      return this.exportType === 'cyclic';
    },
    internalParam() {
      const paramsInit = {
        ...this.params,
        report: this.report,
        title: this.title ? this.title : null,
        ext: this.ext,
        email: this.reportEmail.length ? this.reportEmail.join(',') : null,
      };

      // konfiguracji dla raportu cyklicznego
      if (this.isCyclic) {
        return {
          ...paramsInit,
          period: this.period,
        };
      }
      // dla generowania raportu jednorazowego
      return {
        ...paramsInit,
        ...this.date,
      };
    },
  },
  watch: {
    params: {
      immediate: true,
      deep: true,
      handler(newVal) {
        this.date = {
          interval: newVal.interval ?? null,
          startDate: newVal.startDate ?? null,
          endDate: newVal.endDate ?? null,
        };
      },
    },
  },
  methods: {
    newReport() {
      if (this.isCyclic) {
        this.addCyclicReport();
      } else {
        this.initReportGenerate();
      }
    },
    addCyclicReport() {
      this.resourceStatus = 'initialized';
      this.loading = true;

      this.axios.post(
        `${this.baseUrl}/report_configs`,
        {},
        {
          params: this.internalParam,
        },
      )
        .then(() => {
          this.snackbar.showMessage(
            'success',
            this.$t('common_success'),
          );
          this.$emit('report-added');
        })
        .catch(() => {
          this.errorText = `${this.$t('common_canotGenerateReport')}`;
        });
    },
    initReportGenerate() {
      this.resourceId = null;
      this.loading = true;

      this.axios.post(
        `${this.baseUrl}/reports`,
        {},
        {
          params: this.internalParam,
        },
      )
        .then((response) => {
          this.resourceId = response.data.id;
          this.$emit('init-report-generate', {
            id: this.resourceId,
            title: this.title,
          });
        })
        .catch(() => {
          this.errorText = `${this.$t('common_canotGenerateReport')}`;
          this.loading = false;
        });
    },
  },

};
</script>
