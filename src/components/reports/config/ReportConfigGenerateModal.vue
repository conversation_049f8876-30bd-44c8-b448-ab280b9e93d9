<template>
  <div>
    <btn-download
      @click="generateReport()"
    />
    <v-dialog
      v-model="loading"
      max-width="800"
    >
      <v-card>
        <v-card-title class="title">
          <slot name="title">
            <span class="headline text-uppercase text-h5">{{ $t('common_exportAsyncTitle') }}</span>
            <v-spacer />
            <btn-close @click="close()" />
          </slot>
        </v-card-title>
        <v-card-text>
          <report-file-waiting
            v-if="resourceId"
            :id="resourceId"
          />
          <v-alert
            v-if="errorText!== null"
            border="left"
            class="mt-5"
            text
            type="error"
          >
            {{ errorText }}
          </v-alert>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import BtnDownload from '@components/common/button/BtnDownload.vue';
import ReportFileWaiting from '@components/reports/file/ReportFileWaiting.vue';
import BtnClose from '@components/common/button/BtnClose.vue';

export default {
  components: {
    BtnClose,
    ReportFileWaiting,
    BtnDownload,
  },
  props: {
    id: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      loading: false,
      errorText: null,
      resourceId: null,
    };
  },
  methods: {
    generateReport() {
      this.resourceId = null;
      this.axios.post(
        `/api/report_config/${this.id}/generate`,
      )
        .then((response) => {
          this.resourceId = response.data.id;
          this.propagateUpdate();
          this.loading = true;
        })
        .catch(() => {
          this.errorText = `${this.$t('common_canotGenerateReport')}`;
          this.loading = false;
        });
    },
    propagateUpdate() {
      this.$emit('cyclic-report-generate');
    },
    close() {
      this.loading = false;
    },
  },
};
</script>
