<template>
  <v-tooltip bottom>
    <template #activator="{ on, attrs }">
      <v-icon
        :color="statusInfo.color"
        v-bind="attrs"
        v-on="on"
      >
        {{ statusInfo.icon }}
      </v-icon>
    </template>
    <span> {{ statusInfo.text }}</span>
  </v-tooltip>
</template>

<script>

export default {
  props: {
    status: {
      type: String,
      required: true,
    },
  },
  computed: {
    statusInfo() {
      const STATUS_MAP = {
        error: {
          icon: 'mdi-progress-alert',
          color: 'error',
          text: 'common_progressError',
        },
        new: {
          icon: 'mdi-progress-star',
          color: 'gray',
          text: 'common_progressNew',
        },
        process: {
          icon: 'mdi-progress-clock',
          color: 'progress',
          text: 'common_process',
        },
        done: {
          icon: 'mdi-check-circle-outline',
          color: 'green darken-2',
          text: 'common_done',
        },
      };

      const status = STATUS_MAP[(this.status).toLowerCase()];
      if (status) {
        return {
          icon: status.icon,
          color: status.color,
          text: this.$t(status.text),
        };
      }

      return {
        icon: 'mdi-help-circle',
        color: 'grey',
        text: this.$t('invoice.status.unknown'),
      };
    },
  },
};
</script>
