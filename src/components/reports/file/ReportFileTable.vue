<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
  >
    <v-row>
      <v-col
        cols="12"
        sm="8"
        class="text-sm-start"
      >
        <h2>{{ $t('finance_header') }}</h2>
      </v-col>
      <v-col
        cols="12"
        sm="4"
        class="d-flex justify-end"
      >
        <btn-refresh
          class="mr-2"
          @click="getData"
        />
      </v-col>
    </v-row>
    <v-row>
      <v-col
        cols="12"
        class="pb-0 px-0"
      >
        <v-data-table
          item-key="id"
          :headers="dataTable.headers"
          :items="items"
          :loading="loader"
          :options="filtering.options"
          :single-expand="true"
          :server-items-length="totalItems"
          :footer-props="footerProps"
          mobile-breakpoint="0"
          @update:options="onOptionsChange"
        >
          <template #item="{ item }">
            <template v-if="!loader">
              <tr>
                <td class="text-sm-start">
                  {{ dateInTimezone(item.ctime) }}
                </td>
                <td class="text-sm-start">
                  {{ dateInTimezone(item.etime) }}
                </td>
                <td class="text-sm-start">
                  <report-file-status :status="item.status" />
                </td>
                <td class="text-sm-start">
                  {{ getReportTitleTranslation(item.title) }}
                </td>
                <td class="text-sm-start">
                  {{ item.user.email }}
                </td>
                <td class="text-sm-start">
                  <act-download
                    :url="`/api/report/${item.id}/download`"
                    :disabled="item.status !== 'DONE'"
                  />
                </td>
              </tr>
            </template>
          </template>
        </v-data-table>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import DataFetchMixin from '@components/common/mixins/DataFetchMixin.vue';
import FiltersHandlingMixin from '@components/common/mixins/FiltersHandlingMixin.vue';
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import { mapGetters } from 'vuex';
import ReportNameTranslation from '@components/common/badge/ReportNameTranslation.vue';
import ActDownload from '@components/common/Action/ActDownload.vue';
import ReportFileStatus from '@components/reports/file/ReportFileStatus.vue';

export default {
  components: {
    ReportFileStatus,
    ActDownload,
    BtnRefresh,
  },
  mixins: [
    DataFetchMixin,
    FilterMixin,
    FiltersHandlingMixin,
    ReportNameTranslation,
  ],
  data() {
    return {
      dataUrl: '/api/reports',
      filtering: {
        type: null,
        options: {
          page: 1,
          itemsPerPage: 25,
        },
        status: null,
      },
      footerProps: {
        'items-per-page-options': [10, 25, 50],
        'items-per-page-text': this.$t('common_rowsPerPage'),
      },
      dataTable: {
        headers: [
          {
            text: this.$t('finance_createTime'),
            value: 'ctime',
            sortable: false,
          },
          {
            text: this.$t('finance_endTime'),
            value: 'etime',
            sortable: false,
          },
          {
            text: this.$t('finance_status'),
            value: 'status',
            sortable: false,
          },
          {
            text: this.$t('finance_reportName'),
            value: 'title',
            sortable: false,
          },
          {
            text: this.$t('finance_user'),
            value: 'user',
            sortable: false,
          },
          {
            text: '',
            value: 'status',
            align: 'center',
            sortable: false,
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters({
      timezone: 'auth/userTimezone',
      getCarwashBySerial: 'carwashes/getCarwashBySerial',
    }),
  },
  mounted() {
    this.getData();
  },
  methods: {
    onOptionsChange(options) {
      this.filtering.options = options;
      this.getData();
    },
    parseApiResponseData(data) {
      this.items = data.map((dataColumns) => (
        {
          ...dataColumns,
        }
      ));
    },
    dateInTimezone(utcDateString) {
      if (utcDateString === null) {
        return '-';
      }

      const utcDate = new Date(`${utcDateString}`);

      return utcDate.toLocaleString('pl', { timeZone: this.timezone });
    },
    getParams() {
      return {
        params: {
          page: this.filtering.options.page,
          perPage: this.filtering.options.itemsPerPage,
          type: this.filtering.type,
          // status: this.filtering.status,
        },
      };
    },
  },
};
</script>

<style scoped>
  .subject {
    max-width: 30%;
  }
</style>
