<template>
  <div>
    <div
      v-if="loading"
      class="text-center py-4"
    >
      <v-progress-circular
        class="circleProgress md-2"
        :size="90"
        :width="7"
        color="primary"
        indeterminate
      />
      <h3 class="mt-4">
        {{ $t('common_generating') }}
      </h3>
      <h4 class="mt-4">
        {{ $t('common_reportDownloadOnList') }}:
        <v-btn
          text
          color="primary"
          @click="$router.push('/finance/reports')"
        >
          {{ $t('common_reports') }}
        </v-btn>
      </h4>
    </div>
    <div
      v-else
      class="mt-7"
    >
      <template
        v-if="errorText !== null"
      >
        <v-alert
          border="left"
          class="mt-5"
          text
          type="error"
        >
          {{ errorText }}
        </v-alert>
      </template>
      <div
        v-else
        class="text-center py-4"
      >
        <h3 class="py-4">
          {{ $t('common_reportReady') }}
        </h3>
        <act-download
          :url="`/api/report/${id}/download`"
          :type="'button'"
        />
      </div>
    </div>
  </div>
</template>
<script>
import ActDownload from '@components/common/Action/ActDownload.vue';

export default {
  components: { ActDownload },
  props: {
    id: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      errorText: null,
      resourceStatus: null,
      intervalId: null,
      show: false,
    };
  },
  computed: {
  },
  mounted() {
    this.loading = true;
    this.checkResourceStatus(this.id);
  },
  beforeDestroy() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  },
  methods: {
    checkResourceStatus(resourceId) {
      this.intervalId = setInterval(() => {
        this.axios.get(`/api/report/${resourceId}`)
          .then((response) => {
            this.resourceStatus = response.data.status;
            if (response.status === 200) {
              clearInterval(this.intervalId);
              this.loading = false;
            }
          })
          .catch(() => {
            this.errorText = `${this.$t('common_canotGenerateReport')}`;
            this.loading = false;
            clearInterval(this.intervalId);
            // this.close();
          });
      }, 1000); // Odpytywanie co 1 sekunde
    },
  },

};
</script>
