<template>
  <v-dialog
    ref="dialog"
    v-model="dateRangeModal"
    width="290px"
  >
    <template #activator="{ on, attrs }">
      <v-text-field
        v-model="dateRangeFormatted"
        :label="$t('common_inPeriodCustom')"
        readonly
        v-bind="attrs"
        v-on="on"
      />
    </template>

    <v-date-picker
      v-model="dateRange"
      range
      scrollable
      class="dateRange"
      prepend-icon="mdi-calendar-range"
      :selected-items-text="$t('common_selected')"
      @change="swapDates"
    >
      <v-spacer />
      <v-btn
        text
        @click="cancel"
      >
        {{ $t('actions.cancel') }}
      </v-btn>
      <v-btn
        text
        color="primary"
        @click="confirm"
      >
        {{ $t('actions.chose') }}
      </v-btn>
    </v-date-picker>
  </v-dialog>
</template>

<script>

export default {
  model: {
    prop: 'param',
    event: 'update:param',
  },
  props: {
    param: {
      type: Array,
      default: () => [null, null],
    },
  },
  data() {
    return {
      dateRange: [null, null],
      dateRangeModal: false,
    };
  },
  computed: {
    dateRangeFormatted() {
      if (!this.dateRange[0] || !this.dateRange[1]) return '';
      return `${this.formatDate(this.dateRange[0])} - ${this.formatDate(this.dateRange[1])}`;
    },
  },
  watch: {
    param: {
      immediate: true,
      handler(val) {
        if (Array.isArray(val) && val.length === 2) {
          const [start, end] = val;
          this.dateRange = [start, end];
        }
      },
    },
  },
  methods: {
    formatDate(date) {
      if (!date) return '';
      const d = new Date(date);
      return d.toLocaleDateString(); // lub dopasuj format wg potrzeb
    },
    swapDates() {
      const [start, end] = this.dateRange;
      if (start && end && start > end) {
        this.dateRange = [end, start];
      }
    },
    confirm() {
      if (!this.dateRange[0] || !this.dateRange[1]) {
        this.cancel();
        return;
      }
      this.$emit('update:param', [...this.dateRange]);
      this.closeDateModal();
    },
    cancel() {
      this.dateRange = [...this.param];
      this.closeDateModal();
    },
    closeDateModal() {
      this.dateRangeModal = false;
    },
  },
};
</script>
