<template>
  <multi-select
    v-model="localModelValue"
    :items="carwashesOptions"
    :label="$t('common_filtersCarwash')"
    prepend-icon="mdi-car-wash"
    allow-null
  />
</template>

<script>
import { mapGetters } from 'vuex';
import MultiSelect from '@components/reports/filters/MultiSelect.vue';

export default {
  components: {
    MultiSelect,
  },
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
  props: {
    modelValue: {
      type: Array,
      default: null,
    },
  },
  computed: {
    ...mapGetters({
      carwashes: 'carwashes/carwashes',
    }),
    carwashesOptions() {
      return this.carwashes.map((item) => ({
        text: item.longName,
        value: item.serialNumber,
      }));
    },
    localModelValue: {
      get() {
        return this.modelValue;
      },
      set(newValue) {
        this.$emit('update:modelValue', newValue);
      },
    },
  },
};
</script>
