<template>
  <v-text-field
    :value="value"
    :label="$t('common_search')"
    prepend-icon="mdi-magnify"
    :disabled="disabled"
    clearable
    @input="onChange"
  />
</template>

<script>

export default {
  model: {
    prop: 'value',
    event: 'update:value',
  },
  props: {
    value: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    onChange(searchText) {
      this.$emit('update:value', searchText?.trim());
    },
  },
};
</script>
