<template>
  <div>
    <v-select
      v-model="interval"
      color="text"
      class="d-flex"
      prepend-icon="mdi-calendar"
      :items="availablePresets"
      :label="label"
      :disabled="disabled"
      :menu-props="{ maxHeight: 500 }"
    />
    <v-scroll-y-transition v-if="showDatePicker">
      <date-range
        v-model="dateRange"
      />
    </v-scroll-y-transition>
  </div>
</template>

<script>
import {
  endOfMonth,
  endOfToday,
  endOfWeek,
  endOfYear,
  endOfYesterday,
  startOfDay,
  startOfMonth,
  startOfToday,
  startOfWeek,
  startOfYear,
  startOfYesterday,
  subMonths,
  subWeeks,
  subYears,
} from 'date-fns';

import DateRange from './DateRange.vue';

export default {
  components: { DateRange },

  model: {
    prop: 'param',
    event: 'update:param',
  },

  props: {
    label: {
      type: String,
      default() {
        return this.$t('common_inPeriod');
      },
    },
    param: {
      type: Object,
      default: () => ({}),
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    showPresets: {
      type: Boolean,
      default: true,
    },
    showLastCollection: {
      type: Boolean,
      default: false,
    },
    showCustom: {
      type: Boolean,
      default: true,
    },
  },

  data() {
    return {
      interval: null,
      dateRange: [null, null],
    };
  },

  computed: {
    internalParam() {
      const params = {
        interval: this.interval,
      };

      if (this.interval === 'custom') {
        const [startDate, endDate] = this.dateRange;
        params.startDate = startDate;
        params.endDate = endDate;
      }

      return params;
    },

    presets() {
      return [
        {
          value: 'today',
          text: this.$t('common_today'),
          start: startOfToday(),
          end: endOfToday(),
        },
        {
          value: 'yesterday',
          text: this.$t('common_daterangeYesterday'),
          start: startOfYesterday(),
          end: endOfYesterday(),
        },
        {
          value: 'thisWeek',
          text: this.$t('thisWeek'),
          start: startOfWeek(startOfDay(new Date())),
          end: endOfToday(),
          default: true,
        },
        {
          value: 'lastWeek',
          text: this.$t('lastWeek'),
          start: startOfWeek(subWeeks(new Date(), 1)),
          end: endOfWeek(subWeeks(new Date(), 1)),
          default: true,
        },
        {
          value: 'thisMonth',
          text: this.$t('common_sinceMonthStart'),
          start: startOfMonth(new Date()),
          end: endOfToday(),
        },
        {
          value: 'lastMonth',
          text: this.$t('common_previousMonth'),
          start: startOfMonth(subMonths(new Date(), 1)),
          end: endOfMonth(subMonths(new Date(), 1)),
        },
        {
          value: 'thisYear',
          text: this.$t('common_currentYear'),
          start: startOfYear(new Date()),
          end: endOfToday(),
        },
        {
          value: 'lastYear',
          text: this.$t('common_previousYear'),
          start: startOfYear(subYears(new Date(), 1)),
          end: endOfYear(subYears(new Date(), 1)),
        },
      ];
    },

    showDatePicker() {
      return this.interval === 'custom';
    },

    availablePresets() {
      const presets = [...this.presets];

      if (this.showLastCollection) {
        presets.unshift({
          value: 'lastCollection',
          text: this.$t('common_last'),
        });
      }

      if (this.showCustom) {
        presets.push({
          value: 'custom',
          text: this.$t('common_custom'),
        });
      }

      return presets;
    },
  },

  watch: {
    internalParam: {
      handler(val) {
        this.$emit('update:param', val);
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    this.interval = this.param?.interval || null;

    if (this.interval === 'custom') {
      const { startDate = null, endDate = null } = this.param;
      this.dateRange = [startDate, endDate];
    }
  },
};
</script>
