<template>
  <v-switch
    v-model="internalValue"
    :disabled="disabled"
  >
    <template #label>
      <v-icon
        v-if="prependIcon"
      >
        {{ prependIcon }}
      </v-icon>
      <span class="mx-2">
        {{ label }}

      </span>
      <v-icon
        v-if="appendIcon"
      >
        {{ appendIcon }}
      </v-icon>
    </template>
  </v-switch>
</template>

<script>
export default {
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false,
    },
    label: {
      type: String,
      default: '',
    },
    appendIcon: {
      type: String,
      default: null,
    },
    prependIcon: {
      type: String,
      default: null,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    internalValue: {
      get() {
        return this.modelValue;
      },
      set(value) {
        this.$emit('update:modelValue', value);
      },
    },
  },
};
</script>
