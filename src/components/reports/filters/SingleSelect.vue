<template>
  <v-select
    :items="computedItems"
    :label="label"
    :value="internalValue"
    :prepend-icon="prependIcon"
    :multiple="false"
    :clearable="allowNull"
    item-value="value"
    item-text="text"
    @change="onChange"
  >
    <template #item="{ item, attrs, on }">
      <v-list-item
        v-bind="attrs"
        v-on="on"
      >
        <v-list-item-content>
          <v-list-item-title class="d-flex align-center">
            <span>{{ item.text }}</span>
            <v-spacer />
            <v-icon
              v-if="item.icon"
              small
            >
              {{ item.icon }}
            </v-icon>
          </v-list-item-title>
        </v-list-item-content>
      </v-list-item>
    </template>
  </v-select>
</template>

<script>
export default {
  model: {
    prop: 'modelValue',
    event: 'update:modelValue',
  },
  props: {
    modelValue: {
      type: null, // prz<PERSON>j<PERSON><PERSON> typ
      default: null,
    },
    items: {
      type: Array,
      required: true, // np. [{ text, value, icon }]
    },
    label: {
      type: String,
      default: '',
    },
    prependIcon: {
      type: String,
      default: null,
    },
    allowNull: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    internalValue() {
      return this.modelValue;
    },
    computedItems() {
      return this.items;
    },
  },
  methods: {
    onChange(value) {
      this.$emit('update:modelValue', value);
    },
  },
};
</script>
