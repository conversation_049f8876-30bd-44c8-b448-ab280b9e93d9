<template>
  <div>
    <v-btn
      color="primary"
      class="ml-2 white--text"
      :disabled="disabled"
      :loading="loading"
      @click="showModal"
    >
      {{ $t('common_export') }}
    </v-btn>
    <v-dialog
      v-model="show"
      max-width="800"
    >
      <v-card>
        <v-card-title class="title">
          <slot name="title">
            <span class="headline text-uppercase text-h5">{{ $t('common_exportAsyncTitle') }}</span>
            <v-spacer />
            <v-btn
              icon
              text
              tile
              small
              dark
              @click.native="close"
            >
              <v-icon>
                mdi-close
              </v-icon>
            </v-btn>
          </slot>
        </v-card-title>
        <v-card-text>
          <div
            v-if="!loading"
            class="mt-7"
          >
            <template
              v-if="errorText !== null"
            >
              <v-alert
                border="left"
                class="mt-5"
                text
                type="error"
              >
                {{ errorText }}
              </v-alert>
            </template>
            <template
              v-if="!showDownload"
            >
              <div>
                <v-radio-group
                  v-model="selectedExportType"
                  :label="$t('common_typeType')"
                  row
                >
                  <v-radio
                    :label="$t('common_onetime')"
                    value="onetime"
                  />
                  <v-radio
                    :label="$t('common_cyclic')"
                    value="cyclic"
                  />
                </v-radio-group>
                <v-radio-group
                  v-if="selectedExportType === 'cyclic'"
                  v-model="selectedExportPeriod"
                  :label="$t('common_frequency')"
                  row
                >
                  <v-radio
                    :label="$t('common_frequency_daily')"
                    value="daily"
                  />
                  <v-radio
                    :label="$t('common_frequency_weekly')"
                    value="weekly"
                  />
                  <v-radio
                    :label="$t('common_frequency_monthly')"
                    value="monthly"
                  />
                </v-radio-group>
                <date-range-picker
                  v-if="showDates && selectedExportType !== 'cyclic'"
                  :key="`asyncReportDateRange`"
                  :ref="`dateRangeCardReport`"
                  :show-presets="true"
                  :start-preset="preset"
                  prepend-icon="mdi-calendar-range"
                  @reload-transaction-list="onDateRangeChange"
                />
                <v-text-field
                  v-model="title"
                  :label="$t('common_userTitle')"
                  autofocus
                  prepend-icon="mdi-mail"
                  required
                />
                <v-combobox
                  v-model="reportEmail.model"
                  :items="reportEmail.items"
                  :search-input.sync="reportEmail.search"
                  prepend-icon="mdi-email-multiple"
                  hide-selected
                  :data-vv-as="$t('common_email')"
                  :label="$t('common_email')"
                  :error-messages="errors.collect('tableEmail')"
                  :rules="rules.comboboxEmail"
                  multiple
                  small-chips
                  deletable-chips
                >
                  <template #no-data>
                    <v-list-item>
                      <v-list-item-content>
                        <v-list-item-title>
                          {{ $t('common_pressEnterToAddNew') }}
                        </v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                  </template>
                </v-combobox>
                <v-radio-group
                  v-model="selectedExportItem"
                  :label="$t('actions.chose_report_to_generate')"
                  row
                >
                  <v-radio
                    v-for="(item, index) in itemsAsync"
                    :key="index"
                    :label="item.title"
                    :value="item.value"
                  />
                </v-radio-group>
              </div>
              <div
                class="text-center py-4"
              >
                <v-btn
                  color="primary"
                  @click="generateReport"
                >
                  <v-icon>
                    mdi-cached
                  </v-icon>
                  {{ $t('common_exportAsyncTitle') }}
                </v-btn>
              </div>
            </template>
            <div
              v-else
              class="text-center py-4"
            >
              <h3 class="py-4">
                {{ $t('common_reportReady') }}
              </h3>
              <v-btn
                class="mx-2"
                @click.native="clearReportGenerator"
              >
                {{ $t('common_clearReport') }}
              </v-btn>
              <act-download
                :url="`/api/report/${resourceId}/download`"
                :type="'button'"
              />
            </div>
          </div>
          <div
            v-else
            class="text-center py-4"
          >
            <report-file-waiting
              v-if="resourceId"
              :id="resourceId"
            />
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import DateRangePicker from '@components/common/DateRangePicker.vue';
import { format } from 'date-fns';
import ActDownload from '@components/common/Action/ActDownload.vue';
import ReportFileWaiting from '@components/reports/file/ReportFileWaiting.vue';

export default {
  components: {
    ReportFileWaiting,
    ActDownload,
    DateRangePicker,
  },
  props: {
    color: {
      type: String,
      default: 'secondary',
    },
    tailButton: {
      type: Boolean,
      default: false,
    },
    showDates: {
      type: Boolean,
      default: true,
    },
    preset: {
      type: String,
      default: '',
    },
    params: {
      type: Object,
      required: true,
    },
    types: {
      type: Array,
      required: false,
      default: () => (
        [
          {
            value: 'export_xlsx',
            title: 'actions.export_xlsx',
            filetype: 'xlsx',
            icon: 'mdi-file-excel',
            filename: 'test.xlsx',
            params: {
              ext: 'xlsx',
            },
          },
          {
            value: 'export_pdf',
            title: 'actions.export_pdf',
            filetype: 'pdf',
            icon: 'mdi-file',
            filename: 'test.pdf',
            params: {
              ext: 'pdf',
            },
          },
          {
            value: 'export_csv',
            title: 'actions.export_csv',
            filetype: 'pdf',
            icon: 'mdi-file',
            filename: 'test.csv',
            params: {
              ext: 'csv',
            },
          },
        ]
      ),
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    btnClass: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      from: '',
      to: '',
      reportEmail: {
        model: [],
        items: [],
        search: null,
      },
      rules: {
        comboboxEmail: [(v) => /(((^(([^<>()[\]\\.,;:\s@']+(\.[^<>()\\[\]\\.,;:\s@']+)*)|('.+'))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,})))(,{1}((([^<>()[\]\\.,;:\s@']+(\.[^<>()\\[\]\\.,;:\s@']+)*)|('.+'))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))))*)|(^$))$/.test(v) || 'E-mail must be valid'],
      },
      title: null,
      selectedExportType: 'onetime',
      selectedExportPeriod: 'daily',
      selectedExportItem: null,
      loading: false,
      errorText: null,
      resourceId: null,
      resourceStatus: null,
      intervalId: null,
      fileType: null,
      fileName: null,
      show: false,
    };
  },
  computed: {
    ...mapGetters({
      user: 'auth/getUser',
    }),
    itemsAsync() {
      return this.types.map(
        (type) => ({
          ...type,
          title: this.$t(type.title),
          params: {
            ...this.params,
            ...type.params,
          },
        }),
      );
    },
    showDownload() {
      return this.resourceStatus === 'DONE';
    },
  },
  mounted() {
    this.selectedExportItem = this.itemsAsync[0].value;
    this.reportEmail.model = [
      this.user.email,
    ];
  },
  beforeDestroy() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  },
  methods: {
    onDateRangeChange({
      from,
      to,
    }) {
      this.from = from;
      this.to = to;
    },
    generateReport() {
      const reportEntity = this.itemsAsync.find((item) => item.value === this.selectedExportItem);

      if (this.selectedExportType === 'onetime') {
        this.generateAsyncResource(reportEntity);
      } else if (this.selectedExportType === 'cyclic') {
        this.generateCyclicReport(reportEntity);
      }
    },
    generateCyclicReport(exportItem) {
      this.resourceStatus = 'initialized';
      this.loading = true;

      let reportEmail = this.reportEmail.model.join(',');
      if (reportEmail === '') {
        reportEmail = null;
      }

      const params = {
        email: reportEmail,
        title: this.title !== '' ? this.title : null,
        ext: exportItem.params.ext,
        report: exportItem.params.report,
        period: this.selectedExportPeriod,
      };

      this.axios.post(
        '/api/report_configs',
        {},
        {
          params: {
            ...exportItem.params,
            ...params,
          },
        },
      )
        .then(() => {
          this.snackbar.showMessage(
            'success',
            this.$t('common_success'),
          );

          this.loading = false;
          this.close();
        })
        .catch(() => {
          this.errorText = `${this.$t('common_canotGenerateReport')}`;

          this.resourceStatus = 'Błąd podczas generowania zasobu.';
          this.loading = false;
        });
    },
    generateAsyncResource(exportItem) {
      this.resourceStatus = 'initialized';
      this.resourceId = null;
      this.loading = true;

      let reportEmail = this.reportEmail.model.join(',');
      if (reportEmail === '') {
        reportEmail = null;
      }

      let dateParams = {};
      if (this.showDates) {
        dateParams = {
          startDate: format(this.from, 'YYYY-MM-DD'),
          endDate: format(this.to, 'YYYY-MM-DD'),
        };
      }

      this.axios.post(
        '/api/reports',
        {},
        {
          params: {
            email: reportEmail,
            title: this.title !== '' ? this.title : null,
            ...exportItem.params,
            ...dateParams,
          },
        },
      )
        .then((response) => {
          this.resourceId = response.data.id;
        })
        .catch(() => {
          this.errorText = `${this.$t('common_canotGenerateReport')}`;

          this.resourceStatus = 'Błąd podczas generowania zasobu.';
          this.loading = false;
        });
    },
    showModal() {
      this.show = true;
    },
    clearReportGenerator() {
      clearInterval(this.intervalId);
      this.selectedExportItem = this.itemsAsync[0].value;
      this.resourceId = null;
      this.resourceStatus = null;
      this.intervalId = null;
      this.errorText = null;
      this.loading = false;
    },
    close() {
      this.show = false;
      this.clearReportGenerator();
    },
  },
};
</script>
