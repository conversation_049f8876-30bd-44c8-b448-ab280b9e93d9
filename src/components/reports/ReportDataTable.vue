<template>
  <div>
    <v-row align="center">
      <!-- 1) Pierwsza kolumna: tytuł, przyklejona do lewej -->
      <v-col cols="auto">
        <h2 v-if="title">
          {{ title }}
        </h2>
      </v-col>

      <!-- 2) Spacer wypychający kolejne dwie kolumny na prawą stronę -->
      <v-spacer />

      <!-- 4) Trzecia kolumna: akcje, przyklejone do prawej -->
      <v-col
        cols="auto"
        class="d-flex justify-end align-center"
      >
        <slot name="table-actions" />
        <report-create2-modal
          :params="filters"
          :report="report"
          :base-url="baseUrl"
        />
        <btn-refresh
          class="ml-2"
          @click="fetchData"
        />
      </v-col>
    </v-row>

    <v-data-table
      mobile-breakpoint="0"
      :headers="headers"
      :items="items"
      :loading="loading"
      :options.sync="options"
      :server-items-length="totalItems"
      :items-per-page="options.itemsPerPage"
      :hide-default-footer="loading"
      :footer-props="footerProps"
      :single-expand="singleExpand"
    >
      <template #progress>
        <div class="text-center mx-n4">
          <v-progress-linear
            class="loader"
            indeterminate
            color="primary"
          />
        </div>
      </template>
      <template #item="slotProps">
        <tr @click="slotProps.expand(!slotProps.isExpanded)">
          <td
            v-for="header in headers"
            :key="header.value"
            :class="getAlignClass(header.align)"
          >
            <slot
              :name="`item.${header.value}`"
              v-bind="slotProps"
            >
              <blurred-formatter
                v-if="header.protected"
                :value="slotProps.item[header.value]"
              />
              <template v-else>
                {{ slotProps.item[header.value] ?? '-' }}
              </template>
            </slot>
          </td>
        </tr>
      </template>
      <template #expanded-item="props">
        <slot
          name="expanded-item"
          v-bind="props"
        />
      </template>
      <template #[`body.append`]>
        <slot
          v-if="!loading"
          name="footer"
          :total-sum="totalSum"
          :page-sum="pageSum"
        />
      </template>
    </v-data-table>
  </div>
</template>

<script>
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import ReportCreate2Modal from '@components/reports/create/ReportCreate2Modal.vue';
import BlurredFormatter from '@components/common/formatters/BlurredFormatter.vue';

export default {
  components: { BlurredFormatter, ReportCreate2Modal, BtnRefresh },
  props: {
    report: {
      type: String,
      default: null,
    },
    title: {
      type: String,
      required: false,
      default: null,
    },
    url: {
      type: String,
      default: null,
    },
    baseUrl: {
      type: String,
      required: false,
      default: '/api',
    },
    headers: {
      type: Array,
      default: () => [], // jeśli puste, generujemy automatycznie
    },
    filters: {
      type: Object,
      default: () => {}, // jeśli puste, generujemy automatycznie
    },
    singleExpand: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      items: [],
      totalItems: 0,
      totalSum: null,
      pageSum: null,
      loading: false,
      initialized: false,
      lastRequestId: 0,
      footerProps: {
        'items-per-page-options': [25, 50, 100],
        'items-per-page-text': this.$t('common_rowsPerPage'),
      },
      options: {
        page: 1,
        itemsPerPage: 25,
      },
    };
  },
  computed: {
    params() {
      return {
        page: this.options.page,
        perPage: this.options.itemsPerPage,
        report: this.report,
        orderBy: this.options.sortBy.length ? this.options.sortBy[0] : null,
        orderDescending: this.options.sortDesc.length ? this.options.sortDesc[0] : null,
        ...this.filters,
      };
    },
  },
  watch: {
    filters: {
      async handler() {
        this.options.page = 1;
        await this.fetchData();

        this.initialized = true;
      },
      deep: true,
    },
    options: {
      handler() {
        if (!this.initialized) return;

        this.fetchData();
      },
      deep: true,
    },
  },
  methods: {
    getAlignClass(align) {
      switch (align) {
        case 'center':
          return 'text-center';
        case 'right':
          return 'text-right';
        case 'left':
        default:
          return 'text-left';
      }
    },
    async fetchData() {
      this.lastRequestId += 1;
      const requestId = this.lastRequestId; // zapamiętaj ID zapytania
      this.loading = true;
      this.items = [];
      try {
        const response = await this.axios.get(this.url ?? `${this.baseUrl}/reports/data`, { params: this.params });
        // ❗ Sprawdź czy to nadal aktualne zapytanie
        if (requestId !== this.lastRequestId) return;
        const {
          data, total, totalSum, pageSum,
        } = response.data;
        this.items = data;
        this.totalItems = total;
        this.totalSum = totalSum;
        this.pageSum = pageSum;

        this.$emit('data-fetched', response.data);
      } catch (e) {
        this.snackbar.showMessage('error', this.$t('common_error_occurred'));
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>
