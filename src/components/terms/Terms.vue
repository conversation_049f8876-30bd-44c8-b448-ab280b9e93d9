<template>
  <v-app>
    <v-container fill-height>
      <v-row
        align-content="center"
        justify="center"
      >
        <v-col>
          <v-row
            align-content="center"
            justify="center"
          >
            <b>{{ $t('common_termsHeading') }}</b>
          </v-row>

          <v-row
            class="pt-2"
            align-content="center"
            justify="center"
          >
            <v-col cols="12">
              <v-alert
                v-if="loading"
                type="info"
                dense
              >
                {{ $t('common_loading') }}
              </v-alert>

              <v-alert
                v-if="error"
                type="error"
                dense
              >
                {{ $t('common_errorLoading') }}: {{ error }}
              </v-alert>

              <v-card
                v-if="!loading && termsContent"
                flat
              >
                <v-card flat>
                  <v-card-text style="max-height: 70vh; overflow-y: auto;">
                    <div v-html="termsContent" />
                  </v-card-text>
                </v-card>
              </v-card>
            </v-col>
          </v-row>

          <v-row
            class="pt-6"
            align-content="start"
            justify="center"
          >
            <v-btn
              color="primary"
              class="mr-2"
              @click="acceptPolicy()"
            >
              {{ $t('common_accept') }}
            </v-btn>
            <v-btn
              color="error"
              @click="logoutClick()"
            >
              {{ $t('common_cancel') }}
            </v-btn>
          </v-row>
        </v-col>
      </v-row>
    </v-container>
  </v-app>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';

export default {
  name: 'TermsComponent',
  data() {
    return {
      termsContent: '',
      loading: false,
      error: null,
    };
  },
  computed: {
    ...mapGetters({
      isLogged: 'auth/isLoggedIn',
    }),
  },
  mounted() {
    this.fetchTerms();
  },
  methods: {
    async logoutClick() {
      await this.logout();
      this.$router.push({ name: 'login' });
      this.$router.go(0);
    },
    async fetchTerms() {
      this.loading = true;
      this.error = null;
      try {
        const response = await this.axios.get('/api/profile/policy/terms_of_use');
        this.termsContent = response.data;
      } catch (e) {
        this.error = e.message || 'Unknown error';
      } finally {
        this.loading = false;
      }
    },
    ...mapActions({
      logout: 'auth/logout',
      acceptPolicy: 'auth/acceptPolicy',
    }),
  },

};
</script>
