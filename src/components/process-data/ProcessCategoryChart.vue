<template>
  <div class="text-center">
    <v-col
      v-for="(element, index) in option"
      :key="index"
    >
      <v-chart
        v-if="!loading"
        :ref="`chart${index}`"
        class="chart"
        :option="element"
      />
    </v-col>
    <v-progress-circular
      v-if="loading"
      class="circleProgress mb-10 mt-10"
      :size="70"
      :width="2"
      color="primary"
      indeterminate
    />
  </div>
</template>

<script>
import { use } from 'echarts/core';
import {
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  MarkLineComponent,
  MarkPointComponent,
  DataZoomComponent,
} from 'echarts/components';
import { LineChart } from 'echarts/charts';
import { UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';
import VChart from 'vue-echarts';

use([
  TitleComponent,
  ToolboxComponent,
  Toolt<PERSON>Component,
  GridComponent,
  LegendComponent,
  <PERSON><PERSON><PERSON><PERSON>omponent,
  Mark<PERSON>ointComponent,
  <PERSON><PERSON>hart,
  CanvasRenderer,
  UniversalTransition,
  DataZoomComponent,
]);

export default {
  name: 'ProcessCategoryChart',
  components: {
    VChart,
  },
  props: {
    loading: {
      type: Boolean,
      default: true,
    },
    items: {
      type: Array,
      default() {
        return [{
          items: [],
        }];
      },
    },
  },
  data() {
    return {
      chartData: [],
      option: [],
    };
  },
  watch: {
    items(data) {
      this.option = [];
      data.forEach((chart, i) => {
        this.chartData = [];
        let index = 0;
        let unit = null;
        chart.forEach((el) => {
          unit = el.unit;
          if ((el.items.data).length > 0) {
            const dataArr = [];
            el.items.data.forEach((d) => {
              dataArr.push([new Date(d.ct), d.value]);
            });
            this.chartData[index] = {
              name: `${el.name} ${el.unit}`,
              type: 'line',
              symbol: 'none',
              zlevel: index,
              markPoint: {
                data: [
                  { type: 'max', name: 'Max' },
                  { type: 'min', name: 'Min' },
                ],
              },
              markLine: {
                data: [{ type: 'average', name: 'Avg' }],
              },
              data: dataArr,
            };
            index += 1;
          }
        });
        this.option[i] = {
          title: {
            text: '',
          },
          tooltip: {
            trigger: 'axis',
          },
          legend: {},
          toolbox: {
            show: true,
            feature: {
              dataZoom: {
                yAxisIndex: 'none',
              },
            },
          },
          xAxis: {
            type: 'time',
            axisLabel: {
              formatter: '{yyyy}-{MM}-{dd} {HH}:{mm}',
            },
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: `{value} ${unit}`,
            },
          },
          dataZoom: [
            {
              type: 'inside',
              start: 70,
              end: 100,
            },
            {
              start: 0,
              end: 100,
            },
          ],
          series: this.chartData,
        };
      });
    },
  },
};
</script>
<style scoped>
  .chart {
    height: 40vh;
  }
</style>
