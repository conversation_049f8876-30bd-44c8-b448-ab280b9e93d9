<template>
  <div id="processTable">
    <div
      v-if="loader"
      class="d-flex justify-center align-center"
    >
      <v-progress-circular
        class="loader"
        indeterminate
        color="primary"
      />
    </div>
    <v-slide-y-reverse-transition
      hide-on-leave
    >
      <v-simple-table v-if="!loader">
        <thead>
          <tr>
            <th>{{ $t('processData_parameter') }}</th>
            <th class="text-right">
              {{ $t('processData_value') }}
            </th>
            <th
              v-if="itemsComputed[0].definition.chart"
              class="text-right"
            >
              {{ $t('processData_chart') }}
            </th>
            <th
              v-else
              class="hide"
            />
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(item, key) in itemsComputed"
            :key="key"
          >
            <td>
              <span>
                {{ item.definition.paramDescription || item.definition.paramName }}
              </span>
            </td>
            <td>
              <div
                class="text-right"
              >
                <span>{{ item.ai ||item.value }}</span>
                <span v-if="item.definition.unit">{{ item.definition.unit }}</span>
                <v-tooltip
                  right
                >
                  <template #activator="{ on, attrs }">
                    <span
                      v-bind="attrs"
                      v-on="on"
                    >
                      <v-icon
                        v-bind="item.tooltip.icon"
                        small
                        class="ml-1"
                      >
                        {{ item.tooltip.icon.text }}
                      </v-icon>
                    </span>
                  </template>
                  <div class="text-center">
                    <div
                      v-for="(content, tkey) in item.tooltip.content"
                      :key="`tooltip${key}${tkey}`"
                    >
                      <div
                        v-bind="content"
                        v-text="content.text"
                      />
                    </div>
                  </div>
                </v-tooltip>
              </div>
            </td>
            <td
              v-if="item.definition.chart"
            >
              <div style="display: flex; justify-content: flex-end">
                <v-checkbox
                  v-if="item.definition.chart"
                  dense
                  :input-value="isChecked(item)"
                  absolute
                  @change="setParamToChart(item)"
                />
              </div>
            </td>
            <td v-else />
          </tr>
        </tbody>
      </v-simple-table>
    </v-slide-y-reverse-transition>
  </div>
</template>

<script>
import moment from 'moment';
import 'moment-timezone';

export default {
  name: 'ProcessCategoryTable',
  props: {
    loader: {
      type: Boolean,
      default: true,
    },
    items: {
      type: Array,
      default() {
        return [];
      },
    },
    parametersToChart: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  computed: {
    itemsComputed() {
      return this.items.map((item) => ({
        ...item,
        tooltip: this.getParamTooltipData(item),
      }));
    },
  },
  methods: {
    getAgeInMinutes(updateTime) {
      const update = new Date(updateTime);
      const now = moment.tz(moment(), 'Europe/Warsaw');
      const diff = now - update;
      return Math.floor((diff / 1000) / 60);
    },
    isChecked(item) {
      const searchIndex = this.parametersToChart.findIndex((parameter) => parameter.id
        === item.definition.paramId);
      if (searchIndex === -1) {
        return false;
      }
      return true;
    },
    getParamTooltipData(item) {
      const age = this.getAgeInMinutes(item.ct);
      const lastUpdateText = `${this.$t('common_updateTime')}: ${this.$options.filters.formatDateDayTimeWithSeconds(item.ct)}`;
      if (age >= 60) {
        return {
          icon: {
            text: 'mdi-alert-circle-outline',
            color: 'error',
          },
          content: [
            {
              text: lastUpdateText,
            },
            {
              text: this.$t('common_itemOld'),
              class: 'error white--text',
            },
          ],
        };
      }
      if (age >= 15) {
        return {
          icon: {
            text: 'mdi-alert-outline',
            color: 'warning',
          },
          content: [
            {
              text: lastUpdateText,
            },
            {
              text: this.$t('common_itemOld'),
              class: 'warning white--text',
            },
          ],
        };
      }
      return {
        icon: {
          text: 'mdi-information-outline',
          color: 'grey lighten-1',
        },
        content: [
          {
            text: lastUpdateText,
          },
        ],
      };
    },
    setParamToChart(item) {
      const param = {
        id: item.definition.paramId,
        name: item.definition.paramDescription,
        unit: item.definition.unit,
      };
      this.$emit('addParameterToChart', param);
    },
  },
};
</script>
