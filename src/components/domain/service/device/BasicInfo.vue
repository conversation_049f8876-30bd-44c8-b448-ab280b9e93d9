<template>
  <div
    class="issue-details-view"
    style="z-index: 1200"
  >
    <v-card>
      <v-card-title class="title">
        <span class="headline">
          <h5 class="text-uppercase">
            Informacja o urządzeniu
          </h5>
        </span>
        <v-spacer />
      </v-card-title>
      <div
        v-if="loading"
        class="d-flex justify-center align-center"
        style="height: 100px;"
      >
        <v-progress-circular
          indeterminate
          color="primary"
        />
      </div>
      <v-card-text
        v-else
      >
        <v-row>
          <v-col>
            <details-table
              class="my-4"
              header-text="Informacje o urządzeniu"
              :data="deviceDetails"
            />
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import DetailsTable from '@views/app/service/dashboard/DetailsTable.vue';

export default {
  name: 'DeviceBasicDetails',
  components: {
    DetailsTable,
  },
  props: {
    id: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
      loading: true,
      data: {},
    };
  },
  computed: {
    deviceDetails() {
      return {
        ID: this.data.id,
        'Numer seryjny': this.data.serialNumber,
        'Data rozpoczęcia': this.$options.filters.formatDateDay(this.data.startDate),
        'Koniec gwarancji': this.$options.filters.formatDateDay(this.data.warrantyEnd),
        Typ: this.data.type,
        Kod: this.data.code,
        Właściciel: this.data.owner?.name,
        'Umowa serwisowa': this.data.sla?.name || 'brak',
      };
    },
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      this.loading = true;
      try {
        const response = await this.axios.get(`/api/service/device/${this.id}`);
        this.data = response.data;
      } catch (error) {
        // do nothing
      }
      this.loading = false;
    },
  },
};
</script>
