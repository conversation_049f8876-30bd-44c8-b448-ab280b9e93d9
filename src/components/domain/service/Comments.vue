<template>
  <div
    class="issue-details-view"
    style="z-index: 1200"
  >
    <v-card>
      <v-card-title class="title">
        <span class="headline">
          <h5 class="text-uppercase">
            Komentarze
          </h5>
        </span>
        <v-spacer />
      </v-card-title>

      <div
        v-if="loading"
        class="d-flex justify-center align-center"
        style="height: 100px;"
      >
        <v-progress-circular
          indeterminate
          color="primary"
        />
      </div>
      <v-card-text
        v-else
      >
        <v-row>
          <v-col>
            <v-simple-table
              dense
              class="elevation-3 mx-1 mt-2 mb-3"
            >
              <template #default>
                <thead>
                  <tr>
                    <th class="text-left">
                      ID
                    </th>
                    <th class="text-left">
                      Autor
                    </th>
                    <th class="text-left">
                      Komentarz
                    </th>
                    <th class="text-left">
                      Informacja
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="comment in data"
                    :key="comment.id"
                  >
                    <td>{{ comment.id ?? '-' }}</td>
                    <td>{{ comment.user.username ?? '-' }}</td>
                    <td>{{ comment.body ?? '-' }}</td>
                    <td>{{ comment.metadata ?? '-' }}</td>
                  </tr>
                </tbody>
              </template>
            </v-simple-table>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: 'Comments',
  components: {
  },
  props: {
    id: {
      type: Number,
      default: null,
      required: true,
    },
    url: {
      type: String,
      default: null,
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
      loading: true,
      data: {},
    };
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      this.loading = true;
      try {
        const response = await this.axios.get(`/${this.url}/${this.id}/comments`);
        this.data = response.data;
      } catch (error) {
        // do nothing
      }
      this.loading = false;
    },
  },
};
</script>
