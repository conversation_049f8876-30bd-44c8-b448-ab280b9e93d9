<template>
  <div
    class="issue-details-view"
    style="z-index: 1200"
  >
    <v-card>
      <v-card-title class="title">
        <span class="headline">
          <h5 class="text-uppercase">
            Lokalizacja
          </h5>
        </span>
        <v-spacer />
      </v-card-title>
      <v-row>
        <v-col v-if="location && location.id">
          <details-table
            class="my-4"
            header-text="Lokalizacja"
            :data="location"
          >
            <template #Lokalizacja="{ value }">
              <a
                v-if="value"
                :href="getLocationLink(value)"
                target="_blank"
                rel="noopener noreferrer"
              >
                Pokaż na mapie
              </a>
              <div v-else>
                -
              </div>
            </template>
          </details-table>
        </v-col>
      </v-row>
    </v-card>
  </div>
</template>

<script>
import DetailsTable from '@views/app/service/dashboard/DetailsTable.vue';

export default {
  name: 'LocationDetails',
  components: {
    DetailsTable,
  },
  props: {
    location: {
      type: Object,
      required: true,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      loading: true,
      data: {},
    };
  },
  methods: {
    getLocationLink(location) {
      const url = `https://www.google.com/maps/search/?api=1&query=${location.lat},${location.lon}`;
      return encodeURI(url);
    },
  },
};
</script>
