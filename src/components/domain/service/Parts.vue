<template>
  <div
    class="issue-details-view"
    style="z-index: 1200"
  >
    <v-card>
      <v-card-title class="title">
        <span class="headline">
          <h5 class="text-uppercase">
            <PERSON><PERSON><PERSON><PERSON><PERSON>
          </h5>
        </span>
      </v-card-title>
      <div
        v-if="loading"
        class="d-flex justify-center align-center"
        style="height: 100px;"
      >
        <v-progress-circular
          indeterminate
          color="primary"
        />
      </div>
      <v-card-text
        v-else
      >
        <v-row>
          <v-col>
            <v-simple-table
              dense
              class="elevation-3 mx-1 mt-2 mb-3"
            >
              <template #default>
                <thead>
                  <tr>
                    <th class="text-left">
                      Kod
                    </th>
                    <th class="text-left">
                      Opis
                    </th>
                    <th class="text-left">
                      Il<PERSON><PERSON>ć (użyta)
                    </th>
                    <th class="text-left">
                      Jdn.
                    </th>
                    <th class="text-left">
                      Objawy uszkodzenia
                    </th>
                    <th class="text-left">
                      Prawdopodobna przyczyna
                    </th>
                    <th class="text-left">
                      Uwagi serw.
                    </th>
                    <th class="text-left">
                      Część BKF?
                    </th>
                    <th
                      v-if="!documentGenerated"
                      class="text-left"
                    >
                      RW/WZ
                    </th>
                    <th
                      v-if="!documentGenerated"
                      class="text-left"
                    >
                      Seria
                    </th>
                    <th
                      v-if="documentGenerated"
                      class="text-left"
                    >
                      Dokument ERP
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="part in data"
                    :key="part.id"
                  >
                    <td>{{ part.itemCode ?? '-' }}</td>
                    <td>{{ part.itemDescription ?? '-' }} </td>
                    <td>{{ part.quantityUsed ?? '-' }}</td>
                    <td>{{ part.unit ?? '-' }}</td>
                    <td>{{ part.symptoms ?? '-' }}</td>
                    <td>{{ part.possibleCause ?? '-' }}</td>
                    <td>{{ part.servicemanRemarks ?? '-' }}</td>
                    <td>
                      <v-layout justify-start>
                        <bool-translate :bool-value="part.isOwnedByBKF ?? false" />
                      </v-layout>
                    </td>
                    <td v-if="!documentGenerated">
                      <v-layout justify-start>
                        <ReleaseMode
                          :value="part.releaseMode"
                          :editable="releaseModeChangeable && !documentGenerated"
                          :part-id="part.id"
                          @update:value="val => part.releaseMode = val"
                          @lock-series="handleLockSeries"
                          @unlock-series="handleUnlockSeries"
                        />
                      </v-layout>
                    </td>
                    <td v-if="!documentGenerated">
                      <v-layout justify-start>
                        <ReleaseSeries
                          :value="part.releaseSeries"
                          :editable="!part.locked && releaseModeChangeable && !documentGenerated"
                          @update:value="val => part.releaseSeries = val"
                        />
                      </v-layout>
                    </td>
                    <td
                      v-if="documentGenerated"
                      class="text-left"
                    >
                      {{ part.erpDocNumber ?? '-' }}
                    </td>
                  </tr>
                </tbody>
              </template>
            </v-simple-table>
            <div
              v-if="!documentGenerated"
              class="d-flex mt-4"
              style="gap: 12px;"
            >
              <v-btn
                v-if="releaseModeChangeable"
                release
                color="primary"
                @click="submitReleaseModes"
              >
                Zatwierdź
              </v-btn>
              <v-btn
                release
                color="primary"
                @click="generateErpDocuments"
              >
                Zatwierdź i wygeneruj w ERP
              </v-btn>
            </div>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import BoolTranslate from '@components/common/BoolTranslate.vue';
import ReleaseMode from '@components/domain/service/part/ReleaseMode.vue';
import ReleaseSeries from '@components/domain/service/part/ReleaseSeries.vue';

export default {
  name: 'PartsDetails',
  components: {
    ReleaseSeries,
    ReleaseMode,
    BoolTranslate,
  },
  props: {
    visitId: {
      type: Number,
      default: null,
      required: true,
    },
    releaseModeChangeable: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      dialog: false,
      loading: true,
      data: {},
      documentGenerated: false,
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      this.loading = true;
      try {
        const response = await this.axios.get(`/api/service/visit/${this.visitId}/parts`);
        this.documentGenerated = response.data.some((part) => part.erpDocument);
        this.data = response.data;
        this.data = response.data.map((part) => ({
          ...part,
          erpDocNumber: part.erpDocument?.docNumber ?? part.erpDocNumber,
          locked: part.releaseMode === 'NONE', // or false by default
        }));
      } catch (error) {
        // console.error(error);
      }
      this.loading = false;
    },
    async submitReleaseModes(silentMode = false, noRefresh = false) {
      const invalidParts = this.data.filter((part) => part.releaseMode === undefined
        || part.releaseMode === null
        || part.releaseMode === ''
        || part.releaseSeries === undefined
        || part.releaseSeries === null
        || part.releaseSeries === '');

      if (invalidParts.length > 0) {
        this.snackbar.showMessage('error', 'Nie wszystkie części mają ustawiony tryb i serię.');
        return;
      }
      try {
        const payload = {
          data: this.data.map((part) => ({
            id: part.id,
            releaseMode: part.releaseMode,
            releaseSeries: part.releaseSeries,
          })),
        };
        await this.axios.put(`/api/service/visit/${this.visitId}/parts/multi`, payload);
        if (!silentMode) {
          this.snackbar.showMessage('success', 'Dane zostały zapisane.');
        }
        if (!noRefresh) {
          await this.fetchData();
        }
      } catch (error) {
        this.snackbar.showMessage('error', 'Błąd podczas zapisywania.');
        // console.error('POST error:', error);
      }
    },
    async generateErpDocuments() {
      await this.submitReleaseModes(true, true);
      await this.axios.post(`/api/service/visit/${this.visitId}/erpDocuments`);
      try {
        this.snackbar.showMessage('success', 'Dokumenty zostały wygenerowane.');
        await this.fetchData();
      } catch (error) {
        this.snackbar.showMessage('error', 'Błąd podczas generowania dokumentów.');
        // console.error('POST error:', error);
      }
    },
    async handleLockSeries(partId) {
      const part = this.data.find((p) => p.id === partId);
      if (part) {
        part.releaseSeries = 'NONE';
        part.locked = true; // disable the select
      }
    },
    async handleUnlockSeries(partId) {
      const part = this.data.find((p) => p.id === partId);
      if (part) {
        part.locked = false; // disable the select
      }
    },
  },
};
</script>
<style>
.mini-select {
  font-size: 12px;
  max-width: 100px;
  height: 30px;
}
</style>
