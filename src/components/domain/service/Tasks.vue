<template>
  <div
    class="issue-details-view"
    style="z-index: 1200"
  >
    <v-card>
      <v-card-title class="title">
        <span class="headline">
          <h5 class="text-uppercase">
            Zadania
          </h5>
        </span>
        <v-spacer />
        <TaskAddEditModal
          :id="id"
          :key="`AddTask_${id}`"
          :ref="`AddTask_${id}`"
          :submit-task="(body) => submitTask(id, body)"
          :device-id="deviceId"
          :location-id="locationId"
          @onSubmitted="onTaskSubmitted(`AddTask_${id}`)"
          @error="message => snackbar.showMessage('error', message)"
        />
        <v-tooltip
          v-if="addEnabled"
          bottom
        >
          <template #activator="{ on, attrs }">
            <v-btn
              :key="id"
              class="mt-1 mr-2"
              elevation="1"
              v-bind="attrs"
              rounded
              x-small
              fab
              color="primary"
              v-on="on"
              @click.stop
              @click.native="openModal(`AddTaskTemplate_${id}`)"
            >
              <v-icon>mdi-plus-circle-multiple-outline</v-icon>
            </v-btn>
          </template>
          <span>Importuj zadania z szablonu</span>
        </v-tooltip>
        <TaskTemplateAddModal
          :id="id"
          :key="`AddTaskTemplate_${id}`"
          :ref="`AddTaskTemplate_${id}`"
          :url="url"
          :issue-id="id"
          @onSubmitted="onTasksImported(`AddTaskTemplate_${id}`)"
        />
        <v-tooltip
          v-if="addEnabled"
          bottom
        >
          <template #activator="{ on, attrs }">
            <v-btn
              :key="id"
              class="mt-1 mr-2"
              elevation="1"
              v-bind="attrs"
              rounded
              x-small
              fab
              color="primary"
              v-on="on"
              @click.stop
              @click.native="openModal(`AddTask_${id}`)"
            >
              <v-icon>mdi-plus</v-icon>
            </v-btn>
          </template>
          <span>Dodaj zadanie</span>
        </v-tooltip>
      </v-card-title>
      <div
        v-if="loading"
        class="d-flex justify-center align-center"
        style="height: 100px;"
      >
        <v-progress-circular
          indeterminate
          color="primary"
        />
      </div>
      <v-card-text
        v-else
      >
        <v-row>
          <v-col>
            <v-simple-table
              dense
              class="elevation-3 mx-1 mt-2 mb-3"
            >
              <template #default>
                <thead>
                  <tr>
                    <th class="text-left">
                      ID
                    </th>
                    <th class="text-left">
                      Status
                    </th>
                    <th class="text-left">
                      Kategoria
                    </th>
                    <th class="text-left">
                      Data utworzenia
                    </th>
                    <th class="text-left">
                      Czas docelowy
                    </th>
                    <th class="text-left">
                      Symptom
                    </th>
                    <th class="text-left">
                      Opis
                    </th>
                    <th
                      v-if="addEnabled"
                      class="text-left"
                    >
                      Edytuj
                    </th>
                    <th
                      v-if="addEnabled"
                      class="text-left"
                    >
                      Usuń
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="task in data"
                    :key="task.id"
                  >
                    <td>{{ task.id ?? '-' }}</td>
                    <td>
                      <v-layout justify-start>
                        <task-status :status="task.status" />
                      </v-layout>
                    </td>
                    <td>{{ task.category ?? '-' }}</td>
                    <td>{{ task.ctime | formatDateDayTime }}</td>
                    <td>{{ task.targetTime | formatDateDayTime }}</td>
                    <td>{{ task.symptom ?? '-' }}</td>
                    <td>{{ task.description ?? '-' }}</td>
                    <td v-if="addEnabled">
                      <v-tooltip bottom>
                        <template #activator="{ on, attrs }">
                          <v-icon
                            small
                            class="mr-2"
                            color="primary"
                            v-bind="attrs"
                            v-on="on"
                            @click.stop
                            @click="openEditModal(`EditTask_${task.id}`)"
                          >
                            mdi-pencil
                          </v-icon>
                          <TaskAddEditModal
                            :id="task.id"
                            :key="`EditTask_${task.id}`"
                            :ref="`EditTask_${task.id}`"
                            :description="task.description"
                            :category="task.category"
                            :symptom="task.symptom"
                            :is-edit-mode="true"
                            :submit-task="(body) => submitTaskEdit(task.id, body)"
                            :device-id="deviceId"
                            :location-id="locationId"
                            @onSubmitted="onTaskSubmitted(`EditTask_${task.id}`)"
                            @error="message => snackbar.showMessage('error', message)"
                          />
                        </template>
                        <span>Edycja zadania</span>
                      </v-tooltip>
                    </td>
                    <td v-if="addEnabled">
                      <v-tooltip bottom>
                        <template #activator="{ on, attrs }">
                          <v-icon
                            small
                            class="mr-2"
                            color="red"
                            v-bind="attrs"
                            v-on="on"
                            @click="deleteTask(task.id)"
                          >
                            mdi-delete
                          </v-icon>
                        </template>
                        <span>Usuń zadanie</span>
                      </v-tooltip>
                    </td>
                  </tr>
                </tbody>
              </template>
            </v-simple-table>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import TaskAddEditModal from '@components/domain/service/task/AddEditModal.vue';
import TaskStatus from '@components/domain/service/task/Status.vue';
import TaskTemplateAddModal from '@components/domain/service/task/TemplateAddModal.vue';

export default {
  name: 'TasksDetails',
  components: {
    TaskAddEditModal,
    TaskStatus,
    TaskTemplateAddModal,
  },
  props: {
    id: { // np. Visit.id lub Issue.id
      type: Number,
      default: null,
      required: true,
    },
    url: {
      type: String,
      default: null,
      required: true,
    },
    locationId: {
      type: Number,
      required: false,
      default: null,
      validator: (value) => value === null || Number.isInteger(value),
    },
    deviceId: {
      type: Number,
      required: false,
      default: null,
      validator: (value) => value === null || Number.isInteger(value),
    },
    addEnabled: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  data() {
    return {
      dialog: false,
      loading: true,
      data: {},
    };
  },
  created() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      this.loading = true;
      try {
        const response = await this.axios.get(`/${this.url}/${this.id}/tasks`);
        this.data = response.data;
        // console.log(this.data);
      } catch (error) {
        // console.error(error)
      }
      this.loading = false;
    },
    async submitTask(id, body) {
      await this.axios.post(`/${this.url}/${this.id}/tasks`, body);
    },
    async submitTaskEdit(taskId, body) {
      await this.axios.put(`/api/service/task/${taskId}`, body);
    },
    openModal(id) {
      if (this.$refs[id]) {
        this.$refs[id].dialog = true;
      }
    },
    openEditModal(id) {
      const ref = this.$refs[id];
      if (Array.isArray(ref)) {
        // Use first instance if multiple
        ref[0].dialog = true;
      } else if (ref) {
        ref.dialog = true;
      }
    },
    onTaskSubmitted(refName) {
      this.$refs[refName].dialog = false;
      this.fetchData();
    },
    onTasksImported(refName) {
      this.$refs[refName].dialog = false;
      this.snackbar.showMessage('success', 'Zadania szablonowe zostały dodane.');
      this.fetchData();
    },
    async deleteTask(taskId) {
      // eslint-disable-next-line no-alert
      const isConfirmed = window.confirm('Czy na pewno skasować to zadanie?');
      if (!isConfirmed) {
        return;
      }
      try {
        const response = await this.axios.delete(`/api/service/task/${taskId}`);
        this.data = response.data;
        this.snackbar.showMessage('success', 'Zadanie zostało usunięte.');
        await this.fetchData();
      } catch (error) {
        this.snackbar.showMessage('error', 'Błąd usuwania zadania');
      }
    },
  },
};
</script>
