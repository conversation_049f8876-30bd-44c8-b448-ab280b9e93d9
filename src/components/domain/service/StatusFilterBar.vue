<template>
  <v-col
    v-if="statuses != null"
    cols="3"
  >
    <v-select
      v-model="selectedStatuses"
      label="Status"
      :items="statuses"
      prepend-inner-icon="mdi-group"
      item-value="value"
      item-text="name"
      multiple
      chips
      small-chips
      class="mr-2 ml-2"
      @input="emitStatusChange"
    />
  </v-col>
</template>

<script>
export default {
  name: 'StatusFilterBar',
  props: {
    statuses: {
      type: [Array, null],
      default: null,
      required: false,
    },
    onStatusChange: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      selectedStatuses: this.statuses != null && this.statuses.length ? [this.statuses[0]] : [],
    };
  },
  methods: {
    emitStatusChange() {
      this.$emit('status-change', this.selectedStatuses);
    },
  },
};
</script>
