<template>
  <v-dialog
    v-model="dialog"
    style="z-index: 1200"
    content-class="dialogWidth-3"
  >
    <v-card>
      <v-card-title class="title">
        <span class="headline">
          <h5 class="text-uppercase">
            W<PERSON><PERSON>rz szablon zadań
          </h5>
        </span>
        <v-spacer />
        <v-btn
          icon
          class="white--text"
          @click.native="dialog = false"
        >
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>

      <v-card-text
        class="pt-4"
      >
        <v-form @submit.prevent="submit">
          <v-select
            v-model="templateId"
            :items="templates"
            item-value="value"
            item-text="text"
            label="Szablony zadań"
            filled
            background-color="white"
            dense
            required
          />
          <v-row
            justify="end"
          >
            <v-btn
              color="primary"
              type="submit"
              class="mr-4 mb-4"
            >
              Importuj
            </v-btn>
          </v-row>
        </v-form>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  name: 'TaskTemplateAddModal',
  props: {
    issueId: {
      type: Number,
      required: true,
    },
    url: {
      type: String,
      default: null,
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
      templates: [], // Array to hold the fetched template data
      templateId: null,
    };
  },
  created() {
    this.fetchTemplates();
  },
  methods: {
    async submit() {
      this.loading = true;
      try {
        if (!this.templateId) {
          this.snackbar.showMessage('error', 'Należy wybrać szablon zadań.');
          this.loading = false;
          return;
        }
        await this.axios.post(
          `/${this.url}/${this.issueId}/template/${this.templateId}/import`,
          { },
        );
        this.$emit('onSubmitted');
      } catch (error) {
        this.snackbar.showMessage('error', 'Błąd podczas dodawania szablonu zadań');
      }
      this.loading = false;
    },
    async fetchTemplates() {
      try {
        const response = await this.axios.get('/api/service/templateTaskGroups?category=STND');
        const templatesData = response.data.data;
        if (!templatesData || !Array.isArray(templatesData)) {
          Error('Invalid data format received from the API');
        }
        this.templates = templatesData.map((template) => ({
          value: template.id,
          text: template.title,
        }));
      } catch (error) {
        Error(`Error fetching templates: ${error}`);
      }
    },
  },
};
</script>
