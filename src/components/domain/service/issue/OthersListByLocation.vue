<template>
  <div
    class="issue-details-view"
    style="z-index: 1200"
  >
    <v-card>
      <v-card-title class="title">
        <span class="headline">
          <h5 class="text-uppercase">
            Inne zgłoszenia dla lokalizacji
          </h5>
        </span>
        <v-spacer />
      </v-card-title>

      <div
        v-if="loading"
        class="d-flex justify-center align-center"
        style="height: 100px;"
      >
        <v-progress-circular
          indeterminate
          color="primary"
        />
      </div>
      <v-card-text v-else>
        <v-row>
          <v-col>
            <div
              v-if="filteredData.length === 0"
              class="text-center py-6 grey--text text--darken-1"
            >
              <h5>Brak innych zgłoszeń dla tej lokalizacji</h5>
            </div>

            <v-simple-table
              v-else
              dense
              class="elevation-3 mx-1 mt-2 mb-3"
            >
              <template #default>
                <thead>
                  <tr>
                    <th class="text-left">
                      ID
                    </th>
                    <th class="text-left">
                      <PERSON><PERSON><PERSON><PERSON><PERSON>ie
                    </th>
                    <th class="text-left">
                      Tytuł
                    </th>
                    <th class="text-left">
                      Treść
                    </th>
                    <th class="text-left">
                      Status
                    </th>
                    <th class="text-left">
                      Priorytet
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="issue in filteredData"
                    :key="issue.id"
                  >
                    <td>{{ issue.id ?? '-' }}</td>
                    <td>{{ issue.device.serialNumber }}</td>
                    <td>{{ issue.title ?? '-' }}</td>
                    <td>{{ issue.content ?? '-' }}</td>
                    <td>
                      <v-layout justify-start>
                        <issue-status :status="issue.status" />
                      </v-layout>
                    </td>
                    <td>{{ issue.priority ?? 'brak' }}</td>
                  </tr>
                </tbody>
              </template>
            </v-simple-table>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import IssueStatus from '@components/domain/service/issue/Status.vue';

export default {

  name: 'IssueOtherLisByLocation',
  components: {
    IssueStatus,
  },
  props: {
    id: { // location.id
      type: Number,
      required: true,
    },
    exceptedIssueId: { // issue.id
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
      loading: true,
      data: {},
    };
  },
  computed: {
    filteredData() {
      if (!Array.isArray(this.data)) return [];
      return this.data.filter((issue) => issue.id !== this.exceptedIssueId);
    },
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      this.loading = true;
      try {
        const response = await this.axios.get('/api/service/issues', {
          params: {
            status: 'open,reviewed',
            location: this.id,
          },
        });
        this.data = Array.isArray(response.data.data) ? response.data.data : [];
      } catch (error) {
        // do nothing
      }
      this.loading = false;
    },
  },
};
</script>
