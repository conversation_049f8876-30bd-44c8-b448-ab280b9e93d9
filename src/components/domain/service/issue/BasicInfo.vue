<template>
  <div
    class="issue-details-view"
    style="z-index: 1200"
  >
    <v-card>
      <v-card-title class="title">
        <span class="headline">
          <h5 class="text-uppercase">
            Informacje o zgłoszeniu
          </h5>
        </span>
        <v-spacer />
      </v-card-title>
      <div
        v-if="loading"
        class="d-flex justify-center align-center"
        style="height: 100px;"
      >
        <v-progress-circular
          indeterminate
          color="primary"
        />
      </div>
      <v-card-text
        v-else
      >
        <v-row>
          <v-col>
            <details-table
              class="my-4"
              header-text="Informacje o zgłoszeniu"
              :data="issueMainDetails"
            >
              <template #Status="{ value }">
                <v-layout justify-start>
                  <issue-status :status="value" />
                </v-layout>
              </template>
              <template #Priorytet="{ value }">
                {{ value }}
                <v-tooltip
                  v-if="data.status!=='completed'"
                  bottom
                >
                  <template #activator="{ on, attrs }">
                    <v-icon
                      small
                      class="mr-2"
                      color="primary"
                      v-bind="attrs"
                      v-on="on"
                      @click.stop
                      @click="openEditModal(`EditIssue_${id}`)"
                    >
                      mdi-pencil
                    </v-icon>
                    <IssueEditModal
                      :key="`EditIssue_${id}`"
                      :ref="`EditIssue_${id}`"
                      :issue-id="id"
                      :priority="data.priority"
                      :submit-task="(body) => submitIssueEdit(id, body)"
                      @onSubmitted="onIssueSubmitted(`EditIssue_${id}`)"
                      @error="message => snackbar.showMessage('error', message)"
                    />
                  </template>
                  <span>Edycja priorytetu zgłoszenia</span>
                </v-tooltip>
              </template>
            </details-table>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import IssueStatus from '@components/domain/service/issue/Status.vue';
import DetailsTable from '@views/app/service/dashboard/DetailsTable.vue';
import IssueEditModal from '@components/domain/service/issue/EditModal.vue';

export default {
  name: 'IssueBasicInfo',
  components: {
    IssueEditModal,
    IssueStatus,
    DetailsTable,
  },
  props: {
    id: {
      type: Number,
      default: null,
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
      loading: true,
      data: {},
    };
  },
  computed: {
    issueMainDetails() {
      return {
        ID: this.data.id,
        Tytuł: this.data.title,
        Treść: this.data.content,
        Źródło: this.data.source,
        Status: this.data.status,
        Priorytet: this.data.priority || 'brak',
        'Data utworzenia': this.$options.filters.formatDateDayTime(this.data.ctime),
        'Data zaraportowania': this.$options.filters.formatDateDayTime(this.data.reportTime),
        'Czas docelowy': this.$options.filters.formatDateDayTime(this.data.targetTime),
        'Czas końcowy': this.$options.filters.formatDateDayTime(this.data.endTime),
        'Zaraportowano przez': this.data.reportedBy,
      };
    },
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      this.loading = true;
      try {
        const response = await this.axios.get(`/api/service/issue/${this.id}`);
        this.data = response.data;
      } catch (error) {
        // do nothing
      }
      this.loading = false;
    },
    async submitIssueEdit(issueId, body) {
      await this.axios.put(`/api/service/issue/${issueId}`, body);
    },
    openEditModal(id) {
      if (this.$refs[id]) {
        this.$refs[id].dialog = true;
      }
    },
    onIssueSubmitted(refName) {
      this.$refs[refName].dialog = false;
      this.fetchData();
    },
  },
};
</script>
