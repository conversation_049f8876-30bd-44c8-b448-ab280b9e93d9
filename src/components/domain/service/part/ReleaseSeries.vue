<template>
  <div v-if="editable">
    <v-select
      v-model="internalValue"
      :items="items"
      dense
      class="mini-select"
    />
  </div>
  <div v-else>
    {{ internalValue }}
  </div>
</template>

<script>
export default {
  name: 'ReleaseSeries',
  props: {
    editable: {
      type: Boolean,
      default: false,
    },
    value: {
      type: String,
      default: '',
      required: false,
    },
  },
  data() {
    return {
      items: ['SER', 'MON', 'MON-B', 'ABO', 'GWA', 'REK', 'REKSE', 'KOM-S', 'ROZ', 'INW', '10TKA', 'NONE'],
      internalValue: this.value,
    };
  },
  watch: {
    value(newVal) {
      this.internalValue = newVal;
    },
    internalValue(newVal) {
      this.$emit('update:value', newVal); // Vue-compliant binding
    },
  },
};
</script>
