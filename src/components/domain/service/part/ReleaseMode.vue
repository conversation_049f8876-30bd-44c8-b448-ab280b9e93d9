<template>
  <div v-if="editable">
    <v-select
      v-model="internalValue"
      :items="items"
      dense
      class="mini-select"
    />
  </div>
  <div v-else>
    {{ internalValue }}
  </div>
</template>
<script>
export default {
  name: 'ReleaseMode',
  props: {
    editable: {
      type: Boolean,
      default: false,
    },
    value: {
      type: String,
      default: '',
      required: false,
    },
    partId: {
      type: Number,
      default: null,
      required: false,
    },
  },
  data() {
    return {
      items: ['RW', 'WZ', 'NONE'],
      internalValue: this.value,
    };
  },
  watch: {
    value(newVal) {
      this.internalValue = newVal;
    },
    internalValue(newVal) {
      this.$emit('update:value', newVal); // Allow v-model support
      if (newVal === 'NONE') {
        this.$emit('lock-series', this.partId); // Custom event for 'lock Series'
      } else {
        this.$emit('unlock-series', this.partId); // Custom event for 'lock Series'
      }
    },
  },
};
</script>
