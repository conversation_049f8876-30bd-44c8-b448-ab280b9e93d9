<template>
  <div
    class="issue-details-view"
    style="z-index: 1200"
  >
    <v-card>
      <v-card-title class="title">
        <span class="headline">
          <h5 class="text-uppercase">
            Informacje o wizycie
          </h5>
        </span>
        <v-spacer />
      </v-card-title>
      <div
        v-if="loading"
        class="d-flex justify-center align-center"
        style="height: 100px;"
      >
        <v-progress-circular
          indeterminate
          color="primary"
        />
      </div>
      <v-card-text
        v-else
      >
        <v-row>
          <v-col>
            <details-table
              class="my-4"
              header-text="Informacje o wizycie"
              :data="visitMainDetails"
            >
              <template #Status="{ value }">
                <v-layout justify-start>
                  <visit-status :status="value" />
                </v-layout>
              </template>
              <template #Przegląd?="{ value }">
                <v-layout justify-start>
                  <bool-translate :bool-value="value === true || value=== 'true'" />
                </v-layout>
              </template>
            </details-table>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import VisitStatus from '@components/domain/service/visit/Status.vue';
import BoolTranslate from '@components/common/BoolTranslate.vue';
import DetailsTable from '@views/app/service/dashboard/DetailsTable.vue';

export default {
  name: 'VisitBasicInfo',
  components: {
    VisitStatus,
    BoolTranslate,
    DetailsTable,
  },
  props: {
    id: {
      type: Number,
      default: null,
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
      loading: true,
      data: {},
    };
  },
  computed: {
    visitMainDetails() {
      return {
        Numer: this.data.visitTag,
        Status: this.data.status,
        Serwisant: this.data.serviceman?.username,
        'Planowana data': this.$options.filters.formatDateDay(this.data.plannedDate),
        'Przegląd?': this.data.surveyRelated,
      };
    },
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      this.loading = true;
      try {
        const response = await this.axios.get(`/api/service/visit/${this.id}`);
        this.data = response.data;
      } catch (error) {
        // do nothing
      }
      this.loading = false;
    },
  },
};
</script>
