<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
    card
  >
    <v-row>
      <v-col>
        <text-search v-model="config.search" />
      </v-col>
      <v-col>
        <date-select
          v-model="config.date"
        />
      </v-col>
      <v-col
        cols="12"
        md="auto"
        class="text-right"
      >
        <fleet-user-invite-modal
          :app="app"
          :user-id="userId"
          :base-url="baseUrl"
          @invitation-sent="$emit('invitation-sent')"
        />
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import debounce from 'lodash/debounce';
import TextSearch from '@components/reports/filters/TextSearch.vue';
import DateSelect from '@components/reports/filters/DateSelect.vue';
import FleetUserInviteModal from './modal/FleetUserInviteModal.vue';

const SettingsKey = 'fleet-users-filter';

export default {
  components: {
    TextSearch,
    DateSelect,
    FleetUserInviteModal,
  },
  model: {
    prop: 'param',
    event: 'update:param',
  },
  props: {
    param: {
      type: Object,
      default: () => ({}),
    },
    app: {
      type: String,
      required: true,
    },
    userId: {
      type: Number,
      required: true,
    },
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    const savedConfig = JSON.parse(localStorage.getItem(SettingsKey) || '{}');

    return {
      config: {
        search: savedConfig.search ?? null,
        date: savedConfig.date ?? null,
      },
    };
  },
  computed: {
    internalParam() {
      return {
        search: this.config.search,
        ...this.config.date,
      };
    },
  },
  watch: {
    internalParam: {
      handler(newVal) {
        this.debouncedUpdate(newVal);
      },
      deep: true,
      immediate: true,
    },
    config: {
      handler(newVal) {
        localStorage.setItem(SettingsKey, JSON.stringify(newVal));
      },
      deep: true,
    },
  },
  beforeCreate() {
    this.debouncedUpdate = debounce((val) => {
      this.$emit('update:param', val);
    }, 300);
  },
};
</script>
