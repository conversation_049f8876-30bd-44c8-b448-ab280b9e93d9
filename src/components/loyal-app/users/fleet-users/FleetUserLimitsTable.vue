<template>
  <v-card
    class="px-3 py-3"
    outlined
    color="blue-grey lighten-5"
  >
    <div class="d-flex justify-space-between mb-3">
      <h3>
        {{ $t('loyalApp_fleetUserLimits') }}
      </h3>
      <fleet-user-limits-edit-modal
        v-if="hasData"
        :limits="data"
        :currency-symbol="currencySymbol"
        :email="email"
        :app="app"
        :user-id="userId"
        :fleet-user-id="fleetUserId"
        :base-url="baseUrl"
        @limits-updated="onLimitsUpdated"
      />
    </div>
    <v-simple-table
      v-if="hasData"
      dense
      class="elevation-3 py-2"
    >
      <template #default>
        <thead>
          <tr>
            <th>{{ $t('loyalApp_hourlyLimit') }}</th>
            <th>{{ $t('loyalApp_hourlyUsage') }}</th>
            <th>{{ $t('loyalApp_dailyLimit') }}</th>
            <th>{{ $t('loyalApp_dailyUsage') }}</th>
            <th>{{ $t('loyalApp_monthlyLimit') }}</th>
            <th>{{ $t('loyalApp_monthlyUsage') }}</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>
              <currency-formatter
                v-if="currencySymbol && data.hourly?.limit && typeof data.hourly.limit === 'number'"
                :value="data.hourly.limit"
                :symbol="currencySymbol"
              />
              <span v-else>{{ data.hourly?.limit ?? '-' }}</span>
            </td>
            <td>
              <currency-formatter
                v-if="currencySymbol && data.hourly?.usage && typeof data.hourly.usage === 'number'"
                :value="data.hourly.usage"
                :symbol="currencySymbol"
              />
              <span v-else>{{ data.hourly?.usage ?? '-' }}</span>
            </td>
            <td>
              <currency-formatter
                v-if="currencySymbol && data.daily?.limit && typeof data.daily.limit === 'number'"
                :value="data.daily.limit"
                :symbol="currencySymbol"
              />
              <span v-else>{{ data.daily?.limit ?? '-' }}</span>
            </td>
            <td>
              <currency-formatter
                v-if="currencySymbol && data.daily?.usage && typeof data.daily.usage === 'number'"
                :value="data.daily.usage"
                :symbol="currencySymbol"
              />
              <span v-else>{{ data.daily?.usage ?? '-' }}</span>
            </td>
            <td>
              <currency-formatter
                v-if="
                  currencySymbol
                    && data.monthly?.limit
                    && typeof data.monthly.limit === 'number'
                "
                :value="data.monthly.limit"
                :symbol="currencySymbol"
              />
              <span v-else>{{ data.monthly?.limit ?? '-' }}</span>
            </td>
            <td>
              <currency-formatter
                v-if="
                  currencySymbol
                    && data.monthly?.usage
                    && typeof data.monthly.usage === 'number'
                "
                :value="data.monthly.usage"
                :symbol="currencySymbol"
              />
              <span v-else>{{ data.monthly?.usage ?? '-' }}</span>
            </td>
          </tr>
        </tbody>
      </template>
    </v-simple-table>
    <v-card-text
      v-else
      class="text-center"
    >
      <v-icon small>
        mdi-alert-circle-outline
      </v-icon>
      <i class="ml-2">{{ $t('common_noData') }}</i>
    </v-card-text>
  </v-card>
</template>

<script>
import CurrencyFormatter from '@components/common/formatters/CurrencyFormatter.vue';
import FleetUserLimitsEditModal from './modal/FleetUserLimitsEditModal.vue';

export default {
  components: {
    CurrencyFormatter,
    FleetUserLimitsEditModal,
  },
  props: {
    data: {
      type: Object,
      required: true,
    },
    currencySymbol: {
      type: String,
      default: '',
    },
    email: {
      type: String,
      default: '',
    },
    app: {
      type: String,
      required: true,
    },
    userId: {
      type: Number,
      required: true,
    },
    fleetUserId: {
      type: Number,
      required: true,
    },
    baseUrl: {
      type: String,
      required: true,
    },
  },
  computed: {
    hasData() {
      return this.data && Object.keys(this.data).length > 0;
    },
  },
  methods: {
    onLimitsUpdated() {
      this.$emit('limits-updated');
    },
  },
};
</script>
