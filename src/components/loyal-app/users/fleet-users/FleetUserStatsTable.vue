<template>
  <v-card
    class="px-3 py-3"
    outlined
    color="blue-grey lighten-5"
  >
    <h3 class="mb-3">
      {{ $t('loyalApp_stats') }}
    </h3>
    <v-simple-table
      v-if="data.length"
      dense
      class="elevation-3 py-2"
    >
      <template #default>
        <thead>
          <tr>
            <th>{{ $t('common_paymentMethod') }}</th>
            <th>{{ $t('common_type') }}</th>
            <th>{{ $t('common_value') }}</th>
            <th>{{ $t('common_count') }}</th>
            <th>{{ $t('dashboard_average') }}</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="stat in data"
            :key="stat.issuer + stat.type"
          >
            <td>{{ stat.issuer }}</td>
            <td>{{ stat.type }}</td>
            <td>
              <currency-formatter
                v-if="currencySymbol && typeof stat.value === 'number'"
                :value="stat.value"
                :symbol="currencySymbol"
              />
              <span v-else>{{ stat.value }}</span>
            </td>
            <td>{{ stat.count }}</td>
            <td>
              <currency-formatter
                v-if="currencySymbol && typeof stat.avg === 'number'"
                :value="stat.avg"
                :symbol="currencySymbol"
              />
              <span v-else>{{ stat.avg }}</span>
            </td>
          </tr>
        </tbody>
      </template>
    </v-simple-table>
    <v-card-text
      v-else
      class="text-center"
    >
      <v-icon small>
        mdi-alert-circle-outline
      </v-icon>
      <i class="ml-2">{{ $t('common_noData') }}</i>
    </v-card-text>
  </v-card>
</template>

<script>
import CurrencyFormatter from '@components/common/formatters/CurrencyFormatter.vue';

export default {
  components: {
    CurrencyFormatter,
  },
  props: {
    data: {
      type: Array,
      required: true,
    },
    currencySymbol: {
      type: String,
      default: '',
    },
  },
};
</script>
