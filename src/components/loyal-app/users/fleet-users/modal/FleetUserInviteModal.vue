<template>
  <div>
    <v-tooltip bottom>
      <template #activator="{ on, attrs }">
        <v-btn
          color="primary"
          v-bind="attrs"
          v-on="on"
          @click="openDialog"
        >
          <v-icon left>
            mdi-account-plus
          </v-icon>
          {{ $t('actions.invite_user') }}
        </v-btn>
      </template>
      <span>{{ $t('actions.invite_user') }}</span>
    </v-tooltip>
    <generic-modal
      v-model="dialog"
      :title="$t('loyalApp_inviteFleetUser')"
      :loading="loading"
      :confirm-button-text="$t('actions.send_invitation')"
      :cancel-button-text="$t('actions.cancel')"
      @submit="submit"
      @close="closeDialog"
    >
      <v-container
        class="pt-6"
        grid-list-md
        fluid
      >
        <v-row>
          <v-col cols="12">
            <v-text-field
              v-model="email"
              :label="$t('common_email')"
              type="email"
              prepend-icon="mdi-email"
              outlined
              dense
              :error-messages="emailError"
            />
          </v-col>
        </v-row>
      </v-container>
    </generic-modal>
  </div>
</template>

<script>
import GenericModal from '@components/common/GenericModal.vue';

export default {
  components: {
    GenericModal,
  },
  props: {
    app: {
      type: String,
      required: true,
    },
    userId: {
      type: Number,
      required: true,
    },
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
      loading: false,
      email: '',
      emailError: '',
    };
  },
  methods: {
    async submit() {
      // Validate email
      if (!this.email) {
        this.emailError = this.$t('common_fieldRequired');
        return;
      }
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.email)) {
        this.emailError = this.$t('common_invalidEmail');
        return;
      }

      this.loading = true;
      this.emailError = '';

      try {
        await this.axios.post(
          `${this.baseUrl}/user/${this.userId}/fleet/users/invite?app=${this.app}`,
          { email: this.email },
        );

        this.snackbar.showMessage({
          content: this.$t('loyalApp_invitationSent'),
          color: 'success',
        });

        this.$emit('invitation-sent');
        this.closeDialog();
      } catch (error) {
        this.snackbar.showMessage({
          content: error.response?.data?.message || this.$t('common_fetchError'),
          color: 'error',
        });
      } finally {
        this.loading = false;
      }
    },
    openDialog() {
      this.dialog = true;
    },
    closeDialog() {
      this.dialog = false;
      this.resetModalState();
    },
    resetModalState() {
      this.email = '';
      this.emailError = '';
      this.loading = false;
    },
  },
};
</script>
