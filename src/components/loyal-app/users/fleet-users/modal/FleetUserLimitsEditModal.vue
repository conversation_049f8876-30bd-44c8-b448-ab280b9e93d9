<template>
  <div>
    <v-tooltip bottom>
      <template #activator="{ on, attrs }">
        <v-btn
          tile
          rounded
          elevation="1"
          x-small
          fab
          color="primary"
          v-bind="attrs"
          v-on="on"
          @click="openDialog"
        >
          <v-icon>
            mdi-pencil
          </v-icon>
        </v-btn>
      </template>
      <span>{{ $t('actions.edit') }}</span>
    </v-tooltip>
    <generic-modal
      v-model="dialog"
      :title="modalTitle"
      :loading="loading"
      :confirm-button-text="$t('actions.save')"
      :cancel-button-text="$t('actions.cancel')"
      max-width="600"
      @submit="submit"
      @close="closeDialog"
    >
      <v-container class="pt-10">
        <v-row>
          <v-col cols="12">
            <v-text-field
              v-model.number="limitsData.hourly.limit"
              :label="$t('loyalApp_hourlyLimit')"
              type="number"
              step="1"
              min="0"
              prepend-icon="mdi-clock-outline"
              outlined
              dense
              :suffix="currencySymbol"
            />
          </v-col>
          <v-col cols="12">
            <v-text-field
              v-model.number="limitsData.daily.limit"
              :label="$t('loyalApp_dailyLimit')"
              type="number"
              step="1"
              min="0"
              prepend-icon="mdi-calendar-today"
              outlined
              dense
              :suffix="currencySymbol"
            />
          </v-col>
          <v-col cols="12">
            <v-text-field
              v-model.number="limitsData.monthly.limit"
              :label="$t('loyalApp_monthlyLimit')"
              type="number"
              step="1"
              min="0"
              prepend-icon="mdi-calendar-month"
              outlined
              dense
              :suffix="currencySymbol"
            />
          </v-col>
        </v-row>
      </v-container>
    </generic-modal>
  </div>
</template>

<script>
import GenericModal from '@components/common/GenericModal.vue';

export default {
  components: {
    GenericModal,
  },
  props: {
    limits: {
      type: Object,
      default: () => ({}),
    },
    currencySymbol: {
      type: String,
      default: '',
    },
    email: {
      type: String,
      default: '',
    },
    app: {
      type: String,
      required: true,
    },
    userId: {
      type: Number,
      required: true,
    },
    fleetUserId: {
      type: Number,
      required: true,
    },
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
      loading: false,
      limitsData: {
        hourly: { limit: null },
        daily: { limit: null },
        monthly: { limit: null },
      },
    };
  },
  computed: {
    modalTitle() {
      const baseTitle = this.$t('loyalApp_editFleetUserLimits');
      if (this.email) {
        return `${baseTitle}: ${this.email}`;
      }
      return baseTitle;
    },
  },
  methods: {
    normalizeValue(value) {
      // Convert empty string, 0, null, undefined to null
      // Keep valid numbers (including negative numbers if needed)
      if (value === '' || value === null || value === undefined) {
        return null;
      }
      // If it's a valid number, return it
      const numValue = Number(value);
      if (!Number.isNaN(numValue)) {
        return numValue;
      }
      // Fallback to null for any other invalid values
      return null;
    },
    async submit() {
      this.loading = true;
      try {
        const payload = {
          limit_hourly: this.normalizeValue(this.limitsData.hourly.limit),
          limit_daily: this.normalizeValue(this.limitsData.daily.limit),
          limit_monthly: this.normalizeValue(this.limitsData.monthly.limit),
        };

        await this.axios.post(
          `${this.baseUrl}/user/${this.userId}/fleet/users/${this.fleetUserId}/settings?app=${this.app}`,
          payload,
        );

        this.$emit('limits-updated');
        this.closeDialog();
      } catch (error) {
        this.snackbar.showMessage({
          content: error.message || this.$t('common_fetchError'),
          color: 'error',
        });
      } finally {
        this.loading = false;
      }
    },
    openDialog() {
      this.loadLimitsData();
      this.dialog = true;
    },
    loadLimitsData() {
      if (this.limits && Object.keys(this.limits).length > 0) {
        this.limitsData = {
          hourly: { limit: this.limits.hourly?.limit || null },
          daily: { limit: this.limits.daily?.limit || null },
          monthly: { limit: this.limits.monthly?.limit || null },
        };
      }
    },
    closeDialog() {
      this.dialog = false;
      this.resetModalState();
    },
    resetModalState() {
      this.limitsData = {
        hourly: { limit: null },
        daily: { limit: null },
        monthly: { limit: null },
      };
      this.loading = false;
    },
  },
};
</script>
