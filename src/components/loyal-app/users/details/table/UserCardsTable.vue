<template>
  <v-card
    class="px-3 py-3"
    outlined
    color="blue-grey lighten-5"
  >
    <slot name="title">
      <h3 class="mb-3">
        {{ $t('wla_user_card_list_header') }}
      </h3>
    </slot>
    <v-simple-table
      v-if="!loading"
      dense
      class="elevation-3 py-2"
    >
      <template #default>
        <tbody>
          <tr>
            <td
              class="border-right text-left font-weight-bold"
            >
              {{ $t('wla_user_card_name') }}
            </td>
            <td
              class="border-right text-left font-weight-bold"
            >
              {{ $t('wla_user_card_balance') }}
            </td>
            <td
              class="text-left font-weight-bold"
            >
              {{ $t('wla_user_card_end_time') }}
            </td>
          </tr>
          <tr
            v-for="[key, value] in items"
            :key="`item-${key}`"
          >
            <td
              class="border-right text-left"
            >
              {{ value.type_key }}
            </td>
            <td
              class="border-right text-left"
            >
              {{ value.balance }}
            </td>
            <td
              class="text-left"
            >
              {{ value.end_time|formatDateDayTime }}
            </td>
          </tr>
        </tbody>
      </template>
    </v-simple-table>
    <v-card-text v-else>
      <div
        class="d-flex justify-center align-center"
        style="min-height: 100px;"
      >
        <v-progress-circular
          color="primary"
          indeterminate
          size="32"
        />
      </div>
    </v-card-text>
  </v-card>
</template>

<script>

export default {
  props: {
    app: {
      type: String,
      default: null,
    },
    userId: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      loading: true,
      items: null,
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    async getData() {
      this.loading = true;
      this.items = null;
      const response = await this.axios.get(`/api/gateway/wla-admin/user/${this.userId}/cards?app=${this.app}`);

      if (response.status === 200) {
        this.items = Object.entries(response.data.items);
      }
      this.loading = false;
    },
  },
};
</script>
