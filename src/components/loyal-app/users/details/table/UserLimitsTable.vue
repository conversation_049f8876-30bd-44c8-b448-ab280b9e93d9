<template>
  <v-card
    class="px-3 py-3"
    outlined
    color="blue-grey lighten-5"
  >
    <h3 class="mb-3">
      {{ $t('loyalApp_limit') }}
    </h3>
    <v-simple-table
      v-if="items.length"
      dense
      class="elevation-3 py-2"
    >
      <template #default>
        <tbody>
          <tr>
            <td class="border-right" />
            <td
              v-for="[key, header] in headers"
              :key="`headers-${key}`"
              class="border-right font-weight-bold"
            >
              {{ header }}
            </td>
          </tr>
          <tr
            v-for="[key, value] in items"
            :key="`item-${key}`"
          >
            <td
              :key="`itemName-${key}`"
              class="border-right text-left font-weight-bold"
            >
              {{ key }}
            </td>
            <template
              v-for="[key2, arrayItemValue] in Object.entries(value)"
            >
              <td
                :key="`item-${key}-item-array-value-${key2}`"
                class="border-right"
              >
                <currency-formatter
                  v-if="arrayItemValue !== null"
                  :value="arrayItemValue"
                  :symbol="currencySymbol"
                />
              </td>
            </template>
          </tr>
        </tbody>
      </template>
    </v-simple-table>
    <v-card-text v-else>
      <v-icon small>
        mdi-alert-circle-outline
      </v-icon>
      <i class="ml-2">{{ $t('common_noData') }}</i>
    </v-card-text>
  </v-card>
</template>

<script>
import CurrencyFormatter from '@/components/common/formatters/CurrencyFormatter.vue';

export default {
  components: {
    CurrencyFormatter,
  },
  props: {
    data: {
      type: Object,
      required: true,
    },
    currencySymbol: {
      type: String,
      required: true,
    },
  },
  computed: {
    headers() {
      if (typeof this.data === 'undefined') {
        return [];
      }

      const firstKey = Object.keys(this.data)[0];

      if (typeof firstKey === 'undefined') {
        return [];
      }

      return Object.entries(Object.keys(this.data[firstKey]));
    },
    items() {
      if (typeof this.data === 'undefined') {
        return [];
      }

      return Object.entries(this.data);
    },
  },
};
</script>
