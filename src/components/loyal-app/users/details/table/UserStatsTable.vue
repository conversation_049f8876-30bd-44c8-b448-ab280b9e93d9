<template>
  <v-card
    class="px-3 py-3"
    outlined
    color="blue-grey lighten-5"
  >
    <v-row class="mb-3 align-center justify-space-between">
      <v-col>
        <h3>
          {{ $t('loyalApp_stats') }}
        </h3>
      </v-col>
      <v-col>
        <date-select
          v-model="dateConfig"
          dense
          :show-custom="false"
        />
      </v-col>
    </v-row>
    <v-simple-table
      v-if="!loading && items.length"
      dense
      class="elevation-3 py-2"
    >
      <template #default>
        <tbody>
          <tr>
            <td
              v-for="[key, header] in headers"
              :key="`headers-${key}`"
              class="border-right font-weight-bold"
            >
              {{ header }}
            </td>
          </tr>
          <tr
            v-for="[key, value] in items"
            :key="`item-${key}`"
          >
            <template
              v-for="[key2, arrayItemValue] in Object.entries(value)"
            >
              <td
                :key="`item-${key}-item-array-value-${key2}`"
                class="border-right "
              >
                <template v-if="key2 === 'value' || key2 === 'avg'">
                  <currency-formatter
                    :value="arrayItemValue"
                    :symbol="currencySymbol"
                  />
                </template>
                <template v-else>
                  {{ arrayItemValue }}
                </template>
              </td>
            </template>
          </tr>
        </tbody>
      </template>
    </v-simple-table>
    <v-card-text v-else-if="loading">
      <div
        class="d-flex justify-center align-center"
        style="min-height: 100px;"
      >
        <v-progress-circular
          color="primary"
          indeterminate
          size="32"
        />
      </div>
    </v-card-text>
    <v-card-text v-else>
      <v-icon small>
        mdi-alert-circle-outline
      </v-icon>
      <i class="ml-2">{{ $t('common_noData') }}</i>
    </v-card-text>
  </v-card>
</template>

<script>
import CurrencyFormatter from '@/components/common/formatters/CurrencyFormatter.vue';
import DateSelect from '@components/reports/filters/DateSelect.vue';

export default {
  components: {
    CurrencyFormatter,
    DateSelect,
  },
  props: {
    app: {
      type: String,
      required: true,
    },
    userId: {
      type: Number,
      required: true,
    },

  },
  data() {
    return {
      loading: true,
      stats: [],
      currencySymbol: null,
      dateConfig: {
        interval: 'thisYear',
      },
    };
  },
  computed: {
    currency() {
      if (this.currencySymbol === null) {
        return '';
      }

      return this.currencySymbol;
    },
    headers() {
      if (typeof this.stats === 'undefined') {
        return [];
      }

      const firstKey = Object.keys(this.stats)[0];

      if (typeof firstKey === 'undefined') {
        return [];
      }

      return Object.entries(Object.keys(this.stats[firstKey]));
    },
    items() {
      if (typeof this.stats === 'undefined' || this.stats === null) {
        return [];
      }

      return Object.entries(this.stats);
    },
  },
  watch: {
    dateConfig: {
      handler() {
        this.getData();
      },
      deep: true,
    },
  },
  mounted() {
    this.getData();
  },
  methods: {
    async getData() {
      this.loading = true;

      const url = `/api/gateway/wla-admin/user/${this.userId}/stats`;

      const response = await this.axios.get(url, {
        params: {
          app: this.app,
          interval: this.dateConfig?.interval,
        },
      });
      const { data } = response;

      this.stats = data.stats;
      this.currencySymbol = data.currency;

      this.loading = false;
    },
  },
};
</script>
