<template>
  <div>
    <v-tooltip bottom>
      <template #activator="{ on, attrs }">
        <v-btn
          tile
          rounded
          x-small
          fab
          elevation="1"
          color="primary"
          class="mr-2"
          v-bind="attrs"
          v-on="on"
          @click.stop
          @click.native="openDialog"
        >
          <v-icon>
            {{ hasData ? 'mdi-pencil' : 'mdi-plus' }}
          </v-icon>
        </v-btn>
      </template>
      <span>{{ hasData ? $t('actions.edit') : $t('actions.add') }}</span>
    </v-tooltip>

    <generic-modal
      v-model="dialog"
      :title="$t('common_clientInvoiceData')"
      :loading="loading"
      :cancel-button-text="$t('actions.return_to_list')"
      @submit="submit"
      @close="close"
    >
      <form
        class="mt-6"
      >
        <v-text-field
          v-model="email"
          v-validate="'required|email|max:128'"
          :label="$t('common_email')"
          :counter="128"
          :error-messages="errors.collect('email')"
          data-vv-name="email"
          :data-vv-as="$t('common_email')"
          :disabled="loading"
          required
        />
        <v-text-field
          v-model="taxNumber"
          v-validate="'required|max:20'"
          :label="$t('common_taxNumber')"
          :error-messages="errors.collect('nip')"
          data-vv-name="nip"
          :data-vv-as="$t('common_taxNumber')"
          :disabled="loading"
          required
        />
        <v-text-field
          v-model="name"
          v-validate="'required|max:128'"
          :label="$t('common_invoiceCompanySettingsName')"
          :counter="128"
          :error-messages="errors.collect('name')"
          data-vv-name="name"
          :data-vv-as="$t('common_invoiceCompanySettingsName')"
          :disabled="loading"
          required
        />
        <v-text-field
          v-model="address"
          v-validate="'required|max:255'"
          :label="$t('common_formAddress')"
          :counter="255"
          :error-messages="errors.collect('address')"
          data-vv-name="address"
          :data-vv-as="$t('common_formAddress')"
          :disabled="loading"
          required
        />
        <v-text-field
          v-model="postCode"
          v-validate="'required|max:12'"
          :label="$t('common_postCode')"
          :counter="12"
          :error-messages="errors.collect('post_code')"
          data-vv-name="post_code"
          :data-vv-as="$t('common_postCode')"
          :disabled="loading"
          required
        />
        <v-text-field
          v-model="city"
          v-validate="'required|alpha_spaces|max:128'"
          :label="$t('common_city')"
          :counter="128"
          :error-messages="errors.collect('city')"
          data-vv-name="city"
          :data-vv-as="$t('common_city')"
          :disabled="loading"
          required
        />
        <v-select
          v-model="country"
          v-validate="'required'"
          :items="countries"
          item-text="name"
          item-value="shortName"
          :label="$t('common_country')"
          :error-messages="errors.collect('country')"
          data-vv-name="country"
          :data-vv-as="$t('common_country')"
          :disabled="loading"
          :autocomplete="true"
          required
        />
        <v-switch
          v-model="invoicedAfterTransaction"
          :disabled="disabledSwitches.invoicedAfterTransaction"
        >
          <template #label>
            <div class="pl-3 py-0">
              {{ $t('loyalApp_invoicedAfterTransaction') }}
            </div>
          </template>
        </v-switch>
      </form>
    </generic-modal>
  </div>
</template>

<script>
import axios from '@plugins/axios';
import { mapGetters } from 'vuex';
import GenericModal from '@components/common/GenericModal.vue';

export default {
  components: {
    GenericModal,
  },
  props: {
    app: {
      type: String,
      default: null,
    },
    userId: {
      type: Number,
      default: null,
    },
    hasData: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialog: false,
      loading: false,
      dataExists: false,
      disabledSwitches: {
        invoicedAfterTransaction: (
          this.$store.state.auth.user.fleet_manager
          || this.$store.state.auth.user.trusted_partner) === 1,
      },
      email: '',
      taxNumber: '',
      tax_countries: [
        'PL',
        'CS',
        'EN',
        'FR',
      ],
      name: '',
      address: '',
      postCode: '',
      city: '',
      province: '',
      country: '',
      invoicedAfterTransaction: false,
      countries: [],
      termsOfUseAccepted: this.$store.state.auth.user.terms,
    };
  },
  computed: {
    ...mapGetters({
      taxPayerInfo: 'branding/tax_payer_info',
    }),
  },
  watch: {
    async dialog(val) {
      if (val) {
        this.loading = true;
        await this.getCountriesData();
        await this.getData();
        this.loading = false;
      }
    },
  },
  methods: {
    async getCountriesData() {
      const response = await this.axios.get(
        '/api/lists/countries',
      );

      if (response.status === 200 && response.data) {
        this.countries = response.data;
      }
    },
    save() {
      this.loading = true;
      axios.put(
        `/api/gateway/wla-admin/user/${this.userId}/invoice_data?app=${this.app}`,
        {
          email: this.email,
          tax_number: this.taxNumber,
          name: this.name,
          address: this.address,
          city: this.city,
          country: this.country,
          post_code: this.postCode,
          invoiced_after_transaction: this.invoicedAfterTransaction,
        },
      )
        .then(() => {
          this.loading = false;
          this.dialog = false;
          this.$emit('reload');
        }, (error) => {
          if (error.response.status === 400) {
            if (!Number.isInteger(this.taxNumber)) {
              this.snackbar.showMessage(
                'error',
                this.$i18n.t('alert.nip_number_incorrect'),
              );
            }
            this.loading = false;
          }
        });
    },
    async getData() {
      this.loading = true;
      this.$validator.errors.clear();
      const response = await this.axios.get(`/api/gateway/wla-admin/user/${this.userId}/invoice_data?app=${this.app}`);

      if (response.status === 200) {
        const resData = response.data;

        if (resData === null) {
          this.loading = false;
          this.dataExists = false;
          return;
        }

        this.email = resData.email;
        this.address = resData.address;
        this.city = resData.city;
        this.country = resData.country || null;
        this.name = resData.name;
        this.postCode = resData.post_code;
        this.taxNumber = resData.tax_number;
        this.invoicedAfterTransaction = resData.invoiced_after_transaction;
        this.dataExists = true;

        if (this.$store.state.auth.user.trusted_partner) {
          this.invoicedAfterTransaction = true;
        }
      }
    },
    submit() {
      this.$validator.validateAll()
        .then((result) => {
          if (result) {
            this.save();
          }
        });
    },
    openDialog() {
      this.dialog = true;
      this.getData();
    },
    close() {
      this.dialog = false;
    },
  },
};
</script>
