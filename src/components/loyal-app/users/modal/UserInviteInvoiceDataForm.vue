<template>
  <v-expansion-panels
    v-model="expandedInternal"
    class="mb-3"
  >
    <v-expansion-panel>
      <v-expansion-panel-header>
        <v-row>
          <v-icon>mdi-file-document-edit-outline</v-icon>
          <h6 class="text-h6 ml-2">
            {{ $t('common_dataToInvoice') }}
          </h6>
        </v-row>
      </v-expansion-panel-header>
      <v-expansion-panel-content>
        <v-text-field
          v-model="invoiceDataInternal.taxNumber"
          prepend-icon="mdi-card-account-details"
          :label="$t('common_taxNumber')"
          :validate-on-blur="validateOnBlur"
          @input="updateInvoiceData"
        />
        <v-text-field
          v-model="invoiceDataInternal.name"
          prepend-icon="mdi-domain"
          :label="companyNameLabel"
          :rules="companyNameRules"
          :validate-on-blur="validateOnBlur"
          @input="updateInvoiceData"
        />
        <v-text-field
          v-model="invoiceDataInternal.address"
          prepend-icon="mdi-map-marker"
          :label="$t('common_address')"
          :validate-on-blur="validateOnBlur"
          @input="updateInvoiceData"
        />
        <v-text-field
          v-model="invoiceDataInternal.postCode"
          prepend-icon="mdi-mailbox"
          :label="$t('common_postCode')"
          :validate-on-blur="validateOnBlur"
          @input="updateInvoiceData"
        />
        <v-text-field
          v-model="invoiceDataInternal.city"
          prepend-icon="mdi-city"
          :label="$t('common_city')"
          :validate-on-blur="validateOnBlur"
          @input="updateInvoiceData"
        />
        <v-select
          v-model="invoiceDataInternal.country"
          :items="countries"
          item-text="name"
          item-value="shortName"
          prepend-icon="mdi-earth"
          :label="$t('common_country')"
          :loading="countriesLoader"
          :disabled="countriesLoader"
          clearable
          :validate-on-blur="validateOnBlur"
          @input="updateInvoiceData"
        />
      </v-expansion-panel-content>
    </v-expansion-panel>
  </v-expansion-panels>
</template>

<script>

export default {
  props: {
    value: {
      type: Object,
      default: () => ({
        name: '',
        taxNumber: '',
        address: '',
        postCode: '',
        city: '',
        country: '',
      }),
    },
    expanded: {
      type: [Number, Array],
      default: null,
    },
    validateOnBlur: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      countries: [],
      countriesLoader: false,
      invoiceDataInternal: {
        name: '',
        taxNumber: '',
        address: '',
        postCode: '',
        city: '',
        country: '',
      },
    };
  },
  computed: {
    expandedInternal: {
      get() {
        return this.expanded;
      },
      set(value) {
        this.$emit('update:expanded', value);
      },
    },
    hasAnyInvoiceData() {
      const data = this.invoiceDataInternal;
      return !!(data.taxNumber || data.address || data.postCode || data.city || data.country);
    },
    companyNameLabel() {
      const baseLabel = this.$t('table.company_name');
      return this.hasAnyInvoiceData ? `${baseLabel} *` : baseLabel;
    },
    companyNameRules() {
      if (!this.hasAnyInvoiceData) {
        return [];
      }
      return [
        (v) => !!v || this.$t('common_fieldRequired'),
      ];
    },
  },
  watch: {
    value: {
      handler(newValue) {
        this.invoiceDataInternal = { ...newValue };
      },
      immediate: true,
      deep: true,
    },
    invoiceDataInternal: {
      handler() {
        this.$nextTick(() => {
          this.validateForm();
        });
      },
      deep: true,
    },
  },
  created() {
    this.getCountries();
  },
  methods: {
    updateInvoiceData() {
      this.$emit('input', { ...this.invoiceDataInternal });
      this.validateForm();
    },
    validateForm() {
      const isValid = !this.hasAnyInvoiceData || !!this.invoiceDataInternal.name;
      this.$emit('validation-change', isValid);
    },
    clearData() {
      this.invoiceDataInternal = {
        name: '',
        taxNumber: '',
        address: '',
        postCode: '',
        city: '',
        country: '',
      };
      this.updateInvoiceData();
    },
    isValid() {
      return !this.hasAnyInvoiceData || !!this.invoiceDataInternal.name;
    },
    async getCountries() {
      this.countriesLoader = true;
      try {
        const response = await this.axios.get('/api/lists/countries');
        if (response.status === 200 && response.data) {
          this.countries = response.data;
        }
      } catch (error) {
        this.snackbar.showMessage('error', this.$t('common_error_occurred'));
      } finally {
        this.countriesLoader = false;
      }
    },
  },
};
</script>
