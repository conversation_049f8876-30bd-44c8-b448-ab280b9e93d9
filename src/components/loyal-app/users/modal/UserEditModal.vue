<template>
  <div>
    <btn-edit
      :text="$t('actions.edit')"
      @click="openDialog"
    />
    <generic-modal
      v-if="user"
      v-model="dialog"
      :title="`${$t('loyalApp_userEdit')} : ${user.email}`"
      :loading="loaders.actualize"
      :confirm-button-text="$t('actions.save')"
      :cancel-button-text="$t('actions.return_to_list')"
      max-width="800"
      scrollable
      @submit="submit"
      @close="closeDialog"
    >
      <v-progress-circular
        v-if="loaders.site"
        class="circleProgress"
        :size="90"
        :width="7"
        color="primary"
        indeterminate
      />
      <template v-if="!loaders.site">
        <v-alert
          :value="true"
          :color="isfleet_managerColor + ' lighten-4'"
          :icon="isfleet_managerAlertIcon"
          border="left"
          prominent
          dense
        >
          <v-layout wrap>
            <v-col
              v-if="user.fleet_manager"
              cols="12"
              sm="7"
              md="7"
              class="blue--text text--darken-2"
            >
              <strong>{{ $t('loyalApp_fleetManagerActive') }}.</strong>
            </v-col>
            <v-col
              v-else
              cols="12"
              sm="7"
              md="7"
              class="blue--text text--darken-2"
            >
              <strong>{{ $t('loyalApp_fleetManagerInactive') }}.</strong>
            </v-col>
          </v-layout>
        </v-alert>
        <v-alert
          :value="true"
          prominent
          dense
          border="left"
          :color="isTrustedParnterColor + ' lighten-4'"
          :icon="isTrustedParnterAlertIcon"
        >
          <v-layout wrap>
            <v-col
              v-show="user.trusted_partner"
              cols="12"
              sm="7"
              md="7"
              class="orange--text text--darken-2"
            >
              <strong>{{ $t('loyalApp_trustedPartnerActive') }}.</strong> {{
                $t('loyalApp_trustedPartnerActiveMessage')
              }}
            </v-col>
            <v-col
              v-show="!user.trusted_partner"
              cols="12"
              sm="7"
              md="7"
              class="orange--text text--darken-2"
            >
              <strong>{{ $t('loyalApp_trustedPartnerInactive') }}.</strong> {{
                $t('loyalApp_trustedPartnerInactiveMessage')
              }}
            </v-col>
            <v-col
              offset-xs="5"
              offset-sm1
              offset-md="1"
              cols="7"
              sm="4"
              md="4"
              pt-20
            >
              <v-switch
                v-model="user.trusted_partner"
                :sync="true"
                color="orange"
                :disabled="!buttons.trusted_partner"
                hide-details
              >
                <template #label>
                  <div class="pl-3 py-0">
                    {{ isTrustedParnterSwitchText }}
                  </div>
                </template>
              </v-switch>
            </v-col>
          </v-layout>
        </v-alert>
        <v-layout
          v-show="user.trusted_partner"
          v-if="user.post_paid_settings"
          wrap
        >
          <v-col
            cols="12"
            sm="6"
            md="6"
          >
            <v-text-field
              v-model="user.post_paid_settings.min"
              name="min"
              autofocus
              ype="number"
              prepend-icon="mdi-download"
              :label="$t('loyalApp_min')"
              required
              @change="user.post_paid_settings.min = parseInt(user.post_paid_settings.min)"
            />
          </v-col>
          <v-col
            cols="12"
            sm="6"
            md="6"
          >
            <v-text-field
              v-model="user.post_paid_settings.max"
              name="max"
              autofocus
              ype="number"
              prepend-icon="mdi-upload"
              :label="$t('loyalApp_max')"
              required
              @change="user.post_paid_settings.max = parseInt(user.post_paid_settings.max)"
            />
          </v-col>
          <v-col
            cols="12"
            sm="6"
            md="6"
          >
            <v-text-field
              v-model="user.post_paid_settings.discount"
              name="discount"
              autofocus
              ype="number"
              prepend-icon="mdi-percent"
              :label="$t('loyalApp_discount')"
              required
              @change="user.post_paid_settings.discount
                = parseInt(user.post_paid_settings.discount)"
            />
          </v-col>
          <v-col
            cols="12"
            sm="6"
            md="6"
          >
            <v-autocomplete
              v-model="user.post_paid_settings.payment_term"
              item-value="id"
              :label="$t('loyalApp_term')"
              :items="paymentTermOptions"
              :autocomplete="true"
              prepend-icon="mdi-update"
              name="paymentPeriodType"
              :data-vv-as="`${$t('common_paymentPeriod')}`"
            />
          </v-col>
        </v-layout>
      </template>
    </generic-modal>
  </div>
</template>

<script>
import GenericModal from '@components/common/GenericModal.vue';
import BtnEdit from '@/components/common/button/BtnEdit.vue';

export default {
  components: {
    GenericModal,
    BtnEdit,
  },
  props: {
    app: {
      type: String,
      default: null,
    },
    userId: {
      type: Number,
      required: true,
    },
  },
  data() {
    return {
      paymentTermOptions: [
        {
          id: 'P0D',
          text: this.$t('common_0'),
        },
        {
          id: 'P1D',
          text: this.$t('common_1'),
        },
        {
          id: 'P3D',
          text: this.$t('common_3'),
        },
        {
          id: 'P5D',
          text: this.$t('common_5'),
        },
        {
          id: 'P7D',
          text: this.$t('common_7'),
        },
        {
          id: 'P10D',
          text: this.$t('common_10'),
        },
        {
          id: 'P14D',
          text: this.$t('common_14'),
        },
        {
          id: 'P30D',
          text: this.$t('loyalApp_30'),
        },
      ],
      loaders: {
        site: true,
        actualize: false,
      },
      buttons: {
        fleet_managerToggle: true,
        trusted_partner: true,
      },
      user: {
        blocked: false,
        fleet_manager: false,
        trusted_partner: false,
        email: null,
        post_paid_settings: null,
      },
      isUserChanged: false,
      dialog: false,
    };
  },
  computed: {
    isfleet_managerAlertIcon() {
      return 'mdi-account-multiple';
    },
    isfleet_managerColor() {
      return 'blue';
    },
    isfleet_managerSwitchText() {
      return (this.user.fleet_manager) ? this.$t('loyalApp_fleetManager')
        : this.$t('loyalApp_regularUser');
    },
    isTrustedParnterAlertIcon() {
      return 'mdi-account-tie';
    },
    isTrustedParnterColor() {
      return 'orange';
    },
    isTrustedParnterSwitchText() {
      return (this.user.trusted_partner) ? this.$t('loyalApp_trustedPartner') : this.$t(
        'loyalApp_regularUser',
      );
    },
  },
  watch: {
    dialog() {
      // get card data only when dialog shows up
      if (this.dialog) {
        this.getData();
      }
    },
    user: {
      handler() {
        if (this.user.fleet_manager === false) {
          this.buttons.trusted_partner = false;
          this.user.trusted_partner = false;
        } else {
          this.buttons.trusted_partner = true;
        }
        this.isUserChanged = true;
      },
      deep: true,
    },
  },
  methods: {
    getData() {
      this.loaders.site = true;
      this.loaders.actualize = false;
      this.axios.get(
        `/api/gateway/wla-admin/user/${this.userId}?app=${this.app}`,
      )
        .then(
          (response) => {
            if ((response.status === 200) && response.data) {
              const user = response.data;
              this.user.blocked = user.blocked;
              this.user.fleet_manager = user.fleet_manager;
              this.user.trusted_partner = user.trusted_partner;
              this.user.email = user.email;
              this.user.post_paid_settings = user.post_paid_settings || {
                min: 0,
                max: 0,
                discount: 0,
                payment_term: 'P0D',
              };
              this.isUserChanged = false;
              this.loaders.site = false;
            }
          },
          () => {
            this.onError();
          },
        );
    },
    onError() {
      this.closeDialog();
    },
    submit() {
      this.loaders.site = true;
      this.loaders.actualize = true;
      this.axios.patch(
        `/api/gateway/wla-admin/user/${this.userId}?app=${this.app}`,
        this.user,
      )
        .then(
          (response) => {
            if (response.status === 200) {
              if (response.data.error) {
                // this.snackbar.showMessage(
                //   'error',
                //   this.$t('loyalApp_trustedPartnerWithoutfleet_manager'),
                // );
                // on error
                this.loaders.site = false;
                this.loaders.actualize = false;
              } else {
                this.closeDialog();
              }
            }
          },
          () => {
            // on error
            this.loaders.site = false;
            this.loaders.actualize = false;
          },
        );
    },
    closeDialog() {
      this.dialog = false;
    },
    openDialog() {
      this.dialog = true;
    },
  },
};
</script>
