<template>
  <div>
    <btn-top-up
      :text="$t('loyalApp_topupAccountTopup')"
      @click="openDialog"
    />

    <generic-modal
      v-model="dialog"
      :title="`${$t('loyalApp_topupAccountTitle')} ${userEmail}`"
      :loading="loaders.cardRefill"
      :confirm-button-text="$t('actions.refill')"
      :cancel-button-text="$t('actions.return_to_list')"
      :disabled="!forms.refill.valid"
      max-width="600"
      scrollable
      @submit="refillAccount"
      @close="closeDialog"
    >
      <v-form
        ref="formAccountRefill"
        v-model="forms.refill.valid"
        lazy-validation
      >
        <v-row>
          <v-col
            class="pt-0"
            cols="6"
          >
            <v-radio-group
              v-model="topUpType"
              dense
              :label="$t('loyalApp_topupType')"
            >
              <v-radio
                :label="$t('loyalApp_bonus')"
                value="bonus"
              />
              <v-radio
                :disabled="!trustedPartner"
                :label="$t('loyalApp_invoice')"
                value="transfer"
              />
            </v-radio-group>
          </v-col>
          <v-col
            cols="6"
          >
            <v-text-field
              v-model="value"
              prepend-icon="mdi-trending-up"
              :label="`${$t('loyalApp_topupByAmount')} (${currencySymbol})`"
              :rules="[
                forms.validationRules.positiveNumber,
                forms.validationRules.topupValue
              ]"
            />
          </v-col>
        </v-row>
      </v-form>
    </generic-modal>
  </div>
</template>

<script>
import GenericModal from '@components/common/GenericModal.vue';
import BtnTopUp from '@/components/common/button/BtnTopUp.vue';

export default {
  components: {
    GenericModal,
    BtnTopUp,
  },
  props: {
    app: {
      type: String,
      default: null,
    },
    userEmail: {
      type: String,
      default: null,
    },
    trustedPartner: {
      type: Boolean,
      default: null,
    },
    userId: {
      type: Number,
      default: null,
    },
    currencySymbol: {
      type: String,
      default: '!',
    },
  },
  data() {
    return {
      topUpType: 'bonus',
      value: 0,
      dialog: false,
      loaders: {
        cardRefill: false,
      },
      forms: {
        refill: {
          valid: true,
        },
        validationRules: {
          positiveNumber: (v) => Number(v) > 0 || this.$t(
            'loyaltyCards_topUpPositiveNumberOnly',
          ),
          topupValue: (v) => Number(v) < *********** || this.$t(
            'form.validation.top_up_value_to_big',
          ),
        },
      },
    };
  },
  methods: {
    refillAccount() {
      if (this.$refs.formAccountRefill.validate()) {
        this.loaders.cardRefill = true;
        this.axios.post(
          `/api/gateway/wla-admin/user/${this.userId}/${this.topUpType}?app=${this.app}`,
          {
            value: this.value * 100,
          },
        )
          .then(
            () => {
              this.loaders.cardRefill = false;
              this.snackbar.showMessage(
                'success',
                this.$t('common_topupSent'),
              );
              this.$emit('reload');
              this.closeDialog();
            },
            (error) => {
              this.loaders.cardRefill = false;
              if (error.request && error.request.status >= 400) {
                this.snackbar.showMessage(
                  'warning',
                  this.$t('loyalApp_problem'),
                );
              }
              this.closeDialog();
            },
          );
      }
    },
    closeDialog() {
      this.dialog = false;
    },
    openDialog() {
      this.dialog = true;
    },
  },
};
</script>
