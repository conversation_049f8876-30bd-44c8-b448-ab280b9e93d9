<template>
  <div>
    <btn-users
      :text="$t('loyalApp_fleetUsers')"
      @click="dialog = true"
    />

    <generic-modal
      v-model="dialog"
      :title="`${$t('loyalApp_fleetUsers')} - ${email}`"
      :loading="false"
      fullscreen
      :show-actions="false"
    >
      <div class="mt-10">
        <fleet-users-filters
          v-model="filters"
          :app="app"
          :user-id="userId"
          :base-url="baseUrl"
          @invitation-sent="onInvitationSent"
        />
        <fleet-users-list
          ref="fleetUsersList"
          :app="app"
          :base-url="baseUrl"
          :params="internalParams"
          :user-id="userId"
        />
      </div>
    </generic-modal>
  </div>
</template>

<script>
import GenericModal from '@components/common/GenericModal.vue';
import BtnUsers from '@/components/common/button/BtnUsers.vue';
import FleetUsersFilters from '../fleet-users/FleetUsersFilters.vue';
import FleetUsersList from '../fleet-users/FleetUsersList.vue';

export default {
  components: {
    GenericModal,
    FleetUsersFilters,
    FleetUsersList,
    BtnUsers,
  },
  props: {
    app: {
      type: String,
      required: true,
    },
    baseUrl: {
      type: String,
      required: true,
    },
    userId: {
      type: Number,
      required: true,
    },
    email: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
      filters: {},
    };
  },
  computed: {
    internalParams() {
      return {
        ...this.filters,
        app: this.app,
      };
    },
  },
  watch: {
    dialog(newVal) {
      if (newVal) {
        // Refresh data when modal is opened
        this.$nextTick(() => {
          if (this.$refs.fleetUsersList) {
            this.$refs.fleetUsersList.fetchData();
          }
        });
      }
    },
  },
  methods: {
    onInvitationSent() {
      // Refresh the fleet users list after invitation is sent
      if (this.$refs.fleetUsersList) {
        this.$refs.fleetUsersList.fetchData();
      }
    },
  },
};
</script>
