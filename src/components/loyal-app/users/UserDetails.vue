<template>
  <div>
    <btn-details
      :text="$t('admin_detailsButton')"
      @click="dialog = true"
    />

    <generic-modal
      v-model="dialog"
      :title="$t('admin_usersDetails')"
      :loading="false"
      fullscreen
      :show-actions="false"
    >
      <div>
        <!-- Main data section -->
        <div
          v-if="!loaders"
          class="mt-10"
        >
          <v-card
            v-if="item.alerts.length > 0"
            class="px-3"
            outlined
            color="blue-grey lighten-5"
          >
            <slot name="title">
              <h3 class="my-4">
                {{ $t('loyalApp_loyalappUserAlert') }}
              </h3>
            </slot>
            <alerts-list
              :alerts="item.alerts"
              :dismissible="false"
            />
          </v-card>
          <key-value-table
            :key="`client-details-${item.id}`"
            :header-text="$t('loyalApp_client')"
            :data="clientData"
            :translate-keys="true"
          >
            <template #title>
              <div class="d-flex justify-space-between mb-3">
                <h3 class="mr-2 mt-2">
                  {{ $t('loyalApp_client') }}
                </h3>
                <invoice-data-modal
                  :app="app"
                  :user-id="item.id"
                  :has-data="item.client !== null"
                  @reload="getData"
                />
              </div>
            </template>
          </key-value-table>
          <key-value-table
            v-if="item.info !== null"
            :key="`info-details-${item.id}`"
            :header-text="$t('loyalApp_info')"
            :data="item.info"
          />
          <limit-table
            v-if="item.limit !== null"
            :currency-symbol="item.currency.symbol"
            :data="item.limit"
          />
        </div>
        <div
          v-else
          class="d-flex justify-center align-center"
          style="min-height: 100px;"
        >
          <v-progress-circular
            color="primary"
            indeterminate
          />
        </div>

        <!-- Independent loading tables -->
        <stats-table
          ref="statsTable"
          :key="`stats-details-${userId}`"
          :user-id="userId"
          :app="app"
        />
        <cards-table
          ref="cardsTable"
          :key="`cards-info-${userId}`"
          :user-id="userId"
          :app="app"
        />
      </div>
    </generic-modal>
  </div>
</template>

<script>
import GenericModal from '@components/common/GenericModal.vue';
import AlertsList from '@components/common/AlertsList.vue';
import KeyValueTable from '@components/common/KeyValueTable.vue';
import StatsTable from '@/components/loyal-app/users/details/table/UserStatsTable.vue';
import LimitTable from '@/components/loyal-app/users/details/table/UserLimitsTable.vue';
import CardsTable from '@/components/loyal-app/users/details/table/UserCardsTable.vue';
import InvoiceDataModal from '@/components/loyal-app/users/details/InvoiceDataModal.vue';
import BtnDetails from '@/components/common/button/BtnDetails.vue';

export default {
  components: {
    GenericModal,
    AlertsList,
    CardsTable,
    KeyValueTable,
    InvoiceDataModal,
    LimitTable,
    StatsTable,
    BtnDetails,
  },
  props: {
    userId: {
      type: Number,
      required: true,
    },
    app: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
      item: null,
      loaders: true,
    };
  },
  computed: {
    clientData() {
      const { client } = this.item;
      if (!client) {
        return {};
      }

      return {
        ID: client.id,
        common_name: client.name,
        common_taxNumber: client.tax_number,
        common_address: client.address,
        common_postCode: client.post_code,
        common_city: client.city,
        common_country: client.country,
        common_email: client.email,
      };
    },
  },
  watch: {
    dialog(val) {
      if (val) {
        this.getData();
        this.refreshTables();
      } else {
        // Reset data when closing modal
        this.item = null;
        this.loaders = true;
      }
    },
  },

  methods: {
    getData() {
      this.loaders = true;
      this.axios.get(
        `/api/gateway/wla-admin/user/${this.userId}?app=${this.app}`,
      )
        .then(
          (response) => {
            if (response.status === 200) {
              this.item = response.data;
              this.loaders = false;
            }
          },
          () => {
            // on error
            this.loaders = false;
          },
        );
    },
    refreshTables() {
      // Force refresh of child tables by updating their keys
      this.$nextTick(() => {
        const { statsTable, cardsTable } = this.$refs;

        if (statsTable && statsTable.getData) {
          statsTable.getData();
        }
        if (cardsTable && cardsTable.getData) {
          cardsTable.getData();
        }
      });
    },
  },
};
</script>
