<template>
  <report-data-table
    ref="dataTable"
    report="v2\WlaUsersReport"
    :title="$t('loyalApp_usersListHeading')"
    :headers="headers"
    :filters="paramsInternal"
    :single-expand="true"
  >
    <template #table-actions>
      <user-invite-modal
        :app="app"
        @reload="fetchData"
      />
      <notification-modal
        :url="`/api/gateway/wla-admin/users/send?app=${app}`"
        @success="message => snackbar.showMessage({ content: message, color: 'success' })"
        @error="message => snackbar.showMessage({ content: message, color: 'error' })"
      />
    </template>

    <template #[`item.email`]="{ item }">
      <div class="d-flex align-center">
        <v-tooltip
          v-if="item.alerts && item.alerts.length"
          right
        >
          <template #activator="{ on, attrs }">
            <div
              v-bind="attrs"
              v-on="on"
            >
              <v-icon class="mr-2">
                mdi-alert
              </v-icon>
              <span class="font-weight-bold"><email-formatter :value="item.email" /></span>
            </div>
          </template>
          <span>{{ $t('loyalApp_loyalappExpandDetailsToSeeAlerts') }}</span>
        </v-tooltip>
        <span
          v-else
          class="font-weight-bold"
        > <email-formatter :value="item.email" /> </span>
      </div>
    </template>

    <template #[`item.trustedPartner`]="{ item }">
      <v-icon
        v-if="item.trustedPartner"
      >
        mdi-check
      </v-icon>
      <div v-else />
    </template>

    <template #[`item.balance`]="{ item }">
      <v-tooltip
        v-if="item.fleetMember"
        left
      >
        <template #activator="{ on, attrs }">
          <div
            v-bind="attrs"
            v-on="on"
          >
            {{ $t('loyalApp_balanceFleetMemberNotify') }}
          </div>
        </template>
        <span>{{ $t('loyalApp_balanceFleetMemberTooltip') }}</span>
      </v-tooltip>
      <div v-else>
        <currency-formatter
          v-if="item.balance !== null"
          :value="item.balance"
          :symbol="item.currencySymbol"
        />
      </div>
    </template>

    <template #[`item.actions`]="{ item }">
      <v-row
        justify="end"
        class="d-flex align-center"
      >
        <fleet-users-modal
          v-if="item.fleetManager"
          :app="app"
          :base-url="`/api/gateway/wla-admin`"
          :user-id="item.id"
          :email="item.email"
        />

        <notification-modal
          v-if="item.notificable"
          :url="`/api/gateway/wla-admin/user/${item.id}/send?app=${app}`"
          @success="message => snackbar.showMessage({ content: message, color: 'success' })"
          @error="message => snackbar.showMessage({ content: message, color: 'error' })"
        />
        <user-edit-modal
          :user-id="item.id"
          :app="app"
          @reload="fetchData"
        />
        <user-top-up-modal
          :app="app"
          :user-id="item.id"
          :user-email="item.email"
          :currency-symbol="item.currencySymbol"
          :trusted-partner="item.trustedPartner"
          @reload="fetchData"
        />
      </v-row>
    </template>

    <template
      #[`item.details`]="{ item }"
    >
      <user-details
        :key="`user-details-${item.id}`"
        :app="app"
        :user-id="item.id"
      />
    </template>
  </report-data-table>
</template>

<script>
import ReportDataTable from '@/components/reports/ReportDataTable.vue';
import CurrencyFormatter from '@/components/common/formatters/CurrencyFormatter.vue';
import UserEditModal from '@/components/loyal-app/users/modal/UserEditModal.vue';
import UserInviteModal from '@/components/loyal-app/users/modal/UserInviteModal.vue';
import UserTopUpModal from '@/components/loyal-app/users/modal/UserTopUpModal.vue';
import UserDetails from '@/components/loyal-app/users/UserDetails.vue';
import NotificationModal from '@/components/common/modal/NotificationModal.vue';
import EmailFormatter from '@components/common/formatters/EmailFormatter.vue';
import FleetUsersModal from './modal/FleetUsersModal.vue';

export default {
  components: {
    EmailFormatter,
    ReportDataTable,
    CurrencyFormatter,
    UserEditModal,
    UserInviteModal,
    UserTopUpModal,
    UserDetails,
    NotificationModal,
    FleetUsersModal,
  },
  props: {
    app: {
      type: String,
      required: true,
    },
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    paramsInternal() {
      return {
        ...this.params,
        app: this.app,
      };
    },
    headers() {
      return [
        {
          text: this.$t('common_id'),
          value: 'id',
          showInRowExpand: true,
          sortable: false,
          protected: true,
        },
        {
          text: this.$t('common_email'),
          value: 'email',
          showInRowExpand: true,
          sortable: true,
          protected: true,
        },
        {
          text: this.$t('table.company_name'),
          value: 'companyName',
          showInRowExpand: true,
          sortable: true,
          protected: true,
        },
        {
          text: this.$t('common_tableCreateDate'),
          value: 'ctime',
          showInRowExpand: true,
          displayMethod: 'date',
          sortable: true,
        },
        {
          text: this.$t('common_lastUsage'),
          value: 'lastUsage',
          showInRowExpand: true,
          sortable: true,
        },
        {
          text: this.$t('loyalApp_tableTrustedPartner'),
          value: 'trustedPartner',
          showInRowExpand: true,
          align: 'center',
          sortable: false,
        },
        {
          text: `${this.$t('loyalApp_balance')}`,
          value: 'balance',
          showInRowExpand: true,
          align: 'start',
          sortable: true,
        },
        {
          text: this.$t('actions.actions'),
          value: 'actions',
          align: 'right',
          sortable: false,
        },
        {
          text: '',
          value: 'details',
          align: 'right',
          sortable: false,
          width: '1%',
        },
      ];
    },
  },
  methods: {
    fetchData() {
      this.$refs.dataTable.fetchData();
    },
  },
};
</script>
