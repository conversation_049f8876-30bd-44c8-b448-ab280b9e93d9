<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
    card
  >
    <v-row>
      <v-col>
        <text-search v-model="config.search" />
      </v-col>
      <v-col>
        <switch-select
          v-model="config.trustedPartner"
          :label="$t('loyalApp_tableTrustedPartner')"
        />
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import debounce from 'lodash/debounce';
import TextSearch from '@components/reports/filters/TextSearch.vue';
import SwitchSelect from '@components/reports/filters/SwitchSelect.vue';

const SettingsKey = 'wla-users-filter';

export default {
  components: {
    TextSearch,
    SwitchSelect,
  },
  model: {
    prop: 'param',
    event: 'update:param',
  },
  props: {
    param: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    const savedConfig = JSON.parse(localStorage.getItem(SettingsKey) || '{}');

    return {
      config: {
        search: savedConfig.search ?? null,
        trustedPartner: savedConfig.trustedPartner ?? null,
      },
    };
  },
  computed: {
    trustedPartnerFilters() {
      return [
        { text: this.$t('yes'), value: true },
        { text: this.$t('no'), value: false },
      ];
    },
    internalParam() {
      return {
        search: this.config.search,
        trustedPartner: this.config.trustedPartner === true ? true : null,
      };
    },
  },
  watch: {
    internalParam: {
      handler(newVal) {
        this.debouncedUpdate(newVal);
      },
      deep: true,
      immediate: true,
    },
    config: {
      handler(newVal) {
        localStorage.setItem(SettingsKey, JSON.stringify(newVal));
      },
      deep: true,
    },
  },
  beforeCreate() {
    this.debouncedUpdate = debounce((val) => {
      this.$emit('update:param', val);
    }, 300);
  },

};
</script>
