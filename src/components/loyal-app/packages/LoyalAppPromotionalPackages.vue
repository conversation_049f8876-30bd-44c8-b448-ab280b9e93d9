<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
  >
    <v-row>
      <v-col
        cols="12"
        class="px-0"
      >
        <v-row>
          <v-col
            cols="12"
            sm="12"
            class="px-6"
          >
            <v-layout justify-end>
              <btn-refresh
                class="mt-0"
                size="small"
                :disabled="loader"
                @click="getData"
              />
              <v-btn
                small
                color="primary"
                class="mt-1"
                @click.native="openModal('addPackageDialog')"
              >
                <v-icon left>
                  mdi-plus
                </v-icon>
                {{ $t('actions.add_package') }}
              </v-btn>
            </v-layout>
          </v-col>
        </v-row>
      </v-col>
      <v-col
        cols="12"
        class="px-0"
      >
        <v-data-table
          v-resize="onResize"
          :headers="dataTable.headers"
          :items="dataTable.items"
          item-key="id"
          :options.sync="pagination"
          :loading="loader"
          :footer-props="dataTable.footerProps"
        >
          <template #item="{ item }">
            <template v-if="!loader">
              <tr>
                <td class="text-sm-start pl-5">
                  {{ item.id }}
                </td>
                <td class="text-sm-start">
                  {{ item.title }}
                </td>
                <td class="text-sm-center">
                  {{ item.payment_value }}
                </td>
                <td class="text-sm-center">
                  {{ item.package_value }}
                </td>
                <td class="text-sm-center hidden-md-and-down md-and-up ">
                  {{ item.description }}
                </td>
                <td class="text-sm-center hidden-md-and-down md-and-up ">
                  {{ item.valid_time }}
                </td>
                <td class="text-sm-center hidden-md-and-down md-and-up ">
                  {{ item.start_time|formatDateDay }}
                </td>
                <td class="text-sm-center hidden-md-and-down md-and-up ">
                  {{ item.end_time|formatDateDay }}
                </td>
                <td class="text-sm-center">
                  <v-tooltip bottom>
                    <template #activator="{ on, attrs }">
                      <v-btn
                        v-bind="attrs"
                        x-small
                        tile
                        fab
                        elevation="1"
                        color="secondary"
                        class="mr-1"
                        v-on="on"
                        @click.stop
                        @click.native="openModal(`editPackageDialog${item.id}`, {package: item})"
                      >
                        <v-icon>mdi-pencil</v-icon>
                      </v-btn>
                    </template>
                    <span>{{ $t('actions.edit') }}</span>
                  </v-tooltip>
                  <package-edit-modal
                    :key="`editPackageDialog${item.id}`"
                    :ref="`editPackageDialog${item.id}`"
                    :app="app"
                    :package="item"
                    @reload-package-list="getData"
                  />
                  <v-tooltip bottom>
                    <template #activator="{ on, attrs }">
                      <v-btn
                        v-bind="attrs"
                        x-small
                        tile
                        fab
                        elevation="1"
                        color="white--text red"
                        class="ml-1"
                        v-on="on"
                        @click.stop
                        @click.native="handleDelete(item.id)"
                      >
                        <v-icon>mdi-delete-outline</v-icon>
                      </v-btn>
                    </template>
                    <span>{{ $t('actions.delete') }}</span>
                  </v-tooltip>
                </td>
              </tr>
            </template>
          </template>
        </v-data-table>
      </v-col>
    </v-row>
    <package-add-modal
      ref="addPackageDialog"
      :app="app"
      @reload-package-list="getData"
    />
  </v-container>
</template>

<script>
import BtnRefresh from '@/components/common/button/BtnRefresh.vue';
import PackageAddModal from '@components/loyal-app/packages/PackageAddModal.vue';
import PackageEditModal from '@components/loyal-app/packages/PackageEditModal.vue';

export default {
  components: {
    PackageAddModal,
    PackageEditModal,
    BtnRefresh,
  },
  props: {
    app: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      loader: true,
      filtering: {
        search: {
          text: '',
        },
      },
      pagination: {
        sortBy: ['id'],
        sortDesc: [true],
        page: 1,
        itemsPerPage: 25,
      },
      dataTable: {
        footerProps: {
          'items-per-page-options': [10, 25, 50],
          'items-per-page-text': this.$t('common_rowsPerPage'),
        },
        headers: [
          {
            text: this.$t('common_id'),
            value: 'id',
            class: 'text-sm-start',
            showInRowExpand: false,
            sortable: false,
          },
          {
            text: this.$t('loyalApp_title'),
            value: 'title',
            class: 'text-sm-start',
            showInRowExpand: false,
            sortable: false,
          },
          {
            text: this.$t('loyalApp_value'),
            value: 'payment_value',
            class: 'text-sm-center',
            showInRowExpand: false,
            sortable: false,
          },
          {
            text: this.$t('loyalApp_packageValue'),
            value: 'package_value',
            class: 'text-sm-center',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('common_tableDescription'),
            value: 'description',
            class: 'hidden-md-and-down md-and-up text-sm-center',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('loyalApp_valid_time'),
            value: 'valid_time',
            class: 'hidden-md-and-down md-and-up text-sm-center',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('loyaltyApp_startTime'),
            value: 'start_time',
            class: 'hidden-md-and-down md-and-up text-sm-center',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('loyaltyApp_endTime'),
            value: 'end_time',
            class: 'hidden-md-and-down md-and-up text-sm-center',
            showInRowExpand: true,
            sortable: false,
          },
          {
            text: this.$t('actions.actions'),
            value: 'actions',
            align: 'center',
            sortable: false,
          },
        ],
        items: [],
      },
    };
  },
  watch: {
    app() {
      this.getData();
    },
  },
  mounted() {
    this.getData();
  },
  methods: {
    onResize() {
      this.windowWidth = window.innerWidth;
    },
    getData() {
      this.loader = true;
      this.axios.get(
        '/api/loyalapp/packages',
        {
          params: {
            app: this.app,
          },
        },
      )
        .then((response) => {
          this.dataTable.totalItems = response.data.totalItems;
          this.dataTable.items = response.data.items;
          this.loader = false;
        })
        .catch(() => {
          this.snackbar.showMessage(
            'error',
            this.$t('common_errorHeader'),
          );
        });
    },
    openModal(modal) {
      this.$refs[modal].dialog = true;
    },
    handleDelete(id) {
      this.loader = true;
      this.axios.delete(
        `/api/gateway/wla-admin/package/${id}`,
        {
          params: {
            app: this.app,
          },
        },
      )
        .then(() => {
          this.getData();
        })
        .catch(() => {
          this.snackbar.showMessage(
            'error',
            this.$t('common_errorHeader'),
          );
        });
    },
  },
};
</script>
