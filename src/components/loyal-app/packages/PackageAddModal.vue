<template>
  <v-layout
    row
    justify-center
  >
    <v-dialog
      v-model="dialog"
      max-width="500px"
    >
      <v-card>
        <v-card-title class="title">
          <span class="headline">
            <h5 class="text-uppercase">{{
              $t('loyalApp_addPackage')
            }}</h5>
          </span>
        </v-card-title>
        <v-card-text>
          <v-container grid-list-md>
            <v-form
              ref="form"
              v-model="form.valid"
              lazy-validation
            >
              <v-layout wrap>
                <v-col
                  cols="12"
                  sm="12"
                  offset-md1
                  md="12"
                >
                  <v-text-field
                    v-model="promotionalPackage.title"
                    prepend-icon="mdi-format-title"
                    :label="$t('loyalApp_title')"
                    required
                    :rules="form.validationRules.title"
                  />
                  <v-text-field
                    v-model="promotionalPackage.payment_value"
                    prepend-icon="mdi-cash"
                    type="number"
                    :label="$t('loyalApp_value')"
                    :rules="form.validationRules.payment_value"
                    required
                  />
                  <v-text-field
                    v-model="promotionalPackage.package_value"
                    prepend-icon="mdi-cash"
                    type="number"
                    :label="$t('loyalApp_packageValue')"
                    required
                    :rules="form.validationRules.package_value"
                  />
                  <v-text-field
                    v-model="promotionalPackage.description"
                    prepend-icon="mdi-information-outline"
                    :label="$t('common_tableDescription')"
                    required
                    :rules="form.validationRules.description"
                    multi-line
                  />
                  <v-text-field
                    v-model="promotionalPackage.valid_time"
                    prepend-icon="mdi-timer-sand-empty"
                    type="number"
                    :label="$t('loyalApp_valid_time')"
                    :rules="form.validationRules.valid_time"
                  />
                  <v-text-field
                    v-model="promotionalPackage.start_time"
                    prepend-icon="mdi-calendar-start"
                    type="date"
                    :label="$t('loyaltyApp_startTime')"
                    :rules="form.validationRules.start_time"
                  />
                  <v-text-field
                    v-model="promotionalPackage.end_time"
                    prepend-icon="mdi-calendar-end"
                    type="date"
                    :label="$t('loyaltyApp_endTime')"
                    :rules="form.validationRules.end_time"
                  />
                  <small>*{{ $t('common_fieldRequired') }}</small>
                </v-col>
              </v-layout>
            </v-form>
          </v-container>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary"
            text
            @click.native="closeDialog"
          >
            {{ $t('actions.return_to_list') }}
          </v-btn>
          <v-btn
            color="primary darken-1"
            :loading="loader"
            @click.native="submit"
          >
            {{ $t('actions.save') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-layout>
</template>

<script>
export default {
  props: {
    app: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      dialog: false,
      loader: false,
      promotionalPackage: {
        title: '',
        payment_value: '',
        package_value: '',
        description: '',
        valid_time: '',
        start_time: '',
        end_time: '',
      },
      form: {
        validateOnBlur: true,
        valid: true,
        validationRules: {
          title: [
            (v) => !!v || this.$t('common_fieldRequired'),
            (v) => /^((?!%|\?|!|\)|\(|'|&).)*$/.test(v) || this.$t('loyalApp_invalidValue'),
          ],
          payment_value: [
            (v) => !!v || this.$t('common_fieldRequired'),
            (v) => (v > 0) || this.$t('loyalApp_infoPositiveNumberOnly'),
          ],
          package_value: [
            (v) => !!v || this.$t('common_fieldRequired'),
            (v) => (v > 0) || this.$t('loyalApp_infoPositiveNumberOnly'),
          ],
          description: [
            (v) => !!v || this.$t('common_fieldRequired'),
            (v) => (v ? v.length <= 120 : false) || this.$t('loyalApp_validDescriptionLength'),
          ],
          valid_time: [
            (v) => v === '' || v === undefined || v >= 0 || this.$t('loyalApp_infoPositiveNumberOnly'),
          ],
          end_time: [
            (v) => !v || !this.promotionalPackage.start_time || new Date(v) >= new Date(this.promotionalPackage.start_time) || this.$t('common_startEndDateValidationInfo'),
          ],
        },
      },
    };
  },
  methods: {
    submit() {
      if (this.$refs.form.validate()) {
        this.loader = true;
        this.axios.post('/api/gateway/wla-admin/packages', {
          title: this.promotionalPackage.title,
          payment_value: parseFloat(this.promotionalPackage.payment_value),
          package_value: parseFloat(this.promotionalPackage.package_value),
          description: this.promotionalPackage.description,
          valid_time: this.promotionalPackage.valid_time !== undefined && this.promotionalPackage.valid_time !== ''
            ? parseInt(this.promotionalPackage.valid_time, 10) : null,
          app: this.app,
          start_time: this.promotionalPackage.start_time === '' ? null : this.promotionalPackage.start_time,
          end_time: this.promotionalPackage.end_time === '' ? null : this.promotionalPackage.end_time,
        })
          .then(
            () => {
              this.closeDialog();
              this.$emit('reload-package-list');
            },
          ).catch(() => {
            this.snackbar.showMessage(
              'error',
              this.$t('common_errorHeader'),
            );
          });
        this.closeDialog();
      }
    },
    resetForm() {
      this.$refs.form.reset();
    },
    closeDialog() {
      this.form.valid = true;
      this.loader = false;
      this.form.validateOnBlur = true;
      this.dialog = false;
      this.resetForm();
    },
  },
};
</script>
