<template>
  <div>
    <payments-filters
      v-model="filters"
      :app="app"
    />
    <payments-list
      :app="app"
      :params="filters"
    />
  </div>
</template>

<script>
import PaymentsFilters from '@components/loyal-app/payments/PaymentsFilters.vue';
import PaymentsList from '@components/loyal-app/payments/PaymentsList.vue';

export default {
  components: {
    PaymentsFilters,
    PaymentsList,
  },
  props: {
    app: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      filters: {},
    };
  },
};

</script>
