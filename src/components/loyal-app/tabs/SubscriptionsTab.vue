<template>
  <div>
    <subscriptions-filters
      v-model="filters"
    />
    <subscriptions-list
      :app="app"
      :params="filters"
    />
  </div>
</template>

<script>
import SubscriptionsFilters from '@components/loyal-app/subscriptions/SubscriptionsFilters.vue';
import SubscriptionsList from '@components/loyal-app/subscriptions/SubscriptionsList.vue';

export default {
  components: {
    SubscriptionsFilters,
    SubscriptionsList,
  },
  props: {
    app: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      filters: {},
    };
  },
};

</script>
