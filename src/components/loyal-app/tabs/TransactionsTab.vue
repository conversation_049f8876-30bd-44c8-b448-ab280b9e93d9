<template>
  <div>
    <transactions-filters
      v-model="filters"
      :app="app"
    />
    <transactions-list
      :app="app"
      :params="filters"
    />
  </div>
</template>

<script>
import TransactionsFilters from '@components/loyal-app/transactions/TransactionsFilters.vue';
import TransactionsList from '@components/loyal-app/transactions/TransactionsList.vue';

export default {
  components: {
    TransactionsFilters,
    TransactionsList,
  },
  props: {
    app: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      filters: {},
    };
  },
};

</script>
