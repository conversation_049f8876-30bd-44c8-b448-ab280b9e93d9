<template>
  <div>
    <carwashes-filters
      v-model="filters"
    />
    <carwashes-list
      :base-url="baseUrl"
      :filters="filters"
    />
  </div>
</template>

<script>
import CarwashesFilters from '@components/loyal-app/carwashes/CarwashesFilters.vue';
import CarwashesList from '@components/loyal-app/carwashes/CarwashesList.vue';

export default {
  components: {
    CarwashesFilters,
    CarwashesList,
  },
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      filters: {},
    };
  },
};

</script>
