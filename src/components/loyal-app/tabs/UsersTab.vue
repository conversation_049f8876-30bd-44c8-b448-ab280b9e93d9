<template>
  <div>
    <users-filters
      v-model="filters"
    />
    <users-list
      :app="app"
      :params="filters"
    />
  </div>
</template>

<script>
import UsersFilters from '@/components/loyal-app/users/UsersFilters.vue';
import UsersList from '@components/loyal-app/users/UsersList.vue';

export default {
  components: {
    UsersFilters,
    UsersList,
  },
  props: {
    app: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      filters: {},
    };
  },
};
</script>
