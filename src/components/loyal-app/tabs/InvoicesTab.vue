<template>
  <div>
    <invoices-filters
      v-model="filters"
      :app="app"
    />
    <invoices-list
      :app="app"
      :params="filters"
    />
  </div>
</template>

<script>
import InvoicesFilters from '@components/loyal-app/invoices/InvoicesFilters.vue';
import InvoicesList from '@components/loyal-app/invoices/InvoicesList.vue';

export default {
  components: {
    InvoicesFilters,
    InvoicesList,
  },
  props: {
    app: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      filters: {},
    };
  },
};

</script>
