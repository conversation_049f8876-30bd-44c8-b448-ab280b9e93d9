<template>
  <div>
    <receipts-filters
      v-model="filters"
    />
    <receipts-list
      :app="app"
      :params="filters"
    />
  </div>
</template>

<script>
import ReceiptsFilters from '@components/loyal-app/receipts/ReceiptsFilters.vue';
import ReceiptsList from '@components/loyal-app/receipts/ReceiptsList.vue';

export default {
  components: {
    ReceiptsFilters,
    ReceiptsList,
  },
  props: {
    app: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      filters: {},
    };
  },
};

</script>
