<template>
  <report-data-table
    ref="dataTable"
    report="v2\WlaReceiptsReport"
    :title="$t('loyalApp_receipts')"
    :headers="headers"
    :filters="paramsInternal"
  >
    <template #[`item.gross`]="{ item }">
      <currency-formatter
        :value="item.gross"
        :symbol="item.currency_symbol"
      />
    </template>

    <template #[`item.vat`]="{ item }">
      <currency-formatter
        :value="item.vat"
        :symbol="item.currency_symbol"
      />
    </template>

    <template #[`item.user_email`]="{ item }">
      <email-formatter :value="item.user_email" />
    </template>

    <template #[`item.actions`]="{ item }">
      <v-row
        justify="end"
        class="d-flex align-center"
      >
        <act-download
          :url="getActionUrl('download', item)"
        />
      </v-row>
    </template>
    <template #[`footer`]="{ totalSum }">
      <tr class="table-summary">
        <td
          class="text-start font-weight-bold"
        >
          {{ $t('turnover.table.total') }}
        </td>
        <td />
        <td />
        <td />
        <td />
        <td />
        <td class="text-sm-center font-weight-bold">
          <currency-formatter
            v-if="totalSum"
            :value="totalSum.gross"
            :symbol="totalSum.currency"
          />
        </td>
        <td class="text-sm-center font-weight-bold">
          <currency-formatter
            v-if="totalSum"
            :value="totalSum.vat"
            :symbol="totalSum.currency"
          />
        </td>
        <td />
      </tr>
    </template>
  </report-data-table>
</template>

<script>
import ReportDataTable from '@/components/reports/ReportDataTable.vue';
import CurrencyFormatter from '@/components/common/formatters/CurrencyFormatter.vue';
import ActDownload from '@/components/common/Action/ActDownload.vue';
import EmailFormatter from '@/components/common/formatters/EmailFormatter.vue';

export default {
  components: {
    EmailFormatter,
    ReportDataTable,
    CurrencyFormatter,
    ActDownload,
  },
  props: {
    app: {
      type: String,
      required: true,
    },
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    paramsInternal() {
      return {
        ...this.params,
        app: this.app,
      };
    },
    headers() {
      return [
        {
          text: this.$t('common_id'),
          value: 'id',
          sortable: false,
          protected: true,
        },
        {
          text: this.$t('loyalApp_createDate'),
          value: 'time',
          sortable: false,
          align: 'center',
        },
        {
          text: this.$t('loyalApp_paymentDate'),
          value: 'mobile_payment_confirmed_timestamp',
          sortable: false,
          align: 'center',
        },
        {
          text: this.$t('loyalApp_number'),
          value: 'number',
          sortable: false,
          align: 'center',
          protected: true,
        },
        {
          text: 'JPK ID',
          value: 'jpk_id',
          sortable: false,
          align: 'center',
          protected: true,
        },
        {
          text: this.$t('common_username'),
          value: 'user_email',
          sortable: false,
          align: 'left',
          protected: true,
        },
        {
          text: this.$t('common_value'),
          value: 'gross',
          sortable: false,
          align: 'center',
        },
        {
          text: this.$t('loyalApp_vat'),
          value: 'vat',
          sortable: false,
          align: 'center',
        },
        {
          text: this.$t('actions.actions'),
          value: 'actions',
          sortable: false,
          align: 'end',
        },
      ];
    },
  },
  methods: {
    fetchData() {
      this.$refs.dataTable.fetchData();
    },
    getActionUrl(action, item) {
      return `/api/gateway/wla-admin/receipt/${item.id}/${action}?app=${this.app}`;
    },
  },
};

</script>
