<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
    card
  >
    <v-row>
      <v-col>
        <date-select
          v-model="config.date"
        />
      </v-col>
      <v-col />
    </v-row>
  </v-container>
</template>

<script>
import debounce from 'lodash/debounce';
import DateSelect from '@components/reports/filters/DateSelect.vue';

const SettingsKey = 'wla-receipts-filter';

export default {
  components: {
    DateSelect,
  },
  model: {
    prop: 'param',
    event: 'update:param',
  },
  props: {
    param: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    const savedConfig = JSON.parse(localStorage.getItem(SettingsKey) || '{}');

    return {
      config: {
        date: savedConfig.date ?? null,
      },
    };
  },
  computed: {
    internalParam() {
      return {
        ...this.config.date,
      };
    },
  },
  watch: {
    internalParam: {
      handler(newVal) {
        this.debouncedUpdate(newVal);
      },
      deep: true,
      immediate: true,
    },
    config: {
      handler(newVal) {
        localStorage.setItem(SettingsKey, JSON.stringify(newVal));
      },
      deep: true,
    },
  },
  beforeCreate() {
    this.debouncedUpdate = debounce((val) => {
      this.$emit('update:param', val);
    }, 300);
  },
};
</script>
