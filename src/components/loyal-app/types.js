export const SubscriptionStatusType = [
  {
    value: 'confirmed',
    icon: 'mdi-check-circle-outline',
    color: 'green darken-2',
    text: 'common_confirmed',
  },
  {
    value: 'initiated',
    icon: 'mdi-cached',
    color: 'progress',
    text: 'common_processing',
  },
  {
    value: 'timeout',
    icon: 'mdi-clock-outline',
    color: 'error',
    text: 'common_timeout',
  },
  {
    value: 'rejected',
    icon: 'mdi-alert-outline',
    color: 'error',
    text: 'common_canceled',
  },
  {
    value: 'refunded',
    icon: 'mdi-credit-card-refund-outline',
    color: 'warning',
    text: 'common_refund',
  },
  {
    value: 'unknown',
    icon: 'mdi-help-circle-outline',
    color: 'gray',
    text: 'common_unknown',
  },
  {
    value: 'error',
    icon: 'mdi-close-octagon-outline',
    color: 'error',
    text: 'common_error',
  },
];

export const PaymentStatusType = [
  {
    value: 'confirmed',
    icon: 'mdi-check-circle-outline',
    color: 'green darken-2',
  },
  {
    value: 'initiated',
    icon: 'mdi-cached',
    color: 'progress',
  },
  {
    value: 'waiting',
    icon: 'mdi-progress-clock',
    color: 'progress',
  },
  {
    value: 'pending',
    icon: 'mdi-clock-outline',
    color: 'progress',
  },
  {
    value: 'refunding',
    icon: 'mdi-credit-card-clock-outline',
    color: 'warning',
  },
  {
    value: 'refunded',
    icon: 'mdi-credit-card-refund-outline',
    color: 'warning',
  },
  {
    value: 'canceled',
    icon: 'mdi-close-circle-outline',
    color: 'error',
  },
  {
    value: 'rejected',
    icon: 'mdi-alert-outline',
    color: 'error',
  },
];

export const TransactionStatusType = [
  {
    value: 'confirmed',
    icon: 'mdi-check-circle-outline',
    color: 'green darken-2',
  },
  {
    value: 'initiated',
    icon: 'mdi-cached',
    color: 'progress',
  },
  {
    value: 'timeout',
    icon: 'mdi-clock-alert-outline',
    color: 'error',
  },
  {
    value: 'rejected',
    icon: 'mdi-alert-outline',
    color: 'error',
  },
  {
    value: 'unknown',
    icon: 'mdi-help-circle-outline',
    color: 'gray',
  },
  {
    value: 'error',
    icon: 'mdi-close-octagon-outline',
    color: 'error',
  },
  {
    value: 'refunded',
    icon: 'mdi-credit-card-refund-outline',
    color: 'warning',
  },
];

export const TransactionType = [
  {
    value: 'TOP_UP',
    icon: 'mdi-trending-up',
  },
  {
    value: 'PAYMENT',
    icon: 'mdi-trending-down',
  },
  {
    value: 'TOP_UP_BONUS',
    icon: 'mdi-sale',
  },
  {
    value: 'TOP_UP_CODE',
    icon: 'mdi-card-plus',
  },
  {
    value: 'ACCOUNT_DELETE',
    icon: 'mdi-delete',
  },
];
