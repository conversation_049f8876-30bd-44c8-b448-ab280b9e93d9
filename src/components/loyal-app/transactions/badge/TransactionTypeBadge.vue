<template>
  <v-tooltip bottom>
    <template #activator="{ on, attrs }">
      <v-icon
        v-bind="attrs"
        v-on="on"
      >
        {{ typeInfo.icon }}
      </v-icon>
    </template>
    <span> {{ typeInfo.text }}</span>
  </v-tooltip>
</template>

<script>

import { TransactionType } from '@components/loyal-app/types';

export default {
  props: {
    type: {
      type: String,
      required: true,
    },
  },
  computed: {
    typeInfo() {
      const type = TransactionType.find((item) => item.value === this.type);
      if (type) {
        return {
          icon: type.icon,
          text: this.$t(`transaction_type_${type.value}`),
        };
      }

      return {
        icon: 'mdi-help-circle',
        text: this.type,
      };
    },
  },
};
</script>
