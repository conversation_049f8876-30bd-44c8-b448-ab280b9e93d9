<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
    card
  >
    <v-row>
      <v-col>
        <text-search v-model="config.search" />
      </v-col>
      <v-col>
        <multiselect
          v-model="config.status"
          :items="statusFilters"
          :label="$t('common_state')"
          prepend-icon="mdi-list-status"
          allow-null
        />
      </v-col>
      <v-col>
        <date-select
          v-model="config.date"
        />
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <multiselect
          v-model="config.issuer"
          :items="issuerFilters"
          :label="$t('loyalApp_accountType')"
          prepend-icon="mdi-wallet"
          allow-null
        />
      </v-col>
      <v-col>
        <multiselect
          v-model="config.type"
          :items="typeFilters"
          :label="$t('common_transactionType')"
          prepend-icon="mdi-trending-up"
          allow-null
        />
      </v-col>
      <v-col />
    </v-row>
  </v-container>
</template>

<script>
import debounce from 'lodash/debounce';
import TextSearch from '@components/reports/filters/TextSearch.vue';
import Multiselect from '@components/reports/filters/MultiSelect.vue';
import DateSelect from '@components/reports/filters/DateSelect.vue';
import { TransactionType } from '@components/loyal-app/types';

const SettingsKey = 'wla-transactions-filter';

export default {
  components: {
    TextSearch,
    Multiselect,
    DateSelect,
  },
  model: {
    prop: 'param',
    event: 'update:param',
  },
  props: {
    app: {
      type: String,
      required: true,
    },
    param: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    const savedConfig = JSON.parse(localStorage.getItem(SettingsKey) || '{}');

    return {
      config: {
        search: savedConfig.search ?? null,
        status: savedConfig.status ?? null,
        issuer: savedConfig.issuer ?? null,
        type: savedConfig.type ?? null,
        date: savedConfig.date ?? null,
      },
      statuses: null,
      issuers: null,
      types: null,
    };
  },
  computed: {
    statusFilters() {
      if (!this.statuses) {
        return [];
      }

      return this.statuses.map((item) => ({
        text: this.$t(`filter_${item}`),
        value: item,
      }));
    },
    issuerFilters() {
      if (!this.issuers) {
        return [];
      }

      return this.issuers.map((item) => ({
        text: this.$t(`transaction_issuer_${item}`),
        value: item,
      }));
    },
    typeFilters() {
      if (!this.types) {
        return [];
      }

      return this.types.map((item) => ({
        text: this.$t(`transaction_type_${item}`),
        value: item,
        icon: TransactionType.find((type) => type.value === item)?.icon ?? 'mdi-help-circle',
      }));
    },
    internalParam() {
      return {
        search: this.config.search,
        status: this.config.status?.length ? this.config.status.join(',') : null,
        issuer: this.config.issuer?.length ? this.config.issuer.join(',') : null,
        type: this.config.type?.length ? this.config.type.join(',') : null,
        ...this.config.date,
      };
    },
  },
  watch: {
    app() {
      if (this.app != null) {
        this.fetchFilters();
      }
    },
    internalParam: {
      handler(newVal) {
        this.debouncedUpdate(newVal);
      },
      deep: true,
      immediate: true,
    },
    config: {
      handler(newVal) {
        localStorage.setItem(SettingsKey, JSON.stringify(newVal));
      },
      deep: true,
    },
  },
  mounted() {
    this.fetchFilters();
  },
  beforeCreate() {
    this.debouncedUpdate = debounce((val) => {
      this.$emit('update:param', val);
    }, 300);
  },
  methods: {
    async fetchFilters() {
      const response = await this.axios.get(
        `/api/gateway/wla-admin/transactions/filters?app=${this.app}`,
      );

      this.statuses = response.data.statuses;
      this.issuers = response.data.issuers;
      this.types = response.data.types;

      // przy zmianie aplikacji mozemy mieć innych issuers
      // usuwamy wartości których nie ma w nowej liście
      if (this.config.issuer?.length) {
        const hasInvalidIssuer = this.config.issuer.some(
          (issuer) => !this.issuers.includes(issuer),
        );
        if (hasInvalidIssuer) {
          this.config.issuer = null;
        }
      }
    },
  },
};
</script>
