<template>
  <report-data-table
    ref="dataTable"
    report="v2\WlaTransactionsReport"
    :title="$t('loyal-app-manager.transactions')"
    :headers="headers"
    :filters="paramsInternal"
  >
    <template #[`item.id`]="{ item }">
      <div
        class="d-flex align-center justify-start"
      >
        <transaction-status-badge :status="item.status" />
        <span class="ml-1">
          <blurred-formatter :value="item.id" />
        </span>
      </div>
    </template>

    <template #[`item.bkfpay_user_email`]="{ item }">
      <email-formatter :value="item.bkfpay_user_email" />
    </template>

    <template #[`item.bkfpay_company_email`]="{ item }">
      <email-formatter :value="item.bkfpay_company_email" />
    </template>

    <template #[`item.time`]="{ item }">
      <date-time-formatter :value="item.time" />
    </template>

    <template #[`item.transaction_type`]="{ item }">
      <transaction-type-badge :type="item.transaction_type" />
    </template>

    <template #[`item.issuer`]="{ item }">
      {{ $t(`transaction_issuer_${item.issuer}`) }}
    </template>

    <template #[`item.value`]="{ item }">
      <currency-formatter
        :value="item.value"
        :symbol="item.currency_symbol"
      />
    </template>

    <template #[`item.invoice`]="{ item }">
      <div class="d-flex align-center justify-end">
        <span class="mr-1"> <blurred-formatter :value="item.document_number" /></span>
        <document-badge
          v-if="item.document_type"
          class="ml-1"
          :type="item.document_type"
        />
        <act-download
          v-if="item.document_uri"
          :url="getActionUrl(item)"
        />
      </div>
    </template>

    <template #[`item.expand`]="{ item, isExpanded }">
      <v-icon
        v-if="item.info"
      >
        {{ isExpanded ? 'mdi-chevron-up' : 'mdi-chevron-down' }}
      </v-icon>
    </template>

    <template #[`expanded-item`]="{ item }">
      <td
        v-if="item.info"
        :colspan="headers.length"
      >
        <key-value-table
          :data="item.info"
          :header-text="$t('fiscal_transactions.details.heading')"
        />
      </td>
    </template>

    <template #[`footer`]="{ pageSum, totalSum }">
      <tr class="table-summary">
        <td
          colspan="6"
          class="text-start font-weight-bold"
        >
          {{ $t('common_totalOnPage') }}
        </td>
        <td class="text-end font-weight-bold">
          <currency-formatter
            v-if="pageSum"
            :value="pageSum.value"
            :symbol="pageSum.currency"
          />
        </td>
        <td />
        <td />
      </tr>
      <tr>
        <td
          colspan="6"
          class="text-start font-weight-bold"
        >
          {{ $t('turnover.table.total') }}
        </td>
        <td class="text-end font-weight-bold">
          <currency-formatter
            v-if="totalSum"
            :value="totalSum.value"
            :symbol="totalSum.currency"
          />
        </td>
        <td />
        <td />
      </tr>
    </template>
  </report-data-table>
</template>

<script>
import ReportDataTable from '@/components/reports/ReportDataTable.vue';
import DocumentBadge from '@/components/common/DocumentBadge.vue';
import CurrencyFormatter from '@/components/common/formatters/CurrencyFormatter.vue';
import ActDownload from '@/components/common/Action/ActDownload.vue';
import TransactionStatusBadge from '@/components/loyal-app/transactions/badge/TransactionStatusBadge.vue';
import KeyValueTable from '@/components/common/KeyValueTable.vue';
import DateTimeFormatter from '@/components/common/formatters/DateTimeFormatter.vue';
import TransactionTypeBadge from '@/components/loyal-app/transactions/badge/TransactionTypeBadge.vue';
import BlurredFormatter from '@components/common/formatters/BlurredFormatter.vue';
import EmailFormatter from '@components/common/formatters/EmailFormatter.vue';

export default {
  components: {
    EmailFormatter,
    BlurredFormatter,
    ReportDataTable,
    DocumentBadge,
    CurrencyFormatter,
    ActDownload,
    TransactionStatusBadge,
    KeyValueTable,
    DateTimeFormatter,
    TransactionTypeBadge,
  },
  props: {
    app: {
      type: String,
      required: true,
    },
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    paramsInternal() {
      return {
        ...this.params,
        app: this.app,
      };
    },
    headers() {
      return [
        {
          text: this.$t('common_id'),
          value: 'id',
          sortable: false,
          protected: true,
        },
        {
          text: this.$t('common_tableDate'),
          value: 'time',
          sortable: false,
        },
        {
          text: this.$t('common_user'),
          value: 'bkfpay_user_email',
          sortable: false,
          protected: true,
        },
        {
          text: this.$t('loyalApp_account'),
          value: 'bkfpay_company_email',
          sortable: false,
          protected: true,
        },
        {
          text: this.$t('loyalApp_accountType'),
          value: 'issuer',
          sortable: false,
        },
        {
          text: this.$t('common_transactionType'),
          value: 'transaction_type',
          sortable: false,
        },
        {
          text: this.$t('loyalApp_transactionValue'),
          value: 'value',
          sortable: false,
          align: 'right',
        },
        {
          text: this.$t('loyalApp_salesDocument'),
          value: 'invoice',
          sortable: false,
          align: 'right',
        },
        {
          text: '',
          value: 'expand',
          sortable: false,
          align: 'right',
        },
      ];
    },
  },
  methods: {
    fetchData() {
      this.$refs.dataTable.fetchData();
    },
    getActionUrl(item) {
      return `/api/gateway/wla-admin${item.document_uri}?app=${this.app}`;
    },
  },
};

</script>
