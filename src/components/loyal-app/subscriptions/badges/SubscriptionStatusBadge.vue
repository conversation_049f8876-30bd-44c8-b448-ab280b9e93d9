<template>
  <v-row
    justify="center"
  >
    <v-tooltip
      bottom
    >
      <template #activator="{ on, attrs }">
        <v-icon
          :color="statusInfo.color"
          v-bind="attrs"
          v-on="on"
        >
          {{ statusInfo.icon }}
        </v-icon>
      </template>
      <span> {{ statusInfo.text }}</span>
    </v-tooltip>
  </v-row>
</template>

<script>

import { SubscriptionStatusType } from '@components/loyal-app/types';

export default {
  props: {
    status: {
      type: String,
      required: true,
    },
  },
  computed: {
    statusInfo() {
      const status = SubscriptionStatusType.find((item) => item.value === this.status);
      if (status) {
        return {
          icon: status.icon,
          color: status.color,
          text: this.$t(status.text),
        };
      }

      return {
        icon: 'mdi-help-circle',
        color: 'grey',
        text: this.$t('common_unknown'),
      };
    },
  },
};
</script>
