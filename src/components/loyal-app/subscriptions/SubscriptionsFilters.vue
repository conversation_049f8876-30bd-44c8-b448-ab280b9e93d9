<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
    card
  >
    <v-row>
      <v-col>
        <text-search v-model="config.search" />
      </v-col>
      <v-col>
        <multiselect
          ref="multiselect"
          v-model="config.status"
          :items="statusFilters"
          :label="$t('common_state')"
          prepend-icon="mdi-list-status"
          allow-null
        />
      </v-col>
      <v-col />
    </v-row>
  </v-container>
</template>

<script>
import debounce from 'lodash/debounce';
import TextSearch from '@components/reports/filters/TextSearch.vue';
import Multiselect from '@components/reports/filters/MultiSelect.vue';
import { SubscriptionStatusType } from '@components/loyal-app/types';

const SettingsKey = 'wla-subscriptions-filter';

export default {
  components: {
    TextSearch,
    Multiselect,
  },
  model: {
    prop: 'param',
    event: 'update:param',
  },
  props: {
    param: {
      type: Object,
      default: () => ({}),
    },

  },
  data() {
    const savedConfig = JSON.parse(localStorage.getItem(SettingsKey) || '{}');
    return {
      config: {
        search: savedConfig.search ?? null,
        status: savedConfig.status ?? null,
      },
    };
  },
  computed: {
    statusFilters() {
      return SubscriptionStatusType.map(
        (item) => ({
          text: this.$t(item.text),
          value: item.value,
          icon: item.icon,
          color: item.color,
        }),
      );
    },
    internalParam() {
      return {
        search: this.config.search,
        status: this.config.status?.length ? this.config.status.join(',') : [],
        report: 'v2\\WlaSubscriptionPackagesReport',
      };
    },
  },
  watch: {
    internalParam: {
      handler(newVal) {
        this.debouncedUpdate(newVal);
      },
      deep: true,
      immediate: true,
    },
    config: {
      handler(newVal) {
        localStorage.setItem(SettingsKey, JSON.stringify(newVal));
      },
      deep: true,
    },
  },
  beforeCreate() {
    this.debouncedUpdate = debounce((val) => {
      this.$emit('update:param', val);
    }, 300);
  },
  mounted() {
    const startingPresetValue = this.$refs.dateSelect.getStartingPreset();
    const startingPreset = this.$refs.dateSelect.getPresetByValue(startingPresetValue);

    this.interval = {
      from: startingPreset.start,
      to: startingPreset.end,
    };

    this.$emit('update:param', this.internalParam);
  },
  methods: {

  },
};
</script>
