<template>
  <div>
    <div
      class="d-flex align-center mb-3"
      @click="isFirstTableExpanded = !isFirstTableExpanded"
    >
      <h3 class="mb-0 flex-grow-1">
        {{ $t('loyalApp_statistics_table_heading') }}
      </h3>
      <v-btn
        icon
        small
      >
        <v-icon>{{ isFirstTableExpanded ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
      </v-btn>
    </div>
    <v-data-table
      v-show="isFirstTableExpanded"
      :headers="combinedHeaders"
      :items="combinedItems"
      item-key="period"
      :loading="loader"
      hide-default-footer
      disable-pagination
      mobile-breakpoint="0"
      :sort-by="sortBy"
      :sort-desc="sortDesc"
      class="mb-6"
    >
      <!--Loader-->
      <template #progress>
        <div class="text-center mx-n4">
          <v-progress-linear
            class="loader"
            indeterminate
            color="primary"
          />
        </div>
      </template>

      <!--Item-->
      <template #item="{ item }">
        <tr>
          <td class="text-start tabcell-date font-weight-bold">
            {{ formatPeriod(item.period) }}
          </td>
          <td class="text-end">
            {{ item.user }}
          </td>
          <td class="text-end">
            {{ item.carwash }}
          </td>
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="Math.abs(parseFloat(item.payment))"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="parseFloat(item.direct_payment)"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="parseFloat(item.top_up)"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="parseFloat(item.bonus)"
          />
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="parseFloat(item.balance)"
          />
        </tr>
      </template>

      <!--Summary-->
      <template #[`body.append`]>
        <template v-if="!loader && combinedItems.length > 0">
          <tr class="table-summary">
            <td class="text-start font-weight-bold">
              {{ $t('turnover.table.total') }}
            </td>
            <td class="text-end font-weight-bold">
              {{ totalUsers }}
            </td>
            <td class="text-end font-weight-bold">
              {{ totalCarwashes }}
            </td>
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="totalPayments"
              weight="bold"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="totalDirectPayments"
              weight="bold"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="totalTopUps"
              weight="bold"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="totalBonus"
              weight="bold"
            />
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="lastBalance"
              weight="bold"
            />
          </tr>
        </template>
      </template>
    </v-data-table>

    <div
      class="d-flex align-center mb-3"
      @click="isSecondTableExpanded = !isSecondTableExpanded"
    >
      <h3 class="mb-0 flex-grow-1">
        {{ $t('loyalApp_statistics_chart_by_source') }}
      </h3>
      <v-btn
        icon
        small
      >
        <v-icon>{{ isSecondTableExpanded ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
      </v-btn>
    </div>
    <v-data-table
      v-show="isSecondTableExpanded"
      :headers="statsHeaders"
      :items="statsItems"
      item-key="issuer"
      :loading="loader"
      hide-default-footer
      disable-pagination
      mobile-breakpoint="0"
      class="mb-6"
    >
      <!--Loader-->
      <template #progress>
        <div class="text-center mx-n4">
          <v-progress-linear
            class="loader"
            indeterminate
            color="primary"
          />
        </div>
      </template>

      <!--Item-->
      <template #item="{ item }">
        <tr>
          <td class="text-start font-weight-bold">
            {{ $t(item.issuer) }}
          </td>
          <custom-currency-symbol-cell
            :currency="currencySymbol"
            :value="parseFloat(item.balance)"
          />
        </tr>
      </template>

      <!--Summary-->
      <template #[`body.append`]>
        <template v-if="!loader && statsItems.length > 0">
          <tr class="table-summary">
            <td class="text-start font-weight-bold">
              {{ $t('turnover.table.total') }}
            </td>
            <custom-currency-symbol-cell
              :currency="currencySymbol"
              :value="totalStats"
              weight="bold"
            />
          </tr>
        </template>
      </template>
    </v-data-table>
  </div>
</template>

<script>
import CustomCurrencySymbolCell from '@components/common/CustomCurrencySymbolCell.vue';

export default {
  name: 'LoyalAppStatsTable',
  components: {
    CustomCurrencySymbolCell,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {
          payments: [],
          balance: [],
          stats: [],
        };
      },
    },
    loader: {
      type: Boolean,
      default: false,
    },
    currencySymbol: {
      type: String,
      default: undefined,
    },
    dateFormatFunc: {
      type: Function,
      default: null,
    },
  },
  data() {
    return {
      isFirstTableExpanded: true,
      isSecondTableExpanded: true,
      combinedHeaders: [
        {
          value: 'period',
          text: this.$t('common_period'),
        },
        {
          value: 'user',
          text: this.$t('common_users'),
          align: 'end',
          sortable: true,
        },
        {
          value: 'carwash',
          text: this.$t('common_carwashes'),
          align: 'end',
          sortable: true,
        },
        {
          value: 'payment',
          text: this.$t('loyalApp_payments'),
          align: 'end',
          sortable: true,
        },
        {
          value: 'direct_payment',
          text: this.$t('loyalApp_directPayments'),
          align: 'end',
          sortable: true,
        },
        {
          value: 'top_up',
          text: this.$t('common_topups'),
          align: 'end',
          sortable: true,
        },
        {
          value: 'bonus',
          text: this.$t('loyalApp_bonus'),
          align: 'end',
          sortable: true,
        },
        {
          value: 'balance',
          text: this.$t('loyalApp_balance'),
          align: 'end',
          sortable: true,
        },
      ],
      statsHeaders: [
        {
          value: 'issuer',
          text: this.$t('loyaltyCards_source'),
        },
        {
          value: 'balance',
          text: this.$t('common_value'),
          align: 'end',
          sortable: true,
        },
      ],
      sortBy: 'period',
      sortDesc: true,
    };
  },
  computed: {
    paymentsItems() {
      if (this.loader || !this.data || !this.data.payments) {
        return [];
      }
      return this.data.payments;
    },
    balanceItems() {
      if (this.loader || !this.data || !this.data.balance) {
        return [];
      }
      return this.data.balance;
    },
    statsItems() {
      if (this.loader || !this.data || !this.data.stats) {
        return [];
      }
      return this.data.stats;
    },
    combinedItems() {
      if (this.loader || !this.data || !this.data.payments || !this.data.balance) {
        return [];
      }

      // Tworzymy mapę sald według okresu dla szybkiego dostępu
      const balanceMap = {};
      this.data.balance.forEach((item) => {
        balanceMap[item.period] = item.balance;
      });

      // Łączymy dane płatności z danymi salda
      return this.data.payments.map((item) => ({
        ...item,
        balance: balanceMap[item.period] || 0,
      }));
    },
    totalUsers() {
      if (!this.combinedItems.length) return 0;
      return this.combinedItems.reduce((sum, item) => sum + parseInt(item.user, 10), 0);
    },
    totalCarwashes() {
      if (!this.combinedItems.length) return 0;
      // Zwracamy maksymalną liczbę myjni, ponieważ sumowanie nie ma sensu
      return Math.max(...this.combinedItems.map((item) => parseInt(item.carwash, 10)));
    },
    totalPayments() {
      if (!this.combinedItems.length) return 0;
      return this.combinedItems.reduce((sum, item) => sum + Math.abs(parseFloat(item.payment)), 0);
    },
    totalDirectPayments() {
      if (!this.combinedItems.length) return 0;
      return this.combinedItems.reduce((sum, item) => sum + parseFloat(item.direct_payment), 0);
    },
    totalTopUps() {
      if (!this.combinedItems.length) return 0;
      return this.combinedItems.reduce((sum, item) => sum + parseFloat(item.top_up), 0);
    },
    totalBonus() {
      if (!this.combinedItems.length) return 0;
      return this.combinedItems.reduce((sum, item) => sum + parseFloat(item.bonus), 0);
    },
    lastBalance() {
      if (!this.balanceItems.length) return 0;
      // Zwracamy ostatnie saldo (najnowsze)
      const sortedBalances = [...this.balanceItems]
        .sort((a, b) => b.period.localeCompare(a.period));
      return parseFloat(sortedBalances[0].balance);
    },
    totalStats() {
      if (!this.statsItems.length) return 0;
      return this.statsItems.reduce((sum, item) => sum + parseFloat(item.balance), 0);
    },
  },
  methods: {
    formatPeriod(period) {
      if (!period) return '';

      const [year, month] = period.split('-');
      const date = new Date(parseInt(year, 10), parseInt(month, 10) - 1, 1);

      return this.dateFormatFunc
        ? this.dateFormatFunc(date)
        : this.$options.filters.formatDateMonth(date);
    },
  },
};
</script>

<style>
.tabcell-date {
  min-width: 120px;
}

.d-flex.align-center.mb-3 {
  background-color: #f5f5f5;
  padding: 8px 16px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.d-flex.align-center.mb-3:hover {
  background-color: #e0e0e0;
  cursor: pointer;
}

.d-flex.align-center.mb-3 h3 {
  margin-bottom: 0;
}
</style>
