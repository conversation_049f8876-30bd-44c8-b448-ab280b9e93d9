<template>
  <div>
    <v-col
      cols="12"
    >
      <v-layout
        row
        wrap
        class="d-flex justify-start"
      >
        <v-col
          cols="12"
          md="4"
        >
          <type-filter
            ref="typeFilter"
            :disabled="loader"
            @change="onFiltersChange"
          />
        </v-col>
        <v-col
          cols="12"
          md="8"
        >
          <date-range-picker
            key="dateRange"
            ref="dateRange"
            prepend-icon="mdi-calendar-range"
            :show-presets="true"
            :show-custom="true"
            :disabled="loader"
            start-preset="currentYear"
            settings-namespace="loyalapp:stats:dates"
            @reload-transaction-list="onDateRangeChange"
          />
        </v-col>
      </v-layout>
      <v-layout
        row
        wrap
      >
        <v-col
          cols="12"
          sm="8"
          class="text-sm-start"
        >
          <h2>{{ $t('loyalApp_statistics_heading') }}</h2>
        </v-col>
        <v-col
          cols="12"
          sm="4"
          class="d-flex justify-end"
        >
          <btn-refresh
            class="mr-2"
            :disabled="loader"
            @click="getAllData"
          />
          <report-create-modal
            btn-class="ml-2"
            :params="exportAsyncParams"
            :disabled="loader"
            :preset="filtering.dates.value"
          />
        </v-col>
      </v-layout>
    </v-col>

    <v-alert
      v-if="error"
      type="error"
      class="mt-3"
      dense
    >
      {{ $t('common_error_occurred') }}
    </v-alert>

    <div
      v-if="!error"
    >
      <v-col
        cols="12"
        class="pt-3"
      >
        <loyal-app-stats-charts
          ref="statsChart"
          :loader="loader"
          :data="statsData || { payments: [], balance: [], stats: [] }"
          :currency-symbol="currencySymbol"
          :app="app"
          class="mb-6"
          @error="snackbar.showMessage('error', $event)"
        />

        <loyal-app-stats-table
          :loader="loader"
          :data="statsData || { payments: [], balance: [], stats: [] }"
          :date-format-func="formatDate"
          :currency-symbol="currencySymbol"
        />
      </v-col>
    </div>
  </div>
</template>

<script>
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import FiltersHandlingMixin from '@components/common/mixins/FiltersHandlingMixin.vue';
import DataFetchMixin from '@components/common/mixins/DataFetchMixin.vue';
import ExportMixin from '@components/common/mixins/ExportMixin.vue';
import ReportCreateModal from '@components/reports/ReportCreateModal.vue';
import TypeFilter from '@components/common/filters/TypeFilter.vue';
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import DateRangePicker from '@components/common/DateRangePicker.vue';
import LoyalAppStatsTable from '@components/loyal-app/statistics/LoyalAppStatsTable.vue';
import LoyalAppStatsCharts from '@/components/loyal-app/statistics/LoyalAppStatsCharts.vue';

export default {
  name: 'LoyalAppStatistics',
  components: {
    ReportCreateModal,
    TypeFilter,
    BtnRefresh,
    DateRangePicker,
    LoyalAppStatsTable,
    LoyalAppStatsCharts,
  },
  mixins: [
    FilterMixin,
    FiltersHandlingMixin,
    DataFetchMixin,
    ExportMixin,
  ],
  props: {
    app: {
      type: String,
      default: null,
    },
    showFiltering: {
      type: Boolean,
      default: false,
    },
    autoUpdateTransactions: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      error: false,
      statsData: null,
      currencyData: null,
      filtering: {
        dataSource: {
          value: '',
          text: '',
          dataUrl: '/api/reports/data',
          reportName: this.$t('loyalApp_statistics_heading'),
        },
        grouping: 'monthly',
        dates: {
          from: null,
          to: null,
          value: 'currentYear',
        },
      },
    };
  },
  computed: {
    exportAsyncParams() {
      return {
        report: 'v2\\WlaStatsReport',
        app: this.app,
        startDate: this.filtering.dates.from,
        endDate: this.filtering.dates.to,
        type: this.filtering.grouping || 'daily',
      };
    },
    currencySymbol() {
      if (this.currencyData && this.currencyData.symbol) {
        return this.currencyData.symbol;
      }

      return this.$store.getters['auth/userCurrencySymbol'];
    },
  },
  watch: {
    app(newVal) {
      if (newVal) {
        this.error = false;
        this.currencyData = null;

        this.resetCompareWithPreviousYear();
        this.getAllData();
      }
    },
  },
  mounted() {
    this.$nextTick(() => {
      if (this.$refs.dateRange) {
        const originalEmit = this.$refs.dateRange.$emit;
        this.$refs.dateRange.$emit = (event, ...args) => {
          if (event !== 'reload-transaction-list') {
            originalEmit.call(this.$refs.dateRange, event, ...args);
          }
        };

        this.$refs.dateRange.onPresetChange('currentYear');

        this.$refs.dateRange.$emit = originalEmit;
      }

      if (this.$refs.typeFilter) {
        const originalEmit = this.$refs.typeFilter.$emit;
        this.$refs.typeFilter.$emit = (event, ...args) => {
          if (event !== 'change') {
            originalEmit.call(this.$refs.typeFilter, event, ...args);
          }
        };

        this.$refs.typeFilter.onGroupingChange('monthly');

        this.$refs.typeFilter.$emit = originalEmit;
      }
    });
  },
  methods: {
    async fetch() {
      if (!this.app) {
        throw new Error(this.$t('common_error_occurred'));
      }

      return this.axios.get(
        '/api/reports/data',
        {
          params: {
            report: 'v2\\WlaStatsReport',
            app: this.app,
            startDate: this.filtering.dates.from,
            endDate: this.filtering.dates.to,
            type: this.filtering.grouping || 'daily',
          },
        },
      );
    },
    afterFetchSuccess(response) {
      if (response.data) {
        this.statsData = response.data.data;
        this.items = response.data.data;

        if (response.data.currency) {
          this.currencyData = response.data.currency;
        }
      } else {
        throw new Error(this.$t('common_error_occurred'));
      }
    },
    afterFetchFailure() {
      this.error = true;
    },
    onFiltersChange(filtering) {
      this.filtering = {
        ...this.filtering,
        ...filtering,
      };

      this.error = false;
      this.currencyData = null;

      this.resetCompareWithPreviousYear();
    },
    onDateRangeChange(dates) {
      this.$set(this.filtering, 'dates', dates);

      this.error = false;
      this.currencyData = null;

      this.resetCompareWithPreviousYear();
    },
    resetCompareWithPreviousYear() {
      if (this.$refs.statsChart && this.$refs.statsChart.compareToPreviousYear) {
        this.$refs.statsChart.compareToPreviousYear = false;

        this.$refs.statsChart.onCompareToPreviousYearChange(false);
      }
    },
    formatDate(date) {
      if (this.filtering.grouping === 'monthly') {
        return this.$options.filters.formatDateMonth(date);
      }
      return this.$options.filters.formatDateDay(date);
    },
    formatDateDisplay(date) {
      if (!date) return '-';
      return this.$options.filters.formatDateDay(date);
    },
    getAllData() {
      if (this.lock) {
        return;
      }

      this.error = false;

      this.getData();

      if (this.$refs.statsChart
        && this.$refs.statsChart.onCompareToPreviousYearChange
        && this.$refs.statsChart.compareToPreviousYear) {
        this.$refs.statsChart.onCompareToPreviousYearChange(
          this.$refs.statsChart.compareToPreviousYear,
        );
      }
    },
  },
};
</script>

<style scoped>
/* Style dla komponentu statystyk */
</style>
