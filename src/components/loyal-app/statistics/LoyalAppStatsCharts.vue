<template>
  <div>
    <v-row>
      <v-col
        cols="12"
        lg="8"
      >
        <div class="d-flex flex-column">
          <dual-axis-chart
            class="px-2"
            :title="$t('loyalApp_statistics_chart_payments_users')"
            :legend="chartLegend"
            legend-align="left"
            :x-data="chartRanges"
            :bar-series-values="barChartSeriesValues"
            :line-series-values="lineChartSeriesValues"
            :loader="loader || chartLoader"
            :currency-unit="currencySymbol"
            bar-max-width="90"
            legend-vertical-align="0"
            :selected-legend-options="selectedLegendOptions"
            :left-axis-name="$t('common_monetary_values')"
            :right-axis-name="$t('loyalApp_quantity')"
            @legendselectchanged="legendSelectChanged"
          />
          <v-checkbox
            v-model="compareToPreviousYear"
            :label="$t('finance_compareWithPreviousYear')"
            class="align-self-end mr-14"
            @change="onCompareToPreviousYearChange"
          />
        </div>
      </v-col>
      <v-col
        cols="12"
        lg="4"
        class="d-flex align-items-center"
      >
        <div class="pie-chart-container mt-6">
          <div class="pie-chart-wrapper position-relative">
            <pie-chart
              class="px-2"
              :title="$t('loyalApp_statistics_chart_by_source')"
              :values="pieChartValues"
              :loader="loader || chartLoader"
              :unit="currencySymbol"
              legend-align="left"
            />
            <div
              v-if="!loader && !chartLoader && totalPieChartValue > 0"
              class="pie-chart-sum-overlay"
            >
              <div class="pie-chart-sum-text">
                <strong>
                  {{ $t('turnover.table.total') }}:
                  {{ formatValue(totalPieChartValue) }}{{ currencySymbol }}
                </strong>
              </div>
            </div>
          </div>
        </div>
      </v-col>
    </v-row>
  </div>
</template>

<script>
import PieChart from '@components/common/charts/PieChart.vue';
import DualAxisChart from '@/components/common/charts/DualAxisChart.vue';

export default {
  name: 'LoyalAppStatsCharts',
  components: {
    DualAxisChart,
    PieChart,
  },
  props: {
    data: {
      type: Object,
      default() {
        return {
          payments: [],
          balance: [],
          stats: [],
        };
      },
    },
    loader: {
      type: Boolean,
      default: false,
    },
    currencySymbol: {
      type: String,
      default: '',
    },
    app: {
      type: String,
      default: 'BKFPAY',
    },
  },
  data() {
    return {
      selectedLegendOptions: {
        [this.$t('common_users')]: true,
        [this.$t('common_carwashes')]: true,
        [this.$t('loyalApp_payments')]: true,
        [this.$t('common_topups')]: true,
        [this.$t('loyalApp_bonus')]: true,
        [this.$t('loyalApp_directPayments')]: true,
        [`${this.$t('loyalApp_payments')} (${this.$t('common_previousYear')})`]: true,
      },
      colors: {
        payments: '#629ae9',
        directPayments: '#063970',
        topUps: '#2A5DA3',
        bonus: '#5E7DA8',
        users: '#76b5c5',
        carwashes: '#03a5fc',
        previousYear: '#ff0000',
      },
      compareToPreviousYear: false,
      previousYearData: null,
      previousYearLineData: [],
      chartLoader: false, // Dodatkowy loader dla wykresu
      lastCompareState: null, // Zapamiętujemy ostatni stan porównania
    };
  },
  computed: {
    chartRanges() {
      if (!this.data || !this.data.payments || !this.data.payments.length) {
        return [];
      }

      return this.data.payments.map((item) => this.formatPeriod(item.period));
    },
    barChartSeriesValues() {
      if (!this.data || !this.data.payments || !this.data.payments.length) {
        return [];
      }

      return [
        {
          data: this.data.payments.map((item) => parseFloat(item.top_up)),
          name: this.$t('common_topups'),
          color: this.colors.topUps,
        },
        {
          data: this.data.payments.map((item) => parseFloat(item.bonus)),
          name: this.$t('loyalApp_bonus'),
          color: this.colors.bonus,
        },
        {
          data: this.data.payments.map((item) => parseFloat(item.direct_payment)),
          name: this.$t('loyalApp_directPayments'),
          color: this.colors.directPayments,
        },
      ];
    },
    lineChartSeriesValues() {
      if (!this.data || !this.data.payments || !this.data.payments.length) {
        return [];
      }

      const series = [
        {
          data: this.data.payments.map((item) => Math.abs(parseFloat(item.payment))),
          name: this.$t('loyalApp_payments'),
          color: this.colors.payments,
          yAxisIndex: 0, // Lewa oś (wartości pieniężne)
        },
        {
          data: this.data.payments.map((item) => parseInt(item.user, 10)),
          name: this.$t('common_users'),
          color: this.colors.users,
          yAxisIndex: 1, // Prawa oś (ilości)
        },
        {
          data: this.data.payments.map((item) => parseInt(item.carwash, 10)),
          name: this.$t('common_carwashes'),
          color: this.colors.carwashes,
          yAxisIndex: 1, // Prawa oś (ilości)
        },
      ];

      // Dodaj dane z poprzedniego roku, jeśli są dostępne i opcja jest włączona
      if (this.compareToPreviousYear && this.previousYearLineData.length > 0) {
        series.push({
          data: this.previousYearLineData,
          name: `${this.$t('loyalApp_payments')} (${this.$t('common_previousYear')})`,
          color: this.colors.previousYear,
          yAxisIndex: 0, // Lewa oś (wartości pieniężne)
        });
      }

      return series;
    },
    chartLegend() {
      const legend = [
        { name: this.$t('loyalApp_payments'), lineChart: true },
        { name: this.$t('common_users'), lineChart: true },
        { name: this.$t('common_carwashes'), lineChart: true },
        { name: this.$t('common_topups'), lineChart: false },
        { name: this.$t('loyalApp_bonus'), lineChart: false },
        { name: this.$t('loyalApp_directPayments'), lineChart: false },
      ];

      // Dodaj rok poprzedni do legendy, jeśli opcja jest włączona i dane są dostępne
      if (this.compareToPreviousYear && this.previousYearLineData.length > 0) {
        legend.push({
          name: `${this.$t('loyalApp_payments')} (${this.$t('common_previousYear')})`,
          lineChart: true,
        });
      }

      return legend;
    },
    pieChartValues() {
      if (!this.data || !this.data.stats || !this.data.stats.length) {
        return [];
      }

      const pieColors = [
        this.colors.payments,
        this.colors.topUps,
        this.colors.bonus,
        this.colors.carwashes,
        this.colors.users,
      ];

      return this.data.stats.map((item, index) => ({
        value: parseFloat(item.balance),
        name: this.$t(item.issuer),
        itemStyle: {
          color: pieColors[index % pieColors.length], // Cykliczne używanie kolorów
        },
      }));
    },
    totalPieChartValue() {
      if (!this.data || !this.data.stats || !this.data.stats.length) {
        return 0;
      }

      return this.data.stats.reduce((sum, item) => sum + parseFloat(item.balance), 0);
    },
  },
  methods: {
    formatPeriod(period) {
      if (!period) return '';

      const [year, month] = period.split('-');
      const date = new Date(parseInt(year, 10), parseInt(month, 10) - 1, 1);

      return this.$options.filters.formatDateMonth(date);
    },
    formatValue(value) {
      // Formatowanie liczby z dwoma miejscami po przecinku i separatorem tysięcy
      return new Intl.NumberFormat('pl-PL', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }).format(value);
    },
    legendSelectChanged(selected) {
      this.selectedLegendOptions = selected.selected;
      this.$emit('legendselectchanged', selected);
    },
    async onCompareToPreviousYearChange(compare) {
      // Jeśli nie ma zmiany stanu, nie wykonujemy zapytania
      if (compare === this.lastCompareState) {
        return;
      }

      // Zapamiętujemy ostatni stan porównania
      this.lastCompareState = compare;

      if (compare) {
        try {
          this.chartLoader = true; // Włączamy loader przed pobraniem danych
          await this.fetchPreviousYearData();
        } catch (error) {
          this.snackbar.showMessage('error', this.$t('common_error_occurred'));
        } finally {
          this.chartLoader = false; // Wyłączamy loader po zakończeniu, niezależnie od wyniku
        }
      } else {
        this.previousYearLineData = [];
      }
    },
    async fetchPreviousYearData() {
      if (!this.data || !this.data.payments || !this.data.payments.length) {
        return;
      }

      try {
        // Pobierz pierwszy i ostatni okres z danych
        const periods = this.data.payments.map((item) => item.period).sort();
        const firstPeriod = periods[0];
        const lastPeriod = periods[periods.length - 1];

        // Oblicz daty dla roku poprzedniego
        const [firstYear, firstMonth] = firstPeriod.split('-');
        const [lastYear, lastMonth] = lastPeriod.split('-');

        const prevFirstYear = parseInt(firstYear, 10) - 1;
        const prevLastYear = parseInt(lastYear, 10) - 1;

        const prevFirstPeriod = `${prevFirstYear}-${firstMonth}`;
        const prevLastPeriod = `${prevLastYear}-${lastMonth}`;

        // Pobierz dane z poprzedniego roku
        const response = await this.axios.get('/api/reports/data', {
          params: {
            report: 'v2\\WlaStatsReport',
            app: this.app,
            startDate: prevFirstPeriod,
            endDate: prevLastPeriod,
            type: 'monthly', // Zakładamy, że to jest domyślny typ
          },
        });

        if (response.data && response.data.data && response.data.data.payments) {
          this.previousYearData = response.data.data;

          // Pobierz wartości płatności z poprzedniego roku

          this.previousYearLineData = this.previousYearData.payments.map(
            (item) => Math.abs(parseFloat(item.payment)),
          );
        }
      } catch (error) {
        // Emitujemy błąd zamiast logowania do konsoli
        this.$emit('error', this.$t('common_error_occurred'));
      }
    },
  },
};
</script>

<style scoped>
/* Style dla komponentu wykresów */
.pie-chart-container {
  width: 100%;
}

.pie-chart-wrapper {
  position: relative;
}

.pie-chart-sum-overlay {
  position: absolute;
  top: 6px;
  left: 0;
  right: 50px;
  bottom: 0;
  display: flex;
  justify-content: end;
  align-items: start;
  pointer-events: none; /* Pozwala na interakcję z wykresem pod spodem */
}

.pie-chart-sum-text {
  background-color: rgba(255, 255, 255, 0.85);
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transform: translateY(30px); /* Przesunięcie w dół, aby znalazło się na środku wykresu */
}

@media (min-width: 1264px) {
  .pie-chart-container {
    margin-top: 50px !important;
    /* Przesunięcie wykresu kołowego w dół na większych ekranach */
  }
}
</style>
