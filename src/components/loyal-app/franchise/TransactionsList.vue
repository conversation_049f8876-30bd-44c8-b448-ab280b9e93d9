<template>
  <report-data-table
    ref="dataTable"
    :title="$t('transactions.history')"
    :headers="headers"
    :filters="params"
    :url="url"
  >
    <template #[`item.time`]="{ item }">
      <div class="flex-inline-start">
        <status-badge :status="item.status" />
        <date-time-formatter
          :value="item.time"
        />
      </div>
    </template>

    <template #[`item.value`]="{ item }">
      <currency-formatter
        :value="item.value"
        :symbol="item.currency"
      />
    </template>
  </report-data-table>
</template>

<script>
import ReportDataTable from '@components/reports/ReportDataTable.vue';
import StatusBadge from '@components/loyal-app/franchise/badge/StatusBadge.vue';
import DateTimeFormatter from '@components/common/formatters/DateTimeFormatter.vue';
import CurrencyFormatter from '@components/common/formatters/CurrencyFormatter.vue';

export default {
  components: {
    CurrencyFormatter,
    DateTimeFormatter,
    ReportDataTable,
    StatusBadge,
  },
  props: {
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      carwash: null,
      startDate: null,
      endDate: null,
      interval: 'thisWeek',
      url: '/api/finance/transactions',
      headers: [
        {
          value: 'time',
          text: this.$t('finance_date'),
          sortable: false,
        },
        {
          value: 'carwashName',
          text: this.$t('finance_carwash'),
          sortable: false,
          align: 'left',
        },
        {
          value: 'standCode',
          text: this.$t('finance_standCode'),
          sortable: false,
        },
        {
          value: 'value',
          text: this.$t('finance_value'),
          sortable: false,
          align: 'right',
        },
      ],
    };
  },
  methods: {
    fetchData() {
      this.$refs.dataTable.fetchData();
    },
  },
};
</script>
