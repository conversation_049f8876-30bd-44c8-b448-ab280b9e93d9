<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
    card
  >
    <v-row>
      <v-col cols="4">
        <carwash-single-select
          v-model="config.serial"
        />
      </v-col>
      <v-col cols="4">
        <date-select
          v-model="config.date"
          :show-custom="false"
        />
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import DateSelect from '@components/reports/filters/DateSelect.vue';
import debounce from 'lodash/debounce';
import CarwashSingleSelect from '@components/reports/filters/CarwashSingleSelect.vue';

const SettingsKey = 'finance-franchise-filter';
export default {
  components: {
    CarwashSingleSelect,
    DateSelect,
  },
  model: {
    prop: 'param',
    event: 'update:param',
  },
  props: {
    param: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    const savedConfig = JSON.parse(localStorage.getItem(SettingsKey) || '{}');
    return {
      config: {
        serial: savedConfig.serial ?? null,
        date: savedConfig.date ?? null,
      },
    };
  },
  computed: {
    internalParam() {
      return {
        serial: this.config.serial,
        interval: this.config.date.interval,
      };
    },
  },
  watch: {
    internalParam: {
      handler(newVal) {
        this.debouncedUpdate(newVal);
      },
      deep: true,
      immediate: true,
    },
    config: {
      handler(newVal) {
        localStorage.setItem(SettingsKey, JSON.stringify(newVal));
      },
      deep: true,
    },
  },
  beforeCreate() {
    this.debouncedUpdate = debounce((val) => {
      this.$emit('update:param', val);
    }, 1000);
  },
};
</script>
