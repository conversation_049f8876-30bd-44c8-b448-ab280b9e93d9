<template>
  <div>
    <transactions-filters v-model="filters" />
    <transactions-list
      ref="dataTable"
      :params="filters"
    />
  </div>
</template>

<script>
import TransactionsFilters from '@components/loyal-app/franchise/TransactionsFilters.vue';
import TransactionsList from '@components/loyal-app/franchise/TransactionsList.vue';

export default {
  components: {
    TransactionsList,
    TransactionsFilters,
  },
  data() {
    return {
      filters: {},
    };
  },
};
</script>
