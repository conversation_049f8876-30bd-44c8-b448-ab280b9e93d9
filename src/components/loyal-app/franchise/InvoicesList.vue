<template>
  <report-data-table
    ref="dataTable"
    :title="$t('common_invoices')"
    :headers="headers"
    :url="url"
  >
    <template #[`item.time`]="{ item }">
      <date-time-formatter
        :value="item.invoiceDate"
      />
    </template>

    <template #[`item.totalGross`]="{ item }">
      <currency-formatter
        :value="item.totalGross"
        :symbol="item.currency"
      />
    </template>

    <template #[`item.confirmDate`]="{ item }">
      <btn-confirm2
        :confirmed="item.confirmDate !== null"
        :confirm-date="item.confirmDate"
        :confirm-user="item.confirmEmail"
        :url="`/api/gateway/wla-owner/invoice/${item.id}/confirm`"
      />
    </template>

    <template #[`item.downloadUrl`]="{ item }">
      <act-download
        :text="$t('downloadInvoice')"
        :url="`/api/gateway/wla-owner/invoice/${item.id}/download`"
      />
      <act-download
        :text="$t('common_downloadReport')"
        :url="`/api/gateway/wla-owner/invoice/${item.id}/attachment`"
      />
    </template>
  </report-data-table>
</template>

<script>
import ReportDataTable from '@components/reports/ReportDataTable.vue';
import ActDownload from '@components/common/Action/ActDownload.vue';
import BtnConfirm2 from '@components/common/BtnConfirm2.vue';
import DateTimeFormatter from '@components/common/formatters/DateTimeFormatter.vue';
import CurrencyFormatter from '@components/common/formatters/CurrencyFormatter.vue';

export default {
  components: {
    CurrencyFormatter,
    DateTimeFormatter,
    BtnConfirm2,
    ActDownload,
    ReportDataTable,
  },
  data() {
    return {
      url: '/api/finance/selfinvoices',
      headers: [
        {
          value: 'invoiceDate',
          text: this.$t('common_mobilePaymentInvoicesDate'),
        },
        {
          value: 'period',
          text: this.$t('common_period'),
          sortable: false,
        },
        {
          value: 'number',
          text: this.$t('common_invoiceNumber'),
          sortable: false,
          align: 'left',
        },
        {
          value: 'totalGross',
          text: this.$t('common_valueGross'),
          sortable: false,
          align: 'right',
        },
        {
          value: 'confirmDate',
          text: this.$t('common_confirmation'),
          sortable: false,
          align: 'center',
          width: '130px',
        },
        {
          value: 'downloadUrl',
          text: ' ',
          sortable: false,
          align: 'right',
          width: '130px',
        },
      ],
    };
  },
  methods: {
    fetchData() {
      this.$refs.dataTable.fetchData();
    },
  },
};
</script>
