<template>
  <div>
    <v-row
      no-gutters
      class="d-flex align-center mb-6"
    >
      <v-col class="d-flex justify-center">
        <carwashes-stats :stats="stats" />
      </v-col>
      <v-col cols="auto">
        <btn-refresh
          size="small"
          :disabled="loader"
          @click="getData"
        />
      </v-col>
    </v-row>
    <v-data-table
      :headers="dataTable.headers"
      :items="dataTable.items"
      item-key="id"
      :loading="loader"
      :options.sync="dataTable.pagination"
      :server-items-length="dataTable.totalItems"
      :footer-props="dataTable.footerProps"
      :search="filters.search"
      :custom-filter="filterTable"
      single-expand
    >
      <template #progress>
        <div class="text-center">
          <v-progress-circular
            indeterminate
            color="primary"
          />
        </div>
      </template>

      <template #item="{ item, expand, isExpanded }">
        <template v-if="!loader">
          <tr @click="expand(!isExpanded)">
            <td> {{ item.longName }} </td>
            <td> {{ item.owner?.name }} </td>
            <td> {{ item.fullAddress }} </td>
            <td class="text-end">
              <true-false-badge :value="item.online" />
            </td>
            <td class="text-end">
              <v-icon>
                {{ isExpanded ? 'mdi-chevron-up' : 'mdi-chevron-down' }}
              </v-icon>
            </td>
          </tr>
        </template>
      </template>

      <template #expanded-item="{ headers, item }">
        <tr>
          <td
            :colspan="headers.length"
          >
            <carwash-details
              :key="item.id"
              :carwash-id="item.id"
              :base-url="baseUrl"
            />
          </td>
        </tr>
      </template>
    </v-data-table>
  </div>
</template>

<script>
import CarwashesStats from '@components/loyal-app/carwashes/CarwashesStats.vue';
import TrueFalseBadge from '@components/common/badge/TrueFalseBadge.vue';
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import CarwashDetails from '@/components/loyal-app/carwashes/details/CarwashDetails.vue';

export default {
  components: {
    CarwashesStats,
    TrueFalseBadge,
    BtnRefresh,
    CarwashDetails,
  },
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
    filters: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dataTable: {
        totalItems: 0,
        headers: [
          {
            text: this.$t('carwash'),
            sortable: false,
          },
          {
            text: this.$t('common_usersOwner'),
            sortable: false,
          },
          {
            text: this.$t('common_address'),
            sortable: false,
          },
          {
            text: this.$t('common_online'),
            align: 'right',
            sortable: false,
          },
          {
            text: '',
            align: 'right',
            sortable: false,
          },
        ],
        items: [],
        footerProps: {
          'items-per-page-options': [50, 100, 500, 1000],
          'items-per-page-text': this.$t('common_rowsPerPage'),
        },
        pagination: {
          page: 1,
          itemsPerPage: 50,
          sortBy: ['ctime'],
          sortDesc: [true],
        },
      },
      loader: false,
      detailsLoader: false,

    };
  },
  computed: {
    stats() {
      const stats = { online: 0, offline: 0 };

      Object.keys(this.dataTable.items).forEach((property) => {
        const item = this.dataTable.items[property];

        if (item.online) {
          stats.online += 1;
        } else {
          stats.offline += 1;
        }
      });

      return stats;
    },
  },
  watch: {
    baseUrl() {
      this.getData();
      this.dataTable.pagination.page = 1;
    },

  },
  mounted() {
    if (this.baseUrl !== null) {
      this.getData();
    }
  },
  methods: {
    async getData() {
      this.loader = true;
      const response = await this.axios.get(
        `${this.baseUrl}/carwashes`,
      );

      this.dataTable.items = response.data;
      this.dataTable.totalItems = response.data.totalItems;
      this.loader = false;
    },

    // Zmodyfikowany filterTable:
    filterTable(value, search, item) {
      if (!search) return true;

      const normalizedSearch = search.toString().toLowerCase();

      const fieldsToSearch = [
        item.longName,
        item.owner?.name,
        item.fullAddress,
      ];

      return fieldsToSearch.some((field) => field?.toString()
        .toLowerCase()
        .includes(normalizedSearch));
    },
  },
};

</script>
