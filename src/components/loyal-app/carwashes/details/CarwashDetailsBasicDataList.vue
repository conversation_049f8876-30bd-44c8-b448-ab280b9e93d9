<template>
  <v-card
    class="px-3 py-3"
    outlined
    color="blue-grey lighten-5"
  >
    <div class="d-flex justify-space-between align-center">
      <h3 class="my-3">
        {{ $t('loyalApp_carwashBasicData') }}
      </h3>
      <carwash-details-edit-modal
        :carwash-id="item.id"
        :carwash-data="item"
        :base-url="baseUrl"
        @update-success="onSuccess"
      />
    </div>
    <div class="d-flex justify-space-between my-3">
      <h4>
        {{ $t('common_tableDescription') }}
      </h4>
    </div>
    <v-simple-table
      dense
      class="elevation-3 py-2"
    >
      <template #default>
        <tbody>
          <tr>
            <td
              class="text-left"
            >
              {{ item.description ?? '-' }}
            </td>
          </tr>
        </tbody>
      </template>
    </v-simple-table>
    <div class="d-flex justify-space-between my-3">
      <h4>
        {{ $t('loyalApp_cashback') }}
      </h4>
    </div>
    <v-simple-table
      dense
      class="elevation-3 py-2 my-2"
    >
      <template #default>
        <tbody>
          <tr>
            <td
              class="border-right text-left"
            >
              {{ $t('loyalApp_cashback') }}
            </td>
            <td
              class="text-left"
            >
              {{ item.cashback }}
            </td>
          </tr>
        </tbody>
      </template>
    </v-simple-table>
    <v-container fluid>
      <v-row
        no-gutters
        justify="center"
      >
        <img
          v-if="item.photo && item.photo != 'null'"
          :src="`data:image/png;base64,${item.photo}`"
          class="formLogo"
        >
        <span v-else>
          {{ $t('loyalApp_no_carwash_photo') }}
        </span>
      </v-row>
    </v-container>
  </v-card>
</template>

<script>
import CarwashDetailsEditModal from '@/components/loyal-app/carwashes/details/CarwashDetailsEditModal.vue';

export default {
  components: {
    CarwashDetailsEditModal,
  },
  props: {
    item: {
      type: Object,
      required: true,
    },
    baseUrl: {
      type: String,
      required: true,
    },
  },
  methods: {
    onSuccess() {
      this.$emit('update-success');
    },
  },
};
</script>
