<template>
  <v-card
    class="px-3 pt-3"
    outlined
    color="blue-grey lighten-5"
  >
    <key-value-table
      :data="details"
    />

    <key-value-table
      :data="owner"
      :header-text="$t('common_owner')"
      :translate-keys="true"
    >
      <template #[`loyalApp_agreement`]>
        <a
          :href="item.owner.link"
          target="_blank"
        >
          {{ item.owner.aggrement }}
        </a>
      </template>
    </key-value-table>

    <carwash-details-stands-table
      :items="item.cwApi.stands"
      :base-url="baseUrl"
    />

    <carwash-details-basic-data-table
      :item="item"
      :base-url="baseUrl"
      @update-success="onSuccess"
    />
  </v-card>
</template>

<script>
import i18n from '@/i18n';
import KeyValueTable from '@/components/common/KeyValueTable.vue';
import CarwashDetailsStandsTable from '@/components/loyal-app/carwashes/details/CarwashDetailsStandsList.vue';
import CarwashDetailsBasicDataTable from '@/components/loyal-app/carwashes/details/CarwashDetailsBasicDataList.vue';

export default {
  name: 'DetailsTable',
  components: {
    KeyValueTable,
    CarwashDetailsStandsTable,
    CarwashDetailsBasicDataTable,
  },
  props: {
    data: {
      type: [Object, Array],
      required: true,
    },
    baseUrl: {
      type: String,
      required: true,
    },
    headerText: {
      type: String,
      default: () => i18n.t('dashboard.table-details'),
      required: false,
    },
  },
  data() {
    return {
      deviceIcons: {
        CAR_WASH: 'mdi-car-wash',
        VACUUM_CLEANER: 'mdi-auto-fix',
        DISTRIBUTOR: 'mdi-cup-water',
        MONEY_CHANGER: 'mdi-sync',
      },
      deviceText: {
        CAR_WASH: this.$t('fiscal_transactions.source.CAR_WASH'),
        VACUUM_CLEANER: this.$t('fiscal_transactions.source.VACUUM_CLEANER'),
        DISTRIBUTOR: this.$t('fiscal_transactions.source.DISTRIBUTOR'),
        MONEY_CHANGER: this.$t('fiscal_transactions.source.MONEY_CHANGER'),
      },
    };
  },
  computed: {
    item() {
      if (typeof this.data === 'undefined') {
        return [];
      }

      return this.data;
    },
    details() {
      return {
        address: this.item.address,
        last_mobile: this.item.cwApi.last_mobile_online,
        ip: this.item.cwApi.ip ?? '-',
        software: this.item.cwApi.software ?? '-',
      };
    },
    owner() {
      return {
        'table.company_name': this.item.owner.client?.name ?? '-',
        common_nip: this.item.owner.client?.nip ?? '-',
        loyalApp_agreement: '',
      };
    },
  },
  methods: {
    onSuccess() {
      this.$emit('refresh');
    },
  },
};
</script>
