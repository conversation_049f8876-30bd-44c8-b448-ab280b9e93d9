<template>
  <v-card
    class="px-3 py-3"
    outlined
    color="blue-grey lighten-5"
  >
    <h3 class="mb-3">
      {{ $t('loyalApp_stands') }}
    </h3>
    <v-simple-table
      v-if="items.length"
      dense
      class="elevation-3 py-2"
    >
      <template #default>
        <tbody>
          <tr>
            <td
              class="border-right text-left"
            >
              {{ $t('turnover.table.name') }}
            </td>
            <td
              class="border-right text-left"
            >
              {{ $t('common_standCode') }}
            </td>
            <td
              class="border-right text-center"
            >
              {{ $t('loyalApp_paymentEnabled') }}
            </td>
            <td
              class="text-center"
            >
              {{ $t('loyalApp_qrCode') }}
            </td>
          </tr>
          <tr
            v-for="[key, stand] in Object.entries(items)"
            :key="key"
          >
            <td
              class="border-right text-left"
            >
              <v-icon>
                <!-- w okresie przejsciowym beloyal inaczej zwraca source od mp-->
                {{ getIconForDeviceType(stand?.source.name ?? stand.source) }}
              </v-icon>
              {{ getTextForDeviceType(stand?.source.name ?? stand.source) }}
              {{ `#${stand.bay_id}` }}
            </td>
            <td
              class="border-right text-left"
            >
              <!-- w okresie przejsciowym beloyal inaczej zwraca stand_code od mp-->
              {{ stand.stand_code ?? stand.stand_id }}
            </td>
            <td
              class="border-right text-center"
            >
              <true-false-badge :value="stand.mobile_enable" />
            </td>
            <td class="text-center">
              <act-download
                :url="`${baseUrl}/sticker/${stand.stand_code}`"
              />
            </td>
          </tr>
        </tbody>
      </template>
    </v-simple-table>
    <v-card-text v-else>
      <v-icon small>
        mdi-alert-circle-outline
      </v-icon>
      <i class="ml-2">{{ $t('common_noData') }}</i>
    </v-card-text>
  </v-card>
</template>

<script>
import ActDownload from '@components/common/Action/ActDownload.vue';
import TrueFalseBadge from '@/components/common/badge/TrueFalseBadge.vue';

export default {
  components: {
    ActDownload,
    TrueFalseBadge,
  },
  props: {
    items: {
      type: [Array],
      required: true,
    },
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      deviceIcons: {
        CAR_WASH: 'mdi-car-wash',
        VACUUM_CLEANER: 'mdi-auto-fix',
        DISTRIBUTOR: 'mdi-cup-water',
        MONEY_CHANGER: 'mdi-sync',
      },
      deviceText: {
        CAR_WASH: this.$t('fiscal_transactions.source.CAR_WASH'),
        VACUUM_CLEANER: this.$t('fiscal_transactions.source.VACUUM_CLEANER'),
        DISTRIBUTOR: this.$t('fiscal_transactions.source.DISTRIBUTOR'),
        MONEY_CHANGER: this.$t('fiscal_transactions.source.MONEY_CHANGER'),
      },
    };
  },
  methods: {
    getIconForDeviceType(deviceType) {
      if (deviceType in this.deviceIcons) {
        return this.deviceIcons[deviceType];
      }
      return 'mdi-help';
    },
    getTextForDeviceType(deviceType) {
      if (deviceType in this.deviceText) {
        return this.deviceText[deviceType];
      }
      return deviceType;
    },
  },
};
</script>
