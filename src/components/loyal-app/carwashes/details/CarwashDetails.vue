<template>
  <v-row
    v-if="loader || carwash == null"
    class="d-flex align-center justify-center"
  >
    <v-progress-circular
      indeterminate
      color="primary"
    />
  </v-row>
  <v-row
    v-else
    class="my-4"
  >
    <v-col
      cols="5"
    >
      <gmap-map
        :key="carwashId"
        :center="{lat:carwash?.lat, lng:carwash?.lon}"
        :zoom="14"
        :options="{
          zoomControl: true,
          mapTypeControl: false,
          scaleControl: false,
          streetViewControl: false,
          rotateControl: false,
          fullscreenControl: false,
          disableDefaultUi: false
        }"
        style="min-height:300px; max-height:500px; height: 90%"
      >
        <gmap-marker
          :key="carwashId"
          :position="{lat:carwash?.lat, lng:carwash?.lon}"
          :clickable="true"
          :draggable="false"
        />
      </gmap-map>
    </v-col>
    <v-col
      cols="7"
    >
      <carwash-details-table
        :key="carwashId"
        :data="carwash"
        :base-url="baseUrl"
        @refresh="getData"
      />
    </v-col>
  </v-row>
</template>

<script>
import CarwashDetailsTable from '@/components/loyal-app/carwashes/details/CarwashDetailsTable.vue';

export default {
  components: {
    CarwashDetailsTable,
  },
  props: {
    carwashId: {
      type: Number,
      required: true,
    },
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      loader: false,
      carwash: null,
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      this.loader = true;
      this.axios.get(`${this.baseUrl}/carwash/${this.carwashId}`)
        .then((response) => {
          this.carwash = response.data;
          this.loader = false;
        });
    },
  },
};
</script>
