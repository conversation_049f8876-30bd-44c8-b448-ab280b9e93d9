<template>
  <div>
    <v-tooltip bottom>
      <template #activator="{ on, attrs }">
        <v-btn
          tile
          rounded
          x-small
          fab
          elevation="1"
          color="primary"
          v-bind="attrs"
          v-on="on"
          @click.native="dialog = true"
          @click.stop
        >
          <v-icon>
            mdi-pencil
          </v-icon>
        </v-btn>
      </template>
      <span>{{ $t('actions.edit') }}</span>
    </v-tooltip>

    <generic-modal
      v-model="dialog"
      :title="$t('common_carwash')"
      :loading="loaders.site || loaders.actualize"
      @submit="submit"
    >
      <v-container grid-list-md>
        <v-container
          fluid
        >
          <v-row>
            <v-col
              sm="12"
            >
              <v-form
                ref="formUserEdit"
                v-model="form.valid"
                lazy-validation
              >
                <v-file-input
                  class="pt-0"
                  :rules="rules.fileSize"
                  accept="image/png, image/jpeg, image/bmp"
                  prepend-icon="mdi-camera"
                  :label="$t('loyalApp_carwash_photo')"
                  @change="uploadFile"
                />
                <v-container
                  fluid
                  justify-center
                  fill-height
                >
                  <div
                    class="flex-column"
                    align="center"
                    justify="center"
                  >
                    <img
                      v-if="photo"
                      :src="photo"
                      class="formLogo"
                    >
                  </div>
                </v-container>
              </v-form>
            </v-col>
          </v-row>
        </v-container>
        <v-form
          ref="formClientEdit"
          v-model="form.valid"
          lazy-validation
        >
          <v-layout wrap>
            <v-col
              cols="12"
            >
              <v-text-field
                v-model="carwash.cashback"
                v-validate="'required|min_value:0|max_value:100'"
                :label="$t('loyalApp_cashback')"
                prepend-icon="mdi-percent-outline"
                name="cashback"
                :data-vv-as="`${$t('loyalApp_cashback')}`"
                :error-messages="errors.collect('cashback')"
              />
            </v-col>
            <v-col
              cols="12"
            >
              <v-textarea
                v-model="carwash.description"
                prepend-icon="mdi-text"
                :label="$t('common_tableDescription')"
                class="descriptionText"
                auto-grow
                rows="1"
              />
            </v-col>
          </v-layout>
        </v-form>
      </v-container>
    </generic-modal>
  </div>
</template>
<script>
import GenericModal from '@/components/common/GenericModal.vue';

export default {
  components: {
    GenericModal,
  },
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
    carwashId: {
      type: Number,
      default: null,
    },
    carwashData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      loaders: {
        site: true,
        actualize: false,
      },
      dialog: false,
      form: {
        cancelRefill: {
          valid: true,
        },
        validateOnBlur: true,
        valid: false,
      },
      rules: {
        required: [(v) => !!v || this.$t('common_fieldRequired')],
        selectRequired2: [(v) => (v !== null && v.length > 0) || this.$t('common_fieldRequired')],
        fileSize: [(value) => !value || value.size < 500000 || 'Avatar size should be less than 0,5 MB!'],
      },
      photo: `data:image/png;base64,${this.carwashData.photo}`,
    };
  },
  computed: {
    carwash() {
      if (typeof this.carwashData === 'undefined') {
        return {};
      }

      return this.carwashData;
    },
  },
  watch: {
    dialog(val) {
      if (val) {
        // Reset form and validation state before showing modal
        this.$nextTick(() => {
          this.resetValidationErrorsAndClearFields();
          this.loaders.actualize = false;
          this.loaders.site = false;

          // this.getData();
        });
      } else {
        // Clean up when modal is closed
        this.resetValidationErrorsAndClearFields();
      }
    },
  },
  beforeDestroy() {
    this.resetValidationErrorsAndClearFields();
  },
  methods: {
    uploadFile(file) {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        this.photo = reader.result;
      };
    },
    validateAll() {
      return this.$validator.validateAll();
    },
    resetAndValidate() {
      this.$validator.reset();
      return this.validateAll();
    },
    resetValidationErrorsAndClearFields() {
      // this.clearFormData();
      if (this.$refs.formCarwashEdit) {
        this.$refs.formCarwashEdit.reset();
        this.$refs.formCarwashEdit.resetValidation();
      }
      // this.$validator.reset();
      this.$nextTick(() => {
        if (this.$refs.formCarwashEdit) {
          this.$refs.formCarwashEdit.validate();
        }
      });
    },
    onError() {
      this.$emit('update-fail');
      // on error
      this.closeDialog();
    },
    submit() {
      this.loaders.actualize = true;
      this.loaders.site = true;

      const params = {
        photo: this.photo.replace('data:image/png;base64,', ''),
        description: this.carwash.description,
        cashback: Number(this.carwash.cashback),
      };

      this.axios.patch(
        `${this.baseUrl}/carwash/${this.carwash.id}`,
        params,
      )
        .then(
          (response) => {
            if ((response.status === 200) && response.data) {
              this.loaders.actualize = false;
              this.loaders.site = false;
              this.onSuccess();
              this.closeDialog();
            }
          },
          () => {
            this.onError();
            this.loaders.actualize = false;
            this.loaders.site = false;
          },
        );
    },
    clearFormData() {
      this.carwash = {
        desctiprion: null,
        cashback: true,
      };
    },
    closeDialog() {
      this.dialog = false;
      this.$nextTick(() => {
        this.resetValidationErrorsAndClearFields();
      });
    },
    onSuccess() {
      this.$emit('update-success');
    },
  },
};
</script>
