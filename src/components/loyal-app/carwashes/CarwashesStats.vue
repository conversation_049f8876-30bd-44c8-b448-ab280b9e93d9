<template>
  <div class="d-flex">
    <v-card
      class="card-width mr-6 py-6 px-10"
      elevation="2"
    >
      <v-row
        class="d-flex align-center"
      >
        <v-col
          cols="auto"
          class="d-flex align-center"
        >
          <h3 class="mr-3">
            {{ $t('loyalApp_online') }}
          </h3>
          <v-icon
            color="green darken-2"
          >
            mdi-check-circle-outline
          </v-icon>
        </v-col>
        <v-col class="d-flex justify-center">
          <span class="ml-10">
            {{ stats.online }}
          </span>
        </v-col>
      </v-row>
    </v-card>
    <v-card
      class="card-width py-6 px-10"
      elevation="2"
    >
      <v-row
        class="d-flex align-center"
      >
        <v-col
          cols="auto"
          class="d-flex align-center"
        >
          <h3 class="mr-3">
            {{ $t('loyalApp_offline') }}
          </h3>
          <v-icon
            color="error"
          >
            mdi-close-circle-outline
          </v-icon>
        </v-col>
        <v-col class="d-flex justify-center">
          <span class="ml-10">
            {{ stats.offline }}
          </span>
        </v-col>
      </v-row>
    </v-card>
  </div>
</template>

<script>
export default {
  props: {
    stats: {
      type: Object,
      default: () => ({}),
    },
  },
};
</script>

<style scoped>
@media only screen and (min-width: 800px) {
  .card-width {
    min-width: 350px;
  }
}</style>
