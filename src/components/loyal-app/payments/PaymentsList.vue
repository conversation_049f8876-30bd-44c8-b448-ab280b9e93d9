<template>
  <report-data-table
    ref="dataTable"
    report="v2\WlaExternalPaymentsReport"
    :title="$t('loyalApp_payments')"
    :headers="headers"
    :filters="paramsInternal"
  >
    <template #[`item.id`]="{ item }">
      <div
        class="d-flex align-center justify-start"
      >
        <payment-status-badge :status="item.status" />
        <span class="ml-1">
          <blurred-formatter :value="item.id" />
        </span>
      </div>
    </template>

    <template #[`item.user_email`]="{ item }">
      <email-formatter :value="item.user_email" />
    </template>

    <template #[`item.value`]="{ item }">
      <currency-formatter
        :value="item.value"
        :symbol="item.currency_symbol"
      />
    </template>
    <template #[`item.document`]="{ item }">
      <div class="d-flex align-center justify-end">
        <span class="mr-1"><blurred-formatter :value="item.document_number" /> </span>
        <document-badge
          v-if="item.document_type"
          class="ml-1"
          :type="item.document_type"
        />
        <act-download
          v-if="item.document_uri"
          :url="getActionUrl(item)"
        />
      </div>
    </template>
    <template #[`item.actions`]="{ item }">
      <refund-modal
        class="d-flex justify-end"
        :app="app"
        :payment-id="item.id"
        :enabled="item.status === 'confirmed'"
        @refunded="fetchData()"
      />
    </template>

    <template #[`footer`]="{ pageSum, totalSum }">
      <tr class="table-summary">
        <td
          colspan="7"
          class="text-start font-weight-bold"
        >
          {{ $t('common_totalOnPage') }}
        </td>
        <td class="text-sm-center font-weight-bold">
          <currency-formatter
            v-if="pageSum"
            :value="pageSum.value"
            :symbol="pageSum.currency"
          />
        </td>
        <td />
        <td />
      </tr>
      <tr>
        <td
          colspan="7"
          class="text-start font-weight-bold"
        >
          {{ $t('turnover.table.total') }}
        </td>
        <td class="text-sm-center font-weight-bold">
          <currency-formatter
            v-if="totalSum"
            :value="totalSum.value"
            :symbol="totalSum.currency"
          />
        </td>
        <td />
        <td />
      </tr>
    </template>
  </report-data-table>
</template>

<script>
import ReportDataTable from '@/components/reports/ReportDataTable.vue';
import PaymentStatusBadge from '@/components/loyal-app/payments/badge/PaymentStatusBadge.vue';
import DocumentBadge from '@/components/common/DocumentBadge.vue';
import CurrencyFormatter from '@/components/common/formatters/CurrencyFormatter.vue';
import ActDownload from '@/components/common/Action/ActDownload.vue';
import RefundModal from '@/components/loyal-app/payments/modal/RefundModal.vue';
import BlurredFormatter from '@components/common/formatters/BlurredFormatter.vue';
import EmailFormatter from '@components/common/formatters/EmailFormatter.vue';

export default {
  components: {
    EmailFormatter,
    BlurredFormatter,
    ReportDataTable,
    PaymentStatusBadge,
    DocumentBadge,
    CurrencyFormatter,
    ActDownload,
    RefundModal,
  },
  props: {
    app: {
      type: String,
      required: true,
    },
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    paramsInternal() {
      return {
        ...this.params,
        app: this.app,
      };
    },
    headers() {
      return [
        {
          text: this.$t('common_id'),
          value: 'id',
          sortable: false,
          align: 'start',
        },
        {
          text: this.$t('common_tableDate'),
          value: 'time',
          sortable: false,
        },
        {
          text: this.$t('common_user'),
          value: 'user_email',
          sortable: false,
          protected: true,
        },
        {
          text: this.$t('common_fullCustomerName'),
          value: 'client_name',
          sortable: false,
          align: 'center',
          protected: true,
        },
        {
          text: this.$t('common_taxNumber'),
          value: 'client_tax_number',
          sortable: false,
          align: 'center',
          protected: true,
        },
        {
          text: this.$t('loyalApp_accountType'),
          value: 'issuer',
          sortable: false,
          align: 'center',
        },
        {
          text: this.$t('loyalApp_externalId'),
          value: 'external_id',
          sortable: false,
          align: 'center',
          protected: true,
        },
        {
          text: this.$t('loyalApp_transactionValue'),
          value: 'value',
          sortable: false,
          align: 'center',
        },
        {
          text: this.$t('loyalApp_salesDocument'),
          value: 'document',
          sortable: false,
          align: 'end',
        },
        {
          value: 'actions',
          text: this.$t('actions.actions'),
          align: 'end',
          sortable: false,
        },
      ];
    },
  },
  methods: {
    fetchData() {
      this.$refs.dataTable.fetchData();
    },
    getActionUrl(item) {
      return `/api/gateway/wla-admin${item.document_uri}?app=${this.app}`;
    },
  },
};

</script>
