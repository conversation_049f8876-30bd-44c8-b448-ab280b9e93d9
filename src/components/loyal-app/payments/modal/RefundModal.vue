<template>
  <div>
    <!-- Refund Button -->
    <v-tooltip bottom>
      <template #activator="{ on, attrs }">
        <v-btn
          :disabled="!enabled"
          color="warning"
          elevation="1"
          fab
          tile
          v-bind="attrs"
          x-small
          v-on="on"
          @click="openDialog"
        >
          <v-icon>mdi-credit-card-refund-outline</v-icon>
        </v-btn>
      </template>
      <span>{{ $t('common_refund') }}</span>
    </v-tooltip>

    <generic-modal
      v-model="dialog"
      :title="$t('common_refund') + ' ' + paymentId"
      :confirm-button-text="$t('common_refund')"
      @submit="refund"
    >
      <div
        v-if="loading"
        class="d-flex justify-center align-center pt-10"
      >
        <v-progress-circular
          indeterminate
          color="primary"
        />
      </div>
      <v-card-text v-else>
        <v-row dense>
          <v-col
            cols="12"
            sm="6"
          >
            <strong>Status:</strong> {{ payment?.status }}
          </v-col>
          <v-col
            cols="12"
            sm="6"
          >
            <strong>Kwota:</strong> {{ payment?.value }} {{ payment?.currency_symbol }}
          </v-col>
          <v-col
            cols="12"
            sm="6"
          >
            <strong>Data:</strong> {{ payment?.time }}
          </v-col>
          <v-col
            cols="12"
            sm="6"
          >
            <strong>Metoda:</strong> {{ payment?.issuer }} {{ payment?.external_id }}
          </v-col>
          <v-col
            cols="12"
            sm="6"
          >
            <strong>Email:</strong> <email-formatter :value="payment?.user.email" />
          </v-col>
        </v-row>

        <v-divider class="my-4" />

        <v-text-field
          v-model.number="amount"
          label="Kwota do zwrotu"
          type="number"
          :max="payment?.value"
          min="0"
          step="1"
          outlined
        />
      </v-card-text>
    </generic-modal>
  </div>
</template>

<script>

import GenericModal from '@/components/common/GenericModal.vue';
import EmailFormatter from '@/components/common/formatters/EmailFormatter.vue';

export default {
  components: {
    GenericModal,
    EmailFormatter,
  },

  props: {
    app: {
      type: String,
      required: true,
    },
    paymentId: {
      type: Number,
      required: true,
    },
    enabled: {
      type: Boolean,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      dialog: false,
      payment: null,
      amount: 0,
    };
  },

  methods: {
    openDialog() {
      this.fetchPayment();
      this.dialog = true;
    },
    async fetchPayment() {
      this.loading = true;
      const res = await this.axios
        .get(`/api/gateway/wla-admin/external-payment/${this.paymentId}`, {
          params: {
            app: this.app,
          },
        }).catch(() => {});

      this.payment = res.data;
      this.loading = false;
    },
    refund() {
      this.axios
        .post(`/api/gateway/wla-admin/external-payment/${this.paymentId}/refund`, {
          app: this.app,
          amount: this.amount,
        })
        .then(() => {
          this.amount = 0;
          this.dialog = false;
          this.$emit('refunded');
        });
    },
  },
};
</script>
