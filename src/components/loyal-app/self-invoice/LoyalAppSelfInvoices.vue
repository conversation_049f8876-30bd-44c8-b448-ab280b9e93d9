<template>
  <div>
    <v-layout
      row
      wrap
    >
      <v-col cols="12" />
      <v-col cols="12">
        <v-layout
          row
          wrap
        >
          <v-col
            cols="12"
            sm="4"
          >
            <multiselect
              v-model="confirmed"
              :items="confirmedItems"
              :label="$t('common_state')"
              prepend-icon="mdi-list-status"
              :disabled="loader"
              unified
              single
              allow-null
            />
          </v-col>
        </v-layout>
        <v-layout
          row
          wrap
        >
          <v-col
            cols="12"
            sm="8"
            class="text-sm-start mt-2"
          >
            <h2>
              <span>
                {{ $t('common_invoices') }}
              </span>
            </h2>
          </v-col>
          <v-col
            cols="12"
            sm="4"
            class="d-flex justify-end"
          >
            <btn-refresh
              class="mt-0"
              size="small"
              :disabled="loader"
              @click="getData"
            />
          </v-col>
        </v-layout>
      </v-col>
      <v-col
        cols="12"
        class="pt-0"
      >
        <v-data-table
          dense
          item-key="id"
          mobile-breakpoint="0"
          :headers="headers"
          :items="items"
          :loading="loader"
          sort-by="issuanceDate"
          :sort-desc="true"
          :footer-props="footerProps"
          :options.sync="filtering.options"
          :server-items-length="totalItems"
        >
          <!--Loader-->
          <template #progress>
            <div class="text-center mx-n4">
              <v-progress-linear
                class="loader"
                indeterminate
                color="primary"
              />
            </div>
          </template>

          <!--Item-->
          <template #item="{ item }">
            <template v-if="!loader">
              <tr
                :key="item.id"
                class="text-sm-start"
              >
                <td class="text-start tabcell-carwash font-weight-bold">
                  {{ item.issuanceDate|formatDateDayTime }}
                </td>
                <td class="text-start tabcell-carwash">
                  {{ item.period }}
                </td>
                <td class="text-start">
                  {{ item.number }}
                </td>
                <td class="text-start">
                  {{ item.issuerName }}
                </td>
                <td class="text-start">
                  {{ item.issuerVatId }}
                </td>
                <custom-currency-symbol-cell
                  :value="parseFloat(item.value)"
                  :currency="item.currency"
                />
                <td class="text-center">
                  <confirm-info
                    x-small
                    :confirmed="item.confirmationDate !== null"
                    :confirm-date="item.confirmationDate"
                    :confirm-user="item.confirmedByUser"
                    :confirm-hint-text="$t('common_confirmHint')"
                  />
                </td>
                <td class="text-end">
                  <btn-download
                    x-small
                    @click="invoiceDownload(item)"
                  />
                </td>
              </tr>
            </template>
          </template>
        </v-data-table>
      </v-col>
    </v-layout>
  </div>
</template>

<script>
import DataFetchMixin from '@components/common/mixins/DataFetchMixin.vue';
import FiltersHandlingMixin from '@components/common/mixins/FiltersHandlingMixin.vue';
import BtnDownload from '@components/common/BtnDownload.vue';
import BtnRefresh from '@/components/common/button/BtnRefresh.vue';
import CustomCurrencySymbolCell from '@components/common/CustomCurrencySymbolCell.vue';
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import ExportMixin from '@components/common/mixins/ExportMixin.vue';
import ConfirmInfo from '@components/common/ConfirmInfo.vue';
import Multiselect from '@components/common/filters/Multiselect.vue';

export default {
  name: 'InvoicesListLoyalAppSelfInvoices',
  components: {
    Multiselect,
    CustomCurrencySymbolCell,
    BtnDownload,
    BtnRefresh,
    ConfirmInfo,
  },
  mixins: [
    FiltersHandlingMixin,
    DataFetchMixin,
    FilterMixin,
    ExportMixin,
  ],
  props: {
    app: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      confirmed: null,
      confirmedItems: [
        {
          text: this.$t('common_confirmed'),
          value: true,
          disabled: false,
        },
        {
          text: this.$t('common_notConfirmed'),
          value: false,
          disabled: false,
        },
        {
          text: this.$t('common_all'),
          value: null,
          disabled: false,
        },
      ],
      loader: true,
      items: [],
      dataUrl: '/api/loyalapp/selfinvoices',
      footerProps: {
        'items-per-page-options': [12, 24, 36],
        'items-per-page-text': this.$t('common_rowsPerPage'),
      },
      headers: [
        {
          value: 'issuanceDate',
          text: this.$t('common_mobilePaymentInvoicesDate'),
          sortable: false,
        },
        {
          value: 'period',
          text: this.$t('common_period'),
          sortable: false,
        },
        {
          value: 'number',
          text: this.$t('common_invoiceNumber'),
          sortable: false,
          align: 'start',
        },
        {
          value: 'issuerName',
          text: this.$t('loyalApp_issuerName'),
          sortable: false,
        },
        {
          value: 'issuerVatId',
          text: this.$t('loyalApp_issuerVatId'),
          sortable: false,
        },
        {
          value: 'value',
          text: this.$t('common_valueGross'),
          sortable: false,
          align: 'end',
        },
        {
          value: 'confirmationDate',
          text: this.$t('common_confirmation'),
          sortable: false,
          align: 'center',
          width: '130px',
        },
        {
          value: 'downloadUrl',
          text: ' ',
          sortable: false,
          align: 'end',
          width: '130px',
        },
      ],
      filtering: {
        options: {
          sortBy: ['issuanceDate'],
          sortDesc: [true],
          page: 1,
          itemsPerPage: 24,
        },
      },
    };
  },
  watch: {
    app() {
      this.getData();
    },
    confirmed: {
      handler() {
        this.filtering.options.page = 1;
        this.getData();
      },
    },
    filtering: {
      handler() {
        this.getData();
      },
      deep: true,
    },
  },
  mounted() {
    this.getData();
  },
  methods: {
    invoiceDownload(item) {
      const url = item.downloadUrl;
      const filename = `${item.number}.pdf`;
      this.onExport({
        url,
        filename,
      });
    },
    onFiltersChangePageReset(filters) {
      this.filtering.options.page = 1;
      this.onFiltersChange(filters);
    },
    parseApiResponseData(data) {
      const rows = Object.values(data);
      this.items = rows.map((row) => ({
        id: row.id,
        issuanceDate: row.issuanceDate,
        value: row.price,
        period: row.period,
        downloadUrl: row.downloadUrl,
        number: row.number,
        confirmationDate: row.confirmationDate,
        confirmedByUser: row.confirmedByUser,
        issuerName: row.issuerName,
        currency: row.currency,
        issuerVatId: row.issuerVatId,
      }));
    },
    getParams() {
      return {
        params: {
          order: this.filtering.options.sortDesc[0] ? 'DESC' : 'ASC',
          page: this.filtering.options.page || null,
          perPage: this.filtering.options.itemsPerPage || null,
          confirmed: this.confirmed,
          app: this.app,
        },
      };
    },
  },
};
</script>
