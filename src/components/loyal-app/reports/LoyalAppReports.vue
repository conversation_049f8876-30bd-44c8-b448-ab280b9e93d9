<template>
  <v-container
    fluid
  >
    <v-row>
      <v-col
        cols="12"
        class="text-sm-start pt-0"
      >
        <!--
        <v-tabs
          slider-color="primary"
          centered
        >
          <v-tab
            v-for="(item) in tabs"
            :key="item.key"
            ripple
            :disabled="!item.show"
          >
            {{ item.text }}
            <v-icon> {{ item.icon }} </v-icon>
          </v-tab>
          <v-tab-item
            v-for="(item) in tabs"
            :key="item.key"
          >
          -->
        <component
          :is="item.component"
          v-for="(item) in tabs"
          :key="item.key"
          v-bind="item.props"
        />
        <!--
          </v-tab-item>
        </v-tabs>
        -->
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { mapGetters } from 'vuex';
import CarwashStatsReport from '@components/loyal-app/reports/carwashStats/CarwashStatsReport.vue';

export default {
  components: {
    CarwashStatsReport,
  },
  props: {
    app: {
      type: String,
      default: null,
    },
  },
  computed: {
    ...mapGetters({
      hasRole: 'auth/hasRole',
    }),
    isPremiumSubscription() {
      return this.hasRole('ROLE_SUBSCRIPTION_PREMIUM');
    },
    tabs() {
      return [
        {
          component: CarwashStatsReport,
          icon: 'mdi-invoice-text-edit',
          key: 'carwash-stats-config',
          show: true,
          text: this.$t('loyalApp_CarwashStats'),
          props: {
            app: this.app,
          },
        },
      ];
    },
  },
};
</script>
