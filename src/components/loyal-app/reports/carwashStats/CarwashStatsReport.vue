<template>
  <div>
    <v-layout
      row
      wrap
    >
      <v-col
        sm="12"
        cols="12"
        md="6"
        offset-md="6"
      >
        <date-range-picker
          key="dateRangeLoyalAppUsage"
          ref="dateRangeLoyalAppUsage"
          :show-presets="true"
          settings-namespace="finance:dates"
          @reload-transaction-list="onDateRangeChange"
        />
      </v-col>
    </v-layout>
    <v-layout
      row
      wrap
    >
      <v-col cols="12">
        <v-layout
          row
          wrap
        >
          <v-col
            cols="8"
            xs="12"
            class="text-sm-start mt-2"
          >
            <h2>
              <span>
                {{ $t('loyalApp_CarwashStats') }}
              </span>
            </h2>
          </v-col>
          <v-col
            cols="4"
            xs="12"
            class="d-flex justify-end"
          >
            <btn-refresh
              class="mt-0"
              size="small"
              :disabled="loader"
              @click="getData"
            />
            <report-create-modal
              btn-class="mt-2 ml-2"
              :params="getParams().params"
              :disabled="loader"
              :show-dates="true"
              :preset="filtering.dates.value"
            />
          </v-col>
        </v-layout>
      </v-col>
      <v-col
        cols="12"
        class="pt-0"
      >
        <v-data-table
          dense
          item-key="id"
          mobile-breakpoint="0"
          :headers="headers"
          :items="items"
          :loading="loader"
          sort-by="issuanceDate"
          :sort-desc="true"
          :footer-props="footerProps"
          :options.sync="filtering.options"
          :server-items-length="totalItems"
          hide-default-footer
        >
          <!--Loader-->
          <template #progress>
            <div class="text-center mx-n4">
              <v-progress-linear
                class="loader"
                indeterminate
                color="primary"
              />
            </div>
          </template>

          <!--Item-->
          <template #item="{ item }">
            <template v-if="!loader">
              <tr
                :key="item.carwash"
                class="text-sm-start"
              >
                <td class="text-start font-weight-bold">
                  {{ item.carwash }}
                </td>
                <td class="text-end">
                  {{ item.BKFPAY|currencySymbol(currencyObject.symbol) }}
                </td>
                <td class="text-end">
                  {{ item.EXTERNAL_PAYMENT|currencySymbol(currencyObject.symbol) }}
                </td>
                <td class="text-end">
                  {{ item.SUBSCRIPTION|currencySymbol(currencyObject.symbol) }}
                </td>
              </tr>
            </template>
          </template>

          <template #[`body.append`]>
            <template v-if="!loader">
              <tr class="table-summary">
                <td class="text-start font-weight-bold">
                  {{ $t('turnover.table.total') }}
                </td>
                <custom-currency-symbol-cell
                  weight="bold"
                  :value="Number(totalSums.BKFPAY)"
                  :currency="currencyObject.symbol"
                />
                <custom-currency-symbol-cell
                  weight="bold"
                  :value="Number(totalSums.EXTERNAL_PAYMENT)"
                  :currency="currencyObject.symbol"
                />
                <custom-currency-symbol-cell
                  weight="bold"
                  :value="Number(totalSums.SUBSCRIPTION)"
                  :currency="currencyObject.symbol"
                />
              </tr>
            </template>
          </template>
        </v-data-table>
      </v-col>
    </v-layout>
  </div>
</template>

<script>
import DataFetchMixin from '@components/common/mixins/DataFetchMixin.vue';
import FiltersHandlingMixin from '@components/common/mixins/FiltersHandlingMixin.vue';
import BtnRefresh from '@/components/common/button/BtnRefresh.vue';
import FilterMixin from '@components/common/filters/mixins/FilterMixin.vue';
import DateRangePicker from '@components/common/DateRangePicker.vue';
import ReportCreateModal from '@components/reports/ReportCreateModal.vue';
import CustomCurrencySymbolCell from '@components/common/CustomCurrencySymbolCell.vue';

export default {
  name: 'CarwashStatsReport',
  components: {
    CustomCurrencySymbolCell,
    ReportCreateModal,
    DateRangePicker,
    BtnRefresh,
  },
  mixins: [
    FiltersHandlingMixin,
    DataFetchMixin,
    FilterMixin,
  ],
  props: {
    app: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      loader: true,
      items: [],
      dataUrl: '/api/reports/data',
      footerProps: {
        'items-per-page-options': [12, 24, 36],
        'items-per-page-text': this.$t('common_rowsPerPage'),
      },
      headers: [
        {
          value: 'carwashName',
          text: this.$t('common_carwash'),
          sortable: false,
        },
        {
          value: 'BKFPAY',
          text: 'BKFPAY',
          align: 'right',
          sortable: false,
        },
        {
          value: 'EXTERNAL_PAYMENT',
          text: 'EXTERNAL_PAYMENT',
          align: 'right',
          sortable: false,
        },
        {
          value: 'SUBSCRIPTION',
          text: 'SUBSCRIPTION',
          align: 'right',
          sortable: false,
        },
      ],
      filtering: {
        dates: {
          dateFrom: null,
          dateTo: null,
          value: 'last7Days',
        },
        options: {
          sortBy: ['issuanceDate'],
          sortDesc: [true],
          page: 1,
          itemsPerPage: 24,
        },
      },
    };
  },
  watch: {
    app() {
      this.getData();
    },
    filtering: {
      handler() {
        this.getData();
      },
      deep: true,
    },
  },
  methods: {
    onDateRangeChange({
      from,
      to,
      value,
    }) {
      this.filtering.dates.dateFrom = from;
      this.filtering.dates.dateTo = to;
      this.filtering.dates.value = value;
    },
    onFiltersChangePageReset(filters) {
      this.filtering.options.page = 1;
      this.onFiltersChange(filters);
    },
    getParams() {
      return {
        params: {
          report: 'v2\\WlaCarwashStatsReport',
          startDate: this.filtering.dates.dateFrom
            ? `${this.filtering.dates.dateFrom}`
            : null,
          endDate: this.filtering.dates.dateTo
            ? `${this.filtering.dates.dateTo}`
            : null,
          app: this.app,
        },
      };
    },
  },
};
</script>
