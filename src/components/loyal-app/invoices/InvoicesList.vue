<template>
  <report-data-table
    ref="dataTable"
    report="v2\WlaInvoicesReport"
    :title="$t('common_invoices')"
    :headers="headers"
    :filters="paramsInternal"
  >
    <template #[`item.user_email`]="{ item }">
      <email-formatter :value="item.user_email" />
    </template>
    <template #[`item.number`]="{ item }">
      <div class="d-flex align-center">
        <invoice-status-badge :status="item.status" />
        <span class="ml-1"><blurred-formatter :value="item.number" /></span>
      </div>
    </template>

    <template #[`item.price`]="{ item }">
      <currency-formatter
        :value="item.price"
        :symbol="item.currency_symbol"
      />
    </template>

    <template #[`item.price_excluded_tax`]="{ item }">
      <currency-formatter
        :value="item.price_excluded_tax"
        :symbol="item.currency_symbol"
      />
    </template>

    <template #[`item.tax_amount`]="{ item }">
      <currency-formatter
        :value="item.tax_amount"
        :symbol="item.currency_symbol"
      />
    </template>

    <template #[`item.actions`]="{ item }">
      <v-row
        justify="end"
        class="d-flex align-center"
      >
        <act-confirm
          :url="getActionUrl('confirm', item)"
          :visible="item.status === 'pending'"
          @confirmed="fetchData()"
        />
        <act-download
          :url="getActionUrl('download', item)"
        />
        <act-send
          :url="getActionUrl('send', item)"
        />
      </v-row>
    </template>
  </report-data-table>
</template>

<script>
import ReportDataTable from '@/components/reports/ReportDataTable.vue';
import CurrencyFormatter from '@/components/common/formatters/CurrencyFormatter.vue';
import InvoiceStatusBadge from '@/components/libs/invoices/badge/InvoiceStatusBadge.vue';
import ActDownload from '@/components/common/Action/ActDownload.vue';
import ActSend from '@/components/common/Action/ActSend.vue';
import ActConfirm from '@/components/common/Action/ActConfirm.vue';
import BlurredFormatter from '@components/common/formatters/BlurredFormatter.vue';
import EmailFormatter from '@components/common/formatters/EmailFormatter.vue';

export default {
  components: {
    EmailFormatter,
    BlurredFormatter,
    ReportDataTable,
    CurrencyFormatter,
    InvoiceStatusBadge,
    ActDownload,
    ActSend,
    ActConfirm,
  },
  props: {
    app: {
      type: String,
      required: true,
    },
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    paramsInternal() {
      return {
        ...this.params,
        app: this.app,
      };
    },
    headers() {
      return [
        {
          text: this.$t('common_username'),
          value: 'user_email',
          class: 'hidden-xs-only text-sm-start',
          showInRowExpand: false,
          sortable: false,
          protected: true,
        },
        {
          text: this.$t('loyalApp_taxNumber'),
          value: 'client_tax_number',
          class: 'hidden-md-and-down md-and-up text-sm-center',
          showInRowExpand: true,
          sortable: false,
          align: 'center',
          protected: true,
        },
        {
          text: this.$t('table.company_name'),
          value: 'client_name',
          class: 'hidden-md-and-down md-and-up text-sm-center',
          showInRowExpand: true,
          sortable: false,
          align: 'center',
          protected: true,
        },
        {
          text: this.$t('loyalApp_issuanceDate'),
          value: 'issuance_date',
          class: 'hidden-sm-and-down md-and-up text-sm-center',
          showInRowExpand: true,
          sortable: false,
          align: 'center',
        },
        {
          text: this.$t('common_paymentDate'),
          value: 'payment_date',
          class: 'text-sm-center hidden-sm-and-down md-and-up',
          showInRowExpand: true,
          sortable: false,
          align: 'center',
        },
        {
          text: this.$t('loyalApp_invoiceNumber'),
          value: 'number',
          class: 'text-sm-start',
          showInRowExpand: false,
          sortable: false,
        },
        {
          text: this.$t('loyaltyCards_valueNet'),
          value: 'price',
          class: 'text-sm-center hidden-sm-and-down md-and-up',
          showInRowExpand: true,
          sortable: false,
          align: 'center',
        },
        {
          text: this.$t('loyaltyCards_valueGross'),
          value: 'price_excluded_tax',
          class: 'text-sm-center hidden-md-and-down md-and-up',
          showInRowExpand: true,
          sortable: false,
          align: 'center',
        },
        {
          text: this.$t('loyaltyCards_vatTax'),
          value: 'tax_amount',
          class: 'text-sm-center hidden-md-and-down md-and-up',
          showInRowExpand: true,
          sortable: false,
          align: 'center',
        },
        {
          text: this.$t('actions.actions'),
          value: 'actions',
          class: 'text-sm-end',
          sortable: false,
          showInRowExpand: false,
        },
      ];
    },
  },
  methods: {
    fetchData() {
      this.$refs.dataTable.fetchData();
    },
    getActionUrl(action, item) {
      return `/api/gateway/wla-admin/invoice/${item.id}/${action}?app=${this.app}`;
    },
  },
};

</script>
