<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
    card
  >
    <v-row>
      <v-col>
        <text-search v-model="config.search" />
      </v-col>
      <v-col>
        <multiselect
          v-model="config.status"
          :items="statusFilters"
          :label="$t('common_state')"
          prepend-icon="mdi-list-status"
          allow-null
        />
      </v-col>
      <v-col>
        <date-select
          v-model="config.date"
        />
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import debounce from 'lodash/debounce';
import TextSearch from '@components/reports/filters/TextSearch.vue';
import Multiselect from '@components/reports/filters/MultiSelect.vue';
import DateSelect from '@components/reports/filters/DateSelect.vue';

const SettingsKey = 'wla-invoices-filter';

export default {
  components: {
    TextSearch,
    Multiselect,
    DateSelect,
  },
  model: {
    prop: 'param',
    event: 'update:param',
  },
  props: {
    app: {
      type: String,
      required: true,
    },
    param: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    const savedConfig = JSON.parse(localStorage.getItem(SettingsKey) || '{}');

    return {
      config: {
        search: savedConfig.search ?? null,
        status: savedConfig.status ?? null,
        date: savedConfig.date ?? null,
      },
      statuses: null,
    };
  },
  computed: {
    statusFilters() {
      if (!this.statuses) {
        return [];
      }

      return this.statuses.map((item) => ({
        text: this.$t(`filter_${item}`),
        value: item,
      }));
    },
    internalParam() {
      return {
        search: this.config.search,
        status: this.config.status?.length ? this.config.status.join(',') : [],
        ...this.config.date,
      };
    },
  },
  watch: {
    internalParam: {
      handler(newVal) {
        this.debouncedUpdate(newVal);
      },
      deep: true,
      immediate: true,
    },
    config: {
      handler(newVal) {
        localStorage.setItem(SettingsKey, JSON.stringify(newVal));
      },
      deep: true,
    },
  },
  mounted() {
    this.fetchFilters();
  },
  beforeCreate() {
    this.debouncedUpdate = debounce((val) => {
      this.$emit('update:param', val);
    }, 300);
  },
  methods: {
    async fetchFilters() {
      const response = await this.axios.get(
        `/api/gateway/wla-admin/invoices/filters?app=${this.app}`,
      );

      this.statuses = response.data.statuses;
    },
  },
};
</script>
