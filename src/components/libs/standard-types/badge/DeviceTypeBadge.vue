<template>
  <v-tooltip bottom>
    <template #activator="{ on, attrs }">
      <v-icon
        v-bind="attrs"
        v-on="on"
      >
        {{ statusInfo.icon }}
      </v-icon>
    </template>
    <span> {{ statusInfo.text }}  {{ standId ? `#${standId}` : '' }}</span>
  </v-tooltip>
</template>

<script>

import SourceType from '@components/libs/standard-types/types';

export default {
  props: {
    source: {
      type: String,
      required: true,
    },
    standId: {
      type: Number,
      default: null,
    },
  },
  computed: {
    statusInfo() {
      const status = SourceType.find((item) => item.value === this.source);
      if (status) {
        return {
          icon: status.icon,
          color: status.color,
          text: this.$t(status.text),
        };
      }

      return {
        icon: 'mdi-help-circle',
        color: 'grey',
        text: this.$t('invoice.status.unknown'),
      };
    },
  },
};
</script>
