<template>
  <v-card
    class="mt-5"
    outlined
  >
    <v-card-title class="subtitle-2 grey--text text--darken-1">
      <v-icon
        left
        small
        class="mr-2"
      >
        mdi-comment-plus-outline
      </v-icon>
      {{ $t('common_comment') }}
    </v-card-title>

    <v-card-text>
      <v-textarea
        v-model="newComment"
        auto-grow
        rows="2"
        counter="500"
        :rules="[rules.required]"
      />
    </v-card-text>

    <v-card-actions>
      <v-spacer />
      <v-btn
        color="primary"
        :loading="loading"
        :disabled="!newComment || loading"
        @click="submit"
      >
        <v-icon left>
          mdi-send
        </v-icon>
        {{ $t('submit_comment') }}
      </v-btn>
    </v-card-actions>
  </v-card>
</template>

<script>
export default {
  props: {
    url: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      newComment: '',
      loading: false,
      rules: {
        required: (v) => !!v || this.$t('comment_required'),
      },
    };
  },
  methods: {
    async submit() {
      if (!this.newComment.trim()) return;

      this.loading = true;
      try {
        const response = await this.axios.post(this.url, {
          comment: this.newComment.trim(),
        });

        this.$emit('comment-added', response.data); // emitujemy nowy komentarz

        this.newComment = '';
      } catch (e) {
        this.snackbar.showMessage('error', this.$t('common_error_occurred'));
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>
