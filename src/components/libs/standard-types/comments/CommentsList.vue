<template>
  <div>
    <!-- Nagłówek listy komentarzy -->
    <h3 class="mt-5 mb-2 grey--text text--darken-2">
      <v-icon
        left
        small
        class="mr-1"
      >
        mdi-comment-multiple-outline
      </v-icon>
      {{ $t('admin_actionHistory') }}
    </h3>

    <!-- Lista komentarzy -->
    <v-card
      v-for="item in comments"
      :key="item.id"
      class="mt-5"
      outlined
    >
      <comment-item :item="item" />
    </v-card>

    <!-- Obsługa ładowania, błędów i braku danych -->
    <div v-if="loading">
      <v-skeleton-loader
        type="card"
        class="mt-5"
      />
    </div>
    <div v-else-if="error">
      <v-alert
        type="error"
        class="mt-5"
        dense
      >
        {{ error }}
      </v-alert>
    </div>
    <div v-else-if="!comments.length">
      <v-alert
        type="info"
        class="mt-5"
        dense
      >
        {{ $t('no_comments') }}
      </v-alert>
    </div>

    <!-- formularz dodawania komentarza -->
    <comment-form
      :url="url"
      @comment-added="fetchComments"
    />
  </div>
</template>
<script>
import CommentItem from './CommentItem.vue';
import CommentForm from './CommentForm.vue';

export default {
  components: {
    CommentForm,
    CommentItem,

  },
  props: {
    url: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      comments: [],
      loading: false,
      error: null,
    };
  },
  created() {
    this.fetchComments();
  },
  methods: {
    async fetchComments() {
      this.loading = true;
      this.error = null;
      try {
        const response = await this.axios.get(this.url);
        this.comments = response.data;
      } catch (err) {
        this.error = 'Błąd podczas pobierania komentarzy.';
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>
