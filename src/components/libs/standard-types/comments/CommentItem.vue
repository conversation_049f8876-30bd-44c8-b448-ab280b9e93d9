<template>
  <div>
    <v-card-title
      class="subtitle-2 grey--text text--darken-1 d-flex align-center justify-space-between"
    >
      <div class="d-flex align-center">
        <v-icon
          left
          small
          class="mr-2"
        >
          mdi-comment
        </v-icon>
        <span>{{ item.user?.email || '-' }}</span>
      </div>
      <date-time-formatter :value="item.ctime" />
    </v-card-title>
    <v-divider />
    <v-card-text class="text-body-2">
      {{ item.message }}
    </v-card-text>
  </div>
</template>
<script>
import DateTimeFormatter from '@components/common/formatters/DateTimeFormatter.vue';

export default {
  name: 'CommentItem',
  components: { DateTimeFormatter },
  props: {
    item: {
      type: Object,
      required: true,
    },
  },
};
</script>
<style scoped>
h3 {
  font-weight: 500;
}
</style>
