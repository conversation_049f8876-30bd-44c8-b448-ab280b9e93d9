<template>
  <div v-if="item">
    <table class="pt-2 my-4 summary-table">
      <tr class="font-weight-bold">
        <td>
          {{ $t('loyaltyCards_invoiceNumber') }}
        </td>
        <td>
          {{ $t('loyaltyCards_issuanceDate') }}
        </td>
        <td>
          {{ $t('loyaltyCards_valueNet') }}
        </td>
        <td>
          {{ $t('loyaltyCards_valueGross') }}
        </td>
      </tr>
      <tr>
        <td>
          {{ item.number }}
        </td>
        <td>
          <date-time-formatter
            :value="item.createdAt"
            format="YYYY-MM-DD"
          />
        </td>
        <td>
          <currency-formatter
            :symbol="item.currency"
            :value="item.totalNet"
          />
        </td>
        <td>
          <currency-formatter
            :symbol="item.currency"
            :value="item.totalGross"
          />
        </td>
      </tr>
    </table>
  </div>
</template>

<script>

import CurrencyFormatter from '@components/common/formatters/CurrencyFormatter.vue';
import DateTimeFormatter from '@components/common/formatters/DateTimeFormatter.vue';

export default {
  components: { DateTimeFormatter, CurrencyFormatter },
  props: {
    item: {
      type: Object,
      required: true,
    },
  },
};
</script>

<style scoped>
.summary-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px black solid;
}

.summary-table td {
  padding: 5px;
  border: 1px black solid;
}

.card-text-wrap p {
  font-size: 18px;
  text-align: center;
}

</style>
