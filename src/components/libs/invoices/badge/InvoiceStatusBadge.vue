<template>
  <v-tooltip
    bottom
  >
    <template #activator="{ on, attrs }">
      <v-icon
        :color="statusInfo.color"
        v-bind="attrs"
        v-on="on"
      >
        {{ statusInfo.icon }}
      </v-icon>
    </template>
    <span> {{ statusInfo.text }}</span>
  </v-tooltip>
</template>

<script>

import InvoiceStatusType from '@components/libs/invoices/types';

/**
 * ikonka statusu faktury
 * zgodnie z https://gitlab.bkf.pl/bkf/ebkf/carwashmanager/library/invoices/-/blob/main/src/Enum/PaymentStatus.php
 */
export default {
  props: {
    status: {
      type: String,
      required: true,
    },
  },
  computed: {
    statusInfo() {
      const status = InvoiceStatusType.find((item) => item.value === this.status);
      if (status) {
        return {
          icon: status.icon,
          color: status.color,
          text: this.$t(`filter_${status.value}`),
        };
      }

      return {
        icon: 'mdi-help-circle',
        color: 'grey',
        text: this.$t('invoice.status.unknown'),
      };
    },
  },
};
</script>
