<template>
  <div>
    <v-row>
      <v-col>
        <text-search
          v-model="config.search"
        />
      </v-col>
      <v-col>
        <date-select
          v-model="config.dateselect"
          :show-custom="false"
        />
      </v-col>
    </v-row>
  </div>
</template>
<script>
import TextSearch from '@components/common/filters/TextSearch.vue';
import DateSelect from '@components/reports/filters/DateSelect.vue';
import debounce from 'lodash/debounce';

const SettingsKey = 'bkfpay-invoices-filter';
export default {
  components: {
    DateSelect,
    TextSearch,
  },
  model: {
    prop: 'param',
    event: 'update:param',
  },
  props: {
    param: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    const savedConfig = JSON.parse(localStorage.getItem(SettingsKey) || '{}');
    return {
      config: {
        search: savedConfig.search ?? null,
        dateselect: savedConfig.dateselect ?? null,
      },
    };
  },
  computed: {
    // scalony obiekt filtrów do przekazania
    internalParam() {
      return {
        search: this.config.search,
        ...this.config.dateselect,
      };
    },
  },
  watch: {
    internalParam: {
      handler(newVal) {
        this.debouncedUpdate(newVal);
      },
      deep: true,
      immediate: true,
    },
    config: {
      handler(newVal) {
        localStorage.setItem(SettingsKey, JSON.stringify(newVal));
      },
      deep: true,
    },
  },
  beforeCreate() {
    // jeden debounced watcher na cały internalParam
    this.debouncedUpdate = debounce((val) => {
      this.$emit('update:param', val);
    }, 1000);
  },

  methods: {
    onDateRangeChange(dateRange) {
      this.interval = dateRange.value;
    },
  },
};
</script>
