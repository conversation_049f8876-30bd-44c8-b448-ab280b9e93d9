<template>
  <report-data-table
    ref="dataTable"
    :title="$t('common_invoices_heading')"
    :headers="headers"
    report="v2\LoyaltyInvoices3Report"
    :filters="params"
  >
    <template #[`item.createdAt`]="{ item }">
      <date-time-formatter
        :value="item.createdAt"
        format="YYYY-MM-DD HH:mm"
      />
    </template>

    <template #[`item.serviceDate`]="{ item }">
      <date-time-formatter
        :value="item.serviceDate"
        format="YYYY-MM-DD HH:mm"
      />
    </template>

    <template #[`item.totalNet`]="{ item }">
      <currency-formatter
        :value="item.totalNet"
        :symbol="item.currency"
      />
    </template>

    <template #[`item.total`]="{ item }">
      <currency-formatter
        :value="item.total"
        :symbol="item.currency"
      />
    </template>

    <template #[`item.downloadUrl`]="{ item }">
      <v-col
        class="d-flex justify-end pt-1 pb-1"
      >
        <act-download
          :url="`/api/gateway/bkfpay-owner/invoice/${item.id}/download`"
        />
        <top-up-send-invoice-modal
          v-if="item.clientId !== null"
          :invoice-id="item.id"
          x-small
          :client-id="item.clientId"
          :base-url="baseUrl"
        />
      </v-col>
    </template>
  </report-data-table>
</template>

<script>

import ReportDataTable from '@components/reports/ReportDataTable.vue';
import CurrencyFormatter from '@components/common/formatters/CurrencyFormatter.vue';
import DateTimeFormatter from '@components/common/formatters/DateTimeFormatter.vue';
import TopUpSendInvoiceModal from '@components/loyalty-cards/topUps/modal/TopUpSendInvoiceModal.vue';
import ActDownload from '@components/common/Action/ActDownload.vue';

export default {
  components: {
    ActDownload,
    TopUpSendInvoiceModal,
    DateTimeFormatter,
    CurrencyFormatter,
    ReportDataTable,
  },
  props: {
    params: {
      type: Object,
      default: () => ({}),
    },
    baseUrl: {
      type: String,
      required: true,
    },
  },
  computed: {
    headers() {
      return [
        {
          value: 'createdAt',
          text: this.$t('loyaltyCards_issuanceDate'),
        },
        {
          value: 'serviceDate',
          text: this.$t('loyaltyCards_serviceDate'),
        },
        {
          value: 'number',
          text: this.$t('loyaltyCards_invoiceNumber'),
          align: 'left',
          protected: true,
        },
        {
          value: 'companyName',
          text: this.$t('loyaltyCards_client'),
          align: 'left',
          protected: true,
        },
        {
          value: 'taxNumber',
          text: this.$t('table.tax_number'),
          align: 'left',
          protected: true,
        },
        {
          value: 'sendDate',
          text: this.$t('invoices.send-date'),
        },
        {
          value: 'totalNet',
          text: this.$t('loyaltyCards_valueNet'),
          align: 'right',
        },
        {
          value: 'total',
          text: this.$t('loyaltyCards_valueGross'),
          align: 'right',
        },
        {
          value: 'downloadUrl',
          text: ' ',
          sortable: false,
          align: 'right',
          width: '130px',
        },
      ];
    },
  },
  methods: {
    fetchData() {
      this.$refs.dataTable.fetchData();
    },
  },
};
</script>
