<template>
  <report-data-table
    ref="dataTable"
    :headers="headers"
    :base-url="baseUrl"
    report="v2\LoyaltySubscriptionsPackagesReport"
    :filters="params"
  >
    <template #[`table-actions`]>
      <subscription-add-modal
        :base-url="baseUrl"
        @success="fetchData"
      />
    </template>
    <template #[`item.actions`]="{ item }">
      <v-container
        class="d-flex justify-end"
      >
        <subscription-edit-modal
          :base-url="baseUrl"
          :package="item"
          @success="fetchData"
        />
        <delete-modal
          :text="$t('loyalSystem_package_delete_question')"
          :url="`${baseUrl}/package/${item.id}`"
          @success="fetchData"
        />
      </v-container>
    </template>
    <template #[`item.value`]="{ item }">
      <currency-formatter
        :value="item.value"
        :symbol="item.currencySymbol"
      />
    </template>
    <template #[`item.discount`]="{ item }">
      {{ item.discount }} %
    </template>
  </report-data-table>
</template>

<script>
import ReportDataTable from '@components/reports/ReportDataTable.vue';
import CurrencyFormatter from '@components/common/formatters/CurrencyFormatter.vue';
import SubscriptionEditModal from '@components/loyalty-cards/subscriptions/modal/SubscriptionEditModal.vue';
import SubscriptionAddModal from '@components/loyalty-cards/subscriptions/modal/SubscriptionAddModal.vue';
import DeleteModal from '@components/common/modal/DeleteModal.vue';

export default {
  components: {
    DeleteModal,
    SubscriptionAddModal,
    SubscriptionEditModal,
    CurrencyFormatter,
    ReportDataTable,
  },
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    headers() {
      return [
        {
          text: this.$t('loyalApp_title'),
          value: 'title',
          sortable: false,
          align: 'left',
        },
        {
          text: this.$t('common_tableDescription'),
          value: 'description',
          sortable: false,
          align: 'left',
        },
        {
          text: this.$t('loyalApp_value'),
          value: 'value',
          sortable: false,
          align: 'right',
        },
        {
          text: this.$t('common_discount'),
          value: 'discount',
          sortable: false,
          align: 'right',
        },
        {
          text: this.$t('actions.actions'),
          value: 'actions',
          align: 'right',
          sortable: false,
        },
      ];
    },
  },
  methods: {
    fetchData() {
      this.$refs.dataTable.fetchData();
    },
  },
};
</script>
