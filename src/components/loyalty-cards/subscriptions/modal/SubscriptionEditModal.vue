<template>
  <div>
    <btn-edit
      :text="$t('actions.edit')"
      @click="dialog=true"
    />
    <generic-modal
      v-model="dialog"
      :title="$t('loyalApp_editPackage')"
    >
      <div class="text-center">
        <v-progress-circular
          v-if="loader"
          class="circleProgress"
          :size="90"
          :width="7"
          color="primary"
          indeterminate
        />
      </div>
      <template v-if="!loader">
        <v-container grid-list-md>
          <v-form
            ref="form"
            v-model="form.valid"
            lazy-validation
          >
            <v-row>
              <v-col
                cols="12"
              >
                <v-text-field
                  v-model="promotionalPackage.title"
                  prepend-icon="mdi-format-title"
                  :label="$t('loyalApp_title')"
                  required
                  :rules="form.validationRules.title"
                />
                <v-text-field
                  v-model="promotionalPackage.value"
                  prepend-icon="mdi-cash"
                  type="number"
                  :label="$t('loyalApp_packageValue')"
                  :rules="form.validationRules.positive_value"
                  required
                />
                <v-text-field
                  v-model="promotionalPackage.discount"
                  prepend-icon="mdi-ticket-percent-outline"
                  type="number"
                  :label="$t('common_discount')"
                  required
                  :rules="form.validationRules.discount"
                />
                <v-text-field
                  v-model="promotionalPackage.description"
                  prepend-icon="mdi-information-outline"
                  :label="$t('common_tableDescription')"
                  required
                  :rules="form.validationRules.description"
                  multi-line
                />
              </v-col>
            </v-row>
          </v-form>
        </v-container>
      </template>
      <template #actions>
        <v-spacer />
        <v-btn
          color="primary"
          text
          @click.native="closeDialog"
        >
          {{ $t('actions.return_to_list') }}
        </v-btn>
        <v-btn
          color="primary darken-1"
          :loading="loader"
          @click.native="submit"
        >
          {{ $t('actions.save') }}
        </v-btn>
      </template>
    </generic-modal>
  </div>
</template>

<script>
import BtnEdit from '@components/common/button/BtnEdit.vue';
import GenericModal from '@components/common/GenericModal.vue';

export default {
  components: {
    BtnEdit,
    GenericModal,
  },
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
    package: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
      loader: false,
      promotionalPackage: {
        title: this.package.title,
        value: parseFloat(this.package.value),
        discount: parseFloat(this.package.discount),
        description: this.package.description,
      },
      form: {
        validateOnBlur: true,
        valid: true,
        validationRules: {
          title: [
            (v) => !!v || this.$t('common_fieldRequired'),
          ],
          positive_value: [
            (v) => !!v || this.$t('common_fieldRequired'),
            (v) => (v > 0) || this.$t('loyalApp_infoPositiveNumberOnly'),
          ],
          zero_and_positive: [
            (v) => (v >= 0) || this.$t('loyalApp_infoPositiveNumberOnly'),
          ],
          discount: [
            (v) => (v >= 0) || this.$t('validation_discount'),
            (v) => (v <= 100) || this.$t('validation_discount'),
            (v) => Number.isInteger(Number(v)) || this.$t('validation_integer'),
          ],
          description: [
            (v) => !!v || this.$t('common_fieldRequired'),
            (v) => (v ? v.length <= 120 : false) || this.$t('loyalApp_validDescriptionLength'),
          ],
        },
      },
    };
  },
  methods: {
    submit() {
      if (this.$refs.form.validate()) {
        this.loader = true;
        this.axios.put(`${this.baseUrl}/bkfpay/package/${this.package.id}`, {
          title: this.promotionalPackage.title,
          discount: parseFloat(this.promotionalPackage.discount),
          value: parseFloat(this.promotionalPackage.value),
          description: this.promotionalPackage.description,
        })
          .then(
            () => {
              this.closeDialog();
              this.snackbar.showMessage(
                'success',
                this.$t('common_success'),
              );
              this.$emit('success');
            },
          ).catch(() => {
            this.snackbar.showMessage(
              'error',
              this.$t('common_errorHeader'),
            );
          });
        this.closeDialog();
      }
    },
    closeDialog() {
      this.form.valid = true;
      this.loader = false;
      this.form.validateOnBlur = true;
      this.dialog = false;
    },
  },
};
</script>
