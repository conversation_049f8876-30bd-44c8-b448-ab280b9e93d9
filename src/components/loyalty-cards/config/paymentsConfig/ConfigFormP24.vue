<template>
  <v-row>
    <v-col
      cols="12"
    >
      <v-form
        ref="formPaymentConfigP24"
        v-model="formValid"
        :disabled="!isOwner || !isBasicSubscription"
        lazy-validation
      >
        <v-select
          key="enviroment"
          v-model="formData.config.environment"
          v-validate="'required|min:1'"
          :items="enviromentOptions"
          :label="'enviroment'"
          prepend-icon="mdi-shape"
          name="enviroment"
          :data-vv-as="'environment'"
          :error-messages="errors.collect('environment')"
          :rules="validationRules.selectRequired"
          :disabled="loading || !isOwner || !isBasicSubscription"
        />
        <v-text-field
          key="merchant_id"
          v-model="formData.config.merchant_id"
          v-validate="'required|min:1'"
          :label="'merchant_id'"
          prepend-icon="mdi-source-branch"
          name="merchant_id"
          :data-vv-as="'merchant_id'"
          :error-messages="errors.collect('merchant_id')"
          :rules="validationRules.notEmpty"
          :disabled="loading || !isOwner || !isBasicSubscription"
        />
        <v-text-field
          key="pos_id"
          v-model="formData.config.pos_id"
          v-validate="'required|min:1'"
          :label="'pos_id'"
          prepend-icon="mdi-label-outline"
          name="pos_id"
          :data-vv-as="'pos_id'"
          :error-messages="errors.collect('pos_id')"
          :rules="validationRules.notEmpty"
          :disabled="loading || !isOwner || !isBasicSubscription"
        />
        <v-text-field
          key="salt"
          v-model="formData.config.salt"
          v-validate="'required|min:1'"
          :label="'salt'"
          prepend-icon="mdi-lock-outline"
          name="salt"
          :data-vv-as="'salt'"
          :error-messages="errors.collect('salt')"
          :rules="validationRules.notEmpty"
          :disabled="loading || !isOwner || !isBasicSubscription"
        />
      </v-form>
    </v-col>
    <v-col
      cols="12"
      align="end"
    >
      <v-btn
        color="primary darken-1"
        :loading="loading"
        :disabled="!formValid || !isOwner || !isBasicSubscription"
        @click.native="submitConfig"
      >
        {{ $t('actions.save') }}
      </v-btn>
    </v-col>
  </v-row>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'InvoiceConfigFormInternal',
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      enviromentOptions: [
        { id: 'sandbox', text: 'sandbox' },
        { id: 'secure', text: 'secure' },
      ],
      formData: {
        enviroment: 'sandbox',
        comment: null,
        type: 'p24',
        config: {
          environment: null,
          merchant_id: null,
          pos_id: null,
          salt: null,
        },
      },
      loading: true,
      formValid: false,
      validationRules: {
        notEmpty: [
          (v) => !!v || this.$t('common_fieldRequired'),
        ],
        selectRequired: [(v) => (!!v || v === '') || this.$t('common_fieldRequired')],
      },
    };
  },
  computed: {
    ...mapGetters({
      isOwner: 'auth/isOwner',
      hasRole: 'auth/hasRole',
    }),
    isBasicSubscription() {
      return this.hasRole('ROLE_SUBSCRIPTION_BASIC');
    },
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      if (this.formData.type !== 'p24') {
        this.loading = false;
        return;
      }

      this.loading = true;
      this.axios.get(
        `${this.baseUrl}/bkfpay/gate/config`,
      )
        .then((response) => {
          if ((response.status === 200)
            && response.data
          ) {
            this.formData = {
              ...this.formData,
              ...response.data,
            };
            this.formData.type = 'p24';
            this.$refs.formPaymentConfigP24.validate();
          }
          this.formData.type = 'p24';
          this.loading = false;
        });
    },
    submitConfig() {
      if (this.$refs.formPaymentConfigP24.validate()
        && this.isBasicSubscription
        && this.isOwner
      ) {
        this.loading = true;
        this.axios.put(
          `${this.baseUrl}/bkfpay/gate/config`,
          {
            ...this.formData,
          },
        )
          .then(
            (response) => {
              if ((response.status === 200) && response.data) {
                this.loading = false;
                this.snackbar.showMessage(
                  'success',
                  this.$t('common_success'),
                );
              }
            },
            () => {
              this.loading = false;
              this.snackbar.showMessage(
                'warning',
                this.$t('loyalApp_problem'),
              );
              // this.onError();
            },
          );
      }
    },
  },
};
</script>
