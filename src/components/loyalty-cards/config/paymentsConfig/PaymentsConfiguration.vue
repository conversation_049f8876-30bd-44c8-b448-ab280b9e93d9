<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
  >
    <v-row>
      <loading-overlay
        v-show="loaders.site"
      />
      <v-row>
        <v-col
          cols="12"
          class="text-sm-start"
        >
          <v-alert
            v-if="!isOwner"
            text
            border="left"
            type="info"
          >
            {{ $t('loyaltyCards_notOwnerAlert') }}
          </v-alert>
        </v-col>
        <v-col
          md="12"
          class="text-sm-start"
        >
          <h2>
            <span>{{ $t('loyaltyCards_paymentSettings') }}</span>
          </h2>
        </v-col>
        <v-col
          md="6"
          cols="12"
        >
          <v-col
            cols="12"
            class="text-sm-start"
          >
            <v-autocomplete
              v-model="configType"
              v-validate="'required|min:1'"
              :disabled="!isOwner || !isBasicSubscription"
              :items="configOptions"
              prepend-icon="mdi-tune"
              item-value="id"
              item-text="text"
              name="vatTax"
              :data-vv-as="`${$t('loyaltyCards_paymentSettings')}`"
              :error-messages="errors.collect('configType')"
              :rules="rules.selectRequired"
              required
            />
          </v-col>
          <v-col
            v-if="configType === 'Disabled'"
            cols="12"
            align="end"
          >
            <v-btn
              color="primary darken-1"
              :loading="loaders.site"
              :disabled="!isOwner || !isBasicSubscription"
              @click.native="submitConfig"
            >
              {{ $t('actions.save') }}
            </v-btn>
          </v-col>
          <v-col
            v-if="configType === 'p24'"
            cols="12"
            class="text-sm-start"
          >
            <config-form-p24 :base-url="baseUrl" />
          </v-col>
          <v-col
            v-if="configType === 'payU'"
            cols="12"
            class="text-sm-start"
          >
            <config-form-pay-u :base-url="baseUrl" />
          </v-col>
        </v-col>
      </v-row>
    </v-row>
  </v-container>
</template>

<script>
import { mapGetters } from 'vuex';
import loadingOverlay from '@components/common/LoadingOverlay.vue';
import ConfigFormPayU from '@components/loyalty-cards/config/paymentsConfig/ConfigFormPayU.vue';
import ConfigFormP24 from '@components/loyalty-cards/config/paymentsConfig/ConfigFormP24.vue';

export default {
  components: {
    loadingOverlay,
    ConfigFormPayU,
    ConfigFormP24,
  },
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      configOptions: [
        {
          id: 'Disabled',
          text: this.$t('invoiceConfigType_Disabled'),
        },
        {
          id: 'p24',
          text: 'Przelewy24',
        },
        {
          id: 'payU',
          text: 'PayU',
        },
      ],
      configType: 'Disabled',
      rules: {
        selectRequired: [(v) => !!v || this.$t('common_fieldRequired')],
      },
      loaders: {
        site: true,
        actualize: false,
      },
      form: {
        valid: false,
      },
      configData: null,
    };
  },
  computed: {
    ...mapGetters({
      isOwner: 'auth/isOwner',
      hasRole: 'auth/hasRole',
    }),
    isBasicSubscription() {
      return this.hasRole('ROLE_SUBSCRIPTION_BASIC');
    },
  },
  watch: {
    configType(value) {
      this.configData = {};
      this.getConfigData(value);
    },
  },
  mounted() {
    this.getOwnerConfigType();
    this.getConfigData(this.configType);
  },
  methods: {
    getConfigData(type) {
      if (type === 'Disabled') {
        this.loaders.site = false;
        return;
      }

      this.loaders.site = true;
      this.axios.get(
        `${this.baseUrl}/bkfpay/gate/config`,
      )
        .then((response) => {
          if ((response.status === 200) && response.data) {
            this.configData = response.data;
          }
          this.loaders.site = false;
        });
      // this.loaders.site = false;
    },
    getOwnerConfigType() {
      this.axios.get(
        `${this.baseUrl}/bkfpay/gate/config`,
      )
        .then((response) => {
          if ((response.status === 200) && response.data) {
            this.configType = response.data.type;
          }
        });
    },
    submitConfig() {
      if (this.isOwner && this.isBasicSubscription) {
        this.loaders.actualize = true;
        // this.loaders.site = true;
        this.axios.put(
          `${this.baseUrl}/bkfpay/gate/config`,
          {
            type: this.configType,
          },
        )
          .then(
            (response) => {
              if ((response.status === 200) && response.data) {
                this.loaders.actualize = false;
                this.loaders.site = false;
                this.snackbar.showMessage(
                  'success',
                  this.$t('common_success'),
                );
              }
            },
            () => {
              this.loaders.actualize = false;
              this.loaders.site = false;
              // this.onError();
              this.snackbar.showMessage(
                'warning',
                this.$t('loyalApp_problem'),
              );
            },
          );
      }
    },
  },
};
</script>
