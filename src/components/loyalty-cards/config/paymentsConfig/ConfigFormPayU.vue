<template>
  <v-row>
    <v-col
      cols="12"
    >
      <v-form
        ref="formPaymentConfigPayU"
        v-model="formValid"
        :disabled="!isOwner || !isBasicSubscription"
        lazy-validation
      >
        <v-select
          key="enviroment"
          v-model="formData.config.enviroment"
          v-validate="'required|min:1'"
          :items="enviromentOptions"
          :label="'enviroment'"
          prepend-icon="mdi-shape"
          name="enviroment"
          :data-vv-as="'enviroment'"
          :error-messages="errors.collect('enviroment')"
          :rules="validationRules.selectRequired"
          :disabled="loading || !isOwner || !isBasicSubscription"
        />
        <v-text-field
          key="merchant_pos_id"
          v-model="formData.config.merchant_pos_id"
          v-validate="'required|min:1'"
          :label="'merchant_pos_id'"
          prepend-icon="mdi-source-branch"
          name="departmentId"
          :data-vv-as="'merchant_pos_id'"
          :error-messages="errors.collect('merchant_pos_id')"
          :rules="validationRules.notEmpty"
          :disabled="loading || !isOwner || !isBasicSubscription"
        />
        <v-text-field
          key="signature_key"
          v-model="formData.config.signature_key"
          v-validate="'required|min:1'"
          :label="'signature_key'"
          prepend-icon="mdi-lock-outline"
          name="token"
          :data-vv-as="'signature_key'"
          :error-messages="errors.collect('signature_key')"
          :rules="validationRules.notEmpty"
          :disabled="loading || !isOwner || !isBasicSubscription"
        />
        <v-text-field
          key="o_auth_client_id"
          v-model="formData.config.o_auth_client_id"
          v-validate="'required|min:1'"
          :label="'o_auth_client_id'"
          prepend-icon="mdi-account"
          name="o_auth_client_id"
          :data-vv-as="'o_auth_client_id'"
          :error-messages="errors.collect('o_auth_client_id')"
          :rules="validationRules.notEmpty"
          :disabled="loading || !isOwner || !isBasicSubscription"
        />
        <v-text-field
          key="o_auth_client_secret"
          v-model="formData.config.o_auth_client_secret"
          v-validate="'required|min:1'"
          :label="'o_auth_client_secret'"
          prepend-icon="mdi-key"
          name="o_auth_client_secret"
          :data-vv-as="'o_auth_client_secret'"
          :error-messages="errors.collect('o_auth_client_secret')"
          :rules="validationRules.notEmpty"
          :disabled="loading || !isOwner || !isBasicSubscription"
        />
      </v-form>
    </v-col>
    <v-col
      cols="12"
      align="end"
    >
      <v-btn
        color="primary darken-1"
        :loading="loading"
        :disabled="!formValid || !isOwner || !isBasicSubscription"
        @click.native="submitConfig"
      >
        {{ $t('actions.save') }}
      </v-btn>
    </v-col>
  </v-row>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'InvoiceConfigFormInternal',
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      enviromentOptions: [
        { id: 'sandbox', text: 'sandbox' },
        { id: 'secure', text: 'secure' },
      ],
      formData: {
        config: {
          enviroment: 'sandbox',
          merchant_pos_id: null,
          signature_key: null,
          o_auth_client_id: null,
          o_auth_client_secret: null,
        },
        type: 'payU',
      },
      loading: true,
      formValid: false,
      validationRules: {
        notEmpty: [
          (v) => !!v || this.$t('common_fieldRequired'),
        ],
        selectRequired: [(v) => (!!v || v === '') || this.$t('common_fieldRequired')],
      },
    };
  },
  computed: {
    ...mapGetters({
      isOwner: 'auth/isOwner',
      hasRole: 'auth/hasRole',
    }),
    isBasicSubscription() {
      return this.hasRole('ROLE_SUBSCRIPTION_BASIC');
    },
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      if (this.formData.type !== 'payU') {
        this.loading = false;
        return;
      }

      this.loading = true;
      this.axios.get(
        `${this.baseUrl}/bkfpay/gate/config`,
      )
        .then((response) => {
          if ((response.status === 200) && response.data) {
            this.formData = {
              ...this.formData,
              ...response.data,
            };
            this.formData.type = 'payU';
            this.$refs.formPaymentConfigPayU.validate();
          }
          this.formData.type = 'payU';
          this.loading = false;
        });
    },
    submitConfig() {
      if (this.$refs.formPaymentConfigPayU.validate()
        && this.isBasicSubscription
        && this.isOwner
      ) {
        this.loading = true;
        this.axios.put(
          `${this.baseUrl}/bkfpay/gate/config`,
          this.formData,
        )
          .then(
            (response) => {
              if ((response.status === 200) && response.data) {
                this.loading = false;
                this.snackbar.showMessage(
                  'success',
                  this.$t('common_success'),
                );
              }
            },
            () => {
              this.loading = false;
              // this.onError();
              this.snackbar.showMessage(
                'warning',
                this.$t('loyalApp_problem'),
              );
            },
          );
      }
    },
  },
};
</script>
