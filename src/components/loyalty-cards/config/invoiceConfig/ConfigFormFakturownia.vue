<template>
  <v-row>
    <v-col
      cols="12"
    >
      <v-form
        ref="formConfigFakturownia"
        v-model="formValid"
        :disabled="!isOwner || !isBasicSubscription"
        lazy-validation
      >
        <v-text-field
          key="categoryId"
          v-model="config.categoryId"
          v-validate="'required|min:1'"
          :label="$t('invoiceConfig_category')"
          prepend-icon="mdi-shape"
          name="categoryId"
          :data-vv-as="$t('invoiceConfig_category')"
          :error-messages="errors.collect('categoryId')"
          :rules="validationRules.notEmpty"
          :disabled="loading || !isOwner || !isBasicSubscription"
        />
        <v-text-field
          key="departmentId"
          v-model="config.departmentId"
          v-validate="'required|min:1'"
          :label="$t('invoiceConfig_department_identificator')"
          prepend-icon="mdi-source-branch"
          name="departmentId"
          :data-vv-as="$t('invoiceConfig_department_identificator')"
          :error-messages="errors.collect('departmentId')"
          :rules="validationRules.notEmpty"
          :disabled="loading || !isOwner || !isBasicSubscription"
        />
        <v-text-field
          key="token"
          v-model="config.token"
          v-validate="'required|min:1'"
          :label="$t('invoiceConfig_token')"
          prepend-icon="mdi-lock-outline"
          name="token"
          :data-vv-as="$t('invoiceConfig_token')"
          :error-messages="errors.collect('token')"
          :rules="validationRules.notEmpty"
          :disabled="loading || !isOwner || !isBasicSubscription"
        />
        <v-text-field
          key="url"
          v-model="config.url"
          v-validate="'required|min:1'"
          :label="$t('invoiceConfig_url')"
          prepend-icon="mdi-web"
          name="url"
          :data-vv-as="$t('invoiceConfig_url')"
          :error-messages="errors.collect('url')"
          :rules="validationRules.notEmpty"
          :disabled="loading || !isOwner || !isBasicSubscription"
        />
      </v-form>
    </v-col>
    <v-col
      cols="12"
      align="end"
    >
      <v-btn
        color="primary darken-1"
        :loading="loading"
        :disabled="!formValid || !isOwner || !isBasicSubscription"
        @click.native="submitConfig"
      >
        {{ $t('actions.save') }}
      </v-btn>
    </v-col>
  </v-row>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      config: {
        categoryId: null,
        departmentId: null,
        token: null,
        url: null,
      },
      loading: true,
      formValid: false,
      configType: 'Fakturownia',
      validationRules: {
        notEmpty: [
          (v) => !!v || this.$t('common_fieldRequired'),
        ],
      },
    };
  },
  computed: {
    ...mapGetters({
      isOwner: 'auth/isOwner',
      hasRole: 'auth/hasRole',
    }),
    isBasicSubscription() {
      return this.hasRole('ROLE_SUBSCRIPTION_BASIC');
    },
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      const type = this.configType;
      if (type === 'Disabled') {
        this.loading = false;
        return;
      }
      this.formValid = false;
      this.loading = true;
      this.axios.get(
        `${this.baseUrl}/bkfpay/invoices/config/${type}`,
      )
        .then((response) => {
          if ((response.status === 200) && response.data) {
            this.config = {
              ...this.config,
              ...response.data,
            };
            this.$refs.formConfigFakturownia.validate();
          }
          this.loading = false;
        });
    },
    submitConfig() {
      const type = this.configType;
      if (this.isBasicSubscription) {
        this.loading = true;
        this.axios.put(
          `${this.baseUrl}/bkfpay/invoices/config/${type}`,
          {
            ...this.config,
          },
        )
          .then(
            (response) => {
              if ((response.status === 200) && response.data) {
                this.loading = false;
              }
            },
            () => {
              this.loading = false;
              // this.onError();
            },
          );
      }
    },
  },
};
</script>
