<template>
  <v-row>
    <v-col
      cols="12"
    >
      <v-form
        ref="formConfigInternal"
        v-model="formValid"
        :disabled="!isOwner || !isBasicSubscription"
        lazy-validation
      >
        <v-text-field
          key="numberTemplate"
          v-model="config.numberTemplate"
          v-validate="'required|min:1'"
          :label="$t('loyaltyCards_invoiceNumerator')"
          prepend-icon="mdi-label-outline"
          name="numberTemplate"
          :data-vv-as="$t('loyaltyCards_invoiceNumerator')"
          :error-messages="errors.collect('numberTemplate')"
          :rules="validationRules.notEmpty"
          :disabled="loading || !isOwner || !isBasicSubscription"
        >
          <template #append>
            <v-tooltip
              color="grey lighten-3"
              bottom
            >
              <template #activator="{ on, attrs }">
                <div
                  v-bind="attrs"
                  v-on="on"
                >
                  <v-icon
                    color="primary"
                    small
                  >
                    mdi-alert-circle-outline
                  </v-icon>
                </div>
              </template>
              <v-alert
                border="bottom"
                text
                class="mt-3"
                color="primary"
              >
                <strong>{n}</strong> - {{ $t('loyaltyCards_invoiceNumerator') }} <br>
                <strong>{M}</strong> - {{ $t('loyaltyCards_longMonthFormat') }} <br>
                <strong>{m}</strong> - {{ $t('loyaltyCards_shortMonthFormat') }} <br>
                <strong>{Y}</strong> - {{ $t('loyaltyCards_longYearFormat') }} <br>
                <strong>{y}</strong> - {{ $t('loyaltyCards_shortYearFormat') }} <br>
              </v-alert>
            </v-tooltip>
          </template>
        </v-text-field>
      </v-form>
    </v-col>
    <v-col
      cols="12"
      align="end"
    >
      <v-btn
        color="primary darken-1"
        :loading="loading"
        :disabled="!formValid || !isOwner || !isBasicSubscription"
        @click.native="submitConfig"
      >
        {{ $t('actions.save') }}
      </v-btn>
    </v-col>
  </v-row>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      config: {
        numberTemplate: null,
      },
      loading: true,
      formValid: false,
      configType: 'Internal',
      validationRules: {
        notEmpty: [
          (v) => !!v || this.$t('common_fieldRequired'),
        ],
      },
    };
  },
  computed: {
    ...mapGetters({
      isOwner: 'auth/isOwner',
      hasRole: 'auth/hasRole',
    }),
    isBasicSubscription() {
      return this.hasRole('ROLE_SUBSCRIPTION_BASIC');
    },
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      const type = this.configType;
      if (type === 'Disabled') {
        this.loading = false;
        return;
      }

      this.loading = true;
      this.axios.get(
        `${this.baseUrl}/bkfpay/invoices/config/${type}`,
      )
        .then((response) => {
          if ((response.status === 200) && response.data) {
            this.config = {
              ...this.config,
              ...response.data,
            };
            this.$refs.formConfigInternal.validate();
          }
          this.loading = false;
        });
    },
    submitConfig() {
      const type = this.configType;
      if (this.isBasicSubscription) {
        this.loading = true;
        this.axios.put(
          `${this.baseUrl}/bkfpay/invoices/config/${type}`,
          {
            ...this.config,
          },
        )
          .then(
            (response) => {
              if ((response.status === 200) && response.data) {
                this.loading = false;
              }
            },
            () => {
              this.loading = false;
              // this.onError();
            },
          );
      }
    },
  },
};
</script>
