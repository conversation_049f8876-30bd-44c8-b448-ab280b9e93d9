<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
  >
    <v-row>
      <v-col
        v-if="showHeader"
        cols="12"
        class="text-sm-start"
      >
        <h2>
          <span>{{ $t('common_dataToInvoice') }}</span>
        </h2>
      </v-col>
      <v-col
        cols="12"
        class="px-0 pt-0 my-4"
      >
        <v-form
          ref="formCompanyInvoiceData"
          v-model="form.valid"
          lazy-validation
        >
          <v-row>
            <v-col
              cols="12"
              sm="6"
              class="pt-0 pb-0"
            >
              <v-text-field
                v-model="companyInvoiceData.country"
                v-validate="'required|min:1'"
                :label="$t('common_country')"
                prepend-icon="mdi-earth"
                name="country"
                required
                readonly
                disabled
              />
            </v-col>
            <v-col
              cols="12"
              sm="6"
              class="pt-0 pb-0"
            >
              <v-text-field
                v-model="companyInvoiceData.name"
                :label="$t('common_invoiceCompanySettingsName')"
                prepend-icon="mdi-rename-box"
                name="name"
                readonly
                disabled
              />
            </v-col>
            <v-col
              cols="12"
              sm="6"
              class="pt-0 pb-0"
            >
              <v-text-field
                v-model="companyInvoiceData.taxNumber"
                :label="$t('common_taxNumber')"
                prepend-icon="mdi-numeric"
                name="taxNumber"
                readonly
                disabled
              />
            </v-col>
            <v-col
              cols="12"
              sm="6"
              class="pt-0 pb-0"
            >
              <v-text-field
                v-model="companyInvoiceData.regon"
                :label="$t('common_regonNumber')"
                prepend-icon="mdi-numeric"
                name="regon"
                :data-vv-as="$t('user_nextInvoiceNumber')"
                :error-messages="errors.collect('regon')"
                readonly
                disabled
              />
            </v-col>
            <v-col
              cols="8"
              sm="4"
              class="pt-0 pb-0"
            >
              <v-text-field
                v-model="companyInvoiceData.city"
                v-validate="'max:128'"
                :label="$t('common_city')"
                prepend-icon="mdi-city-variant-outline"
                name="city"
                :data-vv-as="$t('common_city')"
                :error-messages="errors.collect('city')"
                readonly
                disabled
              />
            </v-col>
            <v-col
              cols="4"
              sm="2"
              class="pt-0 pb-0"
            >
              <v-text-field
                v-model="companyInvoiceData.postCode"
                v-validate="'max:12'"
                :label="$t('common_postCode')"
                prepend-icon="mdi-home-city-outline"
                name="postCode"
                :data-vv-as="$t('common_postCode')"
                :error-messages="errors.collect('postCode')"
                readonly
                disabled
              />
            </v-col>
            <v-col
              cols="12"
              sm="6"
              class="pt-0 pb-0"
            >
              <v-text-field
                v-model="companyInvoiceData.bankAccountNumber"
                :label="$t('user_accountNumber')"
                prepend-icon="mdi-bank"
                name="bankAccountNumber"
                :data-vv-as="$t('user_invoiceSettingsAccountNumber')"
                :error-messages="errors.collect('bankAccountNumber')"
                :disabled="loaders.site || !isOwner || !isBasicSubscription"
              />
            </v-col>
            <v-tooltip left>
              <template #activator="{ on }">
                <v-col
                  cols="12"
                  sm="6"
                  class="pt-0 pb-0"
                >
                  <div
                    v-on="on"
                  >
                    <v-text-field
                      v-model="companyInvoiceData.supportEmail"
                      v-validate="'email|max:64|min:6'"
                      :label="$t('user_contactEmail')"
                      prepend-icon="mdi-email"
                      :data-vv-as="$t('user_contactEmail')"
                      :counter="64"
                      name="supportEmail"
                      :error-messages="errors.collect('supportEmail')"
                      :disabled="loaders.site || !isOwner || !isBasicSubscription"
                    />
                  </div>
                </v-col>
              </template>
              <span>
                {{ $t('user_contactEmailTooltip') }}
              </span>
            </v-tooltip>
            <v-col
              sm="6"
              cols="12"
              class="pt-0 pb-0"
            >
              <v-text-field
                v-model="companyInvoiceData.address"
                v-validate="'max:255'"
                :label="$t('common_formAddress')"
                prepend-icon="mdi-map-marker-outline"
                name="address"
                :data-vv-as="`${$t('common_formAddress')}`"
                :error-messages="errors.collect('address')"
                :disabled="loaders.site || !isOwner || !isBasicSubscription"
              />
            </v-col>
            <v-col
              cols="12"
              sm="6"
              class="pt-0 pb-0"
            >
              <v-combobox
                v-model="invoiceCopyEmail.model"
                :items="invoiceCopyEmail.items"
                :search-input.sync="invoiceCopyEmail.search"
                prepend-icon="mdi-email-multiple"
                hide-selected
                :data-vv-as="$t('common_emailCopyEmail')"
                :label="$t('common_emailCopyEmail')"
                :error-messages="errors.collect('invoiceCopyEmail')"
                :rules="rules.comboboxEmail"
                multiple
                small-chips
                deletable-chips
              >
                <template #no-data>
                  <v-list-item>
                    <v-list-item-content>
                      <v-list-item-title>
                        {{ $t('common_pressEnterToAddNew') }}
                      </v-list-item-title>
                    </v-list-item-content>
                  </v-list-item>
                </template>
              </v-combobox>
            </v-col>
            <v-col
              sm="6"
              cols="12"
              class="pt-0 pb-0"
            >
              <v-autocomplete
                v-model="companyInvoiceData.language"
                v-validate="'required|min:1'"
                item-value="locale"
                item-text="name"
                :label="$t('common_language')"
                :items="languagesOptions"
                :autocomplete="true"
                prepend-icon="mdi-web"
                name="language"
                :data-vv-as="`${$t('common_language')}`"
                :error-messages="errors.collect('language')"
                :disabled="loaders.site || !isOwner || !isBasicSubscription"
                required
              />
            </v-col>
            <v-col
              cols="12"
              sm="6"
              class="py-0"
            >
              <v-autocomplete
                v-model="companyInvoiceData.vatTax"
                v-validate="'required|min:1'"
                item-value="taxValue"
                item-text="taxKey"
                :label="$t('loyaltyCards_vatTax')"
                :items="vatTaxOptions"
                :autocomplete="true"
                prepend-icon="mdi-bank-outline"
                name="vatTax"
                :data-vv-as="`${$t('loyaltyCards_vatTax')}`"
                :error-messages="errors.collect('vatTax')"
                :rules="rules.selectRequired"
                required
                :disabled="loaders.site || !isOwner || !isBasicSubscription"
              />
            </v-col>
            <v-col
              cols="12"
              sm="6"
              class="pt-0 pb-0"
            >
              <v-autocomplete
                v-model="companyInvoiceData.defaultPaymentTerms"
                v-validate="'required|min:1'"
                item-value="id"
                :label="$t('common_paymentPeriod')"
                :items="paymentTermOptions"
                :autocomplete="true"
                prepend-icon="mdi-update"
                name="paymentPeriodType"
                :data-vv-as="`${$t('common_paymentPeriod')}`"
                :error-messages="errors.collect('paymentPeriodType')"
                :rules="rules.selectRequired"
                :disabled="loaders.site || !isOwner || !isBasicSubscription"
                required
              />
            </v-col>
            <v-col
              cols="12"
              sm="6"
              class="pt-0 pb-0"
            >
              <v-select
                v-model="companyInvoiceData.defaultPaymentMethod"
                v-validate="'required|min:1'"
                item-value="id"
                item-text="value"
                :label="$t('common_paymentMethod')"
                :items="paymentMethods"
                :autocomplete="true"
                :disabled="loaders.site || !isOwner || !isBasicSubscription"
                prepend-icon="mdi-hand-coin-outline"
                name="paymentMethod"
                required
              />
            </v-col>
            <v-col
              cols="12"
              align="end"
              class="pt-0 pb-0"
            >
              <v-btn
                color="primary darken-1"
                :loading="loaders.actualize"
                :disabled="!isOwner || !form.valid"
                @click.native="submit"
              >
                {{ $t('actions.save') }}
              </v-btn>
            </v-col>
          </v-row>
        </v-form>
      </v-col>
    </v-row>
    <v-row>
      <v-col
        cols="12"
        class="text-sm-start"
      >
        <h2>
          <span>{{ $t('invoiceCompanyData_logo') }}</span>
        </h2>
      </v-col>
      <v-col
        cols="12"
        class="text-sm-start px-0"
      >
        <config-logo :base-url="baseUrl" />
      </v-col>
    </v-row>
  </v-container>
</template>

<script>

import { mapGetters } from 'vuex';
import ConfigLogo from '@components/loyalty-cards/config/companyDataConfig/ConfigLogo.vue';

export default {
  components: {
    ConfigLogo,
  },
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
    showHeader: {
      type: Boolean,
      default: true,
    },
    showNotice: {
      type: Boolean,
      default: false,
    },
    onSuccessSubmit: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      dataUrl: `${this.baseUrl}/bkfpay/config`,
      dealer: null,
      dealersIsLoading: true,
      dealerSearch: null,
      languagesOptions: [
        { locale: 'pl', name: 'polski' },
        { locale: 'en', name: 'english' },
        { locale: 'cs', name: 'český' },
        { locale: 'hr', name: 'hrvatski' },
      ],
      dataNotFilled: false,
      alertType: 'info',
      rules: {
        selectRequired: [(v) => !!v || this.$t('common_fieldRequired')],
        comboboxEmail: [(v) => /(((^(([^<>()[\]\\.,;:\s@']+(\.[^<>()\\[\]\\.,;:\s@']+)*)|('.+'))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,})))(,{1}((([^<>()[\]\\.,;:\s@']+(\.[^<>()\\[\]\\.,;:\s@']+)*)|('.+'))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))))*)|(^$))$/.test(v) || 'E-mail must be valid'],
      },
      loaders: {
        site: true,
        actualize: false,
      },
      form: {
        cancelRefill: {
          valid: true,
        },
        validateOnBlur: true,
        valid: false,
      },
      logo: null,
      paymentMethods: [
        { id: 'cash', value: this.$t('common_cash') },
        { id: 'transfer', value: this.$t('common_transfer') },
      ],
      companyInvoiceData: {
        invoiceCopyEmail: null,
        name: '',
        supportEmail: null,
        bankAccountNumber: null,
        country: null,
        language: null,
        regon: '',
        city: '',
        postCode: '',
        address: '',
        taxNumber: '',
        defaultPaymentTerms: '',
        vatTax: 0,
        defaultPaymentMethod: null,
      },
      vatTaxOptions: null,
      invoiceCopyEmail: {
        model: [],
        items: [],
        search: null,
      },
      paymentTermOptions: [
        {
          id: 'P0D',
          text: this.$t('common_0'),
        },
        {
          id: 'P1D',
          text: this.$t('common_1'),
        },
        {
          id: 'P3D',
          text: this.$t('common_3'),
        },
        {
          id: 'P5D',
          text: this.$t('common_5'),
        },
        {
          id: 'P7D',
          text: this.$t('common_7'),
        },
        {
          id: 'P10D',
          text: this.$t('common_10'),
        },
        {
          id: 'P14D',
          text: this.$t('common_14'),
        },
      ],
    };
  },
  computed: {
    ...mapGetters({
      user: 'auth/getUser',
      isOwner: 'auth/isOwner',
    }),
  },
  mounted() {
    this.getVatTax();
    this.getData();
  },
  methods: {
    isBasicSubscription() {
      return this.hasRole('ROLE_SUBSCRIPTION_BASIC');
    },
    getVatTax() {
      this.axios.get(
        '/api/lists/vat_tax',
      )
        .then((response) => {
          if ((response.status === 200) && response.data) {
            this.vatTaxOptions = response.data;
          }
        });
    },
    getData() {
      this.loaders.site = true;
      this.axios.get(
        this.dataUrl,
      )
        .then(
          (response) => {
            if ((response.status === 200) && response.data) {
              this.$emit('data-changed', response.data);
              this.companyInvoiceData = response.data;

              if (response.data.invoiceCopyEmail !== null) {
                this.invoiceCopyEmail.model = response.data.invoiceCopyEmail.split(';');
              }
            }
            this.loaders.site = false;
          },
          () => {
            this.alertType = 'error';
            this.dataNotFilled = true;
          },
        );
      this.loaders.site = false;
    },
    submit() {
      this.loaders.actualize = true;
      this.loaders.site = true;

      const url = `${this.dataUrl}`;

      this.companyInvoiceData.invoiceCopyEmail = this.invoiceCopyEmail.model.join(';');
      if (this.companyInvoiceData.invoiceCopyEmail === '') {
        this.companyInvoiceData.invoiceCopyEmail = null;
      }

      const params = {
        address: this.companyInvoiceData.address,
        bankAccountNumber: this.companyInvoiceData.bankAccountNumber === '' ? null : this.companyInvoiceData.bankAccountNumber,
        supportEmail: this.companyInvoiceData.supportEmail === '' ? null : this.companyInvoiceData.supportEmail,
        invoiceCopyEmail: this.companyInvoiceData.invoiceCopyEmail === '' ? null : this.companyInvoiceData.invoiceCopyEmail,
        city: this.companyInvoiceData.city,
        regon: this.companyInvoiceData.regon === '' ? null : this.companyInvoiceData.regon,
        postCode: this.companyInvoiceData.postCode,
        language: this.companyInvoiceData.language,
        taxNumber: this.companyInvoiceData.taxNumber,
        country: this.companyInvoiceData.country,
        vatTax: this.companyInvoiceData.vatTax,
        defaultPaymentTerms: this.companyInvoiceData.defaultPaymentTerms,
        defaultPaymentMethod: this.companyInvoiceData.defaultPaymentMethod,
      };

      this.axios.put(
        url,
        params,
      )
        .then(
          (response) => {
            if ((response.status === 200) && response.data) {
              this.loaders.actualize = false;
              this.loaders.site = false;
              this.onSuccessSubmit();
            }
          },
          () => {
            this.loaders.actualize = false;
            this.loaders.site = false;
          },
        );
    },
  },
};
</script>
