<template>
  <v-container
    fluid
  >
    <v-row>
      <v-col
        sm="12"
        class="pr-0 pt-6"
      >
        <v-form
          ref="formUserEdit"
          v-model="form.valid"
          :disabled="!isOwner || !isBasicSubscription"
          lazy-validation
        >
          <v-file-input
            class="pt-0"
            :rules="rules.fileSize"
            accept="image/png, image/jpeg, image/bmp"
            prepend-icon="mdi-camera"
            :label="$t('common_invoice_logo')"
            @change="uploadFile"
          />
          <v-container
            fluid
            justify-center
            fill-height
          >
            <div
              class="flex-column"
              align="center"
              justify="center"
            >
              <img
                v-if="logo"
                :src="logo"
                class="formLogo"
              >
            </div>
          </v-container>
        </v-form>
      </v-col>
      <v-col
        cols="12"
        align="right"
        class="pt-2 pb-0"
      >
        <v-btn
          color="primary darken-1"
          :loading="loaders.actualize"
          :disabled="!form.valid"
          @click.native="submit"
        >
          {{ $t('actions.save') }}
        </v-btn>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>

import { mapGetters } from 'vuex';

export default {
  components: {
  },
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
    isEditable: {
      type: Boolean,
      default: true,
    },
    onSuccessSubmit: {
      type: Function,
      default: () => {},
    },
    subscriber: {
      type: [Array, Object],
      default: null,
    },
  },
  data() {
    return {
      rules: {
        fileSize: [(value) => !value || value.size < 500000 || 'Avatar size should be less than 0,5 MB!'],
      },
      dealer: null,
      loaders: {
        site: true,
        actualize: false,
      },
      form: {
        cancelRefill: {
          valid: true,
        },
        validateOnBlur: true,
        valid: false,
      },
      logo: null,
    };
  },
  computed: {
    ...mapGetters({
      isOwner: 'auth/isOwner',
      hasRole: 'auth/hasRole',
    }),
    isBasicSubscription() {
      return this.hasRole('ROLE_SUBSCRIPTION_BASIC');
    },
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      this.loaders.site = true;
      this.axios.get(
        `${this.baseUrl}/bkfpay/logo`,
      )
        .then(
          (response) => {
            if ((response.status === 200) && response.data) {
              this.logo = `data:image/png;base64,${response.data.logo}`;
            }
            this.loaders.site = false;
          },
          () => {
            this.alertType = 'error';
            this.dataNotFilled = true;
          },
        );
      this.loaders.site = false;
    },
    submit() {
      if (this.isEditable) {
        this.loaders.actualize = true;
        this.loaders.site = true;

        const params = {
          logo: this.logo.replace('data:image/png;base64,', ''),
        };

        this.axios.put(
          `${this.baseUrl}/bkfpay/logo`,
          params,
        )
          .then(
            (response) => {
              if ((response.status === 200) && response.data) {
                this.loaders.actualize = false;
                this.loaders.site = false;
                this.onSuccessSubmit();
              }
            },
            () => {
              this.loaders.actualize = false;
              this.loaders.site = false;
            },
          );
      }
    },
    uploadFile(file) {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        this.logo = reader.result;
      };
    },
  },
};
</script>

<style>
.formLogo {
  height: auto;
  display: block;
  max-width: 250px;
}
</style>
