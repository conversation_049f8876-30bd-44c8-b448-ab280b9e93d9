<template>
  <div>
    <btn-edit
      :text="$t('actions.edit')"
      @click="dialog=true"
    />
    <generic-modal
      v-model="dialog"
      :title="$t('common_cyclicTopUpsHeading')"
    >
      <div class="text-center">
        <v-progress-circular
          v-if="loaders.site"
          class="circleProgress"
          :size="90"
          :width="7"
          color="primary"
          indeterminate
        />
      </div>
      <template v-if="!loaders.site">
        <v-container
          grid-list-md
          class="pt-0"
        >
          <v-form
            ref="formClientEdit"
            v-model="form.valid"
            lazy-validation
          >
            <v-layout wrap>
              <v-col
                cols="12"
                class="pt-0 pb-0"
              >
                <v-checkbox
                  v-model="config.isActive"
                  :label="$t('loyaltyCards_active')"
                  prepend-icon="mdi-tune"
                  name="isActive"
                />
              </v-col>
              <v-col
                cols="6"
                class="pt-0 pb-0"
              >
                <v-text-field
                  v-model="config.card.number"
                  :disabled="true"
                  :label="$t('loyaltyCards_card')"
                  prepend-icon="mdi-credit-card"
                  :data-vv-as="`${$t('loyaltyCards_card')}`"
                  :counter="64"
                  name="cardNumber"
                  :error-messages="errors.collect('cardNumber')"
                  required
                />
              </v-col>
              <v-col
                cols="6"
                class="pt-0 pb-0"
              >
                <v-text-field
                  v-model="companyName"
                  :disabled="true"
                  :label="$t('common_client')"
                  prepend-icon="mdi-account"
                  :data-vv-as="`${$t('common_client')}`"
                  :counter="64"
                  name="id"
                  :error-messages="errors.collect('id')"
                  required
                />
              </v-col>
              <v-col
                cols="6"
                class="pt-0 pb-0"
              >
                <date-range-picker
                  key="dateRangeCyclicTopUp"
                  ref="dateRangeCyclicTopUp"
                  prepend-icon="mdi-calendar-range"
                  :show-presets="false"
                  :start-date-range="[
                    config.startTime,
                    config.endTime
                  ]"
                  :date-maximum="maxDate()"
                  @reload-transaction-list="onDateRangeChange"
                />
              </v-col>
              <v-col
                cols="6"
                class="pt-0 pb-0"
              >
                <v-autocomplete
                  v-model="config.type"
                  item-value="value"
                  item-text="text"
                  :label="$t('loyaltyCards_type')"
                  :items="cyclicTopUpTypesOptions"
                  :autocomplete="true"
                  prepend-icon="mdi-format-list-numbered"
                  name="type"
                  :error-messages="errors.collect('cyclic-top-ups.type.type')"
                  :rules="rules.selectRequired"
                  required
                />
              </v-col>
              <v-col
                cols="6"
                class="pt-0 pb-0"
              >
                <v-text-field
                  v-model="config.value"
                  :label="$t('loyaltyCards_value')"
                  prepend-icon="mdi-currency-usd"
                  :data-vv-as="`${$t('loyaltyCards_value')}`"
                  :counter="64"
                  name="value"
                  type="number"
                  :error-messages="errors.collect('value')"
                  required
                  :rules="[rules.positiveNumber]"
                />
              </v-col>
              <v-col
                cols="6"
                class="pt-0 pb-0"
              >
                <v-text-field
                  v-model="config.discount"
                  :label="$t('loyaltyCards_discount')"
                  prepend-icon="mdi-percent-outline"
                  :data-vv-as="`${$t('loyaltyCards_discount')}`"
                  :counter="64"
                  name="discount"
                  type="number"
                  :error-messages="errors.collect('discount')"
                  required
                  :rules="[rules.nonNegativeNumber]"
                />
              </v-col>
              <v-col
                sm="12"
                class="pt-0 pb-0"
              >
                <v-textarea
                  v-model="config.comment"
                  prepend-icon="mdi-comment-outline"
                  counter="60"
                  required
                  rows="3"
                  :label="$t('loyaltyCards_comment')"
                />
              </v-col>
            </v-layout>
          </v-form>
        </v-container>
      </template>
      <template #actions>
        <v-spacer />
        <v-btn
          color="gray"
          text
          @click.native="closeDialog"
        >
          {{ $t('actions.return_to_list') }}
        </v-btn>
        <v-btn
          color="primary darken-1"
          :loading="loaders.actualize"
          :disabled="!form.valid"
          @click.native="submit"
        >
          {{ $t('actions.save') }}
        </v-btn>
      </template>
    </generic-modal>
  </div>
</template>

<script>
import BtnEdit from '@components/common/button/BtnEdit.vue';
import DateRangePicker from '@components/common/DateRangePicker.vue';
import GenericModal from '@components/common/GenericModal.vue';
import { CyclicTopUpTypeType } from '@components/loyalty-cards/types';

export default {
  components: {
    BtnEdit,
    DateRangePicker,
    GenericModal,
  },
  props: {
    configId: {
      type: Number,
      default: null,
    },
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      loaders: {
        site: true,
        actualize: false,
      },
      dialog: false,
      form: {
        valid: false,
      },
      rules: {
        positiveNumber: (v) => Number(v) > 0 || this.$t(
          'loyaltyCards_topUpPositiveNumberOnly',
        ),
        nonNegativeNumber: (v) => Number(v) >= 0 || this.$t(
          'loyaltyCards_topUpPositiveNumberOnly',
        ),
        required: [(v) => !!v || this.$t('common_fieldRequired')],
        selectRequired: [(v) => (!!v || this.$t('common_fieldRequired'))],
        selectRequired2: [(v) => v.length > 0 || this.$t('common_fieldRequired')],
      },
    };
  },
  computed: {
    cyclicTopUpTypesOptions() {
      return CyclicTopUpTypeType.map((item) => ({
        text: this.$t(`${item.text}`),
        value: item.value,
      }));
    },
    companyName: {
      get() {
        return this.config?.card?.client?.companyName ?? null;
      },
      set() {
      },
    },
  },
  watch: {
    dialog(val) {
      // get client data only when dialog shows up
      if (val) {
        this.loaders.actualize = false;
        this.resetValidationErrorsAndClearFields();
        this.getData();
      }
    },
  },
  methods: {
    maxDate() {
      const today = new Date();
      today.setFullYear(today.getFullYear() + 2);
      today.setMonth(11);
      today.setDate(31);

      return today.toISOString().slice(0, 10);
    },
    validateAll() {
      this.$refs.formClientEdit.validate();
    },
    resetValidationErrorsAndClearFields() {
      this.clearFormData();
      if (this.$refs.formClientEdit) {
        this.$refs.formClientEdit.reset();
        this.$refs.formClientEdit.resetValidation();
        this.$validator.reset();
      }
    },
    getData() {
      this.loaders.site = true;
      this.loaders.actualize = false;
      this.axios.get(
        `${this.baseUrl}/bkfpay/cyclic_top_up/${this.configId}`,
      )
        .then(
          (response) => {
            this.config = {
              ...response.data,
            };

            this.loaders.site = false;
          },
          () => {
            this.onError();
          },
        );
    },
    onError() {
      // on error
      this.closeDialog();
    },
    editConfig() {
      this.$validator.validateAll();
      if (this.$refs.formClientEdit.validate()) {
        this.loaders.site = true;
        this.loaders.actualize = true;

        const parameters = {
          value: parseFloat(this.config.value),
          isActive: this.config.isActive,
          discount: parseFloat(this.config.discount),
          type: this.config.type,
          comment: this.config.comment,
          startTime: this.config.startTime,
          endTime: this.config.endTime,
        };

        const url = `${this.baseUrl}/bkfpay/cyclic_top_up/${this.configId}`;

        this.axios.put(
          url,
          parameters,
        )
          .then(
            () => {
              this.$emit('cyclic-config-edited', {});
              this.closeDialog();
            },
            () => {
              // on error
              this.loaders.site = false;
              this.loaders.actualize = false;
            },
          );
      }
    },
    submit() {
      this.editConfig();
    },
    clearFormData() {
      this.config = {
        id: null,
        typs: null,
        lastCall: null,
        lastPeriod: null,
        isActive: false,
        value: null,
        discount: null,
        comment: null,
        startTime: null,
        endTime: null,
        card: {
          client: {
            id: null,
            companyName: null,
          },
        },
      };
    },
    closeDialog() {
      this.resetValidationErrorsAndClearFields();
      this.dialog = false;
    },
    onDateRangeChange(dates) {
      this.config.startTime = dates.from;
      this.config.endTime = dates.to;
    },
  },
};
</script>
