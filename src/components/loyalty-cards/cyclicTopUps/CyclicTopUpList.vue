<template>
  <report-data-table
    ref="dataTable"
    :title="$t('common_cyclicTopUpsHeading')"
    :headers="headers"
    :base-url="baseUrl"
    report="v2\LoyaltyCyclicTopUps3Report"
    :filters="params"
  >
    <template #[`item.cardNumber`]="{ item }">
      <card-number :number="item.cardNumber" />
    </template>

    <template #[`item.type`]="{ item }">
      <cyclic-top-up-type-badge :item="item" />
    </template>
    <template #[`item.isActive`]="{ item }">
      <cyclic-top-up-active-badge :item="item" />
    </template>
    <template #[`item.value`]="{ item }">
      <currency-formatter
        :symbol="item.cardCurrencySymbol"
        :value="item.value"
      />
    </template>
    <template #[`item.startTime`]="{ item }">
      <date-time-formatter
        :value="item.startTime"
        format="YYYY-MM-DD"
      />
    </template>
    <template #[`item.endTime`]="{ item }">
      <date-time-formatter
        :value="item.endTime"
        format="YYYY-MM-DD"
      />
    </template>
    <template #[`item.lastPeriod`]="{ item }">
      <date-time-formatter
        :value="item.lastPeriod"
        format="YYYY-MM-DD"
      />
    </template>
    <template #[`item.actions`]="{ item }">
      <cyclic-top-up-modal
        :config-id="item.id"
        :base-url="baseUrl"
        @cyclic-config-edited="fetchData"
      />
    </template>
  </report-data-table>
</template>

<script>
import ReportDataTable from '@components/reports/ReportDataTable.vue';
import CurrencyFormatter from '@components/common/formatters/CurrencyFormatter.vue';
import CyclicTopUpTypeBadge from '@components/loyalty-cards/cyclicTopUps/badge/CyclicTopUpTypeBadge.vue';
import CyclicTopUpActiveBadge from '@components/loyalty-cards/cyclicTopUps/badge/CyclicTopUpActiveBadge.vue';
import DateTimeFormatter from '@components/common/formatters/DateTimeFormatter.vue';
import CyclicTopUpModal from '@components/loyalty-cards/cyclicTopUps/modal/CyclicTopUpModal.vue';
import CardNumber from '@components/loyalty-cards/badge/CardNumber.vue';

export default {
  components: {
    CardNumber,
    CurrencyFormatter,
    CyclicTopUpModal,
    ReportDataTable,
    CyclicTopUpTypeBadge,
    CyclicTopUpActiveBadge,
    DateTimeFormatter,
  },
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    headers() {
      return [
        {
          text: this.$t('loyaltyCards_card'),
          value: 'cardNumber',
          sortable: false,
        },
        {
          value: 'cardAlias',
          text: this.$t('loyaltyCards_name'),
          align: 'left',
          sortable: false,
          protected: true,
        },
        {
          text: this.$t('common_client'),
          value: 'client',
          align: 'left',
          sortable: false,
          protected: true,
        },
        {
          text: this.$t('common_comment'),
          value: 'comment',
          align: 'left',
          sortable: false,
          protected: true,
        },
        {
          text: this.$t('loyaltyCards_state'),
          value: 'isActive',
          sortable: false,
          align: 'center',
        },
        {
          text: this.$t('loyaltyCards_startTime'),
          value: 'startTime',
          sortable: false,
          align: 'center',
        },
        {
          text: this.$t('loyaltyCards_endTime'),
          value: 'endTime',
          sortable: false,
          align: 'center',
        },
        {
          text: this.$t('loyaltyCards_type'),
          value: 'type',
          sortable: false,
          align: 'left',
        },
        {
          text: this.$t('loyaltyCards_lastCal'),
          value: 'lastPeriod',
          sortable: false,
          align: 'center',
        },
        {
          text: this.$t('loyaltyCards_value'),
          value: 'value',
          sortable: false,
          align: 'right',
        },
        {
          text: this.$t('actions.actions'),
          value: 'actions',
          align: 'right',
          sortable: false,
        },
      ];
    },
  },
  methods: {
    fetchData() {
      this.$refs.dataTable.fetchData();
    },
  },
};
</script>
