<template>
  <div>
    {{ getTypeName(item) }}
  </div>
</template>

<script>

import { CyclicTopUpTypeType } from '@components/loyalty-cards/types';

export default {
  props: {
    item: {
      type: Object,
      required: true,
    },
  },
  methods: {
    getTypeName(item) {
      const type = CyclicTopUpTypeType.find((i) => i.value === item.type);
      if (type) {
        return this.$t(type.text);
      }

      return this.$t('loyaltyCards_cyclicAlign');
    },
  },
};
</script>
