<template>
  <div>
    <clients-filter v-model="filters" />
    <clients-list
      ref="dataTable"
      :params="filters"
      :base-url="baseUrl"
    />
  </div>
</template>

<script>
import ClientsFilter from '@components/loyalty-cards/clients/ClientsFilter.vue';
import ClientsList from '@components/loyalty-cards/clients/ClientsList.vue';

export default {
  components: {
    ClientsFilter,
    ClientsList,
  },
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      filters: {},
    };
  },
};

</script>
