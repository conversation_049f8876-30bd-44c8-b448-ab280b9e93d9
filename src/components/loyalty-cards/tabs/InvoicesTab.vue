<template>
  <div>
    <invoices-filter v-model="params" />
    <invoices-list
      ref="dataTable"
      :params="innerParams"
      :base-url="baseUrl"
    />
  </div>
</template>

<script>
import InvoicesFilter from '@components/loyalty-cards/invoices/InvoicesFilter.vue';
import InvoicesList from '@components/loyalty-cards/invoices/InvoicesList.vue';

export default {
  components: {
    InvoicesFilter,
    InvoicesList,
  },
  props: {
    clientId: {
      type: Number,
      default: null,
      required: false,
    },
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      params: {},
    };
  },
  computed: {
    innerParams() {
      return {
        ...this.params,
        clientId: this.clientId,
      };
    },
  },
};
</script>
