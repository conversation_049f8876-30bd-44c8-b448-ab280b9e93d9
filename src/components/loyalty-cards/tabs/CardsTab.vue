<template>
  <div>
    <cards-filter v-model="filters" />
    <cards-list
      ref="dataTable"
      :params="filters"
      :base-url="baseUrl"
    />
  </div>
</template>

<script>
import CardsFilter from '@components/loyalty-cards/cards/CardsFilter.vue';
import CardsList from '@components/loyalty-cards/cards/CardsList.vue';

export default {
  components: {
    CardsFilter,
    CardsList,
  },
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      filters: {},
    };
  },
};

</script>
