<template>
  <v-tabs
    show-arrows
    dens
    dark
    background-color="secondary lighten-1"
  >
    <v-tab
      v-for="(item) in tabs"
      :key="item.key"
      ripple
      :disabled="!item.show"
    >
      {{ item.text }}
    </v-tab>

    <v-tab-item
      v-for="(item) in tabs"
      :key="item.key"
      class="pa-4"
    >
      <component
        :is="item.component"
        v-bind="item.props"
      />
    </v-tab-item>
  </v-tabs>
</template>

<script>
import CardsTab from '@components/loyalty-cards/tabs/CardsTab.vue';
import TopUpsTab from '@components/loyalty-cards/tabs/TopUpsTab.vue';
import ClientsTab from '@components/loyalty-cards/tabs/ClientsTab.vue';
import CyclicTopUpTab from '@components/loyalty-cards/tabs/CyclicTopUpTab.vue';
import ConfigurationPage from '@components/loyalty-cards/config/ConfigurationPage.vue';
import InvoicesTab from '@components/loyalty-cards/tabs/InvoicesTab.vue';
import PaymentsTab from '@components/loyalty-cards/tabs/PaymentsTab.vue';
import SubscriptionsTab from '@components/loyalty-cards/tabs/SubscriptionsTab.vue';
import CarwashesList from '@components/loyalty-cards/carwashes/CarwashesList.vue';
import TransactionsTab from '@components/loyalty-cards/tabs/TransactionsTab.vue';

// z loyal-app, do przeniesienia gdzies indziej
import CarwashesTab from '@/components/loyal-app/tabs/CarwashesTab.vue';

import { mapGetters } from 'vuex';

export default {
  components: {
    CardsTab,
    TransactionsTab,
    TopUpsTab,
    CyclicTopUpTab,
    ClientsTab,
    CarwashesList,
    ConfigurationPage,
    InvoicesTab,
    PaymentsTab,
    SubscriptionsTab,
    CarwashesTab,
  },
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
  },
  computed: {
    ...mapGetters({
      canAccess: 'auth/canAccess',
    }),
    tabs() {
      return [
        {
          component: CardsTab,
          key: 'cards',
          props: {
            showFiltering: true,
            baseUrl: this.baseUrl,
          },
          show: true,
          text: this.$t('common_cards'),
        },
        {
          component: TransactionsTab,
          key: 'transactions2',
          props: {
            autoLoad: true,
            autoUpdateTransactions: true,
            showFiltering: true,
            baseUrl: this.baseUrl,
          },
          show: true,
          text: this.$t('card.transactions'),
        },
        {
          component: TopUpsTab,
          key: 'top-ups',
          props: {
            autoLoad: true,
            autoUpdateTransactions: true,
            showFiltering: true,
            baseUrl: this.baseUrl,
          },
          show: true,
          text: this.$t('common_topups'),
        },
        {
          component: CyclicTopUpTab,
          key: 'cyclic-top-ups',
          props: {
            autoLoad: true,
            autoUpdateTransactions: true,
            showFiltering: true,
            baseUrl: this.baseUrl,
          },
          show: this.canAccess('loyalty', 'cyclicTopUps'),
          text: this.$t('common_cyclicTopUpsHeading'),
        },
        {
          component: ClientsTab,
          key: 'clients',
          props: {
            autoLoad: true,
            showFiltering: true,
            baseUrl: this.baseUrl,
          },
          show: this.canAccess('loyalty', 'clients'),
          text: this.$t('loyaltyCards_clients'),
        },
        {
          component: InvoicesTab,
          key: 'invoices',
          props: {
            autoLoad: true,
            autoUpdateTransactions: true,
            showFiltering: true,
            baseUrl: this.baseUrl,
          },
          show: this.canAccess('loyalty', 'invoices'),
          text: this.$t('common_invoices_heading'),
        },
        {
          component: PaymentsTab,
          key: 'payments',
          props: {
            autoLoad: true,
            showFiltering: true,
            autoUpdateTransactions: true,
            baseUrl: this.baseUrl,
          },
          show: this.canAccess('loyalty', 'transactions'),
          text: this.$t('loyaltyCards_payments'),
        },
        {
          component: ConfigurationPage,
          key: 'invoice-data',
          props: {
            baseUrl: this.baseUrl,
          },
          show: this.canAccess('loyalty', 'cards'),
          text: this.$t('loyaltyCards_settings'),
        },
        {
          text: this.$t('loyalSystem_packages'),
          component: 'subscriptions-tab',
          key: 'subscription-packages-tab',
          show: this.canAccess('loyalty', 'cards'),
          props: {
            autoLoad: true,
            showFiltering: true,
            autoUpdateTransactions: true,
            app: this.currentApp,
            baseUrl: this.baseUrl,
          },
        },
        {
          component: CarwashesTab,
          key: 'carwashes',
          props: {
            baseUrl: `${this.baseUrl}/bkfpay`,
          },
          show: true,
          text: this.$t('common_carwashes'),
        },
      ];
    },
  },
};
</script>
