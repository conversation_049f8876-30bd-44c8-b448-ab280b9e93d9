<template>
  <div>
    <payments-filters
      v-model="filters"
      :base-url="baseUrl"
    />
    <payments-list
      :params="filters"
      :base-url="baseUrl"
    />
  </div>
</template>

<script>

import PaymentsFilters from '@components/loyalty-cards/payments/PaymentsFilters.vue';
import PaymentsList from '@components/loyalty-cards/payments/PaymentsList.vue';

export default {
  components: {
    PaymentsList,
    PaymentsFilters,
  },
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      filters: {},
    };
  },
};
</script>
