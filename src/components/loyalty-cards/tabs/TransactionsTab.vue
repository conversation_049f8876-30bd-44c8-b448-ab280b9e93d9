<template>
  <div>
    <transactions-filter v-model="filters" />
    <transactions-list
      :params="filters"
      :base-url="baseUrl"
    />
  </div>
</template>

<script>

import TransactionsFilter from '@components/loyalty-cards/transactions/TransactionsFilter.vue';
import TransactionsList from '@components/loyalty-cards/transactions/TransactionsList.vue';

export default {
  components: {
    TransactionsList,
    TransactionsFilter,
  },
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      loading: false,
      filters: {},
    };
  },
};
</script>
