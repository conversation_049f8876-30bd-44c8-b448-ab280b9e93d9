<template>
  <div>
    <subscriptions-filter v-model="filters" />
    <subscriptions-list
      ref="dataTable"
      :params="filters"
      :base-url="baseUrl"
    />
  </div>
</template>

<script>
import SubscriptionsFilter from '@components/loyalty-cards/subscriptions/SubscriptionsFilter.vue';
import SubscriptionsList from '@components/loyalty-cards/subscriptions/SubscriptionsList.vue';

export default {
  components: {
    SubscriptionsFilter,
    SubscriptionsList,
  },
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      filters: {},
    };
  },
};

</script>
