<template>
  <div>
    <top-ups-filter v-model="params" />
    <top-ups-list
      ref="dataTable"
      :params="params"
      :base-url="baseUrl"
    />
  </div>
</template>

<script>
import TopUpsFilter from '@components/loyalty-cards/topUps/TopUpsFilter.vue';
import TopUpsList from '@components/loyalty-cards/topUps/TopUpsList.vue';

export default {
  components: {
    TopUpsList,
    TopUpsFilter,
  },
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      params: {},
    };
  },
};
</script>
