<template>
  <div>
    <cyclic-top-up-filter v-model="filters" />
    <cyclic-top-up-list
      ref="dataTable"
      :params="filters"
      :base-url="baseUrl"
    />
  </div>
</template>

<script>
import CyclicTopUpFilter from '@components/loyalty-cards/cyclicTopUps/CyclicTopUpFilter.vue';
import CyclicTopUpList from '@components/loyalty-cards/cyclicTopUps/CyclicTopUpList.vue';

export default {
  components: {
    CyclicTopUpFilter,
    CyclicTopUpList,
  },
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      filters: {},
    };
  },
};

</script>
