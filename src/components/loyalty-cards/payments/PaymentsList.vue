<template>
  <report-data-table
    ref="dataTable"
    :title="$t('loyaltyCards_payments')"
    :headers="headers"
    report="v2\LoyaltyPayments3Report"
    :filters="params"
    :base-url="baseUrl"
  >
    <template #[`item.id`]="{ item }">
      <div class="flex-inline-start">
        <payment-status-badge :item="item" />
        <blurred-formatter
          :value="item.id"
        />
      </div>
    </template>

    <template #[`item.time`]="{ item }">
      <date-time-formatter :value="item.time" />
    </template>

    <template #[`item.value`]="{ item }">
      <currency-formatter
        :value="item.value"
        :symbol="item.currency"
      />
    </template>
  </report-data-table>
</template>

<script>

import ReportDataTable from '@components/reports/ReportDataTable.vue';
import CurrencyFormatter from '@components/common/formatters/CurrencyFormatter.vue';
import DateTimeFormatter from '@components/common/formatters/DateTimeFormatter.vue';
import PaymentStatusBadge from '@components/loyalty-cards/payments/badge/PaymentStatusBadge.vue';
import BlurredFormatter from '@components/common/formatters/BlurredFormatter.vue';

export default {
  components: {
    BlurredFormatter,
    PaymentStatusBadge,
    DateTimeFormatter,
    CurrencyFormatter,
    ReportDataTable,
  },
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    headers() {
      return [
        {
          text: this.$t('common_id'),
          value: 'id',
          sortable: false,
          protected: true,
        },
        {
          text: this.$t('common_tableDate'),
          value: 'time',
          displayMethod: 'date',
          sortable: false,
        },
        {
          text: this.$t('common_user'),
          value: 'userEmail',
          class: 'md-and-up',
          sortable: false,
          protected: true,
        },
        {
          text: this.$t('loyaltyCards_invoiceNumber'),
          value: 'invoiceNumber',
          class: 'md-and-up',
          sortable: false,
          protected: true,
        },
        {
          text: this.$t('loyalApp_accountType'),
          value: 'type',
          class: 'md-and-up',
          sortable: false,
        },
        {
          text: this.$t('loyalApp_externalId'),
          value: 'externalId',
          sortable: false,
          align: 'left',
          protected: true,
        },
        {
          text: this.$t('common_value'),
          value: 'value',
          align: 'right',
          sortable: false,
        },
      ];
    },
  },
  methods: {
    fetchData() {
      this.$refs.dataTable.fetchData();
    },
  },
};
</script>
