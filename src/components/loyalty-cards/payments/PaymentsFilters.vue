<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
    card
  >
    <v-row>
      <v-col>
        <text-search v-model="config.search" />
      </v-col>
      <v-col>
        <multiselect
          v-model="config.status"
          :items="statusFilters"
          :label="$t('common_state')"
          prepend-icon="mdi-list-status"
          allow-null
        />
      </v-col>
      <v-col>
        <multiselect
          v-model="config.type"
          :items="typeFilters"
          :label="$t('loyalApp_externalType')"
          prepend-icon="mdi-cash-multiple"
          allow-null
        />
      </v-col>
      <v-col>
        <date-select
          v-model="config.date"
        />
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import debounce from 'lodash/debounce';
import TextSearch from '@components/reports/filters/TextSearch.vue';
import Multiselect from '@components/reports/filters/MultiSelect.vue';
import DateSelect from '@components/reports/filters/DateSelect.vue';
import { PaymentStatusType } from '@components/loyalty-cards/types';

const SettingsKey = 'bkfpay-payments-filter';

export default {
  components: {
    TextSearch,
    Multiselect,
    DateSelect,
  },
  model: {
    prop: 'param',
    event: 'update:param',
  },
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
    param: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    const savedConfig = JSON.parse(localStorage.getItem(SettingsKey) || '{}');

    return {
      config: {
        search: savedConfig.search ?? null,
        status: savedConfig.status ?? null,
        type: savedConfig.type ?? null,
        date: savedConfig.date ?? null,
      },
      statuses: null,
      types: null,
    };
  },
  computed: {
    statusFilters() {
      return PaymentStatusType.map((item) => ({
        text: this.$t(`${item.text}`),
        value: item.value,
        icon: item.icon,
      }));
    },
    typeFilters() {
      if (!this.types) {
        return [];
      }

      return this.types.map((item) => ({
        text: item,
        value: item,
      }));
    },
    internalParam() {
      return {
        search: this.config.search,
        status: this.config.status?.length ? this.config.status.join(',') : null,
        type: this.config.type?.length ? this.config.type.join(',') : null,
        ...this.config.date,
      };
    },
  },
  watch: {
    internalParam: {
      handler(newVal) {
        this.debouncedUpdate(newVal);
      },
      deep: true,
      immediate: true,
    },
    config: {
      handler(newVal) {
        localStorage.setItem(SettingsKey, JSON.stringify(newVal));
      },
      deep: true,
    },
  },
  mounted() {
    this.fetchFilters();
  },
  beforeCreate() {
    this.debouncedUpdate = debounce((val) => {
      this.$emit('update:param', val);
    }, 300);
  },
  methods: {
    async fetchFilters() {
      const response = await this.axios.get(
        `${this.baseUrl}/bkfpay/external_payments/filters`,
      );

      this.types = response.data.types;

      // przy zmianie aplikacji mozemy mieć innych types
      // usuwamy wartości których nie ma w nowej liście
      if (this.config.types?.length) {
        const hasInvalidType = this.config.types.some(
          (types) => !this.types.includes(types),
        );
        if (hasInvalidType) {
          this.config.types = null;
        }
      }
    },
  },
};
</script>
