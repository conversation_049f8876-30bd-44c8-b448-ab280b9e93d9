<template>
  <v-tooltip bottom>
    <template #activator="{ on, attrs }">
      <v-icon
        :color="getStatusDetails(item.status).color"
        v-bind="attrs"
        v-on="on"
      >
        {{ getStatusDetails(item.status).icon }}
      </v-icon>
    </template>
    <span> {{ getStatusDetails(item.status).text }}</span>
  </v-tooltip>
</template>

<script>

export default {
  props: {
    item: {
      type: [Array, Object],
      required: true,
    },
  },
  data() {
    return {
      icons: {
        canceled: 'mdi-close-circle-outline',
        confirmed: 'mdi-check-circle-outline',
        default: 'mdi-help-circle-outline',
        error: 'mdi-close-octagon-outline',
        initiated: 'mdi-progress-star',
        pending: 'mdi-progress-clock',
        refunded: 'mdi-credit-card-refund-outline',
        rejected: 'mdi-alert-outline',
        timeout: 'clock-alert-outline',
        waiting: 'mdi-progress-clock',
      },
      colors: {
        canceled: 'error',
        confirmed: 'green darken-2',
        default: 'gray',
        error: 'error',
        initiated: 'progress',
        pending: 'progress',
        refunded: 'warning',
        rejected: 'error',
        timeout: 'error',
        waiting: 'progress',
      },
      texts: {
        canceled: this.$t('common_canceled'),
        confirmed: this.$t('common_confirmed'),
        default: this.$t('common_unknown'),
        error: this.$t('common_error'),
        initiated: this.$t('common_initiated'),
        pending: this.$t('common_pending'),
        refunded: this.$t('common_refund'),
        rejected: this.$t('common_rejected'),
        timeout: this.$t('common_timeout'),
        waiting: this.$t('common_waiting'),
      },
    };
  },
  methods: {
    getStatusDetails(status) {
      if (status in this.icons) {
        return {
          icon: this.icons[status],
          color: this.colors[status],
          text: this.texts[status],
        };
      }

      return {
        icon: this.icons.default,
        color: this.colors.default,
        text: status,
      };
    },
  },
};
</script>
