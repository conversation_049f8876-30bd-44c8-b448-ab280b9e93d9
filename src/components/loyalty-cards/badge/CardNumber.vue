<template>
  <v-tooltip
    v-if="status === 'BLOCKED'"
    bottom
  >
    <template #activator="{ on, attrs }">
      <div
        class="notice-icon"
        v-bind="attrs"
        style="color: red; text-decoration: line-through;"
        v-on="on"
      >
        <blurred-formatter
          :value="number.toUpperCase()"
        />
      </div>
    </template>
    <span>{{ $t('common_cardBlocked') }}</span>
  </v-tooltip>
  <div v-else>
    <blurred-formatter
      :value="number.toUpperCase()"
    />
  </div>
</template>

<script>

import BlurredFormatter from '@components/common/formatters/BlurredFormatter.vue';

export default {
  components: { BlurredFormatter },
  props: {
    number: {
      type: String,
      required: true,
    },
    status: {
      type: String,
      default: null,
    },
  },

};
</script>
