<template>
  <v-tooltip bottom>
    <template #activator="{ on, attrs }">
      <v-icon
        v-bind="attrs"
        v-on="on"
      >
        {{ statusInfo.icon }}
      </v-icon>
    </template>
    <span> {{ statusInfo.text }}</span>
  </v-tooltip>
</template>

<script>

import { TopUpType } from '@components/loyalty-cards/types';

export default {
  props: {
    status: {
      type: String,
      required: true,
    },
  },
  computed: {
    statusInfo() {
      const status = TopUpType.find((item) => item.value === this.status);
      if (status) {
        return {
          icon: status.icon,
          color: status.color,
          text: this.$t(status.text),
        };
      }

      return {
        icon: 'mdi-help-circle',
        color: 'grey',
        text: this.$t('invoice.status.unknown'),
      };
    },
  },
};
</script>
