<template>
  <div>
    <top-up-generate-invoice-modal
      v-if="item.invoiceGenerate"
      :id="item.id"
      :client-id="item.cardClientId"
      :base-url="baseUrl"
      v-on="$listeners"
    />
    <div v-else-if="item.invoiceNumber">
      <act-download
        :url="`${baseUrl}/bkfpay/invoice/${item.invoice}/download`"
        :text="item.invoiceNumber"
      />
    </div>
  </div>
</template>

<script>

import TopUpGenerateInvoiceModal
  from '@components/loyalty-cards/topUps/modal/TopUpGenerateInvoiceModal.vue';
import ActDownload from '@components/common/Action/ActDownload.vue';

export default {
  components: { ActDownload, TopUpGenerateInvoiceModal },
  props: {
    item: {
      type: Object,
      default: null,
    },
    baseUrl: {
      type: String,
      required: true,
    },
  },

};
</script>
