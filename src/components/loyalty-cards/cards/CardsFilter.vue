<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
    card
  >
    <v-row>
      <v-col>
        <text-search v-model="config.search" />
      </v-col>
      <v-col>
        <single-select
          v-model="config.types"
          :items="filters.types.options"
          :label="$t('loyaltyCards_cardType')"
          prepend-icon="mdi-web"
          allow-null
        />
      </v-col>
      <v-col>
        <div class="d-flex justify-end">
          <v-switch
            v-model="config.usedCards"
            hide-details
            class="mr-2"
          />
          <date-select
            v-model="config.dateselect"
            :disabled="!config.usedCards"
            :show-custom="false"
          />
        </div>
      </v-col>
    </v-row>

    <v-row class="mt-0">
      <v-col class="py-0">
        <single-select
          v-model="config.status"
          :items="filters.status.options"
          :label="$t('loyaltyCards_activity')"
          prepend-icon="mdi-credit-card-settings-outline"
          allow-null
        />
      </v-col>
      <v-col class="py-0">
        <single-select
          v-model="config.foundsExists"
          :items="filters.founds.options"
          :label="$t('loyaltyCards_funds')"
          prepend-icon="mdi-wallet"
          allow-null
        />
      </v-col>
      <v-col class="py-0">
        <single-select
          v-model="config.nameExists"
          :items="filters.name.options"
          :label="$t('loyaltyCards_names')"
          prepend-icon="mdi-format-text"
          allow-null
        />
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import TextSearch from '@components/reports/filters/TextSearch.vue';
import SingleSelect from '@components/reports/filters/SingleSelect.vue';
import DateSelect from '@components/reports/filters/DateSelect.vue';
import debounce from 'lodash/debounce';

const SettingsKey = 'bkfpay-card-filter';
export default {
  components: {
    DateSelect,
    TextSearch,
    SingleSelect,
  },
  model: {
    prop: 'param',
    event: 'update:param',
  },
  props: {
    param: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    const savedConfig = JSON.parse(localStorage.getItem(SettingsKey) || '{}');
    return {
      config: {
        search: savedConfig.search ?? null,
        types: savedConfig.types ?? null,
        status: savedConfig.status ?? null,
        usedCards: savedConfig.usedCards ?? null,
        nameExists: savedConfig.nameExists ?? null,
        foundsExists: savedConfig.foundsExists ?? null,
        dateselect: savedConfig.dateselect ?? null,
      },
      filters: {
        name: {
          options: [
            { text: this.$t('loyaltyCards_withNames'), value: 1, icon: 'mdi-format-title' },
            { text: this.$t('loyaltyCards_withoutNames'), value: 0, icon: 'mdi-format-strikethrough' },
          ],
        },
        types: {
          options: [
            { text: this.$t('loyaltyCards_virtual'), value: 'VIRTUAL', icon: 'mdi-web' },
            { text: this.$t('loyaltyCards_regular'), value: 'MIFARE', icon: 'mdi-credit-card-outline' },
          ],
        },
        status: {
          options: [
            { text: this.$t('loyaltyCards_filtersActive'), value: 'ACTIVE', icon: 'mdi-lock-open' },
            { text: this.$t('loyaltyCards_blocked'), value: 'BLOCKED', icon: 'mdi-lock' },
          ],
        },
        founds: {
          options: [
            { text: this.$t('loyaltyCards_withFounds'), value: 1, icon: 'mdi-currency-usd' },
            { text: this.$t('loyaltyCards_withoutFounds'), value: 0, icon: 'mdi-currency-usd-off' },
          ],
        },
      },
    };
  },
  computed: {
    internalParam() {
      const paramsInit = {
        search: this.config.search ? this.config.search.trim() : null,
        types: this.config.types,
        status: this.config.status,
        nameExists: this.config.nameExists,
        foundsExists: this.config.foundsExists,
      };
      if (!this.config.usedCards) {
        return paramsInit;
      }
      return {
        ...paramsInit,
        ...this.config.dateselect,
      };
    },
  },
  watch: {
    internalParam: {
      handler(newVal) {
        this.debouncedUpdate(newVal);
      },
      deep: true,
      immediate: true,
    },
    config: {
      handler(newVal) {
        localStorage.setItem(SettingsKey, JSON.stringify(newVal));
      },
      deep: true,
    },
  },
  beforeCreate() {
    // jeden debounced watcher na cały internalParam
    this.debouncedUpdate = debounce((val) => {
      this.$emit('update:param', val);
    }, 1000);
  },
};
</script>
