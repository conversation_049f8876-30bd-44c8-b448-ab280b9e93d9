<template>
  <report-data-table
    ref="dataTable"
    :title="$t('loyaltyCards_cardsList')"
    :headers="headers"
    :base-url="baseUrl"
    report="v2\LoyaltyCards3Report"
    :filters="params"
  >
    <template #[`table-actions`]>
      <card-add-modal
        :base-url="baseUrl"
        @reload-card-list="fetchData"
      />
    </template>
    <template #[`item.number`]="{ item }">
      <v-row>
        <card-number
          :number="item.number"
          :status="item.status"
        />
        <card-type-badge :type="item.type" />
        <card-comment-icon :comment="item.fullComment" />
      </v-row>
    </template>
    <template #[`item.lastContact`]="{ item }">
      <date-time-formatter :value="item.lastContact" />
    </template>
    <template #[`item.topUps`]="{ item }">
      <currency-formatter
        :symbol="item.currencySymbol"
        :value="item.topUps"
      />
    </template>
    <template #[`item.payments`]="{ item }">
      <currency-formatter
        :symbol="item.currencySymbol"
        :value="item.payments"
      />
    </template>
    <template #[`item.balance`]="{ item }">
      <card-balance :item="item" />
    </template>
    <template #[`item.actions`]="{ item }">
      <v-container
        class="d-flex justify-end"
      >
        <card-edit-modal
          :card-number="item.number"
          :card-token="item.cardToken"
          :base-url="baseUrl"
          @reload-card-list="fetchData"
        />
        <top-up-card-modal
          :card-number="item.number"
          :card-token="item.cardToken"
          :client-id="item.clientId ? item.clientId : null"
          :base-url="baseUrl"
          @reload-card-list="fetchData"
        />
        <card-transactions-modal
          :card-number="item.number"
          :card-token="item.cardToken"
          :card-name="item.alias"
          :base-url="baseUrl"
        />
        <card-top-ups-modal
          :card-number="item.number"
          :card-token="item.cardToken"
          :card-name="item.alias"
          :base-url="baseUrl"
        />
      </v-container>
    </template>
  </report-data-table>
</template>

<script>
import CardTopUpsModal from '@components/loyalty-cards/cards/modal/CardTopUpsModal.vue';
import ReportDataTable from '@components/reports/ReportDataTable.vue';
import CardBalance from '@components/loyalty-cards/cards/badge/CardBalance.vue';
import TopUpCardModal from '@components/loyalty-cards/topUps/modal/TopUpCardModal.vue';
import CardEditModal from '@components/loyalty-cards/cards/modal/CardEditModal.vue';
import CardTransactionsModal
  from '@components/loyalty-cards/cards/modal/CardTransactionsModal.vue';
import CardAddModal from '@components/loyalty-cards/cards/modal/CardAddModal.vue';
import CardNumber from '@components/loyalty-cards/badge/CardNumber.vue';
import CardCommentIcon from '@components/loyalty-cards/cards/badge/CardCommentIcon.vue';
import CardTypeBadge from '@components/loyalty-cards/cards/badge/CardTypeBadge.vue';
import CurrencyFormatter from '@components/common/formatters/CurrencyFormatter.vue';
import DateTimeFormatter from '@components/common/formatters/DateTimeFormatter.vue';

export default {
  components: {
    CurrencyFormatter,
    DateTimeFormatter,
    CardTypeBadge,
    CardCommentIcon,
    CardTransactionsModal,
    CardEditModal,
    TopUpCardModal,
    CardBalance,
    ReportDataTable,
    CardTopUpsModal,
    CardNumber,
    CardAddModal,
  },
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    headers() {
      return [
        {
          text: this.$t('loyaltyCards_card'),
          value: 'number',
          showInRowExpand: true,
          sortable: false,
          align: 'left',
        },
        {
          text: this.$t('loyaltyCards_name'),
          value: 'alias',
          align: 'left',
          showInRowExpand: true,
          sortable: false,
          protected: true,
        },
        {
          text: this.$t('common_email'),
          value: 'email',
          align: 'left',
          showInRowExpand: false,
          sortable: false,
          protected: true,
        },
        {
          text: this.$t('common_client'),
          value: 'clientName',
          align: 'left',
          sortable: false,
          protected: true,
        },
        {
          text: this.$t('common_lastUsage'),
          value: 'lastContact',
          align: 'left',
          showInRowExpand: true,
          sortable: false,
        },
        {
          text: this.$t('common_topups'),
          value: 'topUps',
          class: 'hidden-sm-and-down md-and-up',
          showInRowExpand: true,
          align: 'right',
          sortable: false,
        },
        {
          text: this.$t('loyaltyCards_payments'),
          value: 'payments',
          class: 'hidden-sm-and-down md-and-up',
          showInRowExpand: true,
          align: 'right',
          sortable: false,
        },
        {
          text: `${this.$t('loyaltyCards_cardFunds')} (${this.$t('loyaltyCards_toSend')})`,
          value: 'balance',
          align: 'right',
          showInRowExpand: true,
          sortable: false,
        },
        {
          text: this.$t('actions.actions'),
          value: 'actions',
          align: 'center',
          sortable: false,
        },
      ];
    },
  },
  methods: {
    fetchData() {
      this.$refs.dataTable.fetchData();
    },
  },
};
</script>
