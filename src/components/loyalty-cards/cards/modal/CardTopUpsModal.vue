<template>
  <div>
    <btn-history
      :text="$t('transactions.topup_card_history')"
      @click="openDialog"
    />
    <generic-modal
      v-model="dialog"
      :title="cardTitle"
      :fullscreen="true"
      :show-actions="false"
    >
      <top-ups-list
        ref="topUps"
        :params="{
          cardToken: cardToken
        }"
        :base-url="baseUrl"
      />
    </generic-modal>
  </div>
</template>

<script>

import BtnHistory from '@components/common/button/BtnHistory.vue';
import TopUpsList from '@components/loyalty-cards/topUps/TopUpsList.vue';
import GenericModal from '@components/common/GenericModal.vue';

export default {
  components: {
    GenericModal,
    TopUpsList,
    BtnHistory,
  },
  props: {
    cardNumber: {
      type: String,
      default: null,
    },
    cardToken: {
      type: String,
      default: null,
    },
    cardName: {
      type: String,
      default: null,
    },
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
    };
  },
  computed: {
    cardTitle() {
      return `${this.$t('transactions.topup_card_history')}  - ${this.cardNumber} (${this.cardName})`;
    },
  },
  methods: {
    openDialog() {
      this.dialog = true;
      this.$nextTick(() => {
        this.$refs.topUps.fetchData();
      });
    },
  },
};
</script>
