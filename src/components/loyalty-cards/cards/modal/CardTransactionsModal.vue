<template>
  <div>
    <btn-history
      :text="$t('transactions.history_for_card')"
      @click="openDialog"
    />
    <generic-modal
      v-model="dialog"
      :fullscreen="true"
      :show-actions="false"
      :title="cardTitle"
    >
      <transactions-list
        ref="transactions"
        :params="{
          cardToken: cardToken
        }"
        :base-url="baseUrl"
      />
    </generic-modal>
  </div>
</template>

<script>

import BtnHistory from '@components/common/button/BtnHistory.vue';
import TransactionsList from '@components/loyalty-cards/transactions/TransactionsList.vue';
import GenericModal from '@components/common/GenericModal.vue';

export default {
  components: {
    GenericModal,
    TransactionsList,
    BtnHistory,
  },
  props: {
    cardToken: {
      type: String,
      default: null,
    },
    cardName: {
      type: String,
      default: null,
    },
    cardNumber: {
      type: String,
      default: null,
    },
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
    };
  },
  computed: {
    cardTitle() {
      return `${this.$t('transactions.history_for_card')} - ${this.cardNumber} (${this.cardName})`;
    },
  },
  methods: {
    openDialog() {
      this.dialog = true;
      this.$nextTick(() => {
        this.$refs.transactions.fetchData();
      });
    },
  },
};
</script>
