<template>
  <v-tooltip
    v-if="type === 'VIRTUAL'"
    bottom
  >
    <template #activator="{ on, attrs }">
      <div
        class="notice-icon"
        v-bind="attrs"
        v-on="on"
      >
        <v-icon>
          mdi-web
        </v-icon>
      </div>
    </template>
    <span>{{ $t('loyaltyCards_tableVirtualCard') }}</span>
  </v-tooltip>
</template>
<script>
export default {
  components: { },
  props: {
    type: {
      type: String,
      default: null,
    },
  },

};

</script>
