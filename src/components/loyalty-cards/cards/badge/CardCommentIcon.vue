<template>
  <div v-if="comment">
    <v-tooltip
      bottom
    >
      <template #activator="{ on, attrs }">
        <div
          class="notice-icon"
          v-bind="attrs"
          v-on="on"
        >
          <v-icon>
            mdi-comment-alert-outline
          </v-icon>
        </div>
      </template>
      <span>{{ comment }}</span>
    </v-tooltip>
  </div>
</template>

<script>
export default {
  props: {
    comment: {
      type: String,
      default: null,
    },
  },
};
</script>
