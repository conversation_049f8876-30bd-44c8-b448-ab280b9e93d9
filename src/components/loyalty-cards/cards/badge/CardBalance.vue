<template>
  <div>
    <v-tooltip bottom>
      <template #activator="{ on, attrs }">
        <div
          v-bind="attrs"
          v-on="on"
        >
          <currency-forrmater
            :symbol="item.currencySymbol"
            :value="item.balance"
          />
        </div>
        <div v-if="item.toSend && item.toSend > 0">
          (+<currency-forrmater
            :symbol="item.currencySymbol"
            :value="item.toSend"
          />)
        </div>
      </template>
      <span>{{ $t('loyaltyCards_cardFundsTooltip') }}</span>
    </v-tooltip>
  </div>
</template>

<script>
import CurrencyForrmater from '@components/common/formatters/CurrencyFormatter.vue';

export default {
  components: {
    CurrencyForrmater,
  },

  props: {
    item: {
      type: Object,
      required: true,
    },
  },
};
</script>
