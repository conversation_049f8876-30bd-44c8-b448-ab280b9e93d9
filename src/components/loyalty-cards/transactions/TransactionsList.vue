<template>
  <report-data-table
    ref="dataTable"
    :title="$t('card.transactions_list')"
    :headers="headers"
    report="v2\LoyaltyTransactions3Report"
    :filters="params"
    :base-url="baseUrl"
  >
    <template #[`table-actions`] />

    <template #[`item.cardNumber`]="{ item }">
      <v-row>
        <card-number :number="item.cardNumber" />
        <card-type-badge :type="item.type" />
      </v-row>
    </template>

    <template #[`item.time`]="{ item }">
      <date-time-formatter :value="item.time" />
    </template>

    <template #[`item.deviceName`]="{ item }">
      <device-type-badge
        :source="item.source"
        :stand-id="item.bayId"
      />
    </template>

    <template #[`item.type`]="{ item }">
      <transaction-type-badge :status="item.type" />
    </template>

    <template #[`item.topUpToSend`]="{ item }">
      <currency-formatter
        :value="item.topUpToSend"
        :symbol="item.currencySymbol"
      />
    </template>

    <template #[`item.value`]="{ item }">
      <currency-formatter
        :value="item.value"
        :symbol="item.currencySymbol"
      />
    </template>
    <template #[`item.balance`]="{ item }">
      <currency-formatter
        :value="item.balance"
        :symbol="item.currencySymbol"
      />
    </template>

    <template #[`item.invoice`]="{ item }">
      <top-up-generate-invoice-modal
        v-if="item.invoiceGenerate"
        :id="item.id"
        :client-id="item.cardClientId"
        :base-url="baseUrl"
        @generateSuccess="getData"
      />
      <div v-else>
        {{ item.invoiceNumber ?? '-' }}
      </div>
    </template>
  </report-data-table>
</template>

<script>

import ReportDataTable from '@components/reports/ReportDataTable.vue';
import DeviceTypeBadge from '@components/libs/standard-types/badge/DeviceTypeBadge.vue';
import TransactionTypeBadge from '@components/loyalty-cards/badge/TransactionTypeBadge.vue';
import CurrencyFormatter from '@components/common/formatters/CurrencyFormatter.vue';
import TopUpGenerateInvoiceModal
  from '@components/loyalty-cards/topUps/modal/TopUpGenerateInvoiceModal.vue';
import CardNumber from '@components/loyalty-cards/badge/CardNumber.vue';
import CardTypeBadge from '@components/loyalty-cards/cards/badge/CardTypeBadge.vue';
import DateTimeFormatter from '@components/common/formatters/DateTimeFormatter.vue';

export default {
  components: {
    DateTimeFormatter,
    CardTypeBadge,
    CardNumber,
    TopUpGenerateInvoiceModal,
    CurrencyFormatter,
    TransactionTypeBadge,
    DeviceTypeBadge,
    ReportDataTable,
  },
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    headers() {
      return [
        {
          text: this.$t('common_tableDate'),
          value: 'time',

        },
        {
          text: this.$t('loyaltyCards_carwash'),
          value: 'carwashName',
        },
        {
          text: this.$t('common_stand'),
          value: 'deviceName',
        },
        {
          text: this.$t('loyaltyCards_card'),
          value: 'cardNumber',
          align: 'left',
        },
        {
          text: this.$t('loyaltyCards_name'),
          value: 'cardAlias',
          align: 'left',
          protected: true,
        },
        {
          text: this.$t('common_type'),
          value: 'type',
        },
        {
          text: this.$t('common_value'),
          value: 'value',
          align: 'right',
        },
        {
          text: this.$t('loyaltyCards_valueAfterTransaction'),
          value: 'balance',
          align: 'right',
        },
      ];
    },
  },
  methods: {
    fetchData() {
      this.$refs.dataTable.fetchData();
    },
  },
};
</script>
