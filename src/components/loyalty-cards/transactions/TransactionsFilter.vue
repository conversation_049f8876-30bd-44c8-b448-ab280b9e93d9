<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
    card
  >
    <v-row>
      <v-col>
        <text-search
          v-model="config.search"
        />
      </v-col>

      <v-col>
        <single-select
          v-model="config.cardTypes"
          :items="CardTypeOptions"
          :label="$t('loyaltyCards_cardType')"
          prepend-icon="mdi-web"
          allow-null
        />
      </v-col>

      <v-col>
        <carwash-select
          v-model="config.carWash"
        />
      </v-col>
      <v-col>
        <date-select
          v-model="config.dateselect"
          :show-custom="true"
        />
      </v-col>
    </v-row>
    <v-row class="mt-0">
      <v-col>
        <multi-select
          v-model="config.type"
          :items="TransactionTypeOptions"
          :label="$t('common_transactionType')"
          prepend-icon="mdi-clipboard-list-outline"
          allow-null
        />
      </v-col>
      <v-col>
        <single-select
          v-model="config.nameExists"
          :items="NamesStatusOptions"
          :label="$t('loyaltyCards_names')"
          prepend-icon="mdi-format-text"
          allow-null
        />
      </v-col>
      <v-col>
        <multi-select
          v-model="config.source"
          :items="sourceOptions"
          :label="$t('loyaltyCards_source')"
          prepend-icon="mdi-point-of-sale"
          allow-null
        />
      </v-col>
    </v-row>
  </v-container>
</template>
<script>
import TextSearch from '@components/common/filters/TextSearch.vue';
import debounce from 'lodash/debounce';

import { CardTypeType, TransactionType } from '@components/loyalty-cards/types';
import CarwashSelect from '@components/reports/filters/CarwashSelect.vue';
import DateSelect from '@components/reports/filters/DateSelect.vue';
import SourceType from '@components/libs/standard-types/types';
import MultiSelect from '@components/reports/filters/MultiSelect.vue';
import SingleSelect from '@components/reports/filters/SingleSelect.vue';

const SettingsKey = 'bkfpay-transaction-filter';
export default {

  components: {
    MultiSelect,
    SingleSelect,
    DateSelect,
    CarwashSelect,
    TextSearch,
  },
  model: {
    prop: 'param',
    event: 'update:param',
  },
  props: {
    param: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    const savedConfig = JSON.parse(localStorage.getItem(SettingsKey) || '{}');
    return {
      config: {
        search: savedConfig.search ?? null,
        cardTypes: savedConfig.cardTypes ?? null,
        carWash: savedConfig.carWash ?? null,
        nameExists: savedConfig.nameExists ?? null,
        type: savedConfig.type ?? null,
        source: savedConfig.source ?? null,
        dateselect: savedConfig.dateselect ?? null,
      },
    };
  },
  computed: {
    CardTypeOptions() {
      return CardTypeType.map((item) => ({
        text: this.$t(item.text),
        value: item.value,
        icon: item.icon,
      }));
    },
    TransactionTypeOptions() {
      return TransactionType.map((item) => ({
        text: this.$t(item.text),
        value: item.value,
        icon: item.icon,
      }));
    },
    sourceOptions() {
      return SourceType.map((item) => ({
        text: this.$t(item.text),
        value: item.value,
        icon: item.icon,
      }));
    },
    NamesStatusOptions() {
      return [
        {
          text: this.$t('loyaltyCards_withNames'),
          value: 1,
          icon: 'mdi-format-title',
        },
        {
          text: this.$t('loyaltyCards_withoutNames'),
          value: 0,
          icon: 'mdi-format-strikethrough',
        },
      ];
    },
    // scalony obiekt filtrów do przekazania
    internalParam() {
      return {
        search: this.config.search,
        cardTypes: this.config.cardTypes ?? null,
        carwash: this.config.carWash?.length ? this.config.carWash?.join(',') : [],
        nameExists: this.config.nameExists ?? null,
        type: this.config.type?.length ? this.config.type.join(',') : [],
        source: this.config.source?.length ? this.config.source.join(',') : [],
        ...this.config.dateselect,
      };
    },
  },
  watch: {
    internalParam: {
      handler(newVal) {
        this.debouncedUpdate(newVal);
      },
      deep: true,
      immediate: true,
    },
    config: {
      handler(newVal) {
        localStorage.setItem(SettingsKey, JSON.stringify(newVal));
      },
      deep: true,
    },
  },
  beforeCreate() {
    // jeden debounced watcher na cały internalParam
    this.debouncedUpdate = debounce((val) => {
      this.$emit('update:param', val);
    }, 300);
  },

  methods: {
    onDateRangeChange(dateRange) {
      this.interval = dateRange.value;
    },
  },
};
</script>
