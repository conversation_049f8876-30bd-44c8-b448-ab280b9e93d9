<template>
  <report-data-table
    ref="dataTable"
    :title="$t('loyaltyCards_heading')"
    :headers="headers"
    :base-url="baseUrl"
    report="v2\LoyaltyClients3Report"
    :filters="params"
  >
    <template #[`table-actions`]>
      <client-add-modal
        :base-url="baseUrl"
        @on-client-update="fetchData()"
      />
    </template>
    <template #[`item.actions`]="{ item }">
      <v-container
        class="d-flex justify-end"
      >
        <client-alert-badge
          :item="item"
        />
        <client-cards-list-modal
          :client-id="item.id"
          :client-name="item.companyName"
          :base-url="baseUrl"
        />
        <client-invoices-list-modal
          :client-id="item.id"
          :client-name="item.companyName"
          :base-url="baseUrl"
        />
        <client-card-top-ups-list-modal
          :client-id="item.id"
          :client-name="item.companyName"
          :base-url="baseUrl"
        />
        <report-create-modal
          :key="`modal_report_modal_client_${item.id}`"
          btn-class="ml-2"
          :params="{
            report: 'v2\\LoyaltyClientUsageReport',
            clientId: item.id,
          }"
          :show-dates="true"
          :tail-button="true"
          color="primary darken-1"
        />
        <client-edit-modal
          :client-id="item.id"
          :base-url="baseUrl"
          @on-client-update="fetchData()"
        />
      </v-container>
    </template>
  </report-data-table>
</template>

<script>
import ReportDataTable from '@components/reports/ReportDataTable.vue';
import ClientAddModal from '@components/loyalty-cards/clients/modal/ClientAddModal.vue';
import ClientEditModal from '@components/loyalty-cards/clients/modal/ClientEditModal.vue';
import ClientAlertBadge from '@components/loyalty-cards/clients/badge/ClientAlertBadge.vue';
import ClientCardsListModal from '@components/loyalty-cards/clients/modal/CardsListModal.vue';
import ClientInvoicesListModal
  from '@components/loyalty-cards/clients/modal/ClientInvoicesListModal.vue';
import ClientCardTopUpsListModal
  from '@components/loyalty-cards/clients/modal/ClientCardTopUpsListModal.vue';
import ReportCreateModal from '@components/reports/ReportCreateModal.vue';

export default {
  components: {
    ReportCreateModal,
    ClientCardTopUpsListModal,
    ClientInvoicesListModal,
    ClientCardsListModal,
    ClientAlertBadge,
    ClientAddModal,
    ClientEditModal,
    ReportDataTable,
  },
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    headers() {
      return [
        {
          text: this.$t('name'),
          value: 'companyName',
          sortable: false,
          align: 'left',
          protected: true,
        },
        {
          text: this.$t('common_city'),
          value: 'city',
          sortable: false,
        },
        {
          text: this.$t('common_formAddress'),
          value: 'address',
          sortable: false,
          protected: true,
        },
        {
          text: this.$t('table.tax_number'),
          value: 'taxNumber',
          align: 'left',
          sortable: false,
          protected: true,
        },
        {
          text: this.$t('loyaltyCards_invoicing'),
          value: 'invoiceStrategyTrans',
          sortable: false,
          align: 'left',
        },
        {
          text: this.$t('actions.actions'),
          value: 'actions',
          align: 'right',
          sortable: false,
        },
      ];
    },
  },
  methods: {
    fetchData() {
      this.$refs.dataTable.fetchData();
    },
  },
};
</script>
