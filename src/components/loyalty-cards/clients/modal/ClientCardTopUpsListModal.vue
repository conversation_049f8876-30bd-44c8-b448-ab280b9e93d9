<template>
  <div>
    <btn-top-up
      :text="$t('common_topups')"
      @click="show()"
    />
    <generic-modal
      v-model="dialog"
      :title="`${$t('common_topups')} ${clientName}`"
      :fullscreen="true"
      :show-actions="false"
    >
      <v-container fluid>
        <top-ups-list
          ref="topUpsList"
          :params="innerParams"
          :base-url="baseUrl"
        />
      </v-container>
    </generic-modal>
  </div>
</template>

<script>
import BtnTopUp from '@components/common/button/BtnTopUp.vue';
import GenericModal from '@components/common/GenericModal.vue';
import TopUpsList from '@components/loyalty-cards/topUps/TopUpsList.vue';

export default {
  components: {
    TopUpsList,
    GenericModal,
    BtnTopUp,
  },
  props: {
    clientId: {
      type: Number,
      required: true,
    },
    clientName: {
      type: [String],
      required: true,
    },
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
    };
  },
  computed: {
    innerParams() {
      return {
        clientId: this.clientId,
      };
    },
  },
  methods: {
    show() {
      this.dialog = true;
      this.$nextTick(() => {
        this.$refs.topUpsList.fetchData();
      });
    },
  },
};
</script>
