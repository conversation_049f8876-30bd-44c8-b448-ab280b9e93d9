<template>
  <div>
    <btn-document
      :text="$t('common_invoices_heading')"
      @click="show()"
    />
    <generic-modal
      v-model="dialog"
      :title="`${$t('common_invoices')} ${clientName}`"
      :fullscreen="true"
      :show-actions="false"
    >
      <invoices-tab
        :client-id="clientId"
        :base-url="baseUrl"
      />
    </generic-modal>
  </div>
</template>

<script>
import InvoicesTab from '@components/loyalty-cards/tabs/InvoicesTab.vue';
import BtnDocument from '@components/common/button/BtnDocument.vue';
import GenericModal from '@components/common/GenericModal.vue';

export default {
  components: {
    GenericModal,
    BtnDocument,
    InvoicesTab,
  },
  props: {
    clientId: {
      type: Number,
      required: true,
    },
    clientName: {
      type: String,
      default: '',
      required: false,
    },
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
    };
  },
  methods: {
    show() {
      this.dialog = true;
    },
  },
};
</script>
