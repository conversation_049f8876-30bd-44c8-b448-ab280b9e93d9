<template>
  <div>
    <btn-cards
      :text="$t('loyaltyCards_cardsList')"
      @click="show"
    />
    <generic-modal
      v-model="dialog"
      :title="`${$t('dashboard_heading')} - ${clientName}`"
      :fullscreen="true"
      :show-actions="false"
    >
      <v-container fluid>
        <cards-list
          ref="cardsList"
          :params="innerParams"
          :base-url="baseUrl"
        />
      </v-container>
    </generic-modal>
  </div>
</template>

<script>
import CardsList from '@components/loyalty-cards/cards/CardsList.vue';
import GenericModal from '@components/common/GenericModal.vue';
import BtnCards from '@components/common/button/BtnCards.vue';

export default {
  name: 'ClientCardsListModal',
  components: {
    BtnCards,
    GenericModal,
    CardsList,
  },
  props: {
    baseUrl: {
      type: String,
      required: true,
    },
    clientId: {
      type: Number,
      required: true,
    },
    clientName: {
      type: [String],
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
    };
  },
  computed: {
    innerParams() {
      return {
        clientId: this.clientId,
      };
    },
  },
  methods: {
    show() {
      this.dialog = true;
      this.$nextTick(() => {
        this.$refs.cardsList.fetchData();
      });
    },
  },
};
</script>
