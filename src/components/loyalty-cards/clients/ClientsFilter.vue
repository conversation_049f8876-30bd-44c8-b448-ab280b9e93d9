<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
    card
  >
    <v-row>
      <v-col>
        <text-search v-model="config.search" />
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import TextSearch from '@components/reports/filters/TextSearch.vue';
import debounce from 'lodash/debounce';

const SettingsKey = 'bkfpay-client-filter';
export default {
  components: {
    TextSearch,
  },
  model: {
    prop: 'param',
    event: 'update:param',
  },
  props: {
    param: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    const savedConfig = JSON.parse(localStorage.getItem(SettingsKey) || '{}');
    return {
      config: {
        search: savedConfig.search ?? null,
      },
    };
  },
  computed: {
    internalParam() {
      const paramsInit = {
        search: this.config.search ? this.config.search.trim() : null,
      };
      if (!this.config.usedCards) {
        return paramsInit;
      }
      return {
        ...paramsInit,
        ...this.config.dateselect,
      };
    },
  },
  watch: {
    internalParam: {
      handler(newVal) {
        this.debouncedUpdate(newVal);
      },
      deep: true,
      immediate: true,
    },
    config: {
      handler(newVal) {
        localStorage.setItem(SettingsKey, JSON.stringify(newVal));
      },
      deep: true,
    },
  },
  beforeCreate() {
    // jeden debounced watcher na cały internalParam
    this.debouncedUpdate = debounce((val) => {
      this.$emit('update:param', val);
    }, 1000);
  },
};
</script>
