<template>
  <div>
    <v-tooltip
      v-if="item.alertLevel"
      bottom
    >
      <template #activator="{ on, attrs }">
        <v-icon
          v-bind="attrs"
          color="orange"
          class="mr-2"
          v-on="on"
        >
          mdi-alert-circle
        </v-icon>
      </template>
      <span>{{ $t('client-modal.clientAlerts.editRequired') }}</span>
    </v-tooltip>
  </div>
</template>

<script>

export default {
  props: {
    item: {
      type: Object,
      required: true,
    },
  },
};
</script>
