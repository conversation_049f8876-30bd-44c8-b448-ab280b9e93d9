export const TopUpProgressType = [
  {
    value: 'WAITING',
    icon: 'mdi-timer-sand-empty',
    text: 'common_toSent',
  },
  {
    value: 'NOT_FULLY_REFILLED',
    icon: 'mdi-autorenew',
    text: 'common_notFullySent',
  },
  {
    value: 'REFILLED',
    icon: 'mdi-check-underline',
    text: 'common_sendToCard',
  },
  {
    value: 'CANCELED',
    icon: 'mdi-cancel',
    text: 'common_canceled',
  },
];

export const TransactionType = [
  {
    value: 'SUBTRACTION',
    icon: 'mdi-trending-down',
    text: 'transactions.payment',
  },
  {
    value: 'ADDITION',
    icon: 'mdi-trending-up',
    text: 'transactions.topup',
  },
  {
    value: 'ALIGNMENT',
    icon: 'mdi-wrench',
    text: 'transactions.balance_adjustment',
  },
  {
    value: 'PROMOTION',
    icon: 'mdi-sale',
    text: 'transactions.promotions',
  },
  {
    value: 'BLOCKADE',
    icon: 'mdi-lock',
    text: 'loyaltyCards_lockFund',
  },
];
export const TopUpType = [
  {
    value: 'ADDITION',
    icon: 'mdi-trending-up',
    text: 'transactions.topup',
  },
  {
    value: 'PROMOTION',
    icon: 'mdi-sale',
    text: 'transactions.promotions',
  },
];
export const CardTypeType = [
  {
    icon: 'mdi-web',
    text: 'loyaltyCards_virtual',
    value: 'VIRTUAL',
  },
  {
    value: 'MIFARE',
    icon: 'mdi-credit-card-outline',
    text: 'loyaltyCards_regular',
  },
];

export const PaymentStatusType = [
  {
    value: 'canceled',
    icon: 'mdi-close-circle-outline',
    text: 'common_canceled',
  },
  {
    value: 'confirmed',
    icon: 'mdi-check-circle-outline',
    text: 'common_confirmed',
  },
  {
    value: 'error',
    icon: 'mdi-close-octagon-outline',
    text: 'common_error',
  },
  {
    value: 'initiated',
    icon: 'mdi-progress-star',
    text: 'common_initiated',
  },
  {
    value: 'pending',
    icon: 'mdi-progress-clock',
    text: 'common_pending',
  },
  {
    value: 'refunded',
    icon: 'mdi-credit-card-refund-outline',
    text: 'common_refund',
  },
  {
    value: 'rejected',
    icon: 'mdi-alert-outline',
    text: 'common_rejected',
  },
  {
    value: 'waiting',
    icon: 'mdi-progress-clock',
    text: 'common_waiting',
  },
];

export const PaymentTypeType = [
  {
    value: 'disabled',
    text: 'disabled',
  },
  {
    value: 'payU',
    text: 'payU',
  },
  {
    value: 'p24',
    text: 'p24',
  },
  {
    value: 'klix',
    text: 'klix',
  },
  {
    value: 'mk',
    text: 'mk',
  },
  {
    value: 'corvus',
    text: 'corvus',
  },
];

export const CyclicTopUpTypeType = [
  {
    value: 'ADD',
    text: 'loyaltyCards_cyclicAdd',
  },
  {
    value: 'ALIGN',
    text: 'loyaltyCards_cyclicAlign',
  },
];
