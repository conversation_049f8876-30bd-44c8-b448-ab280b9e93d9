<template>
  <div>
    <v-row>
      <v-col>
        <text-search
          v-model="config.search"
        />
      </v-col>
      <v-col>
        <single-select
          v-model="config.invoiceExists"
          :items="topUpInvoiceExists"
          prepend-icon="mdi-file-outline"
          :label="$t('common_invoice')"
          allow-null
        />
      </v-col>
      <v-col>
        <date-select
          v-model="config.dateselect"
          :show-custom="false"
        />
      </v-col>
    </v-row>
    <v-row class="mt-0">
      <v-col
        class="py-0"
      >
        <multi-select
          v-model="config.status"
          :items="allowedStatuses"
          prepend-icon="mdi-state-machine"
          :label="$t('common_state')"
          allow-null
        />
      </v-col>
      <v-col
        class="py-0"
      >
        <multi-select
          v-model="config.source"
          :items="sourceOptions"
          :label="$t('loyaltyCards_source')"
          prepend-icon="mdi-point-of-sale"
          allow-null
        />
      </v-col>
      <v-col
        class="py-0"
      >
        <multi-select
          v-model="config.type"
          :items="allowedTypes"
          :label="$t('fiscal_transactions.table.type')"
          prepend-icon="mdi-cash-multiple"
          allow-null
        />
      </v-col>
    </v-row>
  </div>
</template>
<script>
import TextSearch from '@components/common/filters/TextSearch.vue';
import MultiSelect from '@components/reports/filters/MultiSelect.vue';
import SingleSelect from '@components/reports/filters/SingleSelect.vue';
import DateSelect from '@components/reports/filters/DateSelect.vue';
import debounce from 'lodash/debounce';

import SourceType from '@components/libs/standard-types/types';

import { TopUpProgressType, TopUpType } from '@components/loyalty-cards/types';

const SettingsKey = 'bkfpay-topup-filter';
export default {

  components: {
    DateSelect,
    TextSearch,
    MultiSelect,
    SingleSelect,
  },
  model: {
    prop: 'param',
    event: 'update:param',
  },
  props: {
    param: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    const savedConfig = JSON.parse(localStorage.getItem(SettingsKey) || '{}');
    return {
      config: {
        search: savedConfig.search ?? null,
        invoiceExists: savedConfig.invoiceExists ?? null,
        source: savedConfig.source ?? null,
        type: savedConfig.type ?? null,
        status: savedConfig.status ?? null,
        dateselect: savedConfig.dateselect ?? null,
      },
    };
  },
  computed: {
    // scalony obiekt filtrów do przekazania
    internalParam() {
      return {
        search: this.config.search,
        invoiceExists: this.config.invoiceExists,
        source: this.config.source?.length ? this.config.source.join(',') : [],
        status: this.config.status?.length ? this.config.status.join(',') : [],
        type: this.config.type?.length ? this.config.type.join(',') : [],
        ...this.config.dateselect,
      };
    },
    allowedTypes() {
      return TopUpType
        .map((item) => ({
          text: this.$t(item.text),
          value: item.value,
          icon: item.icon,
        }));
    },
    allowedStatuses() {
      return TopUpProgressType.map((item) => ({
        text: this.$t(item.text),
        value: item.value,
        icon: item.icon,
      }));
    },
    sourceOptions() {
      const keys = ['INTERNET', 'MONEY_CHANGER'];
      return SourceType
        .filter((option) => keys.includes(option.value))
        .map((item) => ({
          text: this.$t(item.text),
          value: item.value,
          icon: item.icon,
        }));
    },
    topUpInvoiceExists() {
      return [
        {
          text: this.$t('loyaltyCards_withInvoice'),
          value: 1,
          icon: 'mdi-file-outline',
        },
        {
          text: this.$t('loyaltyCards_withoutInvoice'),
          value: 0,
          icon: 'mdi-file-remove-outline',
        },
      ];
    },
  },
  watch: {
    internalParam: {
      handler(newVal) {
        this.debouncedUpdate(newVal);
      },
      deep: true,
      immediate: true,
    },
    config: {
      handler(newVal) {
        localStorage.setItem(SettingsKey, JSON.stringify(newVal));
      },
      deep: true,
    },
  },
  beforeCreate() {
    // jeden debounced watcher na cały internalParam
    this.debouncedUpdate = debounce((val) => {
      this.$emit('update:param', val);
    }, 1000);
  },

  methods: {
    onDateRangeChange(dateRange) {
      this.interval = dateRange.value;
    },
  },
};
</script>
