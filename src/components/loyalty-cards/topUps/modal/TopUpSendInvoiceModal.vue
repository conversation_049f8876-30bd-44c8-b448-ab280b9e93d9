<template>
  <div>
    <v-tooltip bottom>
      <template #activator="{ on, attrs }">
        <v-btn
          v-bind="attrs"
          x-small
          tile
          rounded
          fab
          class="ml-2"
          color="secondary"
          elevation="1"
          v-on="on"
          @click="onShow"
        >
          <v-icon>
            mdi-email
          </v-icon>
        </v-btn>
      </template>
      <span>{{ $t('actions.send_invoice') }}</span>
    </v-tooltip>
    <form-modal
      ref="formModal"
      v-model="show"
      :confirm-button-text="$t('actions.send')"
      :title="$t('actions.send_invoice')"
      :url="url"
      :loading="loading"
      error-prefix="modal.loyal_card_top_up_invoice.error."
      @beforeSubmit="onBeforeSubmit"
      @submit="onSubmit"
      @submitFail="onSubmitFailure"
    >
      <template #before>
        <loading-overlay v-if="loadingInvoice">
          {{ $t('loyaltyCards_invoiceSendLoading') }}
        </loading-overlay>
      </template>
      <v-slide-y-transition>
        <v-container
          v-show="!loading"
          class="px-0"
        >
          <v-row align="center">
            <v-col class="pb-0">
              <span class="text-h6">{{ $t('loyaltyCards_emailSend') }}:</span>
            </v-col>
          </v-row>
          <v-row align="center">
            <v-col>
              <v-text-field
                v-model="email"
                :label="$t('common_email')"
                hide-details
                prepend-icon="mdi-email-outline"
                name="email"
              />
            </v-col>
          </v-row>
        </v-container>
      </v-slide-y-transition>
    </form-modal>
  </div>
</template>

<script>
import LoadingOverlay from '@components/common/LoadingOverlay.vue';
import FormModal from '@components/common/FormModal.vue';

export default {
  name: 'TopUpSendInvoiceModal',
  components: { FormModal, LoadingOverlay },
  props: {
    clientId: {
      type: [Number, String],
      required: true,
    },
    invoiceId: {
      type: [Number, String],
      required: true,
    },
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      show: false,
      loading: false,
      loadingInvoice: false,
      email: '',
    };
  },
  computed: {
    url() {
      return `${this.baseUrl}/invoice/${this.invoiceId}/send?email=${encodeURIComponent(this.email)}`;
    },
  },
  methods: {
    onShow() {
      this.show = true;
      this.getClientData();
    },
    onBeforeSubmit() {
      this.loadingInvoice = true;
      this.show = false;
    },
    onSubmit() {
      this.loadingInvoice = false;
      this.$emit('generateSuccess');
    },
    onSubmitFailure() {
      this.loadingInvoice = false;
      this.show = true;
    },
    getClientData() {
      this.loading = true;
      this.axios.get(`${this.baseUrl}/client/${this.clientId}`)
        .then(({ data }) => {
          this.email = data.email;
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};
</script>
