<template>
  <div>
    <v-tooltip bottom>
      <template #activator="{ on, attrs }">
        <v-btn
          v-bind="attrs"
          x-small
          tile
          rounded
          fab
          class="ml-2"
          color="secondary"
          elevation="1"
          v-on="on"
          @click="onShow"
        >
          <v-icon>
            mdi-typewriter
          </v-icon>
        </v-btn>
      </template>
      <span>{{ $t('actions.generate_invoice') }}</span>
    </v-tooltip>
    <form-modal
      ref="formModal"
      v-model="show"
      :confirm-button-text="$t('actions.send')"
      :title="$t('actions.generate_invoice')"
      :url="url"
      :loading="loading"
      error-prefix="modal.loyal_card_top_up_invoice.error."
      @beforeSubmit="onBeforeSubmit"
      @submit="onSubmit"
      @submitFail="onSubmitFailure"
    >
      <template #before>
        <loading-overlay v-if="loadingInvoice">
          {{ $t('loyaltyCards_invoiceLoading') }}
        </loading-overlay>
      </template>

      <template #actions>
        <v-spacer />
        <v-btn
          :disabled="clientInvoiceBlocked"
          color="secondary"
          class="ml-2"
          @click="submit"
        >
          {{ $t('actions.generate') }}
          <v-icon right>
            mdi-file-document-outline
          </v-icon>
        </v-btn>
        <v-btn
          :disabled="clientInvoiceBlocked"
          color="primary"
          class="ml-2"
          @click="submitAndDownload"
        >
          {{ $t('actions.generate_and_download') }}
          <v-icon right>
            mdi-file-download-outline
          </v-icon>
        </v-btn>
      </template>

      <v-slide-y-transition>
        <v-container
          v-show="!loading"
          class="px-0"
        >
          <v-row align="center">
            <v-col class="pb-0">
              <span class="text-h6">{{ $t('loyaltyCards_confirmCustomerData') }}</span>
            </v-col>
          </v-row>
          <v-row v-if="clientInvoiceBlocked">
            <v-col
              cols="12"
            >
              <v-alert
                class="white--text my-0"
                color="warning"
                border="left"
              >
                {{ $t('loyaltyCards_clientLockWarning') }}
              </v-alert>
            </v-col>
          </v-row>
          <v-row>
            <v-col
              cols="6"
            >
              <v-select
                v-model="form.invoice.paymentTerm"
                item-value="id"
                item-text="text"
                :label="$t('common_paymentPeriod')"
                :items="paymentTermOptions"
                prepend-icon="mdi-calendar"
                name="paymentTerm"
                hide-details
                required
              />
            </v-col>
            <v-col
              cols="6"
            >
              <v-select
                v-model="form.invoice.paymentMethod"
                item-value="id"
                item-text="value"
                :label="$t('common_paymentMethod')"
                :items="paymentMethods"
                :autocomplete="true"
                prepend-icon="mdi-hand-coin-outline"
                name="paymentMethod"
                hide-details
                required
              />
            </v-col>
          </v-row>
          <v-row align="center">
            <v-col>
              <v-textarea
                v-model="form.invoice.description"
                :label="$t('common_description')"
                name="description"
                hide-details
                rows="1"
                auto-grow
                prepend-icon="mdi-email-outline"
              />
            </v-col>
          </v-row>
          <v-row align="center">
            <v-col>
              <v-text-field
                v-model="form.invoice.companyName"
                :label="$t('common_fullCustomerName')"
                disabled
                hide-details
                prepend-icon="mdi-rename-box"
              />
            </v-col>
          </v-row>
          <v-row>
            <v-col>
              <v-text-field
                v-model="form.invoice.address"
                :label="$t('common_formAddress')"
                disabled
                hide-details
                prepend-icon="mdi-map-marker-outline"
              />
            </v-col>
          </v-row>
          <v-row>
            <v-col>
              <v-text-field
                v-model="form.invoice.city"
                :label="$t('common_city')"
                disabled
                hide-details
                prepend-icon="mdi-city-variant-outline"
              />
            </v-col>
            <v-col cols="4">
              <v-text-field
                v-model="form.invoice.postCode"
                :label="$t('common_postCode')"
                disabled
                hide-details
                prepend-icon="mdi-home-city-outline"
              />
            </v-col>
          </v-row>
          <v-row>
            <v-col>
              <v-text-field
                v-model="form.invoice.taxNumber"
                :label="$t('table.tax_number')"
                disabled
                hide-details
                prepend-icon="mdi-numeric"
              />
            </v-col>
            <v-col cols="3">
              <v-text-field
                v-model="form.invoice.country"
                :label="$t('common_country')"
                disabled
                hide-details
                prepend-icon="mdi-earth"
              />
            </v-col>
            <v-col cols="3">
              <v-text-field
                v-model="form.invoice.invoice_currency"
                :label="$t('loyaltyCards_currency')"
                disabled
                hide-details
                prepend-icon="mdi-currency-usd"
              />
            </v-col>
          </v-row>
          <v-row align="center">
            <v-col class="pb-0">
              <span class="text-h6">{{ $t('loyaltyCards_emailInfo') }}</span>
            </v-col>
          </v-row>
          <v-row align="center">
            <v-col>
              <v-text-field
                v-model="form.invoice.email"
                :label="$t('common_email')"
                disabled
                hide-details
                prepend-icon="mdi-email-outline"
              />
            </v-col>
          </v-row>
        </v-container>
      </v-slide-y-transition>
    </form-modal>
  </div>
</template>

<script>
import FormModal from '@components/common/FormModal.vue';
import LoadingOverlay from '@components/common/LoadingOverlay.vue';
import ExportMixin from '@components/common/mixins/ExportMixin.vue';
import { mapGetters } from 'vuex';

export default {
  name: 'TopUpGenerateInvoiceModal',
  components: { LoadingOverlay, FormModal },
  mixins: [ExportMixin],
  props: {
    id: {
      type: [Number, String],
      required: true,
    },
    clientId: {
      type: [Number, String],
      required: true,
    },
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      paymentTermOptions: [
        {
          id: 'P0D',
          text: this.$t('common_0'),
        },
        {
          id: 'P1D',
          text: this.$t('common_1'),
        },
        {
          id: 'P3D',
          text: this.$t('common_3'),
        },
        {
          id: 'P5D',
          text: this.$t('common_5'),
        },
        {
          id: 'P7D',
          text: this.$t('common_7'),
        },
        {
          id: 'P10D',
          text: this.$t('common_10'),
        },
        {
          id: 'P14D',
          text: this.$t('common_14'),
        },
      ],
      show: false,
      loading: false,
      loadingInvoice: false,
      isModalMounted: false,
      download: false,
      paymentMethods: [
        { id: 'cash', value: this.$t('common_cash') },
        { id: 'transfer', value: this.$t('common_transfer') },
      ],
      form: {
        invoice: {
          address: null,
          city: null,
          country: null,
          invoice_currency: null,
          companyName: null,
          postCode: null,
          invoiceStrategy: null,
          taxNumber: null,
          paymentTerm: null,
          paymentMethod: null,
          description: null,
        },
      },
    };
  },
  computed: {
    url() {
      return `/api/gateway/bkfpay-owner/top_up/${this.id}/invoice`;
    },
    clientInvoiceBlocked() {
      return this.form.invoice.invoiceStrategy === 'block';
    },
    ...mapGetters({
      currencyCode: 'auth/userCurrencyCode',
      currencySymbol: 'auth/userCurrencySymbol',
    }),
  },
  methods: {
    onShow() {
      this.show = true;
      this.getClientData();
    },
    onBeforeSubmit() {
      this.loadingInvoice = true;
      this.show = false;
    },
    submit() {
      this.download = false;
      this.$refs.formModal.submit();
    },
    submitAndDownload() {
      this.download = true;
      this.$refs.formModal.submit();
    },
    onSubmit(data) {
      if (this.download) {
        this.onExport({
          url: `/api/gateway/bkfpay-owner/invoice/${data.data.invoice}/download`,
          filename: `${data.data.card.number}.pdf`,
        });
      }
      this.loadingInvoice = false;
      this.$emit('generateSuccess');
    },
    onSubmitFailure() {
      this.loadingInvoice = false;
      this.show = true;
    },
    getClientData() {
      this.loading = true;
      this.axios.get(`${this.baseUrl}/bkfpay/client/${this.clientId}`)
        .then(({ data }) => {
          this.form.invoice = {
            ...this.form.invoice,
            ...data,
            country: data.country,
            invoice_currency: `${this.currencySymbol} (${this.currencyCode})`,
          };
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};
</script>
