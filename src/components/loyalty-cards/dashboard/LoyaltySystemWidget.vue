<template>
  <widget
    v-show="show"
    :loading="loader"
    :title="$t('dashboard_heading')"
    title-class="title title-widget pr-0 pl-2"
    class="mb-3 mr-0"
  >
    <template #titleActions>
      <widget-period-select
        v-model="period"
        :date-items="dateItems"
      />
    </template>

    <v-data-table
      :headers="dataTable.headers"
      :items="dataTable.items"
      item-key="number"
      :no-data-text="$t('common_noData')"
      class="text-uppercase"
      :hide-default-footer="true"
      mobile-breakpoint="0"
    >
      <template #item="{ item}">
        <template v-if="!loader">
          <tr
            v-for="(row, index) in item"
            :key="index"
          >
            <td class="text-start">
              {{ $t(`loyalsystem-widget.values-name.${index}`) }}
            </td>
            <td class="text-end">
              {{ row }}
            </td>
          </tr>
        </template>
      </template>
    </v-data-table>
  </widget>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import SettingsMixin from '@components/common/mixins/SettingsMixin.vue';
import WidgetPeriodSelect from '@components/common/filters/WidgetPeriodSelect.vue';
import Widget from '@components/common/Widget.vue';

export default {
  components: {
    Widget,
    WidgetPeriodSelect,
  },
  mixins: [
    SettingsMixin,
  ],
  data() {
    return {
      loader: true,
      dataTable: {
        headers: [
          {
            text: '',
            value: 'startDate',
            class: 'text-start',
            sortable: false,
          },
          {
            text: this.$t('dashboard_value'),
            value: 'endDate',
            class: 'text-end',
            sortable: false,
          },
        ],
        items: [],
      },
      dateItems: [
        {
          text: this.$t('common_now'),
          value: 'now',
        },
        {
          text: this.$t('common_yesterday'),
          value: 'yesterday',
        },
        {
          text: this.$t('common_p7d'),
          value: 'P7D',
        },
        {
          text: this.$t('common_p14d'),
          value: 'P14D',
        },
        {
          text: this.$t('common_sinceMonthStart'),
          value: 'since_month_start',
        },
        {
          text: this.$t('dashboard_previousMonth'),
          value: 'previous_month',
        },
      ],
      dateFrom: '',
      dateTo: '',
      loading: false,
    };
  },
  computed: {
    show() {
      return this.canAccess('loyalty', 'cards');
    },
    period: {
      get() {
        const value = this.getSetts('dashboard:loyalsystem:period', 'P7D');
        return this.dateItems.find((item) => item.value === value);
      },
      set(newValue) {
        this.setSetts('dashboard:loyalsystem:period', newValue.value);
        this.getData(newValue);
        return newValue;
      },
    },
    ...mapGetters({
      canAccess: 'auth/canAccess',
      getSettings: 'settings/findByNamespace',
    }),
  },
  mounted() {
    this.loader = true;
    if (this.canAccess('loyalty', 'cards')) {
      this.getData(this.period);
    }
  },
  methods: {
    ...mapActions({
      modifySettings: 'settings/modifySettings',
    }),
    onResize() {
      this.windowWidth = window.innerWidth;
    },
    getData(period) {
      this.loader = true;
      this.axios.get(
        '/api/loyalty/v3/cards/stats',
        {
          params: {
            period: period.value,
          },
        },
      )
        .then((response) => {
          this.dataTable.items[0] = response.data.data;
          this.loader = false;
        });
    },
  },
};
</script>
