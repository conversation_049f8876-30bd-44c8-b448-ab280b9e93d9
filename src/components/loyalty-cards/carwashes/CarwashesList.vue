<template>
  <v-container
    grid-list-md
    text-sm-center
    fluid
  >
    <v-row>
      <v-col
        class="mb-5"
        cols="12"
        sm="12"
        md="12"
      >
        <v-row>
          <v-col
            cols="12"
            sm="6"
          >
            <text-search
              v-if="false"
              v-model="filtering.search.text"
            />
          </v-col>
          <v-col
            cols="12"
            sm="6"
          >
            <v-layout
              justify-end
              align-end
            >
              <btn-refresh
                class="mb-3 mr-2"
                size="small"
                :disabled="loader"
                @click="getData"
              />
            </v-layout>
          </v-col>
        </v-row>
      </v-col>
      <v-col
        cols="12"
        sm="6"
        md="6"
      >
        <v-card
          class="float-md-right float-sm-none card-width"
          elevation="2"
        >
          <v-list class="transparent">
            <v-list-item>
              <v-list-item-title> <h3> {{ $t('loyalApp_online') }} </h3> </v-list-item-title>
              <v-list-item-icon>
                <v-icon
                  color="green darken-2"
                >
                  mdi-check-circle-outline
                </v-icon>
              </v-list-item-icon>
              <v-list-item-subtitle> {{ stats.online }} </v-list-item-subtitle>
            </v-list-item>
          </v-list>
        </v-card>
      </v-col>
      <v-col
        cols="12"
        sm="6"
        md="6"
      >
        <v-card
          class="float-md-left float-sm-none card-width"
          elevation="2"
        >
          <v-list class="transparent">
            <v-list-item>
              <v-list-item-title> <h3>{{ $t('loyalApp_offline') }} </h3> </v-list-item-title>
              <v-list-item-icon>
                <v-icon
                  color="error"
                >
                  mdi-close-circle-outline
                </v-icon>
              </v-list-item-icon>
              <v-list-item-subtitle> {{ stats.offline }} </v-list-item-subtitle>
            </v-list-item>
          </v-list>
        </v-card>
      </v-col>
      <v-col
        cols="12"
        class="px-0"
      >
        <div
          v-if="loader"
          class="loader-background"
        />
        <v-data-table
          v-resize="onResize"
          mobile-breakpoint="0"
          :headers="dataTable.headers"
          :items="dataTable.items"
          item-key="id"
          :loading="loader"
          :options.sync="pagination"
          :server-items-length="dataTable.totalItems"
          :footer-props="filtering.footerProps"
          :search="filtering.search.text"
          :custom-filter="filterTable"
          single-expand
          @current-items="refreshPieChart"
        >
          <template #progress>
            <div class="text-center">
              <v-progress-circular
                class="loader"
                indeterminate
                color="primary"
              />
            </div>
          </template>

          <template #item="{ item, expand, isExpanded }">
            <template v-if="!loader">
              <tr @click="expand(!isExpanded); getDetails(item.id);">
                <td class="text-sm-start">
                  {{ item.longName }}
                </td>
                <td class="text-sm-start border-right">
                  {{ item.address }}
                </td>
                <td class="text-sm-center">
                  <v-icon
                    v-if="item.showOnMap"
                    color="green darken-2"
                  >
                    mdi-check-circle-outline
                  </v-icon>
                  <v-icon
                    v-else
                    color="error"
                  >
                    mdi-close-circle-outline
                  </v-icon>
                </td>
                <td class="text-sm-center">
                  <v-tooltip left>
                    <template #activator="{ on }">
                      <v-icon
                        v-if="item.cwApi && item.cwApi.mobile_ok"
                        color="green darken-2"
                        v-on="on"
                      >
                        mdi-check-circle-outline
                      </v-icon>
                      <v-icon
                        v-else
                        color="error"
                        v-on="on"
                      >
                        mdi-close-circle-outline
                      </v-icon>
                    </template>
                    <span>
                      {{ $t('other_lastOnline') }}: {{
                        item.cwApi && item.cwApi.last_mobile_online
                          ? $options.filters.formatDateDayTime(item.cwApi.last_mobile_online) : '-'
                      }}
                    </span>
                  </v-tooltip>
                </td>
                <td>
                  <v-icon
                    v-if="!isExpanded"
                  >
                    mdi-chevron-down
                  </v-icon>
                  <v-icon
                    v-else
                  >
                    mdi-chevron-up
                  </v-icon>
                </td>
              </tr>
            </template>
          </template>

          <template #expanded-item="{ headers, item }">
            <tr>
              <td
                v-if="detailsLoader"
                :colspan="headers.length"
              >
                <div class="progress-overlay">
                  <span>
                    <v-progress-circular
                      class="loader"
                      indeterminate
                      color="primary"
                    />
                  </span>
                </div>
              </td>
              <template v-else>
                <td
                  :colspan="7"
                  class="pt-8"
                  style="vertical-align: top !important"
                >
                  <v-row>
                    <v-col
                      cols="5"
                    >
                      <gmap-map
                        v-if="true"
                        :key="item.id"
                        :center="{lat:item.lat, lng:item.lon}"
                        :zoom="14"
                        :options="{
                          zoomControl: true,
                          mapTypeControl: false,
                          scaleControl: false,
                          streetViewControl: false,
                          rotateControl: false,
                          fullscreenControl: false,
                          disableDefaultUi: false
                        }"
                        style="min-height:300px; max-height:500px; height: 90%"
                      >
                        <gmap-marker
                          :key="item.id"
                          :position="{lat:item.lat, lng:item.lon}"
                          :clickable="true"
                          :draggable="false"
                        />
                      </gmap-map>
                    </v-col>
                    <v-col
                      cols="7"
                      class="text-sm-start"
                    >
                      <details-table
                        :key="item.id"
                        :data="details"
                        :base-url="baseUrl"
                        @refresh="getDetails(item.id);getData()"
                      />
                    </v-col>
                  </v-row>
                </td>
              </template>
            </tr>
          </template>
        </v-data-table>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import ExportMixin from '@components/common/mixins/ExportMixin.vue';
import DetailsTable from '@components/loyalty-cards/carwashes/DetailsTable.vue';
import BtnRefresh from '@/components/common/button/BtnRefresh.vue';
import TextSearch from '@components/common/filters/TextSearch.vue';

export default {
  components: {
    TextSearch,
    BtnRefresh,
    DetailsTable,
  },
  mixins: [
    ExportMixin,
  ],
  props: {
    autoLoad: {
      type: Boolean,
      default: true,
    },
    baseUrl: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      autoLoadData: this.autoLoad,
      loader: false,
      detailsLoader: false,
      filtering: {
        footerProps: {
          'items-per-page-options': [50, 100, 500, 1000],
          'items-per-page-text': this.$t('common_rowsPerPage'),
        },
        search: {
          text: null,
        },
      },
      stats: {
        online: 0,
        offline: 0,
      },
      pagination: {
        page: 1,
        itemsPerPage: 50,
        sortBy: ['ctime'],
        sortDesc: [true],
      },
      details: {},
      dataTable: {
        totalItems: 0,
        headers: [
          {
            text: this.$t('carwash'),
            value: 'long_name',
            class: 'text-sm-start',
            sortable: false,
          },
          {
            text: this.$t('common_address'),
            value: 'address',
            class: 'text-sm-start',
            sortable: false,
          },
          {
            text: this.$t('loyalApp_showOnMap'),
            value: 'show_on_map',
            class: 'text-sm-center',
            sortable: false,
          },
          {
            text: this.$t('loyalApp_mobileOk'),
            value: 'cwApi.mobile_ok',
            class: 'text-sm-center',
            sortable: false,
          },
          {
            text: '',
            value: '',
            class: 'text-sm-end',
            sortable: false,
          },
        ],
        items: [],
      },
    };
  },
  watch: {
    app() {
      this.getData();
      this.pagination.page = 1;
    },
  },
  mounted() {
    if (this.app !== null) {
      this.getData();
    }
  },
  methods: {
    filterTable(value, search) {
      return value != null
        && search != null
        && typeof value === 'string'
        && value.toString().toLowerCase().includes(search.toLowerCase());
    },
    onResize() {
      this.windowWidth = window.innerWidth;
    },
    getDetails(id) {
      this.detailsLoader = true;
      this.axios.get(
        `${this.baseUrl}/bkfpay/carwash/${id}`,
        {
          params: {
            // app: this.app,
          },
        },
      )
        .then((response) => {
          this.details = response.data;
          this.detailsLoader = false;
        });
    },
    getData() {
      this.loader = true;
      this.axios.get(
        `${this.baseUrl}/bkfpay/carwashes`,
        {},
      )
        .then((response) => {
          this.dataTable.items = response.data;
          this.dataTable.totalItems = response.data.length;
          this.loader = false;
        });
    },
    refreshPieChart() {
      this.stats = { online: 0, offline: 0 };

      Object.keys(this.dataTable.items).forEach((property) => {
        const item = this.dataTable.items[property];
        if (
          this.filtering.search.text
          && !this.filterTable(item.long_name, this.filtering.search.text)
          && !this.filterTable(item.address, this.filtering.search.text)
          && (item.owner === null || !this.filterTable(item.owner.name, this.filtering.search.text))
        ) return;

        if (item.cwApi && item.cwApi.mobile_ok) {
          this.stats.online += 1;
        } else {
          this.stats.offline += 1;
        }
      });
    },
  },
};
</script>

<style lang="css" scoped>
.loader {
  top: 35%;
  z-index: 5;
}

.loader-background {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, .3);
  z-index: 4;
}

.expand-table {
  width: 100%
}

@media only screen and (min-width: 800px) {
  .card-width {
    min-width: 350px;
  }
}
</style>
