/* eslint-disable no-param-reassign */
import formatDate from 'date-fns/format';

function isoDateTimeStringToDateTimeString(date) {
  return date.substring(0, 19).replace('T', ' ');
}

// TODO: refactor this!!!!!!
function isIsoDateTimeString(date) {
  const regex = /(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})[+-](\d{2}):(\d{2})/;
  return regex.test(date);
}

function formatDateDayNull(date) {
  if (!date) {
    return null;
  }

  if (isIsoDateTimeString(date)) {
    date = isoDateTimeStringToDateTimeString(date);
  }
  return formatDate(date, 'YYYY-MM-DD');
}

function formatDateDay(date) {
  if (!date) {
    return '-';
  }

  if (isIsoDateTimeString(date)) {
    date = isoDateTimeStringToDateTimeString(date);
  }
  return formatDate(date, 'YYYY-MM-DD');
}

/**
 * @deprecated
 */
function formatDateDayTime(date) {
  if (!date) {
    return '-';
  }

  if (isIsoDateTimeString(date)) {
    date = isoDateTimeStringToDateTimeString(date);
  }
  return formatDate(date, 'YYYY-MM-DD HH:mm');
}

function formatDateDayTimeWithSeconds(date) {
  if (!date) {
    return '-';
  }

  if (isIsoDateTimeString(date)) {
    date = isoDateTimeStringToDateTimeString(date);
  }
  return formatDate(date, 'YYYY-MM-DD HH:mm:ss');
}

function formatDateMonth(date) {
  if (!date) {
    return '-';
  }

  if (isIsoDateTimeString(date)) {
    date = isoDateTimeStringToDateTimeString(date);
  }
  return formatDate(date, 'YYYY-MM');
}

export default (Vue) => [
  Vue.filter('formatDateDayNull', formatDateDayNull),
  Vue.filter('formatDateDay', formatDateDay),
  Vue.filter('formatDateDayTime', formatDateDayTime),
  Vue.filter('formatDateDayTimeWithSeconds', formatDateDayTimeWithSeconds),
  Vue.filter('formatDateMonth', formatDateMonth),
];
