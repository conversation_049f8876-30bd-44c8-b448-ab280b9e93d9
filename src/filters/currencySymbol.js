import * as Sentry from '@sentry/vue';

function currencySymbol(value, symbol) {
  if (value === undefined) {
    return '';
  }

  if (symbol === undefined || symbol === null || symbol === '') {
    try {
      throw new Error('Currency symbol undefined in currencySymbol filter');
    } catch (err) {
      Sentry.captureException(err);
    }

    return `${Number(value).toFixed(2)}`;
  }

  if (value === null) {
    return '-';
  }

  return `${Number(value).toFixed(2)} ${symbol}`;
}

export default (Vue) => [
  Vue.filter('currencySymbol', currencySymbol),
];
