/* eslint-disable no-param-reassign */
/* eslint-disable no-return-assign */
/* eslint-disable no-shadow */
import Vue from 'vue';

const state = () => ({
  messages: [],
  loaded: false,
});

const getters = {
  messages: (state) => state.messages,
  count: (state, getters) => getters.unread.length ?? null,
  menu_messages: (state, getters) => getters.unread.slice(0, 5),
  unread: (state, getters) => getters.messages.filter((message) => !message.read),
  read: (state) => state.messages.filter(
    (message) => (message.read === true),
  ),
};

const mutations = {
  messages: (state, messages) => state.messages = messages,
  loaded: (state) => state.loaded = true,
  mark_read: (state, id) => {
    const index = state.messages.findIndex((m) => m.id === id);
    if (index !== -1) {
      state.messages[index].read = true;
    }
  },
};

const actions = {
  fetch: ({ commit }) => Vue.axios.get('/cm_new/messages')
    .then((response) => {
      if (response) {
        commit('messages', response.data);
        commit('loaded');
      }
    }),
  mark_read: ({ commit }, messageId) => {
    commit('mark_read', messageId);
    Vue.axios.patch(`/cm_new/messages/${messageId}/read`);
  },
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations,
};
