/* eslint-disable no-param-reassign */
/* eslint-disable no-return-assign */
/* eslint-disable no-shadow */
import Vue from 'vue';

const state = () => ({
  settings: [],
});

const getters = {
  findByNamespace: (state) => (checkNamespace) => state.settings.find(
    (check) => check.namespace === checkNamespace,
  ),
};

const mutations = {
  settings_success(state, {
    settings,
  }) {
    state.settings = settings;
  },
};

const actions = {
  async initSettings({ commit }) {
    return new Promise((resolve, reject) => {
      Vue.axios.get('/api/settings/')
        .then((resp) => {
          if (resp !== undefined) {
            commit('settings_success', {
              settings: resp.data,
            });
          }

          resolve(resp);
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  async modifySettings({ commit }, settings) {
    return new Promise((resolve, reject) => {
      Vue.axios.patch(
        '/api/settings/',
        settings,
      )
        .then(() => {
          Vue.axios.get('/api/settings/')
            .then((resp) => {
              if (resp !== undefined) {
                commit('settings_success', {
                  settings: resp.data,
                });
              }
              resolve(resp);
            })
            .catch((err) => {
              reject(err);
            });
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations,
};
