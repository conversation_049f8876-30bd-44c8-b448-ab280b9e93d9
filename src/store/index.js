import Vue from 'vue';
import Vuex from 'vuex';
import auth from '@store/auth';
import messages from '@store/messages';
import carwashes from '@store/carwashes';
import currency from '@store/currency';
import settings from '@store/settings';
import snackbar from '@store/snackbar';

Vue.use(Vuex);

export default new Vuex.Store({
  modules: {
    auth,
    carwashes,
    messages,
    currency,
    settings,
    snackbar,
  },
});
