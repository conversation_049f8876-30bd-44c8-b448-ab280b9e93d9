/* eslint-disable no-param-reassign */
/* eslint-disable no-return-assign */
/* eslint-disable no-shadow */

const state = () => ({
  currency: {},
});

const getters = {
  getCurrency: (state, getters, rootState, rootGetters) => rootGetters['auth/userCurrency'],
  getSymbol: (state, getters) => (getters.getCurrency !== undefined ? getters.getCurrency.symbol : ''),
  getCode: (state, getters) => (getters.getCurrency !== undefined ? getters.getCurrency.code : ''),
};

const mutations = {
};

const actions = {};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations,
};
