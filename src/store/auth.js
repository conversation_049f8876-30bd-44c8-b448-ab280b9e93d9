/* eslint-disable no-param-reassign */
/* eslint-disable no-return-assign */
/* eslint-disable no-shadow */
import Vue from 'vue';
import moment from 'moment';
import i18n from '@/i18n';
import * as Sentry from '@sentry/vue';

const state = () => ({
  status: '',
  reset_status: '',
  access_token: localStorage.getItem('access_token') || null,
  user: {},
  access_rights: {},
  locale: 'pl',
});

const getters = {
  isLoggedIn: (state) => state.access_token !== null,
  getUser: (state) => state.user,
  getRoles: (state) => state.user?.roles,
  hasRole: (state) => (role) => state.user?.roles && state.user?.roles.includes(role),
  isOwner: (state) => state.user?.owner,
  countryCode: (state) => state.user?.subscriber?.country?.shorName,
  isPolicyAccepted: (state) => state.user?.privacyPolicyAccepted,
  userTimezone: (state) => state.user?.timezone?.location,
  alerts: (state) => state.user?.info,
  userCountry: (state) => state.user?.subscriber?.country,
  userCurrencySymbol: (state) => state.user?.subscriber?.currency?.symbol,
  userCurrency: (state) => state.user?.subscriber?.currency,
  userCurrencyCode: (state) => state.user?.subscriber?.currency?.code,
  subscriptionType: (state) => state.user?.subscriber?.details?.subscription?.code,
  subscriptionDetails: (state) => state.user?.subscriber?.details?.subscription,
  contactEmail: (state) => state.user?.subscriber?.details?.subscription?.contact,
  canAccess: (state) => (namespace, module) => (typeof state.access_rights !== 'undefined' && state.access_rights !== null)
    && state.access_rights[namespace][module] === true,
};

const mutations = {
  reset_success(state) {
    state.reset_status = 'success';
  },
  policy_accept_success(state) {
    state.user.privacyPolicyAccepted = true;
  },
  auth_request(state) {
    state.status = 'loading';
  },
  auth_success(state, {
    accessToken,
  }) {
    state.status = 'success';
    state.access_token = accessToken;
  },
  user_success(state, {
    user,
  }) {
    state.user = user;
  },
  access_success(state, {
    accessRights,
  }) {
    state.access_rights = accessRights;
  },
  auth_error(state) {
    state.status = 'error';
  },
  logout(state) {
    state.status = '';
    state.user = {};
    state.access_token = null;
  },
  locale(state, locale) {
    state.locale = locale;
  },
  change_success(state) {
    state.reset_status = 'changed';
  },
};

const actions = {
  async initAccess({ commit }) {
    return new Promise((resolve, reject) => {
      Vue.axios.get('/api/access/info')
        .then((resp) => {
          if (resp.status === 200 && resp.data !== undefined) {
            commit('access_success', {
              accessRights: resp.data,
            });
          }
          resolve(resp);
        })
        .catch((err) => {
          commit('auth_error');
          localStorage.removeItem('access_token');
          reject(err);
        });
    });
  },
  async initUser({ commit, dispatch }) {
    return new Promise((resolve, reject) => {
      Vue.axios.get('/api/profile')
        .then((resp) => {
          if (resp.status === 200 && resp.data !== undefined) {
            commit('user_success', {
              user: resp.data,
            });
            dispatch('changeLanguage', resp.data.language.locale);
            Sentry.setUser({ email: resp.data.username });
          }
          resolve(resp);
        })
        .catch((err) => {
          commit('auth_error');
          localStorage.removeItem('access_token');
          reject(err);
        });
    });
  },
  async login({ commit }, user) {
    return new Promise((resolve, reject) => {
      commit('auth_request');
      Vue.axios.post(
        '/login_api',
        { username: user.username, password: user.password },
      )
        .then((resp) => {
          const accessToken = resp.data.token;
          localStorage.setItem('access_token', accessToken);
          Vue.axios.defaults.headers.common.Authorization = `Bearer ${accessToken}`;
          commit('auth_success', {
            accessToken,
          });
          resolve(resp);
        })
        .catch((err) => {
          commit('auth_error');
          localStorage.removeItem('access_token');
          reject(err);
        });
    });
  },
  async resetPassword({ commit }, email) {
    return new Promise((resolve, reject) => {
      Vue.axios.post(
        '/api/users/password/reset',
        { email },
      )
        .then((resp) => {
          resolve(resp);
          commit('reset_success');
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  async changePassword({ commit }, { password, token }) {
    return new Promise((resolve, reject) => {
      Vue.axios.patch(
        '/api/users/password',
        { password, token },
      )
        .then((resp) => {
          resolve(resp);
          commit('change_success');
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  async logout({ commit }) {
    return new Promise((resolve) => {
      commit('logout');
      localStorage.removeItem('access_token');
      delete Vue.axios.defaults.headers.common.Authorization;
      resolve();
    });
  },
  async switchUser({ commit }, id) {
    return new Promise((resolve, reject) => {
      Vue.axios.get(
        `/administration/user/${id}/switch`,
      )
        .then((resp) => {
          const accessToken = resp.data.token;
          localStorage.setItem('access_token', accessToken);
          Vue.axios.defaults.headers.common.Authorization = `Bearer ${accessToken}`;
          commit('auth_success', {
            accessToken,
          });
          resolve(resp);
        })
        .catch((err) => {
          commit('auth_error');
          reject(err);
        });
    });
  },
  async acceptPolicy({ commit }) {
    return new Promise((resolve, reject) => {
      Vue.axios.post(
        '/api/profile/policy/accept',
      )
        .then((resp) => {
          resolve(resp);
          commit('policy_accept_success');
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  changeLanguage({ commit, getters }, lang) {
    commit('locale', lang);
    moment.locale(lang);
    i18n.locale = lang;
    i18n.setNumberFormat(lang, {
      currency: {
        style: 'currency',
        currency: getters.userCurrency.code,
      },
    });
  },
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations,
};
