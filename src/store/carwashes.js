/* eslint-disable no-param-reassign */
/* eslint-disable no-return-assign */
/* eslint-disable no-shadow */

const state = () => ({
  carwashes: [],
});

const getters = {
  carwashes: (state) => state.carwashes,
  hasPortalCarwash: (state, getters) => getters.portalCarwashes.length > 0,
  selfServiceCarwashes: (state, getters) => getters.carwashes.filter(
    (carwash) => !carwash.rollover,
  ),
  portalCarwashes: (state, getters) => getters.carwashes.filter(
    (carwash) => carwash.rollover,
  ),
  getCarwashBySerial: (state, getters) => (serial) => getters.carwashes.find(
    (carwash) => carwash.serialNumber === serial,
  ),
};

const mutations = {
  carwashes_success(state, {
    carwashes,
  }) {
    state.carwashes = carwashes;
  },
};

const actions = {
  async initCarwashes({ commit, rootState }) {
    const carwashes = rootState.auth.user.carwashes ?? [];
    commit('carwashes_success', { carwashes });
  },
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations,
};
