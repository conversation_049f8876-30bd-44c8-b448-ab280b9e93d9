<template>
  <div>
    <div v-if="!isLogged">
      <v-app>
        <router-view />
      </v-app>
    </div>

    <template v-else>
      <!-- User logged in -->
      <main-view />
    </template>
  </div>
</template>

<script>

import { mapGetters } from 'vuex';
import MainView from './views/Main.vue';

export default {
  name: 'CM',
  components: {
    MainView,
  },
  computed: {
    ...mapGetters({
      isLogged: 'auth/isLoggedIn',
    }),
  },
};
</script>
