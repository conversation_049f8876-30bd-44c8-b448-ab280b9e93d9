import { utils, writeFile } from 'xlsx';
// import PDF from 'pdfmake/build/pdfmake';
// import PDFFont from 'pdfmake/build/vfs_fonts';

export default {
  install(Vue) {
    // eslint-disable-next-line no-param-reassign
    Vue.prototype.exporter = {
      export(data, type, filename) {
        if (!Array.isArray(data)) {
          return false;
        }

        if (type === 'pdf') {
          return this.pdfExport(data, filename);
        }
        const fileType = type || 'xlsx';
        const fn = filename || 'export';

        /* Generate file */
        const wb = utils.book_new();
        const ws = utils.aoa_to_sheet(data);
        utils.book_append_sheet(wb, ws, 'SheetJS');

        /* Trigger Download with `writeFile` */
        writeFile(
          wb,
          fn,
          {
            bookType: fileType,
            compression: false,
          },
        );
        return true;
      },
      pdfExport() {
        return true;
      },
    };
  },
};
