import VeeValidate from 'vee-validate';
import cs from 'vee-validate/dist/locale/cs';
import de from 'vee-validate/dist/locale/de';
import en from 'vee-validate/dist/locale/en';
import hu from 'vee-validate/dist/locale/hu';
import lt from 'vee-validate/dist/locale/lt';
import pl from 'vee-validate/dist/locale/pl';
import ru from 'vee-validate/dist/locale/ru';
import hr from 'vee-validate/dist/locale/hr';

VeeValidate.Validator.localize({
  cs,
  de,
  en,
  hu,
  lt,
  pl,
  ru,
  hr,
});

export default VeeValidate;
