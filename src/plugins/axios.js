/* eslint-disable no-shadow */
/* eslint-disable consistent-return */
/* eslint-disable no-underscore-dangle */
/* eslint-disable no-param-reassign */
import axios from 'axios';
import store from '@store';
import router from '@/router';
import i18n from '@/i18n';

const config = {
  headers: {
    'Content-Type': 'application/json',
    Accept: 'application/json',
    'X-AUTH-API': 'v1.0',
  },
  baseURL: process.env.VUE_APP_API_URL,
};

const _axios = axios.create(config);

_axios.interceptors.request.use(
  (config) => {
    if (!config.__isRetryRequest) {
      if (localStorage.getItem('access_token') !== null) {
        const auth = localStorage.getItem('access_token');
        const accessToken = auth || null;
        if (accessToken !== null) {
          config.headers.Authorization = `Bearer ${accessToken}`;
        }
      }
    }
    return config;
  },
  (error) => Promise.reject(error)
  ,
);

// Add a response interceptor
_axios.interceptors.response.use(
  (response) => response,
  // eslint-disable-next-line consistent-return
  async (error) => {
    if ((error.response?.status === 401)
    && !error.config.__isRetryRequest) {
      store.dispatch('auth/logout').then(() => {
        if (router.history.current.name !== 'login') {
          router.replace({ name: 'login' });
          router.go(0);
        }
      });
      window.location.reload(true);
      return Promise.resolve(error);
    }

    if (error.response?.status === 402) {
      store.commit('snackbar/showMessage', {
        content: i18n.t('allowed-in-higher-subscription'),
        color: 'warning',
      });
    }

    if (error.response?.status >= 500) {
      store.commit('snackbar/showMessage', {
        content: i18n.t('common_errorHeader'),
        color: 'error',
      });
    }

    return Promise.reject(error);
  },
);

export default _axios;
