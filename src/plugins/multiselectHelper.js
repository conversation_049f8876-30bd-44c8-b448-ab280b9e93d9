export default {
  install(Vue) {
    // eslint-disable-next-line no-param-reassign
    Vue.prototype.multiselectHelper = {
      toSelect: ((data) => Object.keys(data)
        .map((i) => data[i])),
      getFilteredOptions: (serchFor, selectOptions) => {
        const find = serchFor ? serchFor.toLowerCase() : null;
        const filteredIds = selectOptions.filter(
          (option) => {
            if (option.text != null) {
              return option.text.toLowerCase()
                .indexOf(find) !== -1;
            }
            return false;
          },
        )
          .map(
            (option) => option.id,
          );

        return Array.isArray(filteredIds) ? filteredIds.join(',') : filteredIds;
      },
    };
  },
};
