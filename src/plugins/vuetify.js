import Vue from 'vue';
import Vuetify from 'vuetify/lib/framework';
import { pl, en } from 'vuetify/lib/locale/';

Vue.use(Vuetify);

const options = {
  theme: {
    themes: {
      light: {
        header: '#6E7d96',
        primary: '#48A7F2',
        progress: '#48A7F2',
        secondary: '#6E7D95',
        accent: '#48A7F2',
        error: '#E2001A',
        info: '#15A4FA',
        success: '#7ED321',
        warning: '#F5A623',
        darkBackground: '#35384C',
        accentDark: '#26364f',
        accentLight: '#81a9e6',
      },
    },
  },
  lang: {
    locales: { pl, en },
    current: 'pl',
  },
};

export default new Vuetify(options);
