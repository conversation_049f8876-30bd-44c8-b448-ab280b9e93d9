<template>
  <v-main
    class="fill-height bg-image"
  >
    <v-container
      fluid
      justify-center
      fill-height
    >
      <v-container>
        <v-row
          class="flex-column"
          align="center"
          justify="center"
        >
          <div class="bl-logo__wrapper">
            <v-img
              class="bl-logo py-4"
              contain
            />
          </div>
          <v-alert
            border="left"
            width="650"
            transition="slide-y-transition"
            :value="!!message.text"
            :type="message.type"
          >
            {{ message.text }}
          </v-alert>
          <v-card
            class="d-block w-100 mb-7"
            width="650"
            elevation="6"
            raised
            outlined
          >
            <v-card-title>
              <img
                class="login-logo"
                src="@assets/logo.png"
              >
            </v-card-title>
            <v-form
              ref="loginForm"
              @submit.prevent="onLoginSubmit()"
            >
              <v-card-text class="py-0">
                <v-text-field
                  v-model="email"
                  name="login"
                  autofocus
                  prepend-icon="mdi-account"
                  :rules="[rules.required]"
                  :label="$t('login_email')"
                  required
                />
                <v-text-field
                  v-model="password"
                  type="password"
                  name="password"
                  prepend-icon="mdi-key"
                  :rules="[rules.required]"
                  :label="$t('login_password')"
                />
              </v-card-text>
              <v-card-text class="pt-4 pb-6 justify-center text-center">
                <v-btn
                  color="primary"
                  :loading="loading"
                  @click="login"
                >
                  {{ $t('login_login') }}
                </v-btn>
              </v-card-text>
            </v-form>
          </v-card>
          <v-row
            class="d-flex pb-2"
            align="center"
            justify="space-between"
          >
            <v-col
              cols="12"
              class="text-center"
            >
              <v-btn
                text
                dark
                @click="$router.push('/reset-password')"
              >
                {{ $t('login_passwordForgot') }}
              </v-btn>
            </v-col>
          </v-row>
          <v-row
            class="d-flex pb-7"
            align="center"
            justify="space-between"
          >
            <v-col
              sm="6"
              xs="12"
              class="text-center"
            >
              <v-btn
                text
                @click="goToUrl('https://play.google.com/store/apps/details?id=com.bkf.carwash_manager')"
              >
                <img
                  style="height: 50px"
                  src="@assets/store-badges/en/googleplay.png"
                >
              </v-btn>
            </v-col>
            <v-col
              sm="6"
              xs="12"
              class="text-center"
            >
              <v-btn
                text
                @click="goToUrl('https://apps.apple.com/kg/app/carwash-manager/id1560935824')"
              >
                <img
                  style="height: 50px"
                  src="@assets/store-badges/en/appstore.svg"
                >
              </v-btn>
            </v-col>
          </v-row>
        </v-row>
      </v-container>
    </v-container>
  </v-main>
</template>

<script>
import SetWebLocale from '@components/mixins/SetWebLocale.vue';

export default {
  name: 'LoginView',
  mixins: [SetWebLocale],
  data() {
    return {
      loading: false,
      email: '',
      password: '',
      message: {
        type: 'error',
        text: null,
      },
      messages: {
        loginFailure: {
          type: 'error',
          text: this.$t('login_loginFailure'),
        },
      },
      rules: {
        required: (v) => !!v,
        passwordSame: (v) => v === this.register.password,
      },
    };
  },
  methods: {
    goToUrl(link) {
      window.location.href = link;
    },
    login() {
      const { email, password } = this;
      const isFormValid = this.$refs.loginForm.validate();
      if (isFormValid) {
        this.loading = true;
        this.$store.dispatch('auth/login', {
          username: email,
          password,
        }).then(() => {
          this.loading = false;
          this.$router.push('/');
        }).catch(() => {
          this.loading = false;
          this.message = { ...this.messages.loginFailure };
        });
      }
    },
  },
};
</script>

<style>

.bg-image {
  background-image: url('@assets/bg.jpg');
  background-size: cover;
  background-position: center;
}
.bg-image .v-main__wrap {
  background-color: transparent !important;
}

.login-logo {
  height: 40px;
}

.cm-card-title {
  background-color: #6e7d96;
}

</style>
