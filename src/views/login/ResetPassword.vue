<template>
  <v-main
    class="fill-height bg-image"
  >
    <v-container
      fluid
      justify-center
      fill-height
    >
      <v-container>
        <v-row
          class="flex-column"
          align="center"
          justify="center"
        >
          <div class="bl-logo__wrapper">
            <v-img
              class="bl-logo py-4"
              contain
            />
          </div>
          <v-alert
            border="left"
            transition="slide-y-transition"
            :value="!!message.text"
            :type="message.type"
            color="green darken-3"
            width="650"
          >
            {{ message.text }}
          </v-alert>
          <v-card
            class="d-block w-100 mb-7"
            width="650"
            elevation="6"
            raised
            outlined
          >
            <v-card-title>
              <img
                class="login-logo"
                src="@assets/logo.png"
              >
            </v-card-title>
            <v-form
              ref="loginForm"
              @submit.prevent="onLoginSubmit()"
            >
              <v-card-text class="py-0">
                <v-text-field
                  v-model="email"
                  autofocus
                  prepend-icon="mdi-account"
                  :rules="[rules.required]"
                  :label="$t('login_email')"
                  required
                />
              </v-card-text>
              <v-card-text class="pt-4 pb-6 justify-center text-center">
                <v-btn
                  color="primary"
                  :disabled="disabled"
                  :loading="loading"
                  @click="reset"
                >
                  {{ $t('login_reset') }}
                </v-btn>
              </v-card-text>
            </v-form>
          </v-card>
          <v-row
            class="d-flex pb-7"
            align="center"
            justify="space-between"
          >
            <v-col
              cols="12"
              class="text-center"
            >
              <v-btn
                text
                dark
                @click="$router.push('/login')"
              >
                {{ $t('login_backToLogin') }}
              </v-btn>
            </v-col>
          </v-row>
        </v-row>
      </v-container>
    </v-container>
  </v-main>
</template>

<script>
import SetWebLocale from '@components/mixins/SetWebLocale.vue';

export default {
  name: 'ResetPasswordView',
  mixins: [SetWebLocale],
  data() {
    return {
      loading: false,
      disabled: false,
      email: '',
      message: {
        type: 'error',
        text: null,
      },
      messages: {
        forgotSuccess: {
          type: 'success',
          text: this.$t('login_forgotSuccess'),
        },
      },
      rules: {
        required: (v) => !!v,
      },
    };
  },
  methods: {
    async reset() {
      this.loading = true;
      const { email } = this;
      const isFormValid = this.$refs.loginForm.validate();
      if (isFormValid) {
        await this.$store.dispatch('auth/resetPassword', email).then(() => {
          this.message = { ...this.messages.forgotSuccess };
          this.disabled = true;
        });
      }
      this.loading = false;
    },
  },
};
</script>

<style>

.bg-image {
  background-image: url('@assets/bg.jpg');
  background-size: cover;
  background-position: center;
}
.bg-image .v-main__wrap {
  background-color: transparent !important;
}

.login-logo {
  height: 40px;
}

</style>
