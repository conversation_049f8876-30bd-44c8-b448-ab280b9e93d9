<template>
  <v-main
    class="fill-height bg-image"
  >
    <v-container
      fluid
      justify-center
      fill-height
    >
      <v-container>
        <v-row
          class="flex-column"
          align="center"
          justify="center"
        >
          <div class="bl-logo__wrapper">
            <v-img
              class="bl-logo py-4"
              contain
            />
          </div>
          <v-alert
            border="left"
            transition="slide-y-transition"
            :value="!!message.text"
            :type="message.type"
            :color="message.color"
            width="650"
          >
            {{ message.text }}
          </v-alert>
          <v-card
            class="d-block w-100 mb-7"
            width="650"
            elevation="6"
            raised
            outlined
          >
            <v-card-title>
              <img
                class="login-logo"
                src="@assets/logo.png"
              >
            </v-card-title>
            <v-form
              ref="resettingForm"
              @submit.prevent="onLoginSubmit()"
            >
              <v-card-text class="py-0">
                <v-text-field
                  v-model="newPassword"
                  type="password"
                  prepend-icon="mdi-key"
                  data-vv-as="password"
                  :rules="[rules.required, rules.min]"
                  :label="$t('login_newPassword')"
                />
                <v-text-field
                  v-model="confirmPassword"
                  type="password"
                  prepend-icon="mdi-key"
                  :rules="[rules.required, rules.min, rules.passwordSame]"
                  data-vv-as="password"
                  :label="$t('login_confirmPassword')"
                />
              </v-card-text>
              <v-card-text class="pt-4 pb-6 justify-center text-center">
                <v-btn
                  color="primary"
                  :disabled="disabled"
                  :loading="loading"
                  @click="reset"
                >
                  {{ $t('login_changePassword') }}
                </v-btn>
              </v-card-text>
            </v-form>
          </v-card>
          <v-row
            class="d-flex pb-7"
            align="center"
            justify="space-between"
          >
            <v-col
              cols="12"
              class="text-center"
            >
              <v-btn
                text
                dark
                @click="$router.push('/login')"
              >
                {{ $t('login_resettingBackToLogin') }}
              </v-btn>
            </v-col>
          </v-row>
        </v-row>
      </v-container>
    </v-container>
  </v-main>
</template>

<script>
import sleep from 'es7-sleep';
import SetWebLocale from '@components/mixins/SetWebLocale.vue';

export default {
  name: 'ResettingPasswordView',
  mixins: [SetWebLocale],
  data() {
    return {
      loading: false,
      disabled: false,
      newPassword: '',
      confirmPassword: '',
      token: '',
      message: {
        type: 'error',
        text: null,
      },
      rules: {
        required: (v) => !!v || this.$t('common_fieldRequired'),
        min: (v) => v.length >= 5 || this.$t('login_min'),
        passwordSame: (v) => v === this.newPassword || this.$t('login_passwordSame'),
      },
    };
  },
  computed: {
    errorColor: () => 'red darken-3',
    successColor: () => 'green darken-3',
    messages() {
      return {
        success: {
          type: 'success',
          text: this.$t('login_changeSuccess'),
          color: this.successColor,
        },
        tokenExpired: {
          type: 'error',
          text: this.$t('login_tokenExpired'),
          color: this.errorColor,
        },
      };
    },
  },
  created() {
    this.token = this.$route.query.token;
  },
  methods: {
    async reset() {
      this.loading = true;
      const isFormValid = this.$refs.resettingForm.validate();

      if (!isFormValid) {
        this.loading = false;
        return false;
      }

      const { newPassword, token } = this;

      try {
        await this.$store.dispatch('auth/changePassword', { password: newPassword, token });

        this.message = { ...this.messages.success };
        this.disabled = true;
        this.loading = false;
        await sleep(3000);
        this.$router.replace({ name: 'login' });

        return true;
      } catch ({ response }) {
        if (response.status === 404) {
          this.message = { ...this.messages.tokenExpired };
          this.disabled = true;
          this.loading = false;

          return false;
        }
        this.loading = false;

        return false;
      }
    },
  },
};
</script>

<style>

.bg-image {
  background-image: url('@assets/bg.jpg');
  background-size: cover;
  background-position: center;
}
.bg-image .v-main__wrap {
  background-color: transparent !important;
}

.login-logo {
  height: 40px;
}

</style>
