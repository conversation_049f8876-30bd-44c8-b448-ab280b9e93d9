<template>
  <v-app
    standalone
  >
    <div v-if="loaded">
      <template v-if="!isPolicyAccepted">
        <terms />
      </template>
      <template v-else>
        <main-menu />
        <v-main>
          <v-container
            id="app-content"
            pa-4
            fluid
          >
            <transition
              name="fade"
              mode="out-in"
            >
              <router-view />
            </transition>
          </v-container>
          <cm-footer />
          <snackbar-message />
        </v-main>
      </template>
    </div>
    <template v-else>
      <loader />
    </template>
  </v-app>
</template>

<script>

import { mapActions, mapGetters } from 'vuex';
import MainMenu from '@components/scaffold/menu/MainMenu.vue';
import Loader from '@components/loader/Loader.vue';
import CmFooter from '@components/scaffold/footer/Footer.vue';
import Terms from '@components/terms/Terms.vue';
import SnackbarMessage from '@components/snackbar/snackbarMessage.vue';

export default {
  name: 'MainView',
  components: {
    SnackbarMessage,
    MainMenu,
    Lo<PERSON>,
    <PERSON>m<PERSON>ooter,
    Terms,
  },
  data() {
    return {
      loaded: false,
    };
  },
  computed: {
    currentYear() {
      return new Date().getFullYear();
    },
    ...mapGetters({
      isPolicyAccepted: 'auth/isPolicyAccepted',
    }),
  },
  async mounted() {
    await this.initApp();
    this.loaded = true;
  },
  methods: {
    async initApp() {
      await Promise.all([
        this.initUser(),
        this.initAccess(),
        this.initSettings(),
      ]);
      this.initCarwashes();
    },
    ...mapActions({
      initUser: 'auth/initUser',
      initAccess: 'auth/initAccess',
      initCarwashes: 'carwashes/initCarwashes',
      initSettings: 'settings/initSettings',
    }),
  },

};
</script>

<style lang="stylus">
// TODO: refactor
html
  font-size: 13px
#app-content
  min-height calc(100vh - 64px - 74px)

.v-footer
  padding 5px 25px

button.expand-menu-btn
  .btn__content
    width 20px
  width 20px

i.expand-menu-icon
  display flex

.mainnavigationDrawer
  .v-list-item__icon
    margin-right 5px !important

.mainnavigationDrawer
  .v-list-item__title
    font-size 13px !important
  background-color #35384C !important
  color: #D8D8D8 !important

  .icon
  .v-list-item__icon
  .list__item__action
    color: #D8D8D8 !important
    margin-right 5px

    .v-icon
      font-size 1.1em
      width 20px
      margin-right 5px

  .list__item__action:hover
    color: #000000 !important

  .list__group .icon
    font-size 1.1em
    width 20px
    margin-right 5px

  .list__group__header .list__group__header__prepend-icon
    padding-right 0px
    min-width 20px

  .list__group__header .list__tile
    padding-left 0px

  .list__group__items--no-action .list__tile
    padding-left 45px

.navigation-drawer--mini-variant .list__group .list__group__header
  .list__group__header__prepend-icon
    padding-left 5px

.navigation-drawer--mini-variant
  .list__group .icon
    font-size 24px
    width 22px

.no-margin
  margin 0 !important;

.toolbar-summary
  margin 10px
  height 75%

  .summary-block-item
    border-width thin medium
    border-color white

  .toolbar-summary-amount
    font-size 160%
    padding-right 5px
    padding-left 5px
    color white

  .toolbar-summary-label
    padding-right: 10px
    color white

  .btn
    text-transform none

.text-right
  text-align right

.topmainbar
  background-color #FFFFFF !important
  z-index 1

#app
  .avatar
    height 35px
    width 35px
    font-size 24px
    font-weight 400
    color whitesmoke
    text-transform uppercase
    background-color #26364f
    border-radius 18px

span.badge.new
  font-size 0.8rem
  color #fff
  background-color #42a5f5
  border-radius 2px
  position absolute
  top 12px
  right 0px
  width 16px
  border-radius 9px

.mainnavigationDrawer .i.ion
  padding-left 4px

.profile-menu
  .list__tile
    font-size 14px

.profile-menu.list
  padding 0px 0px

.loyalty-beta--vue
  color #ea1b1b
  font-size 10px

#app.v-application--is-ltr .v-list-group--no-action > .v-list-group__items > .v-list-item
  padding-left 36px !important

.logo
  height 40px

.top-logo
  position absolute
  top 15px
  left 62px
  height 40px
  transition padding .2s linear

@media only screen and (max-width: 960px)
  .top-logo
    height 30px
    left:68

.mainnavigationDrawer
  .v-list-group__header__prepend-icon > .v-icon
    color #d8d8d8 !important

  .theme--light.v-icon
    color #d8d8d8 !important

.v-main__wrap
  background #ecf2f6 !important

.v-card
  .title
    background-color: #6e7d96
    color: #fff!important
    padding-top 10px
    padding-bottom 10px
    margin 0

.v-application
  .title
    font-size: 1.25rem!important;
    font-weight: 500;
    letter-spacing: .0125em!important;

  .dialogWidth-3
    max-width: 700px;

  .clickable
    cursor: pointer;

.i2m-card
  border-top-right-radius: 4px;
  border-top-left-radius: 4px;

</style>
