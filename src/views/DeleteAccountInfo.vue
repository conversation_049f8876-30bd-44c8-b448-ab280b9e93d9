<template>
  <v-main
    class="fill-height bg-image"
  >
    <v-container
      fluid
      justify-center
      fill-height
    >
      <v-container>
        <v-row
          class="flex-column"
          align="center"
          justify="center"
        >
          <div class="bl-logo__wrapper">
            <v-img
              class="bl-logo py-4"
              contain
            />
          </div>
          <v-card
            class="d-block w-100 mb-7"
            width="650"
            elevation="6"
            raised
            outlined
          >
            <v-card-title>
              <img
                class="login-logo"
                src="@assets/logo.png"
              >
            </v-card-title>
            <p class="text-center">
              {{ $t('user_deleteAccountInfo') }}
            </p>
            <div class="text-center">
              <a href="mailto:<EMAIL>">
                <EMAIL>
              </a>
            </div>
          </v-card>
        </v-row>
      </v-container>
    </v-container>
  </v-main>
</template>

<script>
import SetWebLocale from '@components/mixins/SetWebLocale.vue';

export default {
  name: 'DeleteAccountInfo',
  mixins: [SetWebLocale],
};
</script>

<style scoped>

p {
  font-size: 14px;
  padding: 20px 20px 0px 20px;
}

a {
  display:inline-block;
  padding-bottom: 40px;
  font-size: 30px;
}

</style>
