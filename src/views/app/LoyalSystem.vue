<template>
  <v-card>
    <v-toolbar
      color="secondary"
      dark
      flat
    >
      <img
        alt="logo"
        class="logo"
        src="@assets/logobkfpay.png"
      >
      <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
        {{ $t('common_loyalsystem') }}
      </v-toolbar-title>
      <v-spacer />
    </v-toolbar>

    <v-card-text class="pa-0 mt-0">
      <bkf-pay-tabs
        base-url="/api"
      />
    </v-card-text>
  </v-card>
</template>

<script>

import BkfPayTabs from '@components/loyalty-cards/tabs/BkfPayTabs.vue';

export default {
  components: {
    BkfPayTabs,
  },
};
</script>

<style scoped>
.logo {
  height: 26px;
  padding-right: 10px;
}
</style>
