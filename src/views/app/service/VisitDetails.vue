<template>
  <v-card>
    <v-card-title class="title">
      <span class="headline">
        <h5 class="text-uppercase">Szczegóły wizyty</h5>
      </span>
      <v-spacer />
      <v-btn
        icon
        class="white--text"
        @click="$router.go(-1)"
      >
        <v-icon>mdi-arrow-left</v-icon>
      </v-btn>
    </v-card-title>

    <div
      v-if="loading"
      class="d-flex justify-center align-center"
      style="height: 100px;"
    >
      <v-progress-circular
        indeterminate
        color="primary"
      />
    </div>

    <v-card-text v-else>
      <v-expansion-panels
        v-model="expandedPanel"
        multiple
        class="my-4"
        flat
        tile
      >
        <v-expansion-panel>
          <v-expansion-panel-header>
            <h3 class="mb-0">
              Lokalizacja i urządzenie
            </h3>
          </v-expansion-panel-header>
          <v-expansion-panel-content>
            <v-row>
              <v-col>
                <location
                  :location="location"
                />
              </v-col>
              <v-col>
                <device-basic-details
                  :id="deviceDetails.id"
                />
              </v-col>
            </v-row>
          </v-expansion-panel-content>
        </v-expansion-panel>
      </v-expansion-panels>
      <v-row>
        <v-col>
          <v-card
            class="px-3"
            outlined
            color="blue-grey lighten-5"
          >
            <v-row>
              <v-col>
                <VisitBasicInfo
                  :id="Number(visitId)"
                />
                <VisitPhotos
                  :id="Number(visitId)"
                />
                <VisitFiles
                  :id="Number(visitId)"
                />
                <Comments
                  :id="Number(visitId)"
                  url="api/service/visit"
                />
                <div
                  v-if="visitMainDetails && visitMainDetails.Status === 'completed'"
                >
                  <v-textarea
                    ref="commentInput"
                    v-model="comment"
                    label="Komentarz"
                    :rules="commentRules"
                    auto-grow
                    clearable
                    outlined
                    class="my-4"
                  />
                  <v-row class="mb-4">
                    <v-col cols="6">
                      <v-btn
                        color="success"
                        block
                        @click="submitApproval"
                      >
                        Zatwierdź
                      </v-btn>
                    </v-col>
                    <v-col cols="6">
                      <v-btn
                        color="error"
                        block
                        @click="submitRejection"
                      >
                        Odrzuć
                      </v-btn>
                    </v-col>
                  </v-row>
                </div>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col>
          <v-card
            class="px-3"
            outlined
            color="blue-grey lighten-5"
          >
            <tasks
              :id="Number(visitId)"
              url="api/service/visit"
              :location-id="location.id"
              :device-id="deviceDetails.id"
              :add-enabled="visitMainDetails.Status === 'pending'
                || visitMainDetails.Status === 'planned'"
            />
            <div
              v-if="visitMainDetails && visitMainDetails.Status === 'completed'
                || visitMainDetails.Status === 'rejected'
                || visitMainDetails.Status === 'approved'"
            >
              <Parts
                :visit-id="Number(visitId)"
                :release-mode-changeable="visitMainDetails.Status === 'completed'
                  || visitMainDetails.Status === 'rejected'"
              />
            </div>
            <div
              v-if="visitMainDetails && visitMainDetails.Status === 'completed'
                || visitMainDetails.Status === 'rejected'
                || visitMainDetails.Status === 'approved'"
            >
              <VisitSummary
                :id="Number(visitId)"
              />
            </div>
          </v-card>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>

<script>
import VisitPhotos from '@views/app/service/visit-details/VisitPhotos.vue';
import VisitFiles from '@views/app/service/visit-details/VisitFiles.vue';
import Comments from '@components/domain/service/Comments.vue';
import VisitBasicInfo from '@components/domain/service/visit/BasicInfo.vue';
import Location from '@components/domain/service/location/BasicInfo.vue';
import DeviceBasicDetails from '@components/domain/service/device/BasicInfo.vue';
import Tasks from '@components/domain/service/Tasks.vue';
import VisitSummary from '@components/domain/service/visit/Summary.vue';
import Parts from '@components/domain/service/Parts.vue';

export default {
  name: 'VisitDetails',
  components: {
    VisitPhotos,
    VisitFiles,
    Comments,
    VisitBasicInfo,
    Location,
    DeviceBasicDetails,
    Tasks,
    VisitSummary,
    Parts,
  },
  data() {
    return {
      dialog: false,
      loading: true,
      visitId: this.$route.params.id,
      expandedPanel: [0], // This works with 'multiple'
      comment: '',
      isRejection: false,
    };
  },
  computed: {
    deviceDetails() {
      const { device } = this.data;
      if (!device) {
        return {};
      }
      return {
        id: device.id,
        'Numer seryjny': device.serialNumber,
        'Data rozpoczęcia': this.$options.filters.formatDateDay(device.startDate),
        'Koniec gwarancji': this.$options.filters.formatDateDay(device.warrantyEnd),
        Typ: device.type,
        Kod: device.code,
        Właściciel: device.owner?.name,
        'Umowa serwisowa': device.sla?.name || 'brak',
      };
    },
    location() {
      const { location } = this.data;
      if (!location) {
        return {};
      }
      return {
        id: location.id,
        Nazwa: location.name,
        Kraj: location.country,
        Miasto: location.city,
        Adres: location.address,
        Lokalizacja: {
          lat: location.lat,
          lon: location.lon,
        },
      };
    },
    visitMainDetails() {
      return {
        Numer: this.data.visitTag,
        Status: this.data.status,
        Serwisant: this.data.serviceman?.username,
        'Planowana data': this.$options.filters.formatDateDay(this.data.plannedDate),
        'Przegląd?': this.data.surveyRelated,
      };
    },
    commentRules() {
      return this.isRejection
        ? [(v) => !!v || 'Komentarz jest wymagany przy odrzuceniu']
        : [];
    },
  },
  watch: {
    dialog() {
      this.fetchData();
    },
  },
  created() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      this.loading = true;
      try {
        const response = await this.axios.get(`/api/service/visit/${this.visitId}`);
        this.data = response.data;
      } catch (error) {
        // Handle error if needed
      }
      this.loading = false;
    },
    async submitApproval() {
      this.isRejection = false;
      try {
        await this.axios.post(`/api/service/visit/${this.visitId}/status/approved`, {
          comment: this.comment || '',
        });
        this.snackbar.showMessage('success', 'Wizyta zatwierdzona');
        window.location.reload();
      } catch (err) {
        this.snackbar.showMessage('error', 'Błąd podczas zatwierdzania wizyty');
      }
    },
    async submitRejection() {
      this.isRejection = true;

      // Validate before submission
      const isValid = await this.$refs.commentInput.validate?.();
      if (!isValid) {
        this.snackbar.showMessage('error', 'Komentarz jest wymagany do odrzucenia wizyty');
        return;
      }

      try {
        await this.axios.post(`/api/service/visit/${this.visitId}/status/rejected`, {
          comment: this.comment,
        });
        this.snackbar.showMessage('success', 'Wizyta odrzucona');
        window.location.reload();
      } catch (err) {
        this.snackbar.showMessage('error', 'Błąd podczas odrzucania wizyty');
      }
    },

  },
};
</script>
