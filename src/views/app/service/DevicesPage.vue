<template>
  <service-page
    title="Urządzenia"
    icon-name="mdi-devices"
    api-url="/api/service/devices"
    :headers="headers"
    :can-expand="(item) => true"
  >
    <template #expanded-item="{ item }">
      <fields-container
        title="Szczegóły"
        :items="getFields(item)"
      >
        <template #[`location.value`]>
          <a
            v-if="item.location"
            :href="getLocationLink(item.location)"
            target="_blank"
            rel="noopener noreferrer"
          >{{ item.location.address }}, {{ item.location.city }}
          </a>
          <div v-else>
            -
          </div>
        </template>
        <template #[`startDate.value`]>
          {{ item.startDate|formatDateDay }}
        </template>
        <template #[`warrantyEnd.value`]>
          {{ item.warrantyEnd|formatDateDay }}
        </template>
      </fields-container>
    </template>
  </service-page>
</template>

<script>
import ServicePage from '@/components/service/ServicePage.vue';
import FieldsContainer from '@/components/common/containers/FieldsContainer.vue';

export default {
  name: 'DevicesPage',

  components: {
    ServicePage,
    FieldsContainer,
  },

  data() {
    return {
      headers: [
        {
          text: 'Id',
          sortable: false,
          align: 'sm-start',
          value: 'id',
        },
        {
          text: 'Numer seryjny',
          sortable: false,
          align: 'sm-start',
          value: 'serialNumber',
        },
        {
          text: 'Nazwa',
          sortable: false,
          align: 'sm-start',
          value: 'objectName',
        },
        {
          text: 'Nr objektu',
          sortable: false,
          align: 'sm-start',
          value: 'objectNumber',
        },
        {
          text: 'Właściciel',
          sortable: false,
          value: 'owner.name',
        },
        {
          text: 'Typ',
          sortable: false,
          value: 'type',
          align: 'sm-start',
        },
        { text: '', value: 'data-table-expand' },
      ],
    };
  },

  methods: {
    getLocationLink(location) {
      const url = `https://www.google.com/maps/search/?api=1&query=${location.lat},${location.lon}`;

      return encodeURI(url);
    },
    getFields(item) {
      return [
        {
          label: 'Lokalizacja',
          field: 'location',
          value: item.location,
        },
        {
          label: 'Data startu',
          field: 'startDate',
          value: item.startDate,
        },
        {
          label: 'Koniec gwaracji',
          field: 'warrantyEnd',
          value: item.warrantyEnd,
        },
        {
          label: 'Kod',
          field: 'code',
          value: item.code,
        },
      ];
    },
  },
};
</script>
