<template>
  <div
    class="issue-details-view"
    style="z-index: 1200"
  >
    <v-card>
      <v-card-title class="title">
        <span class="headline">
          <h5 class="text-uppercase">
            Zd<PERSON>ęcia wizyty
          </h5>
        </span>
        <v-spacer />
      </v-card-title>

      <div
        v-if="loading"
        class="d-flex justify-center align-center"
        style="height: 100px;"
      >
        <v-progress-circular
          indeterminate
          color="primary"
        />
      </div>
      <v-card-text
        v-else
      >
        <v-row>
          <v-col>
            <v-simple-table
              dense
              class="elevation-3 mx-1 mt-2 mb-3"
            >
              <template #default>
                <thead>
                  <tr>
                    <th class="text-left">
                      ID
                    </th>
                    <th class="text-left">
                      Zdjęcie
                    </th>
                    <th class="text-left">
                      Podgląd
                    </th>
                    <th class="text-left">
                      Kategoria
                    </th>
                    <th class="text-left">
                      Subkategoria
                    </th>
                    <th class="text-left">
                      Pobierz
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="photo in data"
                    :key="photo.id"
                  >
                    <td>{{ photo.id ?? '-' }}</td>
                    <td>{{ photo.filename ?? '-' }}</td>
                    <!-- Thumbnail Column -->
                    <td>
                      <div>
                        <!-- If thumbnail content exists, show the image -->
                        <v-img
                          v-if="photo.thumbnailContent"
                          :src="'data:image/jpeg;base64,' + photo.thumbnailContent"
                          max-width="80"
                          max-height="80"
                          contain
                          class="rounded elevation-2"
                        />

                        <!-- If no thumbnail, show icon with tooltip -->
                        <v-tooltip
                          v-else
                          bottom
                        >
                          <template #activator="{ on }">
                            <v-icon
                              color="grey"
                              small
                              class="mt-4"
                              v-on="on"
                            >
                              mdi-image-off
                            </v-icon>
                          </template>
                          <span>Brak miniaturki zdjęcia</span>
                        </v-tooltip>
                      </div>
                    </td>
                    <td>
                      <v-layout justify-start>
                        <visit-photo-category :category="photo.category" />
                      </v-layout>
                    </td>
                    <td>{{ photo.subCategory ?? '-' }}</td>
                    <td>
                      <act-download
                        :url="`/api/service/photo/${photo.id}/download`"
                      />
                    </td>
                  </tr>
                </tbody>
              </template>
            </v-simple-table>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import VisitPhotoCategory from '@components/domain/service/photo/Category.vue';
import ActDownload from '@components/common/Action/ActDownload.vue';

export default {
  name: 'VisitPhotos',
  components: {
    VisitPhotoCategory,
    ActDownload,
  },
  props: {
    id: {
      type: Number,
      default: null,
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
      loading: true,
      data: {},
    };
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      this.loading = true;
      try {
        const response = await this.axios.get(`/api/service/visit/${this.id}/photos/`);
        this.data = response.data;
      } catch (error) {
        // do nothing
      }
      this.loading = false;
    },

  },
};
</script>
