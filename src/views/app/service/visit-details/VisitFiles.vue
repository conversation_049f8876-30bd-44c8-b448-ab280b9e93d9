<template>
  <div
    class="issue-details-view"
    style="z-index: 1200"
  >
    <v-card>
      <v-card-title class="title">
        <span class="headline">
          <h5 class="text-uppercase">
            Pliki wizyty
          </h5>
        </span>
        <v-spacer />
      </v-card-title>

      <div
        v-if="loading"
        class="d-flex justify-center align-center"
        style="height: 100px;"
      >
        <v-progress-circular
          indeterminate
          color="primary"
        />
      </div>
      <v-card-text
        v-else
      >
        <v-row>
          <v-col>
            <v-simple-table
              dense
              class="elevation-3 mx-1 mt-2 mb-3"
            >
              <template #default>
                <thead>
                  <tr>
                    <th class="text-left">
                      ID
                    </th>
                    <th class="text-left">
                      Plik
                    </th>
                    <th class="text-left">
                      Pobierz
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="file in data"
                    :key="file.id"
                  >
                    <td>{{ file.id ?? '-' }}</td>
                    <td>{{ file.filename?? '-' }}</td>
                    <td>
                      <act-download
                        :url="`/api/service/file/${file.id}/download`"
                      />
                    </td>
                  </tr>
                </tbody>
              </template>
            </v-simple-table>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import ActDownload from '@components/common/Action/ActDownload.vue';

export default {
  name: 'VisitFiles',
  components: {
    ActDownload,
  },
  props: {
    id: {
      type: Number,
      default: null,
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
      loading: true,
      data: {},
    };
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      this.loading = true;
      try {
        const response = await this.axios.get(`/api/service/visit/${this.id}/files/`);
        this.data = response.data;
      } catch (error) {
        // do nothing
      }
      this.loading = false;
    },
  },
};
</script>
