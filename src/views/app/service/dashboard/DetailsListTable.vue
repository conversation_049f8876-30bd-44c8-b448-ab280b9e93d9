<template>
  <v-card
    class="px-3 py-3"
    outlined
    color="blue-grey lighten-5"
  >
    <slot name="title">
      <h3 class="mb-3">
        {{ headerText }}
      </h3>
    </slot>
    <v-simple-table
      v-if="data.length"
      dense
      class="elevation-3 py-2"
    >
      <thead>
        <tr>
          <th
            v-for="(header, index) in headers"
            :key="index"
          >
            {{ header }}
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          v-for="(task, index) in data"
          :key="`task-${index}`"
        >
          <td
            v-for="(header, i) in headers"
            :key="i"
          >
            {{ formatValue(task[header]) }}
          </td>
        </tr>
      </tbody>
    </v-simple-table>
    <v-card-text v-else>
      <v-icon small>
        mdi-alert-circle-outline
      </v-icon>
      <i class="ml-2">{{ $t('common_noData') }}</i>
    </v-card-text>
  </v-card>
</template>

<script>
export default {
  name: 'DetailsListTable',
  props: {
    data: {
      type: Array,
      required: true,
    },
    headerText: {
      type: String,
      default: () => '',
      required: false,
    },
  },
  computed: {
    headers() {
      if (this.data.length === 0) {
        return [];
      }
      // Assuming all tasks have the same keys, we take the headers from the first item
      return Object.keys(this.data[0]);
    },
  },
  methods: {
    formatValue(value) {
      if (this.isValidDate(value)) {
        return this.$options.filters.formatDateDay(value);
      }
      return value !== null ? value : '-';
    },
    isValidDate(value) {
      if (typeof value !== 'string') {
        return false;
      }

      const isoDateRegex = /^\d{4}-\d{2}-\d{2}(T\d{2}:\d{2}:\d{2}(.\d+)?(Z|([+-]\d{2}:\d{2})))?$/;
      if (!isoDateRegex.test(value)) {
        return false;
      }

      const date = new Date(value);
      return !Number.isNaN(date.getTime());
    },
  },
};
</script>
