<template>
  <v-container
    v-if="loading"
    class="d-flex justify-center align-center"
  >
    <v-progress-circular
      indeterminate
      color="primary"
      size="52"
    />
  </v-container>
  <v-row v-else>
    <v-col cols="6">
      <v-row justify="end">
        <v-btn
          text
          x-small
          fab
          color="green"
          class="mt-4 mr-4"
          @click="fetchData"
        >
          <v-icon>mdi-cached</v-icon>
        </v-btn>
      </v-row>
      <v-expansion-panels
        v-model="openPanels"
        accordion
        multiple
        class="mt-4 mb-2"
      >
        <v-expansion-panel>
          <v-expansion-panel-header class="selectable-text">
            <h3>Nieprzypisane</h3>
          </v-expansion-panel-header>
          <v-expansion-panel-content>
            <v-data-table
              ref="notReadyTable"
              :loading="loading"
              :headers="notReadyHeaders"
              :items="notReadyItems"
              item-key="id"
              hide-default-footer
              disable-pagination
            >
              <template #item="{ item }">
                <tr
                  v-if="item"
                  :class="{ 'row-selected': selectedMarker?.id === item.id }"
                  @click="onRowClick(item)"
                >
                  <td>{{ item.visitTag }}</td>
                  <td>{{ item.serviceman?.username }}</td>
                  <td>{{ item.plannedDate | formatDateDay }}</td>
                  <td>{{ item.device?.serialNumber }}</td>
                  <td>
                    <a
                      v-if="item.location"
                      :href="getLocationLink(item.location)"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {{ item.location.address }}, {{ item.location.city }}
                    </a>
                    <div v-else>
                      -
                    </div>
                  </td>
                  <td>{{ item.tasks?.length ?? 0 }}</td>
                  <td>
                    <EditVisitModal
                      :key="`EditVisit_${item.id}`"
                      :ref="`EditVisit_${item.id}`"
                      :tasks="item.tasks"
                      :serviceman-id="item.serviceman?.id"
                      :servicemen="servicemen"
                      :planned-date="item.plannedDate"
                      :device-data="item.device"
                      :location-data="item.location"
                      :visit-id="item.id"
                      :visit-tag="item.visitTag"
                      @onSaved="fetchData"
                      @onTaskSaved="fetchData"
                    />
                    <div>
                      <v-tooltip bottom>
                        <template #activator="{ on, attrs }">
                          <v-btn
                            :key="item.id"
                            :device-data="item.device"
                            :location-data="item.location"
                            :visit-tag="item.visitTag"
                            class="mr-2"
                            elevation="1"
                            v-bind="attrs"
                            tile
                            rounded
                            x-small
                            fab
                            color="secondary"
                            v-on="on"
                            @click.stop
                            @click.native="openModal(`EditVisit_${item.id}`)"
                          >
                            <v-icon>mdi-pencil</v-icon>
                          </v-btn>
                        </template>
                        <span>Edytuj</span>
                      </v-tooltip>

                      <v-tooltip bottom>
                        <template #activator="{ on, attrs }">
                          <v-btn
                            :key="item.id"
                            elevation="1"
                            v-bind="attrs"
                            tile
                            rounded
                            x-small
                            fab
                            color="red"
                            v-on="on"
                            @click.stop
                            @click.native="openModal(`CancelVisit_${item.id}`)"
                          >
                            <v-icon color="white">
                              mdi-cancel
                            </v-icon>
                          </v-btn>
                        </template>
                        <span>Anuluj</span>
                        <CancelVisitModal
                          :key="`CancelVisit_${item.id}`"
                          :ref="`CancelVisit_${item.id}`"
                          :visit-id="item.id"
                          @onDeleted="fetchData"
                        />
                      </v-tooltip>
                    </div>
                    <TaskAddEditModal
                      :id="item.id"
                      :key="`AddTask_${item.id}`"
                      :ref="`AddTask_${item.id}`"
                      :submit-task="(body) => submitTask(item.id, body)"
                      :use-location="false"
                      :device-id="item.device?.id"
                      :location-id="item.location?.id"
                      @onSubmitted="onTaskSubmitted(`AddTask_${item.id}`)"
                      @error="message => snackbar.showMessage('error', message)"
                    />
                  </td>
                </tr>
              </template>
            </v-data-table>
          </v-expansion-panel-content>
        </v-expansion-panel>
        <v-expansion-panel
          v-for="[date, groupedValues] in Object.entries(readyItems)"
          :key="date"
        >
          <v-expansion-panel-header class="selectable-text">
            <h3>{{ date }}</h3>
          </v-expansion-panel-header>
          <v-expansion-panel-content>
            <v-expansion-panels
              v-model="openSubPanels[date]"
              accordion
              multiple
            >
              <v-expansion-panel
                v-for="[serviceman, items] in Object.entries(groupedValues)"
                :key="serviceman"
              >
                <v-expansion-panel-header class="selectable-text">
                  <h3>{{ serviceman }}</h3>
                </v-expansion-panel-header>
                <v-expansion-panel-content>
                  <v-data-table
                    ref="readyTable"
                    :loading="loading"
                    :headers="readyHeaders"
                    :items="items"
                    item-key="id"
                    hide-default-footer
                    disable-pagination
                  >
                    <template #item="{ item }">
                      <tr
                        :class="{ 'row-selected': selectedMarker?.id === item.id }"
                        @click="onRowClick(item)"
                      >
                        <td>{{ item.visitTag }}</td>
                        <td>
                          <a
                            v-if="item.location"
                            :href="getLocationLink(item.location)"
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            {{ item.location.address }}, {{ item.location.city }}
                          </a>
                          <div v-else>
                            -
                          </div>
                        </td>
                        <td>
                          {{ item.device?.serialNumber }}
                        </td>
                        <td>
                          <v-layout justify-start>
                            <visit-status :status="item.status" />
                          </v-layout>
                        </td>
                        <td>{{ item.tasks?.length ?? 0 }}</td>

                        <td>
                          <EditVisitModal
                            :key="`EditVisit_${item.id}`"
                            :ref="`EditVisit_${item.id}`"
                            :tasks="item.tasks"
                            :serviceman-id="item.serviceman?.id"
                            :servicemen="servicemen"
                            :planned-date="item.plannedDate"
                            :device-data="item.device"
                            :location-data="item.location"
                            :visit-id="item.id"
                            :visit-tag="item.visitTag"
                            @onSaved="fetchData"
                            @onTaskSaved="fetchData"
                          />
                          <div style="display: inline-flex; align-items: center;">
                            <v-tooltip bottom>
                              <template #activator="{ on, attrs }">
                                <v-btn
                                  :key="item.id"
                                  :device-data="item.device"
                                  :location-data="item.location"
                                  :visit-tag="item.visitTag"
                                  class="mr-2"
                                  elevation="1"
                                  v-bind="attrs"
                                  tile
                                  rounded
                                  x-small
                                  fab
                                  color="secondary"
                                  v-on="on"
                                  @click.stop
                                  @click.native="openModalArray(`EditVisit_${item.id}`)"
                                >
                                  <v-icon>mdi-pencil</v-icon>
                                </v-btn>
                              </template>
                              <span>Edytuj</span>
                            </v-tooltip>

                            <v-tooltip bottom>
                              <template #activator="{ on, attrs }">
                                <v-btn
                                  :key="item.id"
                                  :visit-tag="item.visitTag"
                                  class="mr-2"
                                  elevation="1"
                                  v-bind="attrs"
                                  tile
                                  rounded
                                  x-small
                                  fab
                                  color="green"
                                  v-on="on"
                                  @click.stop
                                  @click.native="openModalArray(`ConfirmVisit_${item.id}`)"
                                >
                                  <v-icon>mdi-check</v-icon>
                                </v-btn>
                              </template>
                              <span>Zatwierdź</span>
                              <ConfirmVisitModal
                                :key="`ConfirmVisit_${item.id}`"
                                :ref="`ConfirmVisit_${item.id}`"
                                :visit-id="item.id"
                                :visit-tag="item.visitTag"
                                @onConfirmed="fetchData"
                              />
                            </v-tooltip>

                            <v-tooltip bottom>
                              <template #activator="{ on, attrs }">
                                <v-btn
                                  :key="item.number"
                                  elevation="1"
                                  v-bind="attrs"
                                  tile
                                  rounded
                                  x-small
                                  fab
                                  color="red"
                                  v-on="on"
                                  @click.stop
                                  @click.native="openModalArray(`CancelVisit_${item.id}`)"
                                >
                                  <v-icon color="white">
                                    mdi-cancel
                                  </v-icon>
                                </v-btn>
                              </template>
                              <span>Anuluj</span>
                              <CancelVisitModal
                                :key="`CancelVisit_${item.id}`"
                                :ref="`CancelVisit_${item.id}`"
                                :visit-id="item.id"
                                :visit-tag="item.visitTag"
                                @onDeleted="fetchData"
                              />
                            </v-tooltip>
                          </div>
                          <TaskAddEditModal
                            :id="item.id"
                            :key="`AddTask_${item.id}`"
                            :ref="`AddTask_${item.id}`"
                            :submit-task="(body) => submitTask(item.id, body)"
                            :use-location="false"
                            :device-id="item.device?.id"
                            :location-id="item.location?.id"
                            @onSubmitted="onTaskSubmitted(`AddTask_${item.id}`)"
                            @error="message => snackbar.showMessage('error', message)"
                          />
                        </td>
                      </tr>
                    </template>
                  </v-data-table>
                </v-expansion-panel-content>
              </v-expansion-panel>
            </v-expansion-panels>
          </v-expansion-panel-content>
        </v-expansion-panel>
      </v-expansion-panels>
    </v-col>
    <v-col cols="6">
      <gmap-map
        ref="gmap"
        :center="{ lat: 52.29772314949371, lng: 19.35617356845292 }"
        :zoom="6.3"
        :options="{
          zoomControl: true,
          mapTypeControl: false,
          scaleControl: false,
          streetViewControl: false,
          rotateControl: false,
          fullscreenControl: false,
          disableDefaultUi: false
        }"
        style="width: 100%; height:550px;"
        @click="selectedMarker = null"
      >
        <gmap-marker
          v-for="item in mapItems"
          :key="item.id"
          :position="{lat: item.location.lat, lng: item.location.lon}"
          :clickable="true"
          :draggable="false"
          @click="onMarkerClick(item)"
        >
          <gmap-info-window
            v-if="selectedMarker && selectedMarker.id === item.id"
            :position="{lat: item.location.lat, lng: item.location.lon}"
            @closeclick="selectedMarker = null"
          >
            <div>
              <p><strong>Lokalizacja:</strong> {{ item.location?.name ?? '-' }}</p>
              <p><strong>Data:</strong> {{ item.plannedDate | formatDateDay }}</p>
              <p><strong>Serwisant:</strong> {{ item.serviceman?.username ?? '-' }}</p>
            </div>
          </gmap-info-window>
        </gmap-marker>
      </gmap-map>
    </v-col>
  </v-row>
</template>

<script>

import TaskAddEditModal from '@components/domain/service/task/AddEditModal.vue';
import EditVisitModal from '@/views/app/service/dashboard/visits/EditVisitModal.vue';
import CancelVisitModal from '@/views/app/service/dashboard/visits/CancelVisitModal.vue';
import ConfirmVisitModal from '@/views/app/service/dashboard/visits/ConfirmVisitModal.vue';
import VisitStatus from '@components/domain/service/visit/Status.vue';

export default {
  name: 'VisitsTab',

  components: {
    TaskAddEditModal,
    EditVisitModal,
    CancelVisitModal,
    ConfirmVisitModal,
    VisitStatus,
  },
  data() {
    return {
      loading: false,
      allItems: [],
      readyItems: {},
      notReadyItems: [],
      openPanels: [],
      openSubPanels: {},
      selectedMarker: null,
      mapItems: [],
      servicemen: [],
      readyHeaders: [
        {
          text: 'Numer',
          sortable: false,
          align: 'sm-start',
          value: 'visitTag',
        },

        {
          text: 'Lokalizacja',
          sortable: false,
          align: 'sm-start',
          value: 'location',
        },
        {
          text: 'Urządzenie',
          sortable: false,
          align: 'sm-start',
          value: 'device',
        },
        {
          text: 'Status',
          sortable: false,
          align: 'sm-start',
          value: 'status',
        },
        {
          text: 'Ilość zadań',
          sortable: false,
          align: 'sm-start',
          value: 'tasks',
        },
        {
          text: '',
          sortable: false,
          align: 'sm-end',
          value: 'details',
          width: '105px',
        },
      ],
      notReadyHeaders: [
        {
          text: 'ID',
          sortable: false,
          align: 'sm-start',
          value: 'id',
        },
        {
          text: 'Serwisant',
          sortable: false,
          align: 'sm-start',
          value: 'serviceman.username',
        },
        {
          text: 'Data',
          sortable: false,
          align: 'sm-start',
          value: 'plannedDate',
        },
        {
          text: 'Urządzenie',
          sortable: false,
          align: 'sm-start',
          value: 'device.serialnumber',
        },
        {
          text: 'Lokalizacja',
          sortable: false,
          align: 'sm-start',
          value: 'location',
        },
        {
          text: 'Ilość zadań',
          sortable: false,
          align: 'sm-start',
          value: 'tasks',
        },
        {
          text: '',
          sortable: false,
          align: 'sm-end',
          value: 'details',
          width: '105px',
        },
      ],
    };
  },
  computed: {
    expandedItems() {
      return this.openPanels
        .map((index) => Object.keys(this.readyItems)[index - 1] ?? '-');
    },

  },
  watch: {
    expandedItems(current, previous) {
      // zamknij sub panele ktore juz nie sa otwarte
      if (previous.length > current.length) {
        previous.filter((item) => !current.includes(item)).forEach((date) => {
          delete this.openSubPanels[date];
        });
      }

      // czyscimy wszystkie pinezki
      // bedziemy je dodawac w kolejnych krokach
      this.mapItems = [];

      // jezeli wszystko jest zwinięte
      if (current.length === 0) {
        // to pokaz na mapie wszystkie wizyty
        this.mapItems = this.allItems;
      } else {
        // w przeciwnym razie iterujemy po wszystkich dniach
        current.forEach((date) => {
          // jezeli rozwiniete sa niezaplanowane
          if (date === '-') {
            // to pokaz na mapie niezaplanowane
            this.mapItems.push(...this.notReadyItems);
          } else { // jezeli rozwiniete sa zaplanowane
            // pokaz na mapie wszystkie zaplanowane na dany dzień
            this.mapItems.push(...Object.values(this.readyItems[date]).flatMap((array) => array));

            // jezeli jest tylko jeden serwisant w danym dniu
            if (Object.keys(this.readyItems[date]).length === 1) {
              // to go rozwijamy
              this.openSubPanels[date] = [0];
            }
          }
        });
      }
    },

    openSubPanels: {
      handler(newVal) {
        // iterujemy po wszystkich datach w openSubPanels
        Object.keys(newVal).forEach((date) => {
          // oddzielamy elementy na mapie, ktore pasuja do danej daty
          // od tych, ktore sa z innych dni
          const [mapItemsWithDate, otherMapItems] = this.mapItems.reduce(
            ([forDate, others], item) => {
              const arrayRef = this.$options.filters.formatDateDay(item.plannedDate) === date
                ? forDate : others;
              arrayRef.push(item);

              return [forDate, others];
            },
            [[], []],
          );

          this.mapItems = otherMapItems;

          const readyItems = this.readyItems[date];
          const openSubPanelsLength = this.openSubPanels[date].length;
          const allPanelsOpen = openSubPanelsLength === Object.keys(readyItems).length;
          const noPanelsOpen = openSubPanelsLength === 0;

          if (allPanelsOpen || noPanelsOpen) {
            // dodajemy wszystkie elementy z readyItems do mapItems
            this.mapItems.push(...Object.values(readyItems).flatMap((array) => array));
          } else {
            // wyszukujemy elementy na mapie, ktore pasuja do danego serwisanta
            const filteredItems = mapItemsWithDate.filter((item) => this.openSubPanels[date].some(
              (index) => {
                const readyItem = Object.values(this.readyItems[date])[index][0];
                return item.serviceman.username === readyItem.serviceman.username;
              },
            ));

            // Dodajemy przefiltrowane mapItemsFromDate do mapItems
            this.mapItems.push(...filteredItems);
          }
        });
      },
      deep: true,
    },
  },
  created() {
    this.fetchData();
  },
  mounted() {
    window.vm = this;
    document.addEventListener('click', this.handleClickOutside);
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside);
  },
  methods: {
    async fetchData() {
      this.loading = true;
      try {
        let response = await this.axios.get('/api/service/users?limit=1000');
        this.servicemen = response.data.data;

        response = await this.axios.get('/api/service/visits?status=pending&limit=1000');

        this.allItems = response.data.data;
        this.mapItems = this.allItems;

        const { readyItems, notReadyItems } = this.allItems.reduce(
          (acc, item) => {
            if (item.ready) {
              acc.readyItems.push(item);
            } else {
              acc.notReadyItems.push(item);
            }
            return acc;
          },
          { readyItems: [], notReadyItems: [] },
        );

        this.readyItems = this.groupData(readyItems);
        this.notReadyItems = notReadyItems;
      } catch (error) {
        // console.error(error);
      }
      this.loading = false;
    },
    groupData(data) {
      const sortedData = data.sort((a, b) => {
        if (a.plannedDate === null) return -1;
        if (b.plannedDate === null) return 1;
        return new Date(a.plannedDate) - new Date(b.plannedDate);
      });

      const groupedByPlannedDate = sortedData.reduce((acc, item) => {
        const dateKey = item.plannedDate == null
          ? 'Niezaplanowane' : this.$options.filters.formatDateDay(item.plannedDate);

        if (!acc[dateKey]) {
          acc[dateKey] = [];
        }

        acc[dateKey].push(item);
        return acc;
      }, {});

      Object.keys(groupedByPlannedDate).forEach((date) => {
        const servicemanGrouped = groupedByPlannedDate[date].reduce((acc, item) => {
          const servicemanKey = item.serviceman ? item.serviceman.username : 'Nieprzypisane';

          if (!Object.prototype.hasOwnProperty.call(acc, servicemanKey)) {
            acc[servicemanKey] = [];
          }

          acc[servicemanKey].push(item);
          return acc;
        }, {});

        const sortedServicemanGroups = Object.keys(servicemanGrouped)
          .sort((key) => (key === 'Nieprzypisane' ? -1 : 1))
          .reduce((acc, key) => ({ ...acc, [key]: servicemanGrouped[key] }), {});

        groupedByPlannedDate[date] = sortedServicemanGroups;
      });
      return groupedByPlannedDate;
    },

    getLocationLink(location) {
      const url = `https://www.google.com/maps/search/?api=1&query=${location.lat},${location.lon}`;

      return encodeURI(url);
    },
    openModal(refName) {
      this.$refs[refName].dialog = true;
    },
    openModalArray(refName) {
      this.$refs[refName][0].dialog = true;
    },
    cancelVisit() {
      this.fetchData();
    },
    async submitTask(id, body) {
      await this.axios.post(`/api/service/visit/${id}/tasks`, body);
    },
    onTaskSubmitted(refName) {
      this.$refs[refName].dialog = false;

      this.fetchData();
    },
    onRowClick(item) {
      this.selectedMarker = item;
    },
    onMarkerClick(selectedItem) {
      this.selectedMarker = selectedItem;

      const date = this.formatDate(selectedItem.plannedDate);
      const servicemen = this.readyItems[date];
      if (servicemen) {
        const visits = servicemen[selectedItem.serviceman.username];
        if (visits) {
          const panelIndex = Object.keys(this.readyItems).indexOf(date) + 1;

          if (!this.openPanels.includes(panelIndex)) {
            this.openPanels.push(panelIndex);
          }

          const subPanelIndex = Object.keys(servicemen).indexOf(selectedItem.serviceman.username);
          if (!(date in this.openSubPanels) || !this.openSubPanels[date].includes(subPanelIndex)) {
            this.openSubPanels = {
              ...this.openSubPanels,
              [date]: [subPanelIndex],
            };
          }
        }
      } else if (!this.openPanels.includes(0)) {
        this.openPanels.push(0);
      }
    },
    formatDate(date) {
      return date == null ? '-' : this.$options.filters.formatDateDay(date);
    },
    handleClickOutside(event) {
      const map = this.$refs?.gmap?.$el;
      if (!map) return;

      if (!map.contains(event.target)) {
        const notReadyTableEl = this.$refs.notReadyTable?.$el;
        const readyTables = this.$refs.readyTable;

        if ((notReadyTableEl && notReadyTableEl.contains(event.target))
        || (readyTables && readyTables.some(
          (readyTableRef) => readyTableRef?.$el.contains(event.target),
        ))) {
          return;
        }

        this.selectedMarker = null;
      }
    },
  },
};
</script>

  <style scoped>
  .expanded {
    background-color: white;
  }

  .row-selected {
    background-color: #8fdaff4e;
  }
  </style>
