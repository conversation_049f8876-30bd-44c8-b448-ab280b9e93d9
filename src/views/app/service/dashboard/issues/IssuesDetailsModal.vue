<template>
  <v-dialog
    v-model="dialog"
    fullscreen
    hide-overlay
    style="z-index: 1200"
  >
    <v-card>
      <v-card-title class="title">
        <span class="headline">
          <h5 class="text-uppercase">
            Szczegóły zgłoszenia
          </h5>
        </span>
        <v-spacer />
        <v-btn
          icon
          class="white--text"
          @click.native="dialog = false"
        >
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <div
        v-if="loading"
        class="d-flex justify-center align-center"
        style="height: 100px;"
      >
        <v-progress-circular
          indeterminate
          color="primary"
        />
      </div>
      <v-card-text v-else>
        <v-expansion-panels
          v-model="expandedPanel"
          multiple
          class="my-4"
          flat
          tile
        >
          <v-expansion-panel>
            <v-expansion-panel-header>
              <h3 class="mb-0">
                Lokalizacja i urządzenie
              </h3>
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <v-row>
                <v-col>
                  <location :location="location" />
                </v-col>
                <v-col>
                  <device-basic-details :id="deviceDetails.id" />
                </v-col>
              </v-row>
            </v-expansion-panel-content>
          </v-expansion-panel>
        </v-expansion-panels>

        <!-- 🔁 REPLACE this next v-row with the new layout -->
        <v-row>
          <v-col
            cols="12"
            md="6"
          >
            <IssueBasicInfo :id="Number(id)" />
            <IssueOtherListByLocation
              :id="Number(location.id)"
              :excepted-issue-id="Number(id)"
            />
            <Comments
              :id="Number(id)"
              url="api/service/issue"
            />

            <v-textarea
              v-if="['open', 'reviewed'].includes(data.status)"
              ref="commentInput"
              v-model="comment"
              label="Komentarz"
              :rules="commentRules"
              auto-grow
              clearable
              outlined
              class="my-4"
            />

            <v-row class="mb-4">
              <v-col
                v-if="data.status === 'open'"
                cols="6"
              >
                <v-btn
                  color="progress"
                  block
                  @click="submitReviewed"
                >
                  Zakończenie analizy
                </v-btn>
              </v-col>

              <v-col
                v-if="data.status !== 'completed'"
                cols="6"
              >
                <v-btn
                  color="error"
                  block
                  @click="submitCompleted"
                >
                  Zamknij zgłoszenie
                </v-btn>
              </v-col>
            </v-row>
          </v-col>
          <v-col
            cols="12"
            md="6"
          >
            <v-card>
              <tasks
                :id="Number(data.id)"
                url="api/service/issue"
                :location-id="location.id"
                :device-id="deviceDetails.id"
                :add-enabled="data.status !== 'completed'"
              />
            </v-card>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>
import Tasks from '@components/domain/service/Tasks.vue';
import Location from '@components/domain/service/location/BasicInfo.vue';
import DeviceBasicDetails from '@components/domain/service/device/BasicInfo.vue';
import IssueBasicInfo from '@components/domain/service/issue/BasicInfo.vue';
import IssueOtherListByLocation from '@components/domain/service/issue/OthersListByLocation.vue';
import Comments from '@components/domain/service/Comments.vue';

export default {
  name: 'IssuesDetailsModal',
  components: {
    Comments,
    Location,
    DeviceBasicDetails,
    Tasks,
    IssueBasicInfo,
    IssueOtherListByLocation,
  },
  props: {
    id: {
      type: Number,
      default: null,
      required: true,
    },
    useLocation: {
      type: Boolean,
      default: true,
      required: false,
    },
    deviceData: {
      type: Object,
      default: () => ({}),
    },
    locationData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      loading: true,
      expandedPanel: [0], // This works with 'multiple'
      comment: '',
      isCommentRequired: false,
    };
  },
  computed: {
    deviceDetails() {
      const { device } = this.data;
      if (!device) {
        return {};
      }
      return {
        id: device.id,
        'Numer seryjny': device.serialNumber,
        'Data rozpoczęcia': this.$options.filters.formatDateDay(device.startDate),
        'Koniec gwarancji': this.$options.filters.formatDateDay(device.warrantyEnd),
        Typ: device.type,
        Kod: device.code,
        Właściciel: device.owner?.name,
        'Umowa serwisowa': device.sla?.name || 'brak',
      };
    },
    location() {
      const { location } = this.data;
      if (!location) {
        return {};
      }
      return {
        id: location.id,
        Nazwa: location.name,
        Kraj: location.country,
        Miasto: location.city,
        Adres: location.address,
        Lokalizacja: {
          lat: location.lat,
          lon: location.lon,
        },
      };
    },
    commentRules() {
      return this.isCommentRequired
        ? [(v) => !!v || 'Komentarz jest wymagany']
        : [];
    },
  },
  watch: {
    dialog() {
      this.fetchData();
    },
  },
  methods: {
    async fetchData() {
      this.loading = true;
      try {
        const response = await this.axios.get(`/api/service/issue/${this.id}`);
        this.data = response.data;
      } catch (error) {
        // do nothing
      }
      this.loading = false;
    },
    async submitReviewed() {
      this.isCommentRequired = true;
      await this.$nextTick(); // force re-render of validation rules
      // Validate before submission
      const isValid = await this.$refs.commentInput.validate?.();
      if (!isValid) {
        this.snackbar.showMessage('error', 'Komentarz jest wymagany dla trybu analizy');
        return;
      }
      try {
        await this.axios.post(`/api/service/issue/${this.id}/status/reviewed`, {
          comment: this.comment || '',
        });
        this.snackbar.showMessage('success', 'Zgłoszenie oznaczone jako "Po analizie"');
        window.location.reload();
      } catch (err) {
        this.snackbar.showMessage('error', 'Błąd zmiany statusu zgłoszenia');
      }
    },
    async submitCompleted() {
      this.isCommentRequired = true;
      await this.$nextTick(); // force re-render of validation rules
      const isValid = await this.$refs.commentInput.validate?.();

      if (!isValid) {
        this.snackbar.showMessage('error', 'Komentarz jest wymagany przy zamknięciu zgłoszenia');
        return;
      }

      try {
        await this.axios.post(`/api/service/issue/${this.id}/status/completed`, {
          comment: this.comment.trim(),
        });
        this.snackbar.showMessage('success', 'Zgłoszenie zostało zamknięte');
        window.location.reload();
      } catch (err) {
        this.snackbar.showMessage('error', 'Błąd zmiany statusu zgłoszenia');
      }
    },
  },
};
</script>
