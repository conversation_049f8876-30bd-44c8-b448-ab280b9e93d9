<template>
  <div>
    <v-row>
      <status-filter-bar
        :statuses="statuses"
        @status-change="onStatusChange"
      />
      <v-col>
        <v-text-field
          v-model="search"
          label="Szukaj"
          prepend-inner-icon="mdi-magnify"
          class="ml-6 mr-2 mt-4"
          clearable
          @input="onSearch"
        />
      </v-col>
      <v-col
        cols="auto"
        class="d-flex align-center justify-center"
      >
        <v-btn
          text
          x-small
          fab
          color="green"
          @click="fetchData"
        >
          <v-icon>mdi-cached</v-icon>
        </v-btn>
      </v-col>
    </v-row>
    <v-data-table
      :loading="loading"
      :headers="headers"
      :items="data"
      :expanded.sync="expanded"
      :options.sync="pagination"
      :server-items-length="itemsLength"
      item-key="id"
      show-expand
      class="pa-4"
      :footer-props="{
        'items-per-page-options': customItemsPerPageOptions
      }"
      @click:row="onRowClick"
    >
      <template
        #[`item.device`]="{ item }"
      >
        {{ item.device?.type }} {{ item.device?.serialNumber }} - {{ item.device?.code }}
      </template>
      <template
        #[`item.location`]="{ item }"
      >
        <a
          v-if="item.location"
          :href="getLocationLink(item.location)"
          target="_blank"
          rel="noopener noreferrer"
        >{{ item.location.address }}, {{ item.location.city }}
        </a>
        <div v-else>
          -
        </div>
      </template>
      <template
        #[`item.status`]="{ value }"
      >
        <v-layout justify-start>
          <issue-status :status="value" />
        </v-layout>
      </template>

      <template
        #[`item.tasks`]="{ item }"
      >
        {{ item.tasks?.length ?? 0 }}
      </template>
      <template
        #[`item.data-table-expand`]="{ isExpanded }"
      >
        <v-icon
          class="v-data-table__expand-icon ml-1"
          :class="{ 'v-data-table__expand-icon--active': isExpanded }"
        >
          mdi-chevron-down
        </v-icon>
      </template>
      <template
        #[`item.ctime`]="{ item }"
      >
        {{ item.ctime|formatDateDayTime }}
      </template>
      <template
        #[`item.details`]="{ item }"
      >
        <v-btn
          color="secondary"
          @click.stop
          @click.native="openModal(`IssueDetails_${item.id}`)"
        >
          Szczegóły
        </v-btn>
        <IssueDetailsModal
          :id="item.id"
          :key="`IssueDetails_${item.id}`"
          :ref="`IssueDetails_${item.id}`"
          :device-data="item.device"
          :location-data="item.location"
          @error="message => snackbar.showMessage('error', message)"
        />
      </template>
      <template
        #[`expanded-item`]="{ item }"
      >
        <td
          class="expanded text-center py-4"
          :colspan="headers.length"
        >
          <v-container fluid>
            <v-card
              flat
              outlined
              color="blue-grey lighten-5"
            >
              <v-row>
                <v-col>
                  <h3 class="py-3">
                    Zadania
                  </h3>
                </v-col>
                <v-col cols="auto">
                  <v-tooltip bottom>
                    <template #activator="{ on, attrs }">
                      <v-btn
                        :key="item.id"
                        class="mt-1 mr-2"
                        elevation="1"
                        v-bind="attrs"
                        rounded
                        x-small
                        fab
                        color="primary"
                        v-on="on"
                        @click.stop
                        @click.native="openModal(`AddTask_${item.id}`)"
                      >
                        <v-icon>mdi-plus</v-icon>
                      </v-btn>
                    </template>
                    <span>Dodaj zadanie</span>
                  </v-tooltip>
                </v-col>
              </v-row>
              <v-simple-table>
                <template #default>
                  <thead>
                    <tr>
                      <th class="text-left">
                        ID
                      </th>
                      <th class="text-left">
                        Status
                      </th>
                      <th class="text-left">
                        Kategoria
                      </th>
                      <th class="text-left">
                        Data utworzenia
                      </th>
                      <th class="text-left">
                        Czas docelowy
                      </th>
                      <th class="text-left">
                        Symptom
                      </th>
                      <th class="text-left">
                        Opis
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr
                      v-for="task in item.tasks"
                      :key="task.id"
                    >
                      <td class="text-left">
                        {{ task.id ?? '-' }}
                      </td>
                      <td class="text-left">
                        <v-layout justify-start>
                          <task-status :status="task.status" />
                        </v-layout>
                      </td>
                      <td class="text-left">
                        {{ task.category ?? '-' }}
                      </td>
                      <td class="text-left">
                        {{ task.ctime|formatDateDayTime }}
                      </td>
                      <td class="text-left">
                        {{ task.targetTime|formatDateDayTime }}
                      </td>
                      <td class="text-left">
                        {{ task.symptom ?? '-' }}
                      </td>
                      <td class="text-left">
                        {{ task.description ?? '-' }}
                      </td>
                    </tr>
                  </tbody>
                </template>
              </v-simple-table>
            </v-card>
          </v-container>
        </td>
        <TaskAddEditModal
          :id="item.id"
          :key="`AddTask_${item.id}`"
          :ref="`AddTask_${item.id}`"
          :submit-task="(body) => submitTask(item.id, body)"
          :device-id="item.device.id"
          :location-id="item.location.id"
          @onSubmitted="onTaskSubmitted(`AddTask_${item.id}`)"
          @error="message => snackbar.showMessage('error', message)"
        />
      </template>
    </v-data-table>
  </div>
</template>

<script>
import debounce from 'lodash/debounce';
import IssueDetailsModal from '@/views/app/service/dashboard/issues/IssuesDetailsModal.vue';
import TaskAddEditModal from '@components/domain/service/task/AddEditModal.vue';
import IssueStatus from '@components/domain/service/issue/Status.vue';
import StatusFilterBar from '@components/domain/service/StatusFilterBar.vue';
import TaskStatus from '@components/domain/service/task/Status.vue';

export default {
  name: 'IssuesTab',
  components: {
    TaskStatus,
    StatusFilterBar,
    IssueStatus,
    TaskAddEditModal,
    IssueDetailsModal,
  },
  data() {
    return {
      loading: false,
      expanded: [],
      data: [],
      pagination: {
        page: 1,
        itemsPerPage: 25,
      },
      itemsLength: -1,
      customItemsPerPageOptions: [25, 100, 1000],
      search: '',
      statuses: ['open', 'reviewed', 'completed'],
      selectedStatuses: this.statuses != null && this.statuses.length ? [this.statuses[0]] : ['open'],
      headers: [
        {
          text: 'ID',
          sortable: false,
          align: 'sm-start',
          value: 'id',
        },

        {
          text: 'Urządzenie',
          sortable: false,
          align: 'sm-start',
          value: 'device',
        },
        {
          text: 'Tytuł',
          sortable: false,
          align: 'sm-start',
          value: 'title',
        },
        {
          text: 'Opis',
          sortable: false,
          align: 'sm-start',
          value: 'content',
        },
        {
          text: 'Lokalizacja',
          sortable: false,
          align: 'sm-start',
          value: 'location',
        },
        {
          text: 'Utworzono',
          sortable: false,
          align: 'sm-start',
          value: 'ctime',
        },
        {
          text: 'Status',
          sortable: false,
          align: 'sm-start',
          value: 'status',
        },
        {
          text: 'Priorytet',
          sortable: false,
          align: 'sm-start',
          value: 'priority',
        },
        {
          text: 'Ilość zadań',
          sortable: false,
          align: 'sm-start',
          value: 'tasks',
        },
        {
          text: '',
          sortable: false,
          align: 'sm-end',
          value: 'details',
        },
        { text: '', value: 'data-table-expand' },
      ],
    };
  },
  watch: {
    pagination: {
      handler() {
        this.fetchData();
      },
    },
  },
  created() {
    this.onSearch = debounce((text) => {
      this.search = text;
      this.pagination.page = 1;
      this.fetchData();
    }, 400);

    this.onStatusChange = debounce((newStatuses) => {
      // console.log('newStatuses', newStatuses);
      this.selectedStatuses = newStatuses;
      this.pagination.page = 1;
      this.fetchData();
    }, 700);

    this.fetchData();
  },
  methods: {
    async fetchData() {
      this.loading = true;
      const params = {
        search: this.search,
        page: this.pagination.page,
        limit: this.pagination.itemsPerPage,
      };
      if (this.statuses != null) {
        params.status = (this.selectedStatuses.length) ? this.selectedStatuses.join() : null;
      }
      try {
        const response = await this.axios.get('/api/service/issues', { params });
        this.data = response.data.data;
        this.itemsLength = response.data.count;
      } catch (error) {
        // do nothing
      }
      this.loading = false;
    },
    onRowClick(item, data) {
      data.expand(!data.isExpanded);
    },
    getLocationLink(location) {
      const url = `https://www.google.com/maps/search/?api=1&query=${location.lat},${location.lon}`;

      return encodeURI(url);
    },
    openModal(id) {
      this.$refs[id].dialog = true;
    },
    async submitTask(id, body) {
      await this.axios.post(`/api/service/issue/${id}/tasks`, body);
    },
    onTaskSubmitted(refName) {
      this.$refs[refName].dialog = false;

      this.fetchData();
    },
  },
};
</script>

  <style scoped>
  .expanded {
    background-color: white;
  }
  </style>
