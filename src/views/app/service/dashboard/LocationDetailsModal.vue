<template>
  <v-dialog
    v-model="dialog"
    fullscreen
    hide-overlay
    style="z-index: 1200"
  >
    <v-card>
      <v-card-title class="title">
        <span class="headline">
          <h5 class="text-uppercase">
            Szczegóły zgłoszenia
          </h5>
        </span>
        <v-spacer />
        <v-btn
          icon
          class="white--text"
          @click.native="dialog = false"
        >
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <div
        v-if="loading"
        class="d-flex justify-center align-center"
        style="height: 100px;"
      >
        <v-progress-circular
          indeterminate
          color="primary"
        />
      </div>
      <v-card-text
        v-else
      >
        <v-row>
          <v-col>
            <details-table
              class="my-4"
              header-text="Informacje o zgłoszeniu"
              :data="issueDetails"
            />
            <details-table
              class="my-4"
              header-text="Informacje o urządzeniu"
              :data="deviceDetails"
            />
            <details-table
              class="my-4"
              header-text="Lokalizacja"
              :data="location"
            >
              <template #Lokalizacja="{ value }">
                <a
                  v-if="value"
                  :href="getLocationLink(value)"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Pokaż na mapie
                </a>
                <div v-else>
                  -
                </div>
              </template>
            </details-table>
            <details-list-table
              class="my-4"
              header-text="Zadania"
              :data="data.tasks"
            />
          </v-col>
          <v-col>
            <task-form
              @submit-task="handleTaskSubmit"
            />
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>

export default {
  name: 'LocationDetailsModal',
  components: {

  },
  props: {
    id: {
      type: Number,
      default: null,
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
      loading: true,
      data: {},
    };
  },
  computed: {
    issueDetails() {
      return {
        ID: this.data.id,
        Tytuł: this.data.title,
        Treść: this.data.content,
        Źródło: this.data.source,
        Status: this.data.status,
        Priorytet: this.data.priority,
        'Data utworzenia': this.$options.filters.formatDateDayTime(this.data.ctime),
        'Data zaraportowania': this.$options.filters.formatDateDayTime(this.data.reportTime),
        'Czas docelowy': this.$options.filters.formatDateDayTime(this.data.targetTime),
        'Czas końcowy': this.$options.filters.formatDateDayTime(this.data.endTime),
        'Zaraportowano przez': this.data.reportedBy,
      };
    },
    deviceDetails() {
      const { device } = this.data;

      return {
        ID: device.id,
        'Numer seryjny': device.serialNumber,
        'Data rozpoczęcia': this.$options.filters.formatDateDay(device.startDate),
        'Koniec gwarancji': this.$options.filters.formatDateDay(device.warrantyEnd),
        Typ: device.type,
        Kod: device.code,
      };
    },
    location() {
      const { location } = this.data;

      return {
        Nazwa: location.name,
        Kraj: location.country,
        Miasto: location.city,
        Adres: location.address,
        Lokalizacja: {
          lat: location.lat,
          lon: location.lon,
        },
      };
    },
  },
  watch: {
    dialog() {
      this.fetchData();
    },
  },
  methods: {
    async fetchData() {
      this.loading = true;
      try {
        const response = await this.axios.get(`/api/service/issue/${this.id}`);
        this.data = response.data;
      } catch (error) {
        // do nothing
      }
      this.loading = false;
    },
    getLocationLink(location) {
      const url = `https://www.google.com/maps/search/?api=1&query=${location.lat},${location.lon}`;

      return encodeURI(url);
    },
    async handleTaskSubmit(formData) {
      this.loading = true;
      try {
        const response = await this.axios.post(`/api/service/issue/${this.id}/tasks`, {
          symptom: formData.symptom.isEmpty ? null : formData.symptom,
          description: formData.description.isEmpty ? null : formData.description,
          device: {
            id: formData.device ? this.data.device.id : null,
          },
          location: {
            id: formData.location ? this.data.location.id : null,
          },
        });
        this.data = response.data;

        await this.fetchData();
      } catch (error) {
        // do nothing
      }
      this.loading = false;
    },
  },
};
</script>
