<template>
  <v-dialog
    v-model="dialog"
    fullscreen
    hide-overlay
    style="z-index: 1200"
  >
    <v-card>
      <v-card-title class="title">
        <span class="headline">
          <h5 class="text-uppercase">
            Przypisz wizytę
          </h5>
        </span>
        <v-spacer />
        <v-btn
          icon
          class="white--text"
          @click.native="dialog = false"
        >
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>
      <div
        v-if="loading"
        class="d-flex justify-center align-center"
        style="height: 100px;"
      >
        <v-progress-circular
          indeterminate
          color="primary"
        />
      </div>
      <v-card-text
        v-else
      >
        <v-container>
          <v-card
            flat
            outlined
            color="blue-grey lighten-5 mt-6"
          >
            <h3 class="py-3 text-center">
              Zadania
            </h3>
            <v-simple-table>
              <template #default>
                <thead>
                  <tr>
                    <th class="text-left">
                      Wy<PERSON>rz
                    </th>
                    <th class="text-left">
                      ID
                    </th>
                    <th class="text-left">
                      Status
                    </th>
                    <th class="text-left">
                      Data utworzenia
                    </th>
                    <th class="text-left">
                      Czas docelowy
                    </th>
                    <th class="text-left">
                      Symptom
                    </th>
                    <th class="text-left">
                      Opis
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="task in tasks"
                    :key="task.id"
                    @click="onRowClick(task.id)"
                  >
                    <td class="pl-5">
                      <v-checkbox
                        v-model="selectedTasks"
                        :value="task.id"
                        @click.stop
                      />
                    </td>
                    <td class="text-left">
                      {{ task.id ?? '-' }}
                    </td>
                    <td class="text-left">
                      {{ task.status ?? '-' }}
                    </td>
                    <td class="text-left">
                      {{ task.ctime|formatDateDayTime }}
                    </td>
                    <td class="text-left">
                      {{ task.targetTime|formatDateDayTime }}
                    </td>
                    <td class="text-left">
                      {{ task.symptom ?? '-' }}
                    </td>
                    <td class="text-left">
                      {{ task.description ?? '-' }}
                    </td>
                  </tr>
                </tbody>
              </template>
            </v-simple-table>
          </v-card>
          <v-form @submit.prevent="submit">
            <v-row class="mt-6">
              <v-col class="mr-6">
                <v-autocomplete
                  v-model="selectedServicemanId"
                  :items="servicemen"
                  item-text="username"
                  item-value="id"
                  label="Serwisant"
                  clearable
                />
              </v-col>
              <v-col class="mr-6 mt-2">
                <v-row
                  justify="center"
                  align="center"
                  class="mx-0"
                >
                  <v-menu
                    ref="menu"
                    v-model="datePickerVisible"
                    :close-on-content-click="false"
                    :nudge-right="40"
                    transition="scale-transition"
                    offset-y
                    min-width="auto"
                  >
                    <template #activator="{ on, attrs }">
                      <v-btn
                        color="secondary"
                        dark
                        v-bind="attrs"
                        v-on="on"
                      >
                        Wybierz datę
                      </v-btn>
                    </template>
                    <v-date-picker
                      v-model="selectedDate"
                      @input="saveDate"
                    />
                  </v-menu>
                  <v-text-field
                    v-model="selectedDate"
                    label="Data"
                    :disabled="selectedDate == null"
                    :readonly="selectedDate != null"
                    class="ml-6"
                    clearable
                  />
                </v-row>
              </v-col>
              <v-col
                cols="auto"
                class="mt-2"
              >
                <v-btn
                  color="primary"
                  type="submit"
                >
                  Dodaj wizytę
                </v-btn>
              </v-col>
            </v-row>
          </v-form>
        </v-container>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>

<script>

export default {
  name: 'PlanVisitModal',
  components: {
  },
  props: {
    id: {
      type: Number,
      default: null,
      required: true,
    },
    servicemen: {
      type: Array,
      default: () => [],
      required: true,
    },
    tasks: {
      type: Array,
      default: () => [],
      required: true,
    },
  },
  data() {
    return {
      dialog: false,
      loading: false,
      servicemans: [],
      selectedTasks: [],
      selectedServicemanId: null,
      selectedDate: null,
      datePickerVisible: false,
    };
  },
  computed: {

  },
  watch: {

  },
  methods: {

    async handleTaskSubmit(formData) {
      this.loading = true;
      try {
        const response = await this.axios.post(`/api/service/issue/${this.id}/tasks`, {
          symptom: formData.symptom.isEmpty ? null : formData.symptom,
          description: formData.description.isEmpty ? null : formData.description,
          device: {
            id: formData.device ? this.data.device.id : null,
          },
          location: {
            id: formData.location ? this.data.location.id : null,
          },
        });
        this.data = response.data;

        await this.fetchData();
      } catch (error) {
        // do nothing
      }
      this.loading = false;
    },
    onRowClick(taskId) {
      const index = this.selectedTasks.indexOf(taskId);
      if (index === -1) {
        this.selectedTasks.push(taskId);
      } else {
        this.selectedTasks.splice(index, 1);
      }
    },
    saveDate(value) {
      this.selectedDate = value;
      this.datePickerVisible = false;
    },
    async submit() {
      this.loading = true;
      try {
        await this.axios.post(`/api/service/location/${this.id}/visits`, {
          status: 'pending',
          serviceman: this.selectedServicemanId !== null
            ? { id: this.selectedServicemanId }
            : null,
          plannedDate: this.selectedDate,
          tasks:
            this.selectedTasks.map((taskId) => ({
              id: taskId,
            })),

        });

        this.$emit('onSubmitted');
      } catch (error) {
        // do nothing
      }
      this.loading = false;
    },
  },
};
</script>
