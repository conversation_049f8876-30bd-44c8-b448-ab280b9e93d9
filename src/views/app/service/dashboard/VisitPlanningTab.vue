<template>
  <v-container
    v-if="loading"
    class="d-flex justify-center align-center"
  >
    <v-progress-circular
      indeterminate
      color="primary"
      size="52"
    />
  </v-container>
  <v-row v-else>
    <v-col cols="6">
      <v-row>
        <v-col>
          <v-text-field
            v-model="search"
            :label="$t('common_search')"
            prepend-inner-icon="mdi-magnify"
            class="ml-6 mr-2 mt-4"
            clearable
            @input="onSearch"
          />
        </v-col>
        <v-col
          cols="auto"
          class="d-flex align-center justify-center"
        >
          <v-btn
            text
            x-small
            fab
            color="green"
            @click="fetchData"
          >
            <v-icon>mdi-cached</v-icon>
          </v-btn>
        </v-col>
      </v-row>
      <v-row>
        <v-data-table
          :loading="loading"
          :headers="headers"
          :items="filteredData"
          :expanded.sync="expanded"
          item-key="id"
          show-expand
          hide-default-footer
          class="px-4 pb-4"
          disable-pagination
          @click:row="onRowClick"
        >
          <template
            #[`item.tasks`]="{ item }"
          >
            {{ item.tasks?.length ?? 0 }}
          </template>
          <template
            #[`item.data-table-expand`]="{ item, isExpanded }"
          >
            <v-icon
              v-if="item.tasks.length || item.devices.length"
              class="v-data-table__expand-icon"
              :class="{ 'v-data-table__expand-icon--active': isExpanded }"
            >
              mdi-chevron-down
            </v-icon>
          </template>
          <template
            slot="item.coordinates"
            slot-scope="props"
          >
            <a
              :href="getLocationLink(props.item)"
              target="_blank"
              rel="noopener noreferrer"
            >
              Pokaż na mapie
            </a>
          </template>
          <template
            #[`item.details`]="{ item }"
          >
            <v-btn
              color="secondary"
              @click.stop
              @click.native="openModal(`PlanVisit_${item.id}`)"
            >
              Przypisz
            </v-btn>
            <PlanVisitModal
              :id="item.id"
              :key="`PlanVisit_${item.id}`"
              :ref="`PlanVisit_${item.id}`"
              :servicemen="servicemen"
              :tasks="item.tasks"
              @onSubmitted="onVisitPlanned(item.id)"
            />
          </template>
          <template
            #[`expanded-item`]="{ item }"
          >
            <td
              class="expanded text-center py-4"
              :colspan="headers.length"
            >
              <v-container fluid>
                <v-card
                  flat
                  outlined
                  color="blue-grey lighten-5"
                >
                  <h3 class="py-3">
                    Urządzenia
                  </h3>
                  <v-simple-table>
                    <template #default>
                      <thead>
                        <tr>
                          <th class="text-left">
                            ID
                          </th>
                          <th class="text-left">
                            Numer seryjny
                          </th>
                          <th class="text-left">
                            Typ
                          </th>
                          <th class="text-left">
                            Kod
                          </th>
                          <th class="text-left">
                            Data utworzenia
                          </th>
                          <th class="text-left">
                            Koniec gwarancji
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          v-for="device in item.devices"
                          :key="device.id"
                        >
                          <td class="text-left">
                            {{ device.id ?? '-' }}
                          </td>
                          <td class="text-left">
                            {{ device.serialNumber ?? '-' }}
                          </td>
                          <td class="text-left">
                            {{ device.type ?? '-' }}
                          </td>
                          <td class="text-left">
                            {{ device.code ?? '-' }}
                          </td>
                          <td class="text-left">
                            {{ device.startDate|formatDateDay }}
                          </td>
                          <td class="text-left">
                            {{ device.warrantyEnd|formatDateDay }}
                          </td>
                        </tr>
                      </tbody>
                    </template>
                  </v-simple-table>
                </v-card>
                <v-card
                  flat
                  outlined
                  color="blue-grey lighten-5 mt-6"
                >
                  <h3 class="py-3">
                    Zadania
                  </h3>
                  <v-simple-table>
                    <template #default>
                      <thead>
                        <tr>
                          <th class="text-left">
                            ID
                          </th>
                          <th class="text-left">
                            Status
                          </th>
                          <th class="text-left">
                            Data utworzenia
                          </th>
                          <th class="text-left">
                            Czas docelowy
                          </th>
                          <th class="text-left">
                            Symptom
                          </th>
                          <th class="text-left">
                            Opis
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr
                          v-for="task in item.tasks"
                          :key="task.id"
                        >
                          <td class="text-left">
                            {{ task.id ?? '-' }}
                          </td>
                          <td class="text-left">
                            {{ task.status ?? '-' }}
                          </td>
                          <td class="text-left">
                            {{ task.ctime|formatDateDayTime }}
                          </td>
                          <td class="text-left">
                            {{ task.targetTime|formatDateDayTime }}
                          </td>
                          <td class="text-left">
                            {{ task.symptom ?? '-' }}
                          </td>
                          <td class="text-left">
                            {{ task.description ?? '-' }}
                          </td>
                        </tr>
                      </tbody>
                    </template>
                  </v-simple-table>
                </v-card>
              </v-container>
            </td>
          </template>
        </v-data-table>
      </v-row>
    </v-col>
    <v-col cols="6">
      <gmap-map
        :center="{ lat: 52.29772314949371, lng: 19.35617356845292 }"
        :zoom="6.3"
        :options="{
          zoomControl: true,
          mapTypeControl: false,
          scaleControl: false,
          streetViewControl: false,
          rotateControl: false,
          fullscreenControl: false,
          disableDefaultUi: false
        }"
        style="width: 100%; max-height:550px; height: 100%"
      >
        <gmap-marker
          v-for="item in filteredData"
          :key="item.id"
          :position="{lat: item.lat, lng: item.lon}"
          :clickable="true"
          :draggable="false"
        />
      </gmap-map>
    </v-col>
  </v-row>
</template>

<script>

import PlanVisitModal from '@/views/app/service/dashboard/visit-planning/PlanVisitModal.vue';

export default {
  name: 'VisitPlanningTab',

  components: {
    PlanVisitModal,
  },
  data() {
    return {
      loading: false,
      expanded: [],
      data: [],
      filteredData: [],
      search: null,
      servicemen: [],
      headers: [
        {
          text: 'ID',
          sortable: false,
          align: 'sm-start',
          value: 'id',
        },
        {
          text: 'Nazwa',
          sortable: false,
          align: 'sm-start',
          value: 'name',
        },
        {
          text: 'Miasto',
          sortable: false,
          align: 'sm-start',
          value: 'city',
        },
        {
          text: 'Adres',
          sortable: false,
          align: 'sm-start',
          value: 'address',
        },
        {
          text: 'Kraj',
          sortable: false,
          align: 'sm-start',
          value: 'country',
        },
        {
          text: 'Właściciel',
          sortable: false,
          align: 'sm-start',
          value: 'owner.name',
        },
        {
          text: 'Priorytet',
          sortable: false,
          align: 'sm-start',
          value: 'priority',
        },
        {
          text: 'Ilość zadań',
          sortable: false,
          align: 'sm-start',
          value: 'tasks',
        },
        {
          text: '',
          sortable: false,
          align: 'sm-start',
          value: 'coordinates',
        },
        {
          text: '',
          sortable: false,
          align: 'sm-end',
          value: 'details',
        },
        { text: '', value: 'data-table-expand' },
      ],
    };
  },
  created() {
    this.fetchData();
  },

  methods: {
    async fetchData() {
      this.loading = true;
      try {
        let response = await this.axios.get('/api/service/users?limit=1000');
        this.servicemen = response.data.data;

        response = await this.axios.get('/api/service/locations/pending');
        this.data = response.data.data;

        if (this.search != null) {
          this.onSearch(this.search);
        } else {
          this.filteredData = this.data;
        }
      } catch (error) {
        // do nothing
      }
      this.loading = false;
    },
    onRowClick(item, data) {
      if (item.tasks.length) {
        data.expand(!data.isExpanded);
      }
    },
    getLocationLink(location) {
      const url = `https://www.google.com/maps/search/?api=1&query=${location.lat},${location.lon}`;

      return encodeURI(url);
    },
    onSearch(text) {
      this.search = text === '' ? null : text;

      this.filteredData = text === '' || text === null ? this.data : this.data.filter(
        (item) => item.owner?.name?.toLowerCase()?.includes(this.search.toLowerCase())
        || item.name?.toLowerCase()?.includes(this.search.toLowerCase())
        || item.city?.toLowerCase()?.includes(this.search.toLowerCase())
        || item.address?.toLowerCase()?.includes(this.search.toLowerCase()),
      );
    },
    openModal(id) {
      this.$refs[id].dialog = true;
    },
    onVisitPlanned(id) {
      this.$refs[`PlanVisit_${id}`].dialog = false;

      this.fetchData();
    },
  },
};
</script>

<style scoped>
  .expanded {
    background-color: white;
  }
</style>
