<template>
  <v-card
    class="px-3 py-3"
    outlined
    color="blue-grey lighten-5"
  >
    <slot name="title">
      <h3 class="mb-3">
        {{ headerText }}
      </h3>
    </slot>
    <v-simple-table
      v-if="items.length"
      dense
      class="elevation-3 py-2"
    >
      <template #default>
        <tbody>
          <tr
            v-for="item in items"
            :key="`item-${item.key}`"
          >
            <td
              :key="`itemName-${item.key}`"
              class="border-right text-left"
            >
              {{ item.label }}
            </td>
            <td
              :key="`itemValue-${item.key}`"
              class="text-left"
            >
              <slot
                :name="item.key"
                :value="item.value"
              >
                {{ typeof item.value === 'object' ? JSON.stringify(item.value) : item.value }}
              </slot>
            </td>
          </tr>
        </tbody>
      </template>
    </v-simple-table>
    <v-card-text v-else>
      <v-icon small>
        mdi-alert-circle-outline
      </v-icon>
      <i class="ml-2">{{ $t('common_noData') }}</i>
    </v-card-text>
  </v-card>
</template>

<script>

export default {
  name: 'DetailsTable',
  props: {
    data: {
      type: [Object, Array],
      required: true,
    },
    headerText: {
      type: String,
      default: () => '',
      required: false,
    },
  },
  computed: {
    items() {
      if (typeof this.data === 'undefined') {
        return [];
      }
      const entries = Object.entries(this.data).map(([label, value]) => ({
        key: this.normalizeKey(label),
        label,
        value,
      }));
      // console.table(entries);
      return entries;
    },
  },
  methods: {
    normalizeKey(key) {
      return key
        .toString()
        .replace(/\s+/g, '-') // Replace all whitespace (spaces, tabs, etc.) with dashes
        .replace(/^-+|-+$/g, ''); // Trim leading/trailing dashes
    },
  },
};
</script>
