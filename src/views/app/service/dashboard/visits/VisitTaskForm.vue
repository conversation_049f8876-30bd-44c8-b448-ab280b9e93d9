<template>
  <v-card-text>
    <v-form @submit.prevent="submitForm">
      <v-text-field
        v-model="form.symptom"
        label="Symptom"
        filled
        background-color="white"
        dense
        required
      />

      <v-text-field
        v-model="form.description"
        label="Opis"
        filled
        background-color="white"
        dense
      />

      <v-select
        v-model="form.serviceman"
        label="Serwisant"
        :items="servicemanList"
        item-text="email"
        clearable
        return-object
      />

      <v-row
        justify="center"
        align="center"
        class="mx-0"
      >
        <v-menu
          ref="menu"
          v-model="menu"
          :close-on-content-click="false"
          :nudge-right="40"
          transition="scale-transition"
          offset-y
          min-width="auto"
        >
          <template #activator="{ on, attrs }">
            <v-btn
              color="secondary"
              dark
              v-bind="attrs"
              v-on="on"
            >
              Wy<PERSON>rz datę
            </v-btn>
          </template>
          <v-date-picker
            v-model="selectedDate"
            @input="saveDate"
          />
        </v-menu>
        <v-text-field
          v-model="selectedDate"
          label="Data"
          :disabled="selectedDate == null"
          :readonly="selectedDate != null"
          class="ml-6"
          clearable
        />
      </v-row>

      <v-row
        justify="end"
        class="mt-6"
      >
        <v-btn
          color="primary"
          type="submit"
        >
          Dodaj
        </v-btn>
      </v-row>
    </v-form>
  </v-card-text>
</template>

<script>
export default {
  name: 'VisitTaskForm',
  props: {
    servicemanList: {
      type: Array,
      default: () => [],
      required: true,
    },
  },
  data() {
    return {
      form: this.getInitialFormState(),
      menu: false,
      selectedDate: null,
    };
  },
  computed: {

  },
  methods: {
    getInitialFormState() {
      return {
        symptom: '',
        description: '',
        location: false,
        device: false,
        serviceman: null,
      };
    },
    submitForm() {
      // Emit the form data to the parent component
      this.$emit('submit-task', { ...this.form });
      this.form = this.getInitialFormState();
    },
    cancelVisit() {
      this.$emit('cancel-visit');
    },
    saveDate(value) {
      this.selectedDate = value;
      this.menu = false; // Close the menu after selecting a date
    },
  },
};
</script>

    <style scoped>
    .v-card-title {
      font-weight: bold;
      text-align: center;
    }
    </style>
