<template>
  <v-dialog
    v-model="dialog"
    fullscreen
    style="z-index: 1200"
    :persistent="loading"
  >
    <v-card>
      <v-card-title class="title">
        <span class="headline">
          <h5 class="text-uppercase">
            Edytuj wizytę
          </h5>
        </span>
        <v-spacer />
        <v-btn
          icon
          class="white--text"
          :disabled="loading"
          @click.native="dialog = false"
        >
          <v-icon>mdi-close</v-icon>
        </v-btn>
      </v-card-title>

      <v-container>
        <v-card-text
          class="pt-8"
        >
          <v-form>
            <v-row>
              <v-col>
                <v-autocomplete
                  v-model="selectedServicemanId"
                  :items="servicemen"
                  item-text="username"
                  item-value="id"
                  label="Serwisant"
                  clearable
                />
              </v-col>
              <v-col>
                <v-menu
                  ref="datePickerMenu"
                  v-model="datePickerMenu"
                  :close-on-content-click="false"
                  :nudge-right="40"
                  transition="scale-transition"
                  offset-y
                  min-width="290px"
                >
                  <template #activator="{ on, attrs }">
                    <v-text-field
                      v-model="selectedDate"
                      label="Data wizyty"
                      readonly
                      v-bind="attrs"
                      clearable
                      v-on="on"
                    />
                  </template>
                  <v-date-picker
                    v-model="selectedDate"
                    :disabled="loading"
                    @input="datePickerMenu = false"
                  />
                </v-menu>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>

        <v-card-actions>
          <v-spacer />
          <v-btn
            color="primary"
            class="mb-4"
            :disabled="loading"
            @click="saveChanges"
          >
            <v-progress-circular
              v-if="loading"
              indeterminate
              color="white"
              size="20"
            />
            <span
              v-else
              class="white--text"
            >Zapisz</span>
          </v-btn>
        </v-card-actions>
        <v-card
          flat
          outlined
          color="blue-grey lighten-5 ma-4 "
        >
          <v-row>
            <v-col>
              <h3 class="py-3 text-center">
                Zadania dla wizyty {{ visitTag }}
                <span v-if="deviceData?.serialNumber">
                  dla urządzenia {{ deviceData?.serialNumber }}
                </span>
              </h3>
            </v-col>
            <v-col cols="auto">
              <v-tooltip bottom>
                <template #activator="{ on, attrs }">
                  <v-btn
                    :key="visitId"
                    :device-data="deviceData"
                    :location-data="locationData"
                    class="mt-2 mr-2"
                    elevation="1"
                    v-bind="attrs"
                    rounded
                    x-small
                    fab
                    color="primary"
                    v-on="on"
                    @click.stop
                    @click.native="openModal(`AddTask_${visitId}`)"
                  >
                    <v-icon>mdi-plus</v-icon>
                  </v-btn>
                </template>
                <span>Dodaj zadanie</span>
              </v-tooltip>
              <TaskAddEditModal
                :id="visitId"
                :key="`AddTask_${visitId}`"
                :ref="`AddTask_${visitId}`"
                :device-id="deviceData?.id"
                :location-id="locationData?.id"
                :submit-task="(body) => submitTask(visitId, body)"
                @onSubmitted="onTaskSubmitted(`AddTask_${visitId}`)"
                @error="message => snackbar.showMessage('error', message)"
              />
            </v-col>
          </v-row>
          <v-simple-table>
            <template #default>
              <thead>
                <tr>
                  <th class="text-left">
                    ID
                  </th>
                  <th class="text-left">
                    Status
                  </th>
                  <th class="text-left">
                    Kategoria
                  </th>
                  <th class="text-left">
                    Data utworzenia
                  </th>
                  <th class="text-left">
                    Czas docelowy
                  </th>
                  <th class="text-left">
                    Symptom
                  </th>
                  <th class="text-left">
                    Opis
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="task in tasks"
                  :key="task.id"
                >
                  <td class="text-left">
                    {{ task.id ?? '-' }}
                  </td>
                  <td class="text-left">
                    {{ task.status ?? '-' }}
                  </td>
                  <td class="text-left">
                    {{ task.category ?? '-' }}
                  </td>
                  <td class="text-left">
                    {{ task.ctime|formatDateDayTime }}
                  </td>
                  <td class="text-left">
                    {{ task.targetTime|formatDateDayTime }}
                  </td>
                  <td class="text-left">
                    {{ task.symptom ?? '-' }}
                  </td>
                  <td class="text-left">
                    {{ task.description ?? '-' }}
                  </td>
                </tr>
              </tbody>
            </template>
          </v-simple-table>
        </v-card>
      </v-container>
    </v-card>
  </v-dialog>
</template>

<script>
import TaskAddEditModal from '@components/domain/service/task/AddEditModal.vue';

export default {
  name: 'EditVisitModal',
  components: {
    TaskAddEditModal,
  },
  props: {
    visitId: {
      type: Number,
      required: true,
    },
    visitTag: {
      type: String,
      required: true,
    },
    servicemanId: {
      type: Number,
      default: null,
    },
    plannedDate: {
      type: String,
      default: null,
    },
    servicemen: {
      type: Array,
      default: () => [],
      required: true,
    },
    tasks: {
      type: Array,
      default: () => [],
      required: true,
    },
    deviceData: {
      type: Object,
      default: () => ({}),
    },
    locationData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialog: false,
      loading: false,
      selectedServicemanId: this.servicemanId,
      datePickerMenu: false,
      selectedDate: this.plannedDate != null
        ? this.$options.filters.formatDateDay(this.plannedDate) : null,
    };
  },
  watch: {
    dialog() {
      if (this.dialog) {
        this.selectedDate = this.plannedDate != null
          ? this.$options.filters.formatDateDay(this.plannedDate)
          : null;
      }
    },
  },
  methods: {
    async saveChanges() {
      this.loading = true;
      try {
        const body = {
          serviceman: this.selectedServicemanId !== null
            ? { id: this.selectedServicemanId }
            : null,
          plannedDate: this.selectedDate || null,
        };
        await this.axios.put(`/api/service/visit/${this.visitId}`, body);
        this.$emit('onSaved');
        this.dialog = false;
      } catch (error) {
        // do nothing
      }
      this.loading = false;
    },
    openModal(refName) {
      this.$refs[refName].dialog = true;
    },
    async submitTask(id, body) {
      await this.axios.post(`/api/service/visit/${id}/tasks`, body);
      this.$emit('onSubmitted');
    },
    onTaskSubmitted(refName) {
      // this.fetchData(); // TODO (obecnie nie jest odświeżana lista zadań w tym modalu)
      this.$emit('onTaskSaved');
      this.$refs[refName].dialog = false;
      // this.dialog = false;
    },
  },
};
</script>

<style scoped>
.v-card-title {
  font-weight: bold;
  text-align: center;
}
.v-card-actions {
  justify-content: center;
}
</style>
