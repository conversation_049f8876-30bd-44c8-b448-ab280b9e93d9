<template>
  <service-page
    title="Użytkownicy"
    icon-name="mdi-account"
    api-url="/api/service/users"
    :headers="headers"
    :can-expand="(item) => item.info"
  >
    <template #[`item.name`]="{ item }">
      {{ item.name ?? '-' }}
    </template>
    <template #expanded-item="{ item }">
      <td
        class="expanded text-center pa-4"
        :colspan="headers.length"
      >
        <v-row v-if="item.info">
          <v-container fluid>
            <v-card
              flat
              outlined
              color="blue-grey lighten-5"
            >
              <h3 class="py-3">
                Info
              </h3>
              <div
                v-for="(value, key) in item.info"
                :key="key"
                class="white d-flex justify-space-around py-2"
              >
                <div>
                  {{ key }}
                </div>
                <div>
                  {{ value }}
                </div>
              </div>
            </v-card>
          </v-container>
        </v-row>
      </td>
    </template>
  </service-page>
</template>

<script>
import ServicePage from '@/components/service/ServicePage.vue';

export default {
  name: 'UsersPage',

  components: {
    ServicePage,
  },

  data() {
    return {
      headers: [
        {
          text: 'Użytkownik',
          sortable: false,
          value: 'username',
        },
        { text: '', value: 'data-table-expand' },
      ],
    };
  },
};
</script>

<style scoped>
.expanded {
  background-color: white;
}
</style>
