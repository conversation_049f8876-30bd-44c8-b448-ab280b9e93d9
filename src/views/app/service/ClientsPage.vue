<template>
  <service-page
    title="K<PERSON>nci"
    icon-name="mdi-domain"
    api-url="/api/service/clients"
    :headers="headers"
    :can-expand="(item) => item.devices?.length"
  >
    <template #expanded-item="{ item }">
      <list-container
        title="Urządzenia"
        :headers="detailsHeaders"
        :items="item.devices"
      >
        <template #startDate="{ value }">
          {{ value.startDate|formatDateDay }}
        </template>
        <template #warrantyEnd="{ value }">
          {{ value.warrantyEnd|formatDateDay }}
        </template>
      </list-container>
    </template>
  </service-page>
</template>

<script>
import ServicePage from '@/components/service/ServicePage.vue';
import ListContainer from '@/components/common/containers/ListContainer.vue';

export default {
  name: 'ClientsPage',

  components: {
    ServicePage,
    ListContainer,
  },

  data() {
    return {
      headers: [
        {
          text: 'Id',
          sortable: false,
          value: 'id',
        },
        {
          text: 'Nazwa',
          sortable: false,
          value: 'name',
        },
        { text: '', value: 'data-table-expand' },
      ],
      detailsHeaders: [
        {
          label: 'Id',
          field: 'id',
        },
        {
          label: 'Numer seryjny',
          field: 'serialNumber',
        },
        {
          label: 'Kod',
          field: 'code',
        },
        {
          label: 'Typ',
          field: 'type',
        },
        {
          label: 'Data startu',
          field: 'startDate',
        },
        {
          label: 'Koniec gwaracji',
          field: 'warrantyEnd',
        },
      ],
    };
  },
};
</script>
