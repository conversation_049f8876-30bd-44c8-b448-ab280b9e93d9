<template>
  <div>
    <v-card loading="loader">
      <v-toolbar
        color="secondary"
        dark
        flat
      >
        <v-btn
          icon
          dark
          @click="$router.go(-1)"
        >
          <v-icon>mdi-arrow-left</v-icon>
        </v-btn>
        <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
          {{ $t('admin_usersDetails') }} - {{ user.email }}
        </v-toolbar-title>
        <v-spacer />
        <v-tooltip
          v-if="hasRole('ROLE_SUPERADMIN')"
          bottom
        >
          <template
            #activator="{ on, attrs }"
          >
            <v-btn
              icon
              dark
              v-bind="attrs"
              @click="changeUser(user.id)"
              v-on="on"
            >
              <v-icon>mdi-account-switch</v-icon>
            </v-btn>
          </template>
          <span>{{ $t('actions.switch_user') }}</span>
        </v-tooltip>
        <v-tooltip
          v-if="hasRole('ROLE_SUPERADMIN')"
          bottom
        >
          <template #activator="{ on, attrs }">
            <v-btn
              icon
              dark
              v-bind="attrs"
              @click="openModal"
              v-on="on"
            >
              <v-icon>mdi-pencil</v-icon>
            </v-btn>
          </template>
          <span>{{ $t('actions.edit_user') }}</span>
        </v-tooltip>
      </v-toolbar>
      <v-card-text class="pa-0 mt-0">
        <user-details
          key="user-details-tabs"
          v-bind="{
            user: user,
          }"
        />
      </v-card-text>
    </v-card>
    <user-edit-modal
      ref="editModal"
      :edited-user="user"
      @user-edited="edited"
    />
  </div>
</template>

<script>
import UserEditModal from '@components/admin/user/modal/UserEditModal.vue';
import userDetails from '@components/admin/UserDetails.vue';
import { mapGetters, mapActions } from 'vuex';

export default {
  name: 'UserView',
  components: {
    userDetails,
    UserEditModal,
  },
  async beforeRouteUpdate(to, from, next) {
    this.getData(to.params.id);
    next();
  },
  data() {
    return {
      loader: false,
      user: {
        id: null,
        subscriber: {
          ownerBkf: null,
        },
        timezone: {
          id: null,
        },
        users: null,
        firstname: '',
        lastname: '',
        email: '',
        isOwner: true,
      },
    };
  },
  computed: {
    ...mapGetters({
      hasRole: 'auth/hasRole',
    }),
  },
  mounted() {
    this.getData();
  },
  methods: {
    ...mapActions({
      switchUser: 'auth/switchUser',
    }),
    async changeUser(id) {
      await this.switchUser(id);
      this.$router.replace({ name: 'dashboard' });
      this.$router.go(0);
    },
    edited() {
      this.getData();
    },
    openModal() {
      this.$refs.editModal.dialog = true;
    },
    getData(id) {
      let userId = id;
      if (id === undefined) {
        userId = this.$route.params.id;
      }

      this.loader = true;
      this.axios.get(
        `/administration/user/${userId}`,
      )
        .then((response) => {
          this.user = response.data;
          this.loader = false;
        });
    },
  },
};
</script>
