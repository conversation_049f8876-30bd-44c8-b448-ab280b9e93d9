<template>
  <div>
    <v-card>
      <v-toolbar
        color="secondary"
        dark
        flat
      >
        <v-btn
          icon
          dark
          @click="$router.go(-1)"
        >
          <v-icon>mdi-arrow-left</v-icon>
        </v-btn>
        <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
          {{ $t('admin_subscribersDetails') }} - {{ subscriberId }}
        </v-toolbar-title>
        <v-spacer />
      </v-toolbar>
      <v-card-text class="pa-0 mt-0">
        <subscriber-details
          key="subscriber-details-tabs"
          :subscriber-id="subscriberId"
        />
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import SubscriberDetails from '@components/admin/SubscriberDetails.vue';

export default {
  name: 'SubscriberView',
  components: {
    SubscriberDetails,
  },
  data() {
    return {
      loader: false,
    };
  },
  computed: {
    subscriberId() {
      return parseInt(this.$route.params.id, 10);
    },
  },
  methods: {
    openModal() {
      this.$refs.addModal.dialog = true;
    },
  },
};
</script>
