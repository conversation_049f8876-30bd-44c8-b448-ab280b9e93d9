<template>
  <v-card loading="loader">
    <v-toolbar
      color="secondary"
      dark
      flat
    >
      <v-btn
        icon
        dark
        @click="$router.go(-1)"
      >
        <v-icon>mdi-arrow-left</v-icon>
      </v-btn>
      <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
        {{ $t('admin_details') }} - {{ carwashSn }}
      </v-toolbar-title>
    </v-toolbar>
    <v-card-text class="pa-0 mt-0">
      <carwash-details />
    </v-card-text>
  </v-card>
</template>

<script>
import carwashDetails from '@components/admin/CarwashDetails.vue';

export default {
  name: 'CarwashDetails',
  components: {
    carwashDetails,
  },
  data() {
    return {
      loader: false,
      carwashSn: this.$route.params.serialNumber,
    };
  },
};
</script>
