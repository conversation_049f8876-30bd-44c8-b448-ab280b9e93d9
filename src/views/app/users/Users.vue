<template>
  <v-card>
    <v-toolbar
      color="secondary"
      dark
      flat
    >
      <v-btn
        icon
        dark
      >
        <v-icon>mdi-account-group</v-icon>
      </v-btn>

      <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
        {{ $t('common_users') }}
      </v-toolbar-title>
      <v-spacer />
      <v-tooltip bottom>
        <template #activator="{ on, attrs }">
          <v-btn
            icon
            dark
            v-bind="attrs"
            @click="openModal"
            v-on="on"
          >
            <v-icon>mdi-plus</v-icon>
          </v-btn>
        </template>
        <span>{{ $t('actions.add_user') }}</span>
      </v-tooltip>
    </v-toolbar>
    <users-list ref="userList" />
    <user-add-edit-modal
      ref="addUserDialog"
      :user-number="null"
      action="add"
      @update-user-list="updateUserList"
    />
  </v-card>
</template>

<script>
import UserAddEditModal from '@components/user/UserAddEditModal.vue';
import UsersList from '@components/user/UsersList.vue';

export default {
  name: 'UsersView',
  components: {
    UsersList,
    UserAddEditModal,
  },
  methods: {
    openModal() {
      this.$refs.addUserDialog.dialog = true;
    },
    updateUserList() {
      this.$refs.userList.getData();
    },
  },
};
</script>
