<template>
  <v-card>
    <v-toolbar
      color="secondary"
      dark
      flat
    >
      <v-btn
        icon
        dark
      >
        <v-icon>mdi-cellphone</v-icon>
      </v-btn>
      <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
        {{ $t('common_financeMobilePayments') }}
      </v-toolbar-title>
      <v-spacer />
    </v-toolbar>
    <v-card-text class="pa-0 mt-0">
      <!-- TABS -->
      <v-tabs
        v-model="tabs.current"
        dens
        dark
        background-color="secondary lighten-1"
        :grow="$vuetify.breakpoint.smAndDown"
        :show-arrows="false"
      >
        <v-tab
          v-for="(item) in tabs.tabs"
          :key="item.id"
          ripple
        >
          {{ item.text }}
        </v-tab>
      </v-tabs>

      <!--Tabs contents-->
      <v-tabs-items
        v-model="tabs.current"
        touchless
        class="pa-4"
      >
        <v-tab-item key="mobile-payments-transactions">
          <transactions-list />
        </v-tab-item>
        <v-tab-item key="mobile-payments-invoices">
          <invoices-list />
        </v-tab-item>
      </v-tabs-items>
    </v-card-text>
  </v-card>
</template>

<script>
import InvoicesTab from '@components/loyal-app/franchise/InvoicesTab.vue';
import TransactionsTab from '@components/loyal-app/franchise/TransactionsTab.vue';

export default {
  name: 'FranchisePage',
  components: {
    InvoicesList: InvoicesTab,
    TransactionsList: TransactionsTab,
  },
  data() {
    return {
      tabs: {
        current: 0,
        tabs: [
          {
            text: this.$t('transactions.history'),
            key: 'mobile-payments-transactions',
          },
          {
            text: this.$t('common_invoices'),
            key: 'mobile-payments-invoices',
          },
        ],
      },
    };
  },
};
</script>
