<template>
  <v-card>
    <v-toolbar
      color="secondary"
      dark
      flat
    >
      <v-btn
        icon
        dark
      >
        <v-icon>mdi-group</v-icon>
      </v-btn>
      <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
        {{ $t('common_reports') }}
      </v-toolbar-title>
      <v-spacer />
    </v-toolbar>
    <v-card-text class="pa-0 mt-0">
      <v-tabs
        dens
        dark
        background-color="secondary lighten-1"
      >
        <v-tab
          v-for="(item) in tabs"
          :key="item.key"
          ripple
          :disabled="!item.show"
        >
          {{ item.text }}
        </v-tab>
        <v-tab-item
          v-for="(item) in tabsContent"
          :key="item.key"
          class="pa-4"
        >
          <component
            :is="item.component"
            :ref="`${item.key}`"
            v-bind="item.props"
            @cyclic-report-generate="refreshData()"
          />
        </v-tab-item>
      </v-tabs>
    </v-card-text>
  </v-card>
</template>

<script>
import ReportConfigTable from '@components/reports/config/ReportConfigTable.vue';
import ReportFileTable from '@components/reports/file/ReportFileTable.vue';
import ReportConfiguration from '@components/user/ReportConfig.vue';

export default {
  name: 'ReportsCard',
  components: {
    ReportConfigTable,
    ReportFileTable,
  },
  data() {
    return {
      tabsContent: [
        {
          component: ReportFileTable,
          key: 'reportTable',
          props: { showFiltering: true },
        },
        {
          component: ReportConfigTable,
          key: 'cyclicReportConfiguration',
          props: { showFiltering: true },
        },
        {
          component: ReportConfiguration,
          key: 'reportConfiguration',
          props: { showFiltering: true },
        },
      ],
      applicationError: false,
      snackbar: false,
      snackbarText: '',
      snackbarColor: '',
    };
  },
  computed: {
    tabs() {
      return [
        {
          text: this.$t('finance_header'),
          key: 'reportTable',
          show: true,
        },
        {
          text: this.$t('finance_listHeading'),
          key: 'cyclicReportConfiguration',
          show: true,
        },
        {
          text: this.$t('user_reportsAndNotifications'),
          key: 'reportConfiguration',
          show: true,
        },
      ];
    },
  },
  methods: {
    refreshData() {
      this.$refs.reportTable[0].getData();
    },
  },
};
</script>
