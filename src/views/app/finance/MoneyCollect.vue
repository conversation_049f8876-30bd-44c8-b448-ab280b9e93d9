<template>
  <div>
    <v-card>
      <v-toolbar
        color="secondary"
        dark
        flat
      >
        <v-btn
          icon
          dark
        >
          <v-icon>mdi-bank</v-icon>
        </v-btn>
        <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
          {{ $t('common_moneycollect') }}
        </v-toolbar-title>
        <v-spacer />
      </v-toolbar>
      <v-card-text>
        <carwash-and-date-range-global-filter
          settings-namespace="finance:dates"
          carwash-settings-namespace="finance:carwashes"
          :disabled="loader"
          @onChangeCarwash="changeCarwash"
          @onChangeDates="changeDates"
        />
      </v-card-text>
    </v-card>
    <v-card class="mt-5">
      <v-card-text class="pa-0 mt-0">
        <v-tabs
          v-model="tabs.current"
          class="i2m-card"
          dens
          dark
          background-color="secondary lighten-1"
          :grow="$vuetify.breakpoint.smAndDown"
          :show-arrows="false"
        >
          <v-tab
            v-for="(item) in tabs.tabs"
            :key="item.key"
            ripple
          >
            {{ item.text }}
          </v-tab>
          <v-tabs-items
            v-model="tabs.current"
            class="pr-4 pl-4"
          >
            <v-tab-item key="money-collect-stands">
              <money-collect-stands
                :carwash="carwash"
                :dates="dates"
                @startLoading="loading1 = true"
                @stopLoading="loading1 = false"
              />
            </v-tab-item>
            <v-tab-item key="money-collect-money-changer">
              <money-collect-money-changer
                :carwash="carwash"
                :dates="dates"
                @startLoading="loading2 = true"
                @stopLoading="loading2 = false"
              />
            </v-tab-item>
            <v-tab-item key="money-collect-yeti">
              <money-collect-yeti
                :carwash="carwash"
                :dates="dates"
                @startLoading="loading3 = true"
                @stopLoading="loading3 = false"
              />
            </v-tab-item>
          </v-tabs-items>
        </v-tabs>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import MoneyCollectMoneyChanger from '@components/finance/money-collect/money-changer/MoneyCollectMoneyChanger.vue';
import CarwashAndDateRangeGlobalFilter from '@components/common/filters/CarwashAndDateRangeGlobalFilter.vue';
import MoneyCollectStands from '@components/finance/money-collect/stands/MoneyCollectStands.vue';
import MoneyCollectYeti from '@components/finance/money-collect/yeti/MoneyCollectYeti.vue';

export default {
  name: 'MoneyCollect',
  components: {
    CarwashAndDateRangeGlobalFilter,
    MoneyCollectMoneyChanger,
    MoneyCollectStands,
    MoneyCollectYeti,
  },
  data() {
    return {
      loading1: false,
      loading2: false,
      loading3: false,
      carwash: null,
      dates: null,
      tabs: {
        current: 0,
        tabs: [
          {
            text: this.$t('finance_standMoneyCollections'),
            key: 'money-collect-stands',
          },
          {
            text: this.$t('finance_exchangerMoneyCollections'),
            key: 'money-collect-money-changer',
          },
          {
            text: this.$t('finance_yetiMoneyCollections'),
            key: 'money-collect-yeti',
          },
        ],
      },
    };
  },
  computed: {
    loader() {
      return this.loading1 || this.loading2 || this.loading3;
    },
  },
  methods: {
    changeCarwash(carwash) {
      this.carwash = carwash;
    },
    changeDates(dates) {
      this.dates = dates;
    },
  },
};
</script>

<style>
.v-tabs:not(.v-tabs--vertical):not(.v-tabs--right) >
.v-slide-group--is-overflowing.v-tabs-bar--is-mobile:not(.v-slide-group--has-affixes)
.v-slide-group__prev {
  display: none;
}
</style>
