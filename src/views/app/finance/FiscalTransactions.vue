<template>
  <div>
    <v-card>
      <v-toolbar
        color="secondary"
        dark
        flat
      >
        <v-btn
          icon
          dark
        >
          <v-icon>mdi-cash-register</v-icon>
        </v-btn>
        <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
          {{ $t('menu.finance-fiscaltransactions') }}
        </v-toolbar-title>
        <v-spacer />
      </v-toolbar>
      <v-card-text>
        <v-row>
          <v-col
            cols="8"
          >
            <carwash-and-date-range-global-filter
              settings-namespace="finance:dates"
              carwash-settings-namespace="finance:carwashes"
              :disabled="loader"
              @onChangeCarwash="changeCarwash"
              @onChangeDates="changeDates"
            />
          </v-col>
          <v-col
            cols="4"
          >
            <fiscal-status-filter
              :disabled="loader"
              @onChangeFiscal="changeFiscalStatus"
            />
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    <v-card class="mt-5">
      <v-card-text class="pa-0 mt-0">
        <v-tabs
          v-model="tabs.current"
          class="i2m-card"
          dens
          dark
          background-color="secondary lighten-1"
          :grow="$vuetify.breakpoint.smAndDown"
          :show-arrows="false"
        >
          <v-tab
            v-for="(item) in tabs.items"
            :key="item.key"
            ripple
          >
            {{ item.text }}
          </v-tab>
        </v-tabs>
        <v-tabs-items
          v-model="tabs.current"
          class="pa-4"
        >
          <v-tab-item key="fiscal-transactions-summary">
            <fiscal-summary
              :carwash="carwash"
              :dates="dates"
              :fiscal-status="fiscalStatus"
              @startLoading="loading1 = true"
              @stopLoading="loading1 = false"
            />
          </v-tab-item>
          <v-tab-item key="fiscal-transactions">
            <fiscal-transactions
              :carwash="carwash"
              :dates="dates"
              :fiscal-status="fiscalStatus"
              @startLoading="loading1 = true"
              @stopLoading="loading1 = false"
            />
          </v-tab-item>
          <v-tab-item key="fiscal-transactions-by-day">
            <fiscal-by-day
              :carwash="carwash"
              :dates="dates"
              :fiscal-status="fiscalStatus"
              @startLoading="loading1 = true"
              @stopLoading="loading1 = false"
            />
          </v-tab-item>
          <v-tab-item key="fiscal-devices-status">
            <fiscal-devices-status
              :carwash="carwash"
              @startLoading="loading1 = true"
              @stopLoading="loading1 = false"
            />
          </v-tab-item>
        </v-tabs-items>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>

import FiscalTransactions from '@components/finance/fiscal-transactions/FiscalTransactions.vue';
import FiscalSummary from '@components/finance/fiscal-summary/FiscalSummary.vue';
import FiscalByDay from '@components/finance/fiscal-daily/daily/FiscalDaily.vue';
import CarwashAndDateRangeGlobalFilter
  from '@components/common/filters/CarwashAndDateRangeGlobalFilter.vue';
import FiscalStatusFilter from '@components/common/filters/FiscalStatusFilter.vue';
import FiscalDevicesStatus from '@/components/finance/fiscal-devices-status/FiscalDevicesStatus.vue';

export default {
  name: 'FiscalTransactionsView',
  components: {
    CarwashAndDateRangeGlobalFilter,
    FiscalStatusFilter,
    FiscalSummary,
    FiscalTransactions,
    FiscalByDay,
    FiscalDevicesStatus,
  },
  data() {
    return {
      loading1: false,
      loading2: false,
      loading3: false,
      carwash: null,
      dates: null,
      fiscalStatus: null,
      tabs: {
        current: 0,
        items: [
          {
            text: this.$t('finance_summary'),
            key: 'fiscal-transactions-summary',
          },
          {
            text: this.$t('fiscal_transactions.transactions'),
            key: 'fiscal-transactions',
          },
          {
            text: this.$t('fiscal_transactions.grouped'),
            key: 'fiscal-transactions-by-day',
          },
          {
            text: this.$t('common_carwashes'),
            key: 'fiscal-devices-status',
          },
        ],
      },
    };
  },
  computed: {
    loader() {
      return this.loading1 || this.loading2 || this.loading3;
    },
  },
  methods: {
    changeCarwash(carwash) {
      this.carwash = carwash;
    },
    changeDates(dates) {
      this.dates = dates;
    },
    changeFiscalStatus(fiscalStatus) {
      this.fiscalStatus = fiscalStatus;
    },
  },
};
</script>
