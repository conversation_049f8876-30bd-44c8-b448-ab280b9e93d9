<template>
  <v-card>
    <v-toolbar
      color="secondary"
      dark
      flat
    >
      <v-btn
        icon
        dark
      >
        <v-icon>mdi-currency-usd</v-icon>
      </v-btn>
      <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
        {{ $t('common_financeCarwashRates') }}
      </v-toolbar-title>
      <v-spacer />
    </v-toolbar>
    <v-card-text>
      <div>
        <v-row>
          <v-col
            cols="12"
          >
            <div class="d-flex justify-space-between align-center">
              <div class="d-flex align-center ml-4">
                <div class="label-left">
                  {{ $t('finance_valueForCredit') }} {{ currencySymbol }}
                  /{{ $t('finance_minutes') }}
                </div>
                <v-switch
                  v-model="timeUnit"
                  class="switch ml-2"
                  :disabled="loading"
                />
                <div class="label-right ml-2">
                  {{ $t('finance_time') }}
                </div>
              </div>
              <div class="d-flex align-right ml-4 mr-4">
                <btn-refresh
                  :disabled="loading"
                  @click="getRates"
                />
                <report-create-modal
                  btn-class="ml-2"
                  :params="exportAsyncParams"
                  :disabled="loading"
                  :show-dates="false"
                />
              </div>
            </div>
          </v-col>
        </v-row>
        <v-row>
          <v-col
            cols="12"
            class="pt-0"
          >
            <rates-table
              :currency="currency"
              :rates="rates"
              :time-unit="timeUnit"
              :loader="loading"
            />
          </v-col>
        </v-row>
      </div>
    </v-card-text>
  </v-card>
</template>

<script>
import { mapGetters } from 'vuex';
import RatesTable from '@components/finance/rates/RatesTable.vue';
import BtnRefresh from '@/components/common/button/BtnRefresh.vue';
import ReportCreateModal from '@components/reports/ReportCreateModal.vue';

export default {
  name: 'CarwashRates',
  components: {
    RatesTable,
    BtnRefresh,
    ReportCreateModal,
  },
  data() {
    return {
      rates: [],
      timeUnit: false,
      loading: false,
      currency: '',
    };
  },
  computed: {
    ...mapGetters({
      currencySymbol: 'auth/userCurrencySymbol',
    }),
    exportAsyncParams() {
      return {
        report: 'v2\\FinanceCarwashRates',
        useTimeUnit: this.timeUnit,
      };
    },
  },
  watch: {
    timeUnit() {
      this.getRates();
    },
  },
  mounted() {
    this.getRates();
  },
  methods: {
    async getRates() {
      this.loading = true;
      const response = await this.axios.get('/api/reports/data', {
        params: {
          report: 'v2\\FinanceCarwashRates',
          useTimeUnit: this.timeUnit,
        },
      });

      this.rates = response.data.data;
      this.currency = response.data.currency.symbol;
      this.loading = false;
    },
  },
};
</script>

<style scoped>
.switch-container {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.label-left {
  margin-right: 10px;
}
.label-right {
  margin-left: 10px;
}
</style>
