<template>
  <div>
    <v-card>
      <v-toolbar
        color="secondary"
        dark
        flat
      >
        <v-btn
          icon
          dark
        >
          <v-icon>mdi-chart-bar</v-icon>
        </v-btn>

        <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
          {{ $t('finance_turnoverTitle') }}
        </v-toolbar-title>
        <v-spacer />
      </v-toolbar>
      <v-card-text>
        <bkf-alert
          class="mb-0"
          :show.sync="responseAlert.show"
          :message="responseAlert.message"
          :type="responseAlert.type"
        />
        <carwash-and-date-range-global-filter
          settings-namespace="finance:dates"
          carwash-settings-namespace="finance:carwashes"
          :disabled="loader"
          show-last-collection
          @onChangeCarwash="changeCarwash"
          @onChangeDates="changeDates"
        />
      </v-card-text>
    </v-card>
    <v-card class="mt-5">
      <v-card-text class="pa-0 mt-0">
        <!-- TABS -->
        <v-tabs
          v-model="tabs.current"
          class="i2m-card"
          dens
          dark
          background-color="secondary lighten-1"
          :grow="$vuetify.breakpoint.smAndDown"
        >
          <v-tab
            v-for="(item) in tabs.tabs"
            :key="item.key"
            ripple
          >
            {{ item.text }}
          </v-tab>

          <!--Tabs contents-->
          <v-tabs-items
            v-model="tabs.current"
            touchless
            class="pr-4 pl-4"
          >
            <v-tab-item key="turnover-total">
              <turnover-total
                :carwash="carwash"
                :dates="dates"
                @alert="onAlert"
                @startLoading="loading1 = true"
                @stopLoading="loading1 = false"
              />
            </v-tab-item>
            <v-tab-item key="turnover-monthly">
              <turnover-monthly
                :carwash="carwash"
                :dates="dates"
                @alert="onAlert"
                @startLoading="loading2 = true"
                @stopLoading="loading2 = false"
              />
            </v-tab-item>
          </v-tabs-items>
        </v-tabs>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import TurnoverTotal from '@components/finance/turnover/total/TurnoverTotal.vue';
import TurnoverMonthly from '@components/finance/turnover/monthly/TurnoverMonthly.vue';
import BkfAlert from '@components/common/BkfAlert.vue';
import CarwashAndDateRangeGlobalFilter
  from '@components/common/filters/CarwashAndDateRangeGlobalFilter.vue';

export default {
  name: 'TurnoverPage',
  components: {
    CarwashAndDateRangeGlobalFilter,
    TurnoverMonthly,
    TurnoverTotal,
    BkfAlert,
  },
  data() {
    return {
      loading1: false,
      loading2: false,
      carwash: null,
      dates: null,
      responseAlert: {
        show: false,
        message: '',
        type: 'error',
      },
      tabs: {
        current: 0,
        tabs: [
          {
            text: this.$t('finance_tabsTotal'),
            key: 'turnover-total',
          },
          {
            text: this.$t('finance_detailed'),
            key: 'turnover-monthly',
          },
        ],
      },
    };
  },
  computed: {
    loader() {
      return this.loading1 || this.loading2 || this.loading3;
    },
  },
  methods: {
    changeCarwash(carwash) {
      this.carwash = carwash;
    },
    changeDates(dates) {
      this.dates = dates;
    },
    onAlert(alert, type = 'success') {
      this.responseAlert.message = alert || '';
      this.responseAlert.type = type;
      this.responseAlert.show = !!alert;
    },
  },
};
</script>

<style>

.v-data-table__wrapper table {
  border-collapse: collapse;
}

tr.table-summary {
  border-top: 2px solid #6e7d96;
  text-align: center;
}

.cursor-pointer {
  cursor: pointer;
}

td.tabcell-carwash {
  min-width: 150px;
}

td.tabcell-date {
  min-width: 100px;
}

td.border-right{
  position: relative;
}

td.border-right:before {
  content: '';
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  border-right: 1px solid rgba(0, 0, 0, 0.12);
}
</style>
