<template>
  <v-card>
    <v-toolbar
      color="secondary"
      dark
      flat
    >
      <v-btn
        icon
        dark
      >
        <v-icon>mdi-chart-line</v-icon>
      </v-btn>
      <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
        {{ $t('common_total') }}
      </v-toolbar-title>
      <v-spacer />
    </v-toolbar>
    <v-card-text class="pa-0 mt-0">
      <!-- TABS -->
      <v-tabs
        v-model="current"
        dens
        dark
        background-color="secondary lighten-1"
        :grow="$vuetify.breakpoint.smAndDown"
        :show-arrows="false"
      >
        <v-tab>
          {{ $t('finance_carwashUsage') }}
        </v-tab>
        <v-tab v-if="hasPortalCarwash">
          {{ $t('finance_rolloverUsage') }}
        </v-tab>
        <v-tab v-if="hasPortalCarwash">
          {{ $t('financeRollover_programHistory') }}
        </v-tab>
        <v-tab>
          {{ $t('finance_dosage') }}
        </v-tab>
      </v-tabs>

      <v-tabs-items
        v-model="current"
        class="pa-4"
      >
        <v-tab-item>
          <carwash-programs-usage />
        </v-tab-item>
        <v-tab-item
          v-if="hasPortalCarwash"
        >
          <rollover-programs-usage />
        </v-tab-item>
        <v-tab-item
          v-if="hasPortalCarwash"
        >
          <rollover-program-usage-list />
        </v-tab-item>
        <v-tab-item>
          <dosage />
        </v-tab-item>
      </v-tabs-items>
    </v-card-text>
  </v-card>
</template>

<script>
import CarwashProgramsUsage from '@components/finance/program-usage/program-usage/CarwashProgramsUsage.vue';
import RolloverProgramsUsage from '@components/finance/program-usage/rollover-programs/RolloverProgramsUsage.vue';
import RolloverProgramUsageList
  from '@components/finance/program-usage/rollover-program-list/RolloverProgramUsageList.vue';
import Dosage from '@components/finance/program-usage/dosage/Dosage.vue';
import { mapGetters } from 'vuex';

export default {
  name: 'ProgramsUsage',
  components: {
    RolloverProgramUsageList,
    RolloverProgramsUsage,
    CarwashProgramsUsage,
    Dosage,
  },
  data() {
    return {
      current: null,
    };
  },
  computed: {
    ...mapGetters({
      hasPortalCarwash: 'carwashes/hasPortalCarwash',
    }),
  },
};
</script>
