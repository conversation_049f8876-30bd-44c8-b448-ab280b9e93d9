<template>
  <v-layout
    wrap
  >
    <v-col
      v-if="showError"
      cols="12"
      class="pb-0"
    >
      <v-alert
        :value="true"
        dense
        prominent
        border="left"
        text
        color="error"
        type="error"
      >
        {{ $t('dashboard_noPermission') }}
      </v-alert>
    </v-col>
    <v-col
      v-if="alerts && alerts.length"
      cols="12"
    >
      <alerts-list
        :alerts="alerts"
        @refresh="reloadPage"
      />
    </v-col>
    <no-invoice-data ref="no-invoice-data-alert-dashboard" />
    <template
      v-for="(widgetColumn, widgetColumnIndex) in widgetColumns"
    >
      <v-col
        :key="`widgetColumn${widgetColumnIndex}`"
        cols="12"
        sm="12"
        md="6"
        class="pa-0 px-2"
      >
        <template v-for="(widget, widgetIndex) in widgetColumn">
          <component
            :is="widget.component"
            v-if="!showError"
            v-bind="widget.props"
            :key="`widget${widgetIndex}`"
          />
        </template>
      </v-col>
    </template>
  </v-layout>
</template>

<script>
import widgets from '@components/dashboard/widgets/index';
import noInvoiceDataAlert from '@components/common/NoInvoiceDataAlert.vue';
import { mapGetters } from 'vuex';
import AlertsList from '@components/common/AlertsList.vue';

export default {
  name: 'DashboardView',
  components: {
    AlertsList,
    'no-invoice-data': noInvoiceDataAlert,
  },
  data() {
    return {
      widgetColumns: {
        column1: [
          {
            component: widgets.SubscriptionInfoWidget,
          },
          {
            component: widgets.TurnoverWidget,
          },
          {
            component: widgets.PaymentsShareChartWidget,
          },
          {
            component: widgets.FiscalTransactionsWidget,
          },
          {
            component: widgets.LoyaltySystemWidget,
          },
        ],
        column2: [
          {
            component: widgets.IncomeStandRaceWidget,
          },
          {
            component: widgets.CarwashesListWidget,
          },
          {
            component: widgets.ProgamsUsageWidget,
          },
          {
            component: widgets.MoneycollectListWidget,
          },
        ],
      },
    };
  },
  computed: {
    ...mapGetters({
      hasRole: 'auth/hasRole',
      alerts: 'auth/alerts',
    }),
    showError() {
      return !(this.hasRole('ROLE_CM_ALARMS_AND_TECHNICAL_DATA') && this.hasRole('ROLE_CM_FINANCE'));
    },
  },
  methods: {
    reloadPage() {
      window.location.reload();
    },
  },
};
</script>
