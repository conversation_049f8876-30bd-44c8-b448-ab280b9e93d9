<template>
  <v-card>
    <v-toolbar
      color="secondary"
      dark
      flat
    >
      <v-btn
        icon
        dark
      >
        <v-icon>mdi-tag-heart</v-icon>
      </v-btn>
      <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
        {{ $t('common_service') }}
      </v-toolbar-title>
      <v-spacer />
    </v-toolbar>
    <v-card-text>
      <v-row class="pa-4">
        <v-col>
          <v-row>
            <filtering
              :carwashes="filteringOptions.carwash.carwashes"
              :disabled="loader"
              @change="onFiltersChangePageReset"
            />
          </v-row>
          <v-row>
            <v-col
              cols="12"
              sm="8"
              class="text-sm-start"
            >
              <h2>{{ $t('service_serviceHeading') }}</h2>
            </v-col>
            <v-col
              cols="12"
              sm="4"
              class="d-flex justify-end"
            >
              <btn-refresh
                class="mr-2"
                @click="getData"
              />
            </v-col>
          </v-row>
          <v-row>
            <v-col>
              <v-alert
                dense
                outlined
                border="left"
                type="info"
                dark
                class="text-body-1"
              >
                {{ $t('service_text') }}
                <v-btn
                  small
                  class="ml-2"
                  color="primary"
                  target="_blank"
                  href="//bkfmyjnie.pl/usterka/"
                >
                  <v-icon
                    small
                    left
                    color="white"
                  >
                    mdi-launch
                  </v-icon>
                  {{ $t('service_button') }}
                </v-btn>
              </v-alert>
            </v-col>
          </v-row>
          <v-row>
            <v-col
              cols="12"
              class="pt-0 px-0"
            >
              <service-table
                :items="items"
                :loader="loader"
                :items-total="totalItems"
                :options="filtering.options"
                @change="onFiltersChange"
              />
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>

<script>
import DataFetchMixin from '@components/common/mixins/DataFetchMixin.vue';
import FiltersHandlingMixin from '@components/common/mixins/FiltersHandlingMixin.vue';
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import Filtering from '@components/support/service/Filtering.vue';
import ServiceTable from '@components/support/service/ServiceTable.vue';
import { mapGetters } from 'vuex';

export default {
  name: 'ServicePage',
  components: {
    ServiceTable,
    BtnRefresh,
    Filtering,
  },
  mixins: [
    DataFetchMixin,
    FiltersHandlingMixin,
  ],
  data() {
    return {
      dataUrl: '/api/service_issues',
      filtering: {
        carwash: null,
        options: {
          page: 1,
          itemsPerPage: 25,
        },
        status: 'open',
      },
    };
  },
  computed: {
    ...mapGetters({
      carwashes: 'carwashes/carwashes',
    }),
  },
  mounted() {
    this.filtering.carwash = null;
    this.getData();
  },
  methods: {
    onFiltersChangePageReset(filters) {
      this.filtering.options.page = 1;
      this.onFiltersChange(filters);
    },
    parseApiResponseData(data) {
      const rows = Object.values(data);
      this.items = rows;
    },
    getParams() {
      return {
        params: {
          sn: this.filtering.carwash || null,
          page: this.filtering.options.page || null,
          limit: this.filtering.options.itemsPerPage || null,
          status: this.filtering.status,
        },
      };
    },
  },
};
</script>
