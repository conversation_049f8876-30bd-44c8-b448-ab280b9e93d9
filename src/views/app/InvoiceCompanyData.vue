<template>
  <v-card>
    <v-toolbar
      color="secondary"
      dark
      flat
    >
      <v-btn
        icon
        dark
      >
        <v-icon>mdi-key-variant</v-icon>
      </v-btn>

      <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
        {{ $t('common_dataToInvoice') }}
      </v-toolbar-title>
    </v-toolbar>
    <v-card-text class="pa-0 mt-0">
      <v-tabs
        dens
        dark
        background-color="secondary lighten-1"
      >
        <v-tab
          ripple
        >
          {{ $t('common_clientInvoiceData') }}
        </v-tab>
        <v-tab-item
          class="pr-4 pl-4"
        >
          <invoice-company-data-edit
            :is-editable="canAccess('subscriber', 'write')"
          />
        </v-tab-item>
        <v-tab
          ripple
        >
          {{ $t('invoiceCompanyData_logo') }}
        </v-tab>
        <v-tab-item
          class="pr-4 pl-4"
        >
          <logo-edit
            :is-editable="canAccess('subscriber', 'write')"
          />
        </v-tab-item>
      </v-tabs>
    </v-card-text>
  </v-card>
</template>

<script>
import InvoiceCompanyDataEdit from '@components/user/InvoiceCompanyDataEdit.vue';
import LogoEdit from '@components/user/LogoEdit.vue';
import { mapGetters } from 'vuex';

export default {
  components: {
    InvoiceCompanyDataEdit,
    LogoEdit,
  },
  computed: {
    ...mapGetters({
      canAccess: 'auth/canAccess',
    }),
  },
};
</script>
