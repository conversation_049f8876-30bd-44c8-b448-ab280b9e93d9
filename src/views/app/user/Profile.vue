<template>
  <v-card>
    <v-toolbar
      color="secondary"
      dark
      flat
    >
      <v-btn
        icon
        dark
      >
        <v-icon>mdi-account</v-icon>
      </v-btn>

      <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
        {{ $t('user_siteTitle') }}
      </v-toolbar-title>
    </v-toolbar>
    <v-card-text>
      <v-tabs
        color="grey-darken-1"
        slider-color="primary"
      >
        <v-tab
          v-for="(item) in tabs"
          :key="item.key"
          ripple
          :disabled="!item.show"
        >
          {{ item.text }}
        </v-tab>
        <v-tab-item
          v-for="(item) in tabsContent"
          :key="item.key"
        >
          <template v-if="item.key === 'reportConfiguration'">
            <alerts-list
              class="pt-3"
              :alerts="alerts"
              :dismissible="false"
            />
          </template>
          <component
            :is="item.component"
            v-else
            v-bind="item.props"
          />
        </v-tab-item>
      </v-tabs>
    </v-card-text>
  </v-card>
</template>

<script>
import ProfileConfiguration from '@components/user/Profile.vue';
import BkfAlert from '@components/common/BkfAlert.vue';
import AlertsList from '@components/common/AlertsList.vue';

export default {
  name: 'ProfileConfiguration',
  components: {
    AlertsList,
    BkfAlert,
    ProfileConfiguration,
  },
  data() {
    return {
      alerts: [
        {
          title: `${this.$t('common_viewRelocatedTo')}: ${this.$t('menu_finance')} -> ${this.$t('common_reports')}`,
          level: 'info',
          link: '#/finance/reports',
          linkText: this.$t('common_goToSite'),
        },
      ],
      error: false,
      tabsContent: [
        {
          component: ProfileConfiguration,
          key: 'profileConfiguration',
          props: { showFiltering: true },
        },
        {
          component: null,
          key: 'reportConfiguration',
          props: { showFiltering: true },
        },
      ],
    };
  },
  computed: {
    tabs() {
      return [
        {
          text: this.$t('user_account'),
          key: 'profileConfiguration',
          show: true,
        },
        {
          text: this.$t('user_reportsAndNotifications'),
          key: 'reportConfiguration',
          show: true,
        },
      ];
    },
  },
};
</script>
