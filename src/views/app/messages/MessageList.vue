<template>
  <v-card>
    <v-toolbar
      color="secondary"
      dark
      flat
    >
      <v-btn
        icon
        dark
      >
        <v-icon>mdi-message-text</v-icon>
      </v-btn>

      <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
        Wiadomości
      </v-toolbar-title>
    </v-toolbar>
    <v-card-text>
      <v-tabs
        v-model="tab"
      >
        <v-tab>
          {{ $t('messages_unread') }}
        </v-tab>
        <v-tab>
          {{ $t('messages_read') }}
        </v-tab>
      </v-tabs>
      <v-tabs-items v-model="tab">
        <v-tab-item>
          <no-messages
            v-if="!unread.length"
            :text="$t('common_noUnreadMessages')"
          />
          <v-simple-table v-else>
            <template #default>
              <thead>
                <tr>
                  <th class="text-left w200">
                    {{ $t('messages_when') }}
                  </th>
                  <th class="text-left">
                    {{ $t('messages_message') }}
                  </th>
                  <th class="text-left w50" />
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="(unread_msg, id) in unread"
                  :key="id"
                >
                  <td>{{ unread_msg.created_at|formatDateDayTime }}</td>
                  <td>{{ unread_msg.content.title }}</td>
                  <td>
                    <v-tooltip bottom>
                      <template #activator="{ on, attrs }">
                        <v-btn
                          icon
                          small
                          v-bind="attrs"
                          @click="markRead(unread_msg.id)"
                          v-on="on"
                        >
                          <v-icon>
                            mdi-eye
                          </v-icon>
                        </v-btn>
                      </template>
                      <span>{{ $t('common_markRead') }}</span>
                    </v-tooltip>
                  </td>
                </tr>
              </tbody>
            </template>
          </v-simple-table>
        </v-tab-item>
        <v-tab-item>
          <no-messages
            v-if="!read.length"
            :text="$t('messages_noReadMessages')"
          />
          <v-simple-table v-else>
            <template #default>
              <thead>
                <tr>
                  <th class="text-left w200">
                    {{ $t('messages_when') }}
                  </th>
                  <th class="text-left">
                    {{ $t('messages_message') }}
                  </th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="(read_msg, id) in read"
                  :key="id"
                >
                  <td>{{ read_msg.created_at|formatDateDayTime }}</td>
                  <td>{{ read_msg.content.title }}</td>
                </tr>
              </tbody>
            </template>
          </v-simple-table>
        </v-tab-item>
      </v-tabs-items>
    </v-card-text>
  </v-card>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import NoMessages from '@components/messages/NoMessages.vue';

export default {
  components: {
    NoMessages,
  },
  data() {
    return {
      tab: null,
    };
  },
  computed: {
    ...mapGetters({
      count: 'messages/count',
      unread: 'messages/unread',
      read: 'messages/read',
    }),
  },
  methods: {
    ...mapActions({
      markRead: 'messages/mark_read',
    }),
  },
};
</script>

<style scoped>
  .w200 {
    width: 200px;
  }
  .w50 {
    width: 50px;
  }
</style>
