<template>
  <v-card>
    <v-toolbar
      color="secondary"
      dark
      flat
    >
      <v-btn
        icon
        dark
      >
        <v-icon>mdi-frequently-asked-questions</v-icon>
      </v-btn>
      <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
        {{ $t('contact_title') }}
      </v-toolbar-title>
      <v-spacer />
    </v-toolbar>
    <v-card-text>
      <v-sheet id="inspire">
        <error-report-table ref="errorReportTable" />
        <error-report-form ref="errorReportForm" />
      </v-sheet>
      <v-dialog
        v-model="logout"
        content-class="dialogWidth-1"
      >
        <v-card>
          <v-card-title class="headline">
            {{ $t('contact_header') }}
          </v-card-title>
          <v-card-text>{{ $t('contact_loggedOutMessage') }}</v-card-text>
          <v-card-actions>
            <v-spacer />
            <v-btn
              color="primary"
              text
              @click="logoutFromApplication"
            >
              OK
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
      <v-dialog
        v-model="applicationError"
        content-class="dialogWidth-1"
      >
        <v-card>
          <v-card-title class="headline">
            {{ $t('common_errorHeader') }}
          </v-card-title>
          <v-card-text>{{ $t('contact_message') }}</v-card-text>
          <v-card-actions>
            <v-spacer />
            <v-btn
              color="primary"
              text
              @click="applicationError = false"
            >
              OK
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </v-card-text>
  </v-card>
</template>

<script>
import ErrorReportTable from '@components/support/error-report/ErrorReportTable.vue';
import ErrorReportForm from '@components/support/error-report/ErrorReportForm.vue';

export default {
  name: 'ContactPage',
  components: {
    ErrorReportForm,
    ErrorReportTable,
  },
  data() {
    return {
      logout: false,
      applicationError: false,
      snackbar: false,
      snackbarText: '',
      snackbarColor: '',
    };
  },
  methods: {
    logoutFromApplication() {
      window.location = '/logout';
    },
  },
};
</script>
