<template>
  <v-card>
    <v-toolbar
      color="secondary"
      dark
      flat
    >
      <v-btn
        icon
        dark
      >
        <v-icon>mdi-cellphone-wireless</v-icon>
      </v-btn>
      <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
        {{ $t('common_loyalAppManager') }}
      </v-toolbar-title>
      <v-spacer />
      <div style="width: 300px">
        <v-select
          v-model="currentApp"
          class="pt-6"
          item-text="name"
          :loading="loader"
          :items="apps"
          :label="$t('common_loyalAppManager')"
          dense
          small
          outlined
          return-object
        />
      </div>
    </v-toolbar>
    <v-card-text class="pa-0 mt-0">
      <v-tabs
        dens
        dark
        background-color="secondary lighten-1"
        show-arrows
      >
        <v-tab
          v-for="(item) in tabsContent"
          :key="item.key"
          ripple
        >
          {{ item.text }}
        </v-tab>
        <v-tab-item
          v-for="(item) in tabsContent"
          :key="item.key"
          class="pa-4"
        >
          <component
            :is="item.component"
            v-bind="{
              ...item.props,
              app: currentApp?.name,
            }"
          />
        </v-tab-item>
      </v-tabs>
    </v-card-text>
  </v-card>
</template>

<script>
import LoyalAppPromotionalCodes from '@components/loyal-app/codes/LoyalAppPromotionalCodes.vue';
import LoyalAppPromotionalPackages from '@components/loyal-app/packages/LoyalAppPromotionalPackages.vue';
import LoyalAppSelfInvoices from '@components/loyal-app/self-invoice/LoyalAppSelfInvoices.vue';
import LoyalAppAlerts from '@components/loyal-app/alerts/LoyalAppAlerts.vue';
import LoyalAppReports from '@components/loyal-app/reports/LoyalAppReports.vue';
import LoyalAppStatistics from '@components/loyal-app/statistics/LoyalAppStatistics.vue';
import InvoicesTab from '@/components/loyal-app/tabs/InvoicesTab.vue';
import SubscriptionsTab from '@/components/loyal-app/tabs/SubscriptionsTab.vue';
import ReceiptsTab from '@/components/loyal-app/tabs/ReceiptsTab.vue';
import PaymentsTab from '@/components/loyal-app/tabs/PaymentsTab.vue';
import TransactionsTab from '@/components/loyal-app/tabs/TransactionsTab.vue';
import CarwashesTab from '@/components/loyal-app/tabs/CarwashesTab.vue';
import UsersTab from '@/components/loyal-app/tabs/UsersTab.vue';

export default {
  components: {
    LoyalAppPromotionalCodes,
    LoyalAppPromotionalPackages,
    LoyalAppSelfInvoices,
    LoyalAppAlerts,
    LoyalAppReports,
    LoyalAppStatistics,
    InvoicesTab,
    SubscriptionsTab,
    ReceiptsTab,
    PaymentsTab,
    TransactionsTab,
    CarwashesTab,
    UsersTab,
  },
  data() {
    return {
      loader: true,
      currentApp: null,
      apps: [],
      logout: false,
      applicationError: false,
    };
  },
  computed: {
    tabsContent() {
      return [
        {
          text: this.$t('loyalApp_stats'),
          component: 'loyal-app-statistics',
          key: 'statistics',
          props: {
            showFiltering: true,
            autoUpdateTransactions: true,
          },
        },
        {
          text: this.$t('common_carwashes'),
          component: CarwashesTab,
          key: 'carwashes',
          props: {
            baseUrl: this.baseUrl,
          },
        },
        {
          text: this.$t('loyalApp_usersList'),
          component: UsersTab,
          key: 'user',
          props: {
          },
        },
        {
          text: this.$t('loyalApp_promotionalCodes'),
          component: 'loyal-app-promotional-codes',
          key: 'promotional-codes',
          props: {
            autoLoad: true,
            showFiltering: true,
            autoUpdateTransactions: true,
          },
        },
        {
          text: this.$t('common_invoices'),
          component: InvoicesTab,
          key: 'invoices',
          props: {
          },
        },
        {
          text: this.$t('loyalApp_receipts'),
          component: ReceiptsTab,
          key: 'receipts',
          props: {
          },
        },
        {
          text: this.$t('loyalApp_promotionalPackages'),
          component: 'loyal-app-promotional-packages',
          key: 'promotional-packages',
          props: {
            autoLoad: true,
            showFiltering: true,
            autoUpdateTransactions: true,
          },
        },
        {
          text: this.$t('loyal-app-manager.transactions'),
          component: TransactionsTab,
          key: 'transactions',
          props: {
            autoLoad: true,
            showFiltering: true,
            autoUpdateTransactions: true,
          },
        },
        {
          text: this.$t('loyalApp_payments'),
          component: PaymentsTab,
          key: 'payments',
          props: {
          },
        },
        {
          text: this.$t('loyalApp_selfInvoices'),
          component: 'loyal-app-self-invoices',
          key: 'self-invoices',
          props: {
            autoLoad: true,
            showFiltering: true,
            autoUpdateTransactions: true,
          },
        },
        {
          text: this.$t('loyalApp_loyalappUserAlert'),
          component: 'loyal-app-alerts',
          key: 'alertss',
          props: {
            autoLoad: true,
            showFiltering: true,
            autoUpdateTransactions: true,
          },
        },
        {
          text: this.$t('loyalApp_Reports'),
          component: 'loyal-app-reports',
          key: 'reports',
          props: {
            autoLoad: true,
            showFiltering: true,
            autoUpdateTransactions: true,
          },
        },
        {
          text: this.$t('loyalApp_subscriptions'),
          component: SubscriptionsTab,
          key: 'subscriptions',
          props: {
          },
        },
      ];
    },
    baseUrl() {
      return this.currentApp ? `/api/wla/${this.currentApp?.id}` : null;
    },
  },
  mounted() {
    this.getLoyalApps();
  },
  methods: {
    getLoyalApps() {
      this.axios.get(
        '/api/loyalapp/apps',
      )
        .then((response) => {
          this.apps = response.data;
          this.currentApp = response.data[0] ?? null;
          this.loader = false;
        });
    },
  },
};
</script>
