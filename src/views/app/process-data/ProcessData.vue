<template>
  <v-container
    fluid
    class="px-0 py-0"
  >
    <v-row>
      <v-col>
        <widget
          title-prepend-icon="mdi-thermometer-lines"
          :title="$t('common_processData')"
        >
          <template #titleActions>
            <div
              v-if="updatedAt"
              class="d-inline-block ml-3 text-body-2"
            >
              <span>{{ $t('common_updateTime') }}: </span>
              <span>{{ updatedAt|formatDateDayTimeWithSeconds }}</span>
            </div>
            <div class="d-inline-block ml-3">
              <v-btn
                text
                x-small
                fab
                color="white"
                class="mt-0 mr-2"
                @click="liveData = !liveData"
              >
                <v-icon v-if="liveData">
                  mdi-pause
                </v-icon>
                <v-icon v-else>
                  mdi-play
                </v-icon>
              </v-btn>
              <btn-refresh
                color="white"
                @click="fetchData"
              />
            </div>
          </template>
          <v-container
            fluid
            class="px-0 pb-0"
          >
            <v-row>
              <v-col
                cols="8"
              >
                <v-autocomplete
                  v-model="selectedCarwash"
                  :disabled="loading"
                  :items="carwashes"
                  item-value="serialNumber"
                  item-text="longName"
                  prepend-icon="mdi-car-wash"
                  :label="$t('common_filtersCarwash')"
                  hide-details
                  @change="fetchData"
                />
              </v-col>
              <v-col
                cols="4"
              >
                <date-range-picker
                  key="dateRange"
                  ref="dateRange"
                  prepend-icon="mdi-calendar-range"
                  :show-presets="true"
                  :show-custom="true"
                  :presets="dateRangePresets"
                  :settings-namespace="settingsNamespace"
                  @reload-transaction-list="onDateRangeChange"
                />
              </v-col>
            </v-row>
          </v-container>
        </widget>
      </v-col>
    </v-row>
    <v-row v-if="carwashSelected">
      <v-col
        cols="12"
      >
        <v-tabs
          v-if="Object.keys(categories).length"
          v-model="selectedTab"
          class="i2m-card"
          dens
          dark
          background-color="secondary lighten-1"
        >
          <v-tab
            v-for="(tab, index) in tabs"
            :key="index"
            @click="changeTab(tab.id)"
          >
            {{ tab.name }}
          </v-tab>
          <v-tab-item
            v-for="(tab, index) in tabs"
            :key="index"
          >
            <v-container fluid>
              <v-row v-if="tab.id == 1">
                <v-col v-if="Object.keys(parametersToChart).length">
                  <widget
                    title-prepend-icon="mdi-chart-line-variant"
                    :title="$t('common_chart')"
                  >
                    <line-chart
                      ref="linechart"
                      :items="Object.values(charts)"
                      :loading="loading"
                    />
                  </widget>
                </v-col>
              </v-row>
              <v-row>
                <v-col
                  v-for="(component, componentIndex) in tab.components"
                  :key="componentIndex"
                >
                  <v-row v-if="tab.id == 1">
                    <v-col
                      v-for="(item) in categories"
                      :key="`widget-${item.id}`"
                      cols="12"
                      md="6"
                      lg="4"
                    >
                      <widget
                        :title="item.name"
                        :title-prepend-icon="item.icon"
                      >
                        <process-category-table
                          :items="item.items"
                          :loader="!item.items.length"
                          :parameters-to-chart="parametersToChart"
                          @addParameterToChart="addParameterToChart"
                        />
                      </widget>
                    </v-col>
                  </v-row>
                  <v-card
                    v-if="tab.id == 2"
                  >
                    <v-toolbar
                      color="secondary"
                      dark
                      flat
                      height="46"
                    >
                      <v-btn
                        icon
                        dark
                      >
                        <v-icon>mdi-alarm</v-icon>
                      </v-btn>
                      <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
                        {{ $t('common_alarmActive') }}
                      </v-toolbar-title>
                      <v-spacer />
                    </v-toolbar>
                    <alarm-active-list
                      :alarms="selectedCarwashAlarms"
                      :loading="alarmsLoading"
                    />
                  </v-card>
                  <v-card
                    v-if="tab.id == 2"
                  >
                    <v-toolbar
                      color="secondary"
                      class="mt-5"
                      dark
                      flat
                      height="46"
                    >
                      <v-btn
                        icon
                        dark
                      >
                        <v-icon>mdi-alarm</v-icon>
                      </v-btn>
                      <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
                        {{ $t('common_alarmHistory') }}
                      </v-toolbar-title>
                      <v-spacer />
                    </v-toolbar>
                    <alarm-history-list
                      ref="historyList"
                      :selected-carwash="selectedCarwash"
                      :dates="dates"
                      :trigger-get-data="triggerGetData"
                      @resetTriggerGetData="onResetTriggerGetData"
                    />
                  </v-card>
                </v-col>
              </v-row>
            </v-container>
          </v-tab-item>
        </v-tabs>
      </v-col>
    </v-row>
    <v-row v-if="!Object.keys(categories).length">
      <v-col
        cols="12"
      >
        <v-card>
          <v-card-text>
            {{ $t('common_noData') }}
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import ProcessCategoryTable from '@components/process-data/ProcessCategoryTable.vue';
import Widget from '@components/common/Widget.vue';
import BtnRefresh from '@components/common/button/BtnRefresh.vue';
import { mapGetters } from 'vuex';
import moment from 'moment';
import LineChart from '@components/process-data/ProcessCategoryChart.vue';
import DateRangePicker from '@components/common/DateRangePicker.vue';
import AlarmHistoryList from '@components/alarm-history/AlarmHistoryList.vue';
import AlarmActiveList from '@components/alarm-active/AlarmActiveList.vue';
import {
  endOfToday,
  endOfYesterday,
  startOfDay, startOfToday,
  startOfYesterday,
  subDays,
} from 'date-fns';

export default {
  name: 'ProcessData',
  components: {
    BtnRefresh,
    DateRangePicker,
    Widget,
    LineChart,
    AlarmHistoryList,
    AlarmActiveList,
    ProcessCategoryTable,
  },
  data() {
    return {
      settingsNamespace: 'process-data:chart:date',
      dateRangePresets: [
        {
          value: 'today',
          text: this.$t('common_today'),
          start: startOfToday(),
          end: endOfToday(),
          default: true,
        },
        {
          value: 'yesterday',
          text: this.$t('common_daterangeYesterday'),
          start: startOfYesterday(),
          end: endOfYesterday(),
          default: true,
        },
        {
          value: 'last7Days',
          text: this.$t('common_p7d'),
          start: startOfDay(subDays(new Date(), 6)),
          end: endOfToday(),
        },
        {
          value: 'last14Days',
          text: this.$t('common_p14d'),
          start: startOfDay(subDays(new Date(), 13)),
          end: endOfToday(),
        },
      ],
      icons: {
        100: 'mdi-desktop-classic', // Komputer
        210: 'mdi-coolant-temperature', // Ogrzewanie
        220: 'mdi-water-boiler', // Kocioł
        230: 'mdi-car-wash', // Stanowiska
        240: 'mdi-sync', // Cyrkulacja
        300: 'mdi-water', // Woda
        310: 'mdi-gradient-vertical', // Osmoza
        400: 'mdi-flask-outline', // Chemia
      },
      liveData: true,
      loading: false,
      dataFetchInterval: null,
      dataFetchIntervalTimeout: 60000,
      selectedCarwash: null,
      items: [],
      updatedAt: null,
      chartParamData: [],
      dateMenu: false,
      parametersToChart: [],
      select: { id: 1, title: this.$t('common_now'), period: 'now' },
      dateFrom: '',
      dateTo: '',
      period: 'now',
      selectedTab: 0,
      tabs: [
        {
          name: this.$t('common_processData'),
          id: 1,
          components: [
            { name: ProcessCategoryTable },
          ],
        },
        {
          name: this.$t('common_alarms'),
          id: 2,
          components: [
            { name: AlarmActiveList },
          ],
        },
      ],
      triggerGetData: false,
      dates: [],
      selectedCarwashAlarms: [],
      alarmsLoading: false,
    };
  },
  computed: {
    ...mapGetters({
      carwashes: 'carwashes/carwashes',
    }),
    categories() {
      const categories = {};
      if (this.items) {
        this.items.forEach((param) => {
          if (!param.category.categoryDescription) {
            return;
          }
          if (!(param.category.categoryDescription in categories)) {
            categories[param.category.categoryDescription] = {
              id: param.category.categoryId,
              name: param.category.categoryDescription,
              description: param.category.categoryDescription,
              icon: this.icons[param.category.categoryId] || 'mdi-clipboard-list-outline',
              items: [],
            };
          }
          categories[param.category.categoryDescription].items.push(param);
        });
      }
      return Object.values(categories);
    },
    carwashSelected() {
      return this.selectedCarwash !== null;
    },
    charts() {
      const groupedArray = this.chartParamData.reduce((result, item) => {
        const { items, unit, name } = item;
        if (!result[unit]) {
          // eslint-disable-next-line no-param-reassign
          result[unit] = [];
        }
        result[unit].push({ items, name, unit });
        return result;
      }, {});
      return groupedArray;
    },
  },
  watch: {
    liveData() {
      this.setDataFetchInterval();
    },
  },
  mounted() {
    this.selectedCarwash = null;
    if (this.carwashes.length > 0) {
      this.selectedCarwash = this.carwashes[0].serialNumber;
    }
    this.fetchData();
  },
  beforeMount() {
    this.setDataFetchInterval();
  },
  beforeDestroy() {
    clearInterval(this.dataFetchInterval);
  },
  methods: {
    async fetchData() {
      if (this.carwashSelected) {
        this.loading = true;
        this.chartParamData = [];
        const data = await this.axios.get(`/cm_new/param/?sn=${this.selectedCarwash}`);
        this.items = data.data.data;
        this.updatedAt = moment().format('YYYY-MM-DD HH:mm:ss');
        if (this.parametersToChart && this.parametersToChart.length) {
          await this.fetchParamsHistory();
        } else {
          this.loading = false;
        }
        this.triggerGetData = true;
        await this.fetchActiveAlams();
      }
    },
    async fetchActiveAlams() {
      this.selectedCarwashAlarms = [];
      if (this.carwashSelected) {
        this.alarmsLoading = true;
        await this.axios.get(
          `/cm/alarm/by_serials/${this.selectedCarwash}`,
        )
          .then((response) => {
            const object = response.data.shift();
            this.selectedCarwashAlarms = object.alarms;
            this.alarmsLoading = false;
          });
      }
    },
    getParams() {
      return {
        params: {
          sn: this.filtering.carwash,
        },
      };
    },
    addParameterToChart(param) {
      const searchIndex = this.parametersToChart.findIndex((parameter) => parameter.id
        === param.id);
      if (searchIndex === -1) {
        this.parametersToChart.push({ id: param.id, unit: param.unit, name: param.name });
      } else {
        this.parametersToChart.splice(searchIndex, 1);
      }
      this.fetchParamsHistory();
    },
    async fetchParamsHistory() {
      if (!this.carwashSelected) {
        return;
      }

      this.loading = true;
      await this.axios.post(
        `/cm_new/param-history?sn=${this.selectedCarwash}`,
        {
          date_from: this.dateFrom,
          date_to: this.dateTo,
          params: JSON.stringify(this.parametersToChart.map((a) => a.id)),
        },
      )
        .then(
          (response) => {
            if (response.status === 200) {
              this.chartParamData = [];
              response.data.forEach((paramData, index) => {
                this.chartParamData.push({
                  items: paramData,
                  unit: this.parametersToChart[index].unit,
                  name: this.parametersToChart[index].name,
                });
              });
            }
            this.loading = false;
          },
        )
        .catch(() => {
        });
    },
    onDateRangeChange({
      from,
      to,
    }) {
      const dateFromArray = from.split(' ');
      const dateFrom = dateFromArray[0];
      this.dateFrom = dateFrom;
      const dateToArray = to.split(' ');
      const dateTo = dateToArray[0];
      this.dateTo = dateTo;
      this.dates = [this.dateFrom, this.dateTo];
      this.fetchParamsHistory();
    },
    changeTab(tab) {
      if (this.parametersToChart.length && tab === 1) {
        this.fetchParamsHistory();
      }
    },
    onResetTriggerGetData() {
      this.triggerGetData = false;
    },
    setDataFetchInterval() {
      clearInterval(this.dataFetchInterval);
      if (this.liveData) {
        this.dataFetchInterval = setInterval(this.fetchData, this.dataFetchIntervalTimeout);
      }
    },
  },
};
</script>
