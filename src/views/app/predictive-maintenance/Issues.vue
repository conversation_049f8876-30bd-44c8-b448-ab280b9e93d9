<template>
  <v-card>
    <v-toolbar
      color="secondary"
      dark
      flat
    >
      <v-btn
        icon
        dark
      >
        <v-icon>mdi-alarm</v-icon>
      </v-btn>
      <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
        {{ $t('common_alarmManagement') }}
      </v-toolbar-title>
    </v-toolbar>
    <v-card-text>
      <v-container
        grid-list-md
        text-sm-center
        fluid
        main-container
      >
        <v-row no-gutters>
          <v-col>
            <v-text-field
              v-model="searchFilter"
              :label="$t('common_search')"
              prepend-inner-icon="mdi-magnify"
              class="mr-2"
            />
          </v-col>
          <v-col cols="1">
            <v-btn
              color="secondary"
              class="ml-2 mt-3"
              @click="onRefreshClick"
            >
              <v-icon>mdi-refresh</v-icon>
            </v-btn>
          </v-col>
        </v-row>
        <v-row
          no-gutters
          justify="center"
        >
          <v-col>
            <v-progress-circular
              v-if="loading"
              indeterminate
              class="mt-8 mb-5"
              color="primary"
            />
            <v-container v-else>
              <issues-stats
                :stats="stats"
                :filter-incidents="filterIncidents"
                :selected-alarm-levels="selectedAlarmLevels"
                :selected-owners="selectedOwners"
                @incidents-filter-changed="onIncidentsFilterChanged"
                @selected-alarm-levels-changed="onSelectedAlarmLevelsChanged"
                @selected-owners-changed="onSelectedOwnersChanged"
              />
              <alarms-list
                v-if="filteredData.length"
                :data="filteredData"
                :issue-groups="selectedIssueGroups"
                @action-performed="fetchData()"
              />
              <p
                v-else
                class="pt-12"
              >
                {{ $t('common_noData') }}
              </p>
            </v-container>
          </v-col>
        </v-row>
      </v-container>
    </v-card-text>
  </v-card>
</template>

<script>
import AlarmsList from '@components/predictive-maintenance/AlarmsList.vue';
import IssuesStats from '@/components/predictive-maintenance/IssuesStats.vue';
import alarmLevels from '@components/predictive-maintenance/AlarmLevels';

export default {
  name: 'IssuesPage',
  components: {
    AlarmsList,
    IssuesStats,
  },
  data() {
    return {
      alarmLevels,
      loading: false,
      data: [],
      stats: {},
      selectedAlarmLevels: [],
      issueGroups: [
        {
          name: this.$t('common_new'),
          value: 'new',
        },
        {
          name: this.$t('common_open'),
          value: 'open',
        },
        {
          name: this.$t('common_duedate'),
          value: 'duedate',
        },
        {
          name: this.$t('common_jr'),
          value: 'JR',
        },
      ],
      selectedIssueGroups: ['new', 'open'],
      searchFilter: '',
      selectedOwners: [],
      filterIncidents: false,
    };
  },
  computed: {
    filteredData() {
      return this.data.filter((item) => {
        let incidentsFilter = this.filterIncidents ? item.incidents.length > 0 : true;
        let alarmFilter = this.selectedAlarmLevels.length
          ? this.selectedAlarmLevels.includes(item.level.name) : true;

        // jezeli włączone filtrowanie zdarzeń i po poziomie jednocześnie
        // to "przepuszczamy" jeeli filtr zdarzeń LUB poziomu jest true
        if (this.selectedAlarmLevels.length > 0 && this.filterIncidents) {
          incidentsFilter = incidentsFilter || alarmFilter;
          alarmFilter = incidentsFilter;
        }

        // console.log('incidentsFilter', incidentsFilter);
        // console.log('alarmFilter', alarmFilter);

        const searchFilter = (`${item.product} ${item.name} ${item.sn} ${item.address}`).toLowerCase().includes(this.searchFilter.toLocaleLowerCase());
        const ownersFilter = this.selectedOwners.length
          ? this.selectedOwners.includes(item.owner.id) : true;

        // console.log('ownersFilter', ownersFilter);

        return incidentsFilter && alarmFilter && searchFilter && ownersFilter;
      });
    },
  },
  mounted() {
    this.fetchData();
  },
  methods: {
    async fetchData() {
      this.loading = true;

      const response = await this.axios.get(
        '/administration/carwashissues',
        {
          params: {
            groups: this.selectedIssueGroups.join(),
          },
        },
      );

      this.data = Object.values(response.data.data).map((value) => value);

      this.stats = response.data.stats;
      this.loading = false;
    },
    onIncidentsFilterChanged() {
      this.filterIncidents = !this.filterIncidents;
    },
    onSelectedAlarmLevelsChanged(name) {
      const index = this.selectedAlarmLevels.indexOf(name);
      if (index < 0) {
        this.selectedAlarmLevels.push(name);
      } else {
        this.selectedAlarmLevels.splice(index, 1);
      }
    },
    onSelectedOwnersChanged(id) {
      const index = this.selectedOwners.indexOf(id);
      if (index < 0) {
        this.selectedOwners.push(id);
      } else {
        this.selectedOwners.splice(index, 1);
      }
    },
    onRefreshClick() {
      this.resetFilters();
      this.fetchData();
    },
    resetFilters() {
      this.filterIncidents = false;
      this.selectedOwners = [];
      this.selectedAlarmLevels = [];
    },
  },
};
</script>
