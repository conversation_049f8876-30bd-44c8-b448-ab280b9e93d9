<template>
  <v-card>
    <v-toolbar
      color="secondary"
      dark
      flat
    >
      <v-btn
        icon
        dark
      >
        <v-icon>mdi-alarm</v-icon>
      </v-btn>

      <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
        {{ $t('predictiveMaintenance_alarmsHistory') }}
      </v-toolbar-title>
    </v-toolbar>
    <v-card-text>
      <issues-archive
        selected-group="close"
      />
    </v-card-text>
  </v-card>
</template>

<script>
import IssuesArchive from '@components/admin/IssuesArchive.vue';

export default {
  name: 'AdminCarwashes',
  components: {
    IssuesArchive,
  },
};
</script>
