<template>
  <div>
    <v-card>
      <v-toolbar
        color="secondary"
        dark
        flat
      >
        <v-btn
          icon
          dark
        >
          <v-icon>mdi-list-box</v-icon>
        </v-btn>
        <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
          Wszystkie problemy
        </v-toolbar-title>
      </v-toolbar>
      <v-card-text>
        <v-container
          grid-list-md
          text-sm-center
          fluid
          main-container
        >
          <v-row
            no-gutters
            class="mb-6"
          >
            <v-col cols="3">
              <v-select
                v-model="selectedIssueGroups"
                :label="$t('common_status')"
                :items="issuesGroups"
                prepend-inner-icon="mdi-group"
                item-value="value"
                item-text="name"
                multiple
                chips
                small-chips
                clearable
                class="mr-6"
                @input="fetchDataDebounced"
              />
            </v-col>
            <v-col>
              <v-text-field
                v-model="search"
                :label="$t('common_search')"
                prepend-inner-icon="mdi-magnify"
                class="mr-2"
                clearable
                @input="onSearch"
              />
            </v-col>
            <v-col cols="1">
              <v-btn
                color="secondary"
                class="ml-2 mt-3"
                @click="fetchData"
              >
                <v-icon>mdi-refresh</v-icon>
              </v-btn>
            </v-col>
          </v-row>
          <v-row
            no-gutters
            justify="center"
          >
            <v-col>
              <v-data-table
                :loading="loading"
                :headers="tableHeaders"
                :items="data"
                :expanded.sync="expanded"
                :server-items-length="dataLength"
                :options.sync="pagination"
                :footer-props="footerProps"
                item-key="id"
                show-expand
                @click:row="onRowClick"
              >
                <template #[`item.alarmDefinition`]="{ item }">
                  {{ `${item.alarmDefinition.id} - ${item.alarmDefinition.text}` }}
                </template>

                <template #[`item.level`]="{ item }">
                  <v-tooltip
                    v-if="item.level?.name != null"
                    bottom
                  >
                    <template #activator="{ on }">
                      <v-icon
                        class="pr-2"
                        :color="alarmLevels[item.level.name].color"
                        v-on="on"
                      >
                        {{ alarmLevels[item.level.name].icon }}
                      </v-icon>
                    </template>
                    <span>
                      {{ item.level.name }}
                    </span>
                  </v-tooltip>
                </template>

                <template #[`item.carwash.owner`]="{ item }">
                  <v-tooltip bottom>
                    <template #activator="{ on, attrs }">
                      <img
                        v-if="item.carwash.owner.logo"
                        v-bind="attrs"
                        :src="item.carwash.owner.logo"
                        alt="Image"
                        style="max-height: 35px; width: auto;"
                        v-on="on"
                      >
                      <v-icon
                        v-else
                        size="35"
                        v-bind="attrs"
                        v-on="on"
                      >
                        mdi-help
                      </v-icon>
                    </template>
                    <span>{{ item.carwash.owner.name }}</span>
                  </v-tooltip>
                </template>

                <template #[`item.etime`]="{ item }">
                  {{ item.etime ?? '-' }}
                </template>

                <template #expanded-item="{ headers, item }">
                  <td
                    class="expanded text-center pa-4"
                    :colspan="headers.length"
                  >
                    <v-row>
                      <v-col>
                        <div><b>Komentarz</b></div>
                        <div>{{ item.comment ?? '-' }}</div>
                      </v-col>
                      <v-col>
                        <div><b>Użytkownik</b></div>
                        <div>{{ item.lastUser ?? '-' }}</div>
                      </v-col>
                    </v-row>
                  </td>
                </template>
              </v-data-table>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import alarmLevels from '@components/predictive-maintenance/AlarmLevels';
import debounce from 'lodash/debounce';

export default {
  name: 'IssuesList',

  data() {
    return {
      alarmLevels,
      loading: false,
      expanded: [],
      data: [],
      dataLength: -1,
      pagination: {
        page: 1,
        itemsPerPage: 25,
        // sortBy: ['ctime'],
        // sortDesc: [true],
      },
      search: null,
      selectedIssueGroups: [],
      issuesGroups: [
        {
          name: this.$t('common_new'),
          value: 'new',
        },
        {
          name: this.$t('common_open'),
          value: 'open',
        },
        {
          name: this.$t('common_duedate'),
          value: 'duedate',
        },
        {
          name: this.$t('common_jr'),
          value: 'JR',
        },
        {
          name: this.$t('common_close'),
          value: 'close',
        },
      ],
      footerProps: {
        'items-per-page-options': [25, 50, 100],
        'items-per-page-text': this.$t('common_rowsPerPage'),
      },
      tableHeaders: [
        {
          text: 'ID',
          sortable: false,
          align: 'sm-start',
          value: 'id',
        },
        {
          text: 'Poziom problemu',
          sortable: false,
          align: 'center',
          value: 'level',
        },
        {
          text: 'Nazwa myjni',
          sortable: false,
          value: 'carwash.name',
        },
        {
          text: 'Status',
          sortable: false,
          value: 'status',
          align: 'center',
        },
        {
          text: 'Opis problemu',
          sortable: false,
          value: 'alarmDefinition',
        },
        {
          text: 'Data utworzenia',
          sortable: false,
          value: 'ctime',
          align: 'center',
        },
        {
          text: 'Data zakończenia',
          sortable: false,
          value: 'etime',
          align: 'center',
        },
        {
          text: 'Właściciel',
          sortable: false,
          value: 'carwash.owner',
          align: 'sm-center',
        },
        { text: '', value: 'data-table-expand' },
      ],
    };
  },

  watch: {
    pagination: {
      handler(newValue, oldValue) {
        if (oldValue.page !== newValue.page
          || oldValue.itemsPerPage !== newValue.itemsPerPage) {
          this.fetchData();
        }
      },
      deep: true,
    },
  },

  created() {
    this.onSearch = debounce((text) => {
      this.search = text === '' ? null : text;
      this.fetchData();
    }, 700);

    this.fetchDataDebounced = debounce(() => {
      this.fetchData();
    }, 700);
  },

  mounted() {
    this.fetchData();
  },

  methods: {
    async fetchData() {
      this.loading = true;
      const response = await this.axios.get(
        '/administration/carwashissues/list',
        {
          params: {
            page: this.pagination.page,
            perPage: this.pagination.itemsPerPage,
            search: this.search,
            groups: (this.selectedIssueGroups.length) ? this.selectedIssueGroups.join() : null,
          },
        },
      );

      this.data = response.data.data;
      this.dataLength = response.data.total;
      this.loading = false;
    },
    onRowClick(item, data) {
      data.expand(!data.isExpanded);
    },
  },
};
</script>

<style scoped>
.expanded {
  background-color: white;
}
</style>
