<template>
  <div>
    <v-card>
      <v-toolbar
        color="secondary"
        dark
        flat
      >
        <v-btn
          icon
          dark
        >
          <v-icon>mdi-account-supervisor</v-icon>
        </v-btn>
        <v-toolbar-title class="text-uppercase ml-1 pl-0 white--text">
          A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
        </v-toolbar-title>
      </v-toolbar>
      <v-card-text>
        <v-container
          grid-list-md
          text-sm-center
          fluid
          main-container
        >
          <v-row
            no-gutters
            class="mb-6"
          >
            <v-col>
              <v-text-field
                v-model="search"
                :label="$t('common_search')"
                prepend-inner-icon="mdi-magnify"
                class="mr-2"
                clearable
                @input="onSearch"
              />
            </v-col>
            <v-col cols="1">
              <v-btn
                color="secondary"
                class="ml-2 mt-3"
                @click="fetchData"
              >
                <v-icon>mdi-refresh</v-icon>
              </v-btn>
            </v-col>
          </v-row>
          <v-row>
            <v-col>
              <div
                v-if="stats.loading"
                style="height: 150px; display: flex; justify-content: center; align-items: center;"
              >
                <v-progress-circular
                  indeterminate
                  size="32"
                  color="primary"
                />
              </div>
              <heatmap-chart
                v-else
                :x-axis-data="chartDateValues"
                :y-axis-data="stats.users"
                :data="chartDataGrouped"
                :max-value="stats.maxValue"
              />
            </v-col>
          </v-row>
          <v-row
            no-gutters
            justify="center"
          >
            <v-col>
              <v-data-table
                :loading="loading"
                :headers="tableHeaders"
                :items="data"
                item-key="vueKey"
                :expanded.sync="expanded"
                :server-items-length="dataLength"
                :options.sync="pagination"
                :footer-props="footerProps"
                show-expand
                @click:row="onRowClick"
              >
                <template #[`item.level`]="{ item }">
                  <v-tooltip
                    bottom
                  >
                    <template #activator="{ on }">
                      <v-icon
                        class="pr-2"
                        :color="issueLevels[item.level.toLowerCase()].color"
                        v-on="on"
                      >
                        {{ issueLevels[item.level.toLowerCase()].icon }}
                      </v-icon>
                    </template>
                    <span>
                      {{ item.level }}
                    </span>
                  </v-tooltip>
                </template>

                <template #[`item.issue.carwash.owner`]="{ item }">
                  <v-tooltip bottom>
                    <template #activator="{ on, attrs }">
                      <img
                        v-if="item.issue.carwash.owner.logo"
                        v-bind="attrs"
                        :src="item.issue.carwash.owner.logo"
                        alt="Image"
                        style="max-height: 35px; width: auto;"
                        v-on="on"
                      >
                      <v-icon
                        v-else
                        size="35"
                        v-bind="attrs"
                        v-on="on"
                      >
                        mdi-help
                      </v-icon>
                    </template>
                    <span>{{ item.issue.carwash.owner.name }}</span>
                  </v-tooltip>
                </template>

                <template #[`item.issue.etime`]="{ item }">
                  {{ item.issue.etime ?? '-' }}
                </template>

                <template #expanded-item="{ headers, item }">
                  <td
                    class="expanded text-center pa-4"
                    :colspan="headers.length"
                  >
                    <v-row>
                      <v-col>
                        <div><b>ID alarmu</b></div>
                        <div>{{ item.issue.alarmDefinition.id ?? '-' }}</div>
                      </v-col>
                      <v-col>
                        <div><b>Opis alarmu</b></div>
                        <div>{{ item.issue.alarmDefinition.text ?? '-' }}</div>
                      </v-col>
                      <v-col>
                        <div><b>Poziom problemu</b></div>
                        <div>
                          <v-tooltip
                            bottom
                          >
                            <template #activator="{ on }">
                              <v-icon
                                class="pr-2"
                                :color="alarmLevels[item.issue.level.name].color"
                                v-on="on"
                              >
                                {{ alarmLevels[item.issue.level.name].icon }}
                              </v-icon>
                            </template>
                            <span>
                              {{ item.issue.level.name }}
                            </span>
                          </v-tooltip>
                        </div>
                      </v-col>
                    </v-row>
                  </td>
                </template>
              </v-data-table>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import alarmLevels from '@components/predictive-maintenance/AlarmLevels';
import debounce from 'lodash/debounce';
import HeatmapChart from '@components/common/charts/HeatmapChart.vue';

export default {
  name: 'ActivityList',
  components: {
    HeatmapChart,
  },
  data() {
    return {
      alarmLevels,
      loading: false,
      expanded: [],
      data: [],
      dataLength: -1,
      pagination: {
        page: 1,
        itemsPerPage: 25,
      },
      search: null,
      footerProps: {
        'items-per-page-options': [25, 50, 100],
        'items-per-page-text': this.$t('common_rowsPerPage'),
      },
      tableHeaders: [
        {
          text: 'ID',
          sortable: false,
          align: 'sm-start',
          value: 'issue.id',
        },
        {
          text: 'Data utworzenia',
          sortable: false,
          value: 'ctime',
          align: 'center',
        },
        {
          text: 'Email',
          sortable: false,
          value: 'email',
          align: 'center',
        },
        {
          text: 'Komentarz',
          sortable: false,
          value: 'comment',
          align: 'center',
        },
        {
          text: 'Status',
          sortable: false,
          value: 'status',
          align: 'center',
        },
        {
          text: 'Poziom',
          sortable: false,
          align: 'center',
          value: 'level',
        },
        {
          text: 'Nazwa myjni',
          sortable: false,
          align: 'center',
          value: 'issue.carwash.name',
        },
        {
          text: 'Właściciel',
          sortable: false,
          value: 'issue.carwash.owner',
          align: 'sm-center',
        },
        { text: '', value: 'data-table-expand' },
      ],
      issueLevels: {
        notice: {
          icon: 'mdi-information',
          color: 'grey',
        },
        info: {
          icon: 'mdi-information',
          color: 'blue',
        },
        error: {
          icon: 'mdi-alert-circle',
          color: 'red',
        },
      },
      stats: {
        loading: false,
        users: [],
        days: this.generateDateList(),
        data: [],
        maxValue: 0,
      },
    };
  },

  computed: {
    chartDateValues() {
      // convert date format from YYYY-mm-HH to mm-HH
      return this.stats.days.map((item) => item.substring(5));
    },
    chartDataGrouped() {
      return this.stats.data.map((item) => {
        const dayIndex = this.stats.days.indexOf(item.day);
        const userIndex = this.stats.users.indexOf(item.email);

        return [dayIndex, userIndex, item.count];
      });
    },
  },

  watch: {
    pagination: {
      handler(newValue, oldValue) {
        if (oldValue.page !== newValue.page
          || oldValue.itemsPerPage !== newValue.itemsPerPage) {
          this.fetchData();
        }
      },
      deep: true,
    },
  },

  created() {
    this.onSearch = debounce((text) => {
      this.search = text === '' ? null : text;
      this.fetchData();
    }, 700);

    this.fetchDataDebounced = debounce(() => {
      this.fetchData();
    }, 700);
  },

  mounted() {
    this.fetchData();
  },

  methods: {
    async fetchData() {
      this.fetchStatsData();

      this.loading = true;
      const response = await this.axios.get(
        '/administration/carwashissues/history',
        {
          params: {
            page: this.pagination.page,
            perPage: this.pagination.itemsPerPage,
            search: this.search,
          },
        },
      );

      this.data = response.data.data.map((item, index) => ({
        ...item,
        vueKey: index,
      }));
      this.dataLength = response.data.total;
      this.loading = false;
    },
    async fetchStatsData() {
      this.stats.loading = true;

      const today = new Date();
      const pastMonth = new Date();
      pastMonth.setMonth(today.getMonth() - 1);

      const response = await this.axios.get(
        '/administration/carwashissues/history/stats',
        {
          params: {
            dateFrom: pastMonth.toISOString().slice(0, 10),
            dateTo: today.toISOString().slice(0, 10),
          },
        },
      );

      const { data } = response.data;

      // remove email duplications
      this.stats.users = [...new Set(data.map((item) => item.email))];
      this.stats.maxValue = response.data.maxCount;
      this.stats.data = data;

      this.stats.loading = false;
    },
    onRowClick(item, data) {
      data.expand(!data.isExpanded);
    },
    generateDateList() {
      const dateList = [];
      const today = new Date();
      const pastMonth = new Date();
      pastMonth.setMonth(today.getMonth() - 1);

      for (let date = pastMonth; date <= today; date.setDate(date.getDate() + 1)) {
        const formattedDate = date.toISOString().slice(0, 10);
        dateList.push(formattedDate);
      }

      return dateList;
    },
  },
};
</script>

<style scoped>
.expanded {
  background-color: white;
}
</style>
