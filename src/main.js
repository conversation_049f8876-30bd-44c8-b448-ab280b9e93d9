import Vue from 'vue';
import VueAxios from 'vue-axios';
import * as Sentry from '@sentry/vue';
import { BrowserTracing } from '@sentry/tracing';

import axios from '@plugins/axios';
import vuetify from '@plugins/vuetify';
import VeeValidate from '@plugins/vee-validate';
import multiselectHelper from '@plugins/multiselectHelper';
import snackbar from '@plugins/snackbar';
import exporter from '@plugins/exporter';
import valueByPath from '@plugins/valueByPath';
// eslint-disable-next-line import/no-extraneous-dependencies
import * as VueGoogleMaps from 'vue2-google-maps';

import filters from './filters';
import i18n from './i18n';
import store from './store';
import router from './router';
import App from './App.vue';

Vue.use(VueAxios, axios);
Vue.use(snackbar);
Vue.use(filters);
Vue.use(VeeValidate);
Vue.use(multiselectHelper);
Vue.use(exporter);
Vue.use(valueByPath);
Vue.use(VueGoogleMaps, {
  load: {
    key: 'AIzaSyCWY_JjSUbbVg4ZbhhVi4K_ihdh3E4QARk',
  },
});

if (process.env.VUE_APP_ENVIRONMENT !== 'development') {
  Sentry.init({
    Vue,
    dsn: 'https://<EMAIL>/5751494',
    integrations: [
      new BrowserTracing({
        routingInstrumentation: Sentry.vueRouterInstrumentation(router),
        tracingOrigins: ['localhost', 'my-site-url.com', /^\//],
      }),
    ],
    // beforeSend: (event, hint) => {
    //   // not send error when response send 401
    //   const { response } = hint.originalException;
    //   if (response && response.status && response.status === 401) {
    //     return null;
    //   }
    //   return event;
    // },
    tracesSampleRate: 1.0,
    release: APP_VERSION,
  });
}

new Vue({
  router,
  store,
  vuetify,
  i18n,
  render: (h) => h(App),
}).$mount('#app');
