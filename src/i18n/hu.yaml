actions.actions: <PERSON><PERSON><PERSON><PERSON>
actions.actualize: <PERSON><PERSON><PERSON><PERSON><PERSON>
actions.add_attachment: <PERSON><PERSON><PERSON><PERSON> hozzáadása
actions.add_client: <PERSON><PERSON> hozz<PERSON> az ügyfeleket
actions.add_key: BKF Key hozzáadása
actions.add_user: <PERSON><PERSON><PERSON>z<PERSON><PERSON><PERSON> hozzáadása
actions.cancel: <PERSON>égsem
actions.chose: Választ
actions.clear: Egyértelmű
actions.click_to_show_more_details: A részletek megtekintéséhez kattintson a gombra
actions.close: <PERSON><PERSON><PERSON><PERSON><PERSON>
actions.delete: Töröl
actions.download-cards-usage-report: Töltse le az Ügyfélkártyák jelentését
actions.edit_user: Felhasználó szerkesztése
actions.export: Exportálás
actions.export_csv: CSV exportálás
actions.export_summary: Összesítő exportálás
actions.export_transactions: Tranzakciók exportálása
actions.export_xlsx: XLSX exportálás
actions.generate: Generálja
actions.generate_and_download: Generálás és letöltés
actions.generate_invoice: <PERSON>r<PERSON><PERSON><PERSON> és küldjön
actions.lock: Blokk
actions.refill: Töltse fel
actions.refill_and_issue_invoice: Töltse fel és adjon ki egy számlát
actions.return_to_list: Vissza a listához
actions.save: Mentés
actions.save-and-add-other: Mentse és adjon hozzá további
actions.send_invoice: Számla küldése
actions.show_doc: Перейти к документации
actions.unlock: Kinyit
common_address: Cím
admin_country: Ország
admin_details: Az autómosó részletei
admin_heading: Az összes autómosó cm -jének listája
common_owner: Tulajdonos
admin_ownerBkf: A BKF tulajdonosa
admin_product: Termék
admin_serialNumber: Sorozatszám
admin_startDate: Kezdő dátum
admin_warrantyVoided: Garancia vége
admin_add: Adjon hozzá egy előfizetést
admin_alreadyCancel: Az előfizetést már lemondták
admin_automaticPayment: Automatikus fizetés.
admin_cancel: Törölje az előfizetést
admin_manualCancelNotPossible: Kézi lemondás
administration.subscription.status.canceled: Megsemmisített
administration.subscription.status.initiated: Feldolgozott
administration.subscription.status.manually_canceled: Törölve
administration.subscription.status.paid: Fizetett
administration.subscription.status.unknown: Ismeretlen
admin_usersHeading: A CM -ben szereplő összes felhasználó listája
admin_isOwner: Tulajdonos
admin_lastLogin: Utolsó bejelentkezés
common_usersOwner: Tulajdonos
admin_subscriptionCode: Aktív előfizetés
admin_subscriptionEnds: Az utolsó előfizetés vége
common_createDate: Riasztás létrehozásának dátuma
common_duration: Riasztás időtartama
common_endDate: A riasztás befejezésének dátuma
loyalApp_trustedPartnerWithoutFleetManager: Egy átlagos felhasználó, aki nem flottakezelőt lehet megbízható partner. Fleet Csak a felhasználó biztos lehet a megbízható partner.
common_actionSucced: Az akció sikeres volt
service_sending: Küldés...
common_errorHeader: Alkalmazás hiba
contact_message: Alkalmazáshiba történt! Lépjen kapcsolatba az alkalmazás fejlesztőjével, hogy megoldja!
dashboard_average: Átlagos
dashboard_max: Maximum
dashboard_min: Minimális
dashboard_title: Forgalmi állvány
loyaltyCards_virtualCard: Virtuális kártya
loyaltyCards_activeTopups: Aktív feltöltések
loyaltyCards_clientLockWarning: A kézi számlázás blokkolva van. A bekapcsoláshoz módosítsa a kiválasztott ügyfél számlázási beállításait.
loyaltyCards_cyclicTopUpList: Aktív ciklikus topupok
loyaltyCards_notSentList: Felső UPS várja a küldésre
common_cardActive: A kártya aktív
loyaltyCards_activeMessage: Ha megnyomja a kapcsolót, megakadályozza, hogy a kártyát az autómosásra használják. Az elérhető pénzeszközök és a kártyahasználati előzmények nem változnak meg.
common_cardBlocked: A kártya letiltva
loyaltyCards_blockedMessage: Ha rákattint a kapcsolóra, akkor újra használhatja a kártyát az autómosóban.
loyaltyCards_card: Kártya
common_cards: Térképek
loyaltyCards_cardsList: Hűségkártyák listája
loyaltyCards_cleanHistoryAndBalance: törli a kártyán lévő pénzeket és a használati történetet.
loyaltyCards_invoiceLoading: Folyamatban van a számlakészítés...
loyaltyCards_invoiceSendLoading: Számla küldése...
loyaltyCards_modifyTime: Módosítási dátum
loyaltyCards_name: A nevét, a kártya
loyaltyCards_number: Kártya száma
loyaltyCards_removal: A kártya eltávolítása
loyaltyCards_source: Forrás
loyaltyCards_sourceStatus: Forrás / állapot
loyalApp_topup: Feltöltés
common_topups: Nyomás alatt van
card.transactions: Kártya tranzakciók
card.transactions_list: Hűségkártya-tranzakciók története
loyaltyCards_client: Ügyfél
common_heading: Invoices
loyaltyCards_invoiceNumber: Számlaszám
loyaltyCards_issuanceDate: kiadás dátuma
loyaltyCards_serviceDate: A fizetés dátuma
loyaltyCards_valueGross: Bruttó érték
loyaltyCards_valueNet: A nettó érték
carwash: Autómosó
common_rollover: Kefés autómosók
common_selfService: Önkiszolgáló autómosó
common_carwashSn: Autómosó sorozatszám
finance_error: Hiba
loyalApp_qrCode: QR-kód
common_standCode: Pozíciós kód
finance_stands: Állvány
finance_topupDisabled: A prémium előfizetésben rendelkezésre álló pozíció feltöltése egy autómosónál, amely támogatja a mobil fizetéseket.
finance_topup: Feltölt
finance_refillFor: Töltse fel a pozíciót Fr.
finance_topupStand: Töltse fel a pozíciót
loyaltyCards_configuration: Konfiguráció
loyaltyCards_generateStrategy: Számlázási stratégia
common_paymentMethod: Lehetviteli módszer
common_cash: Készpénz
common_transfer: Átruházás
client-modal.invoice.strategies.auto-after-top-up: Kiállítás a feltöltés után
client-modal.invoice.strategies.block: Ne adjon ki
client-modal.invoice.strategies.manual: Kérésre beállítva
client-modal.invoice.strategies.undefined: "-"
loyaltyCards_editClient: Szerkessze az Ügyfél
loyaltyCards_heading: Ügyféllista
loyaltyCards_invoicing: Számlázás
loyaltyCards_comment: Megjegyzés
loyaltyCards_discount: Kedvezmény
loyaltyCards_endTime: Vége
common_cyclicTopUpsHeading: Ciklikus feltölt
loyaltyCards_lastCal: Utolsó hívás
loyaltyCards_startTime: Rajt
loyaltyCards_active: Aktív
loyaltyCards_state: Állapot
loyaltyCards_cyclicAdd: Ciklikus az összegért
loyaltyCards_cyclicAlign: Ciklikus újratöltés az egyensúlyhoz
loyaltyCards_oneTime: Egyszer
loyaltyCards_topUpType: feltölt típus
loyaltyCards_type: típus
loyaltyCards_value: Érték
common_alarms: Riasztások
common_date: Idő
dashboard.moneycollect.CAR_WASH: Pozíciók
dashboard.moneycollect.MONEY_CHANGER: Pénzváltó
dashboard.moneycollect.YETI: YETI
common_name: Név
common_noAlarms: Nincs riasztás
dashboard.payment-share.amount: Összeg
dashboard.payment-share.bank_card: Bankkártya
dashboard.payment-share.bill: Bankjegyek
dashboard.payment-share.bkf_card: Hűségkártyák
dashboard.payment-share.client: Ügyfelek
dashboard.payment-share.clientCount: Az ügyfelek számítanak
dashboard.payment-share.coin: Érmek
dashboard_header: Fizetések aránya
dashboard.payment-share.paymentType: Fizetési mód
dashboard.payment-share.percentage: Százalék
dashboard.payment-share.promotion: Promóció
dashboard.payment-share.service: Szerviz
dashboard.payment-share.sum-payments: Fizetési összeg
dashboard.payment-share.token: Zseton
common_p14d: Az elmúlt 14 nap
common_p7d: Az elmúlt 7 nap
common_last: Az utolsó pénzgyűjtés óta
common_now: Ma
dashboard_previousMonth: Előző hónap
common_sinceMonthStart: Mivel aktuális hónap kezdete
common_yesterday: Tegnap
common_software: Szoftver
dashboard.subscription.component_header: Ends előfizetés Carwash Manager
admin_subscription: Feliratkozás
dashboard.subscription.subscriptionClick: Ha meg szeretné hosszabbítani előfizetését, kattintson az alábbi gombra.
dashboard.subscription.subscriptionEnd: A Carwash Manager előfizetése lejárt.
dashboard.subscription.subscriptionEndIn: "A Carwash Manager előfizetése befejeződik:"
dashboard.subscription.subscriptionEndTitle: Az előfizetés véget ért
dashboard.subscription.subscriptionHelpText: "Ha kérdése van, kérjük, vegye fel velünk a e-mailben:"
dashboard_sum: Összeg
dashboard_summary: Együtt
dashboard_yourCarwash: A te autómosók
date.length.12m: 12 hónap
date.length.1m: 1 hónap
date.length.3m: 3 hónap
date.length.6m: 6 hónap
finance_0: Vasárnap
finance_1: Hétfő
finance_2: Kedd
finance_3: Szerda
finance_4: Csütörtök
finance_5: Péntek
finance_6: Szombat
service_attachment: Csatolás
service_description: Leírás
service_heading: Új beadvány
service_close: Kérje a probléma lezárását
service_closedAt: Zárt
service_createdAt: Létrehozva
service_eventsClose: Jelentés lezárva
service_readyToClose: Az ügyfél kérte a probléma lezárását
service_start: Létrehozott jelentés
service_waitingForResponse: Várjuk az ügyfél válaszát
service_listHeading: Értesítések
service_status: Állapot
service_statusesClose: Zárt
service_new: Új
service_open: Megnyílt
service_statusesReadyToClose: Kész
service_waiting: Várakozás a reakcióra
service_subject: Tárgy
service_user: Kezdeményező
service_replySent: Válaszát elküldtük
service_replySentProblem: Hiba történt a válasz elküldésekor
service_reportSent: Hibajelentés elküldésre került
service_reportSentProblem: Probléma adódott a jelentés elküldésével kapcsolatban.
service_clientAdd: Ügyfél hozzáadása probléma
service_financeData: Pénzügyi adatok problémája
service_loyaltyCards: Hűségkártya probléma
service_other: Egyéb probléma, ami nem autómosó meghibásodás.
service_subscirption: Feliratkozási probléma
service_respond: Írd meg a válaszod...
service_errorReportSubject: A beadvány tárgya
contact_title: Támogatás
loyalApp_accessDenied: Nem rendelkezik engedéllyel a művelet elvégzéséhez.
loyaltyCards_addExisting: A kártyát már regisztrálták a rendszerben.
loyaltyCards_blockedTopup: A kártyának aktívnak kell lennie annak érdekében, hogy feltöltse fiókját.
loyalApp_userAlreadyExists: A megadott felhasználó már létezik. Nem hívhat meg egy létező felhasználó.
loyaltyCards_filtersActive: Aktív
loyaltyCards_activity: Tevékenység
service_allFeminine: All
loyaltyCards_blocked: Zárt
loyaltyCards_cardType: Kártyatípus
service_completed: Befejezett
filters.daterange.all: Mindig
common_currentYear: Idén
common_custom: Bármi
common_previousMonth: Előző hónap
common_previousYear: Előző év
common_today: Ma
common_daterangeYesterday: Tegnap
admin_deselectAll: Minden választás törlése
loyaltyCards_funds: Intézkedések
common_inPeriod: Időszakban
common_inPeriodCustom: Válasszon egy dátumtartományt
loyaltyCards_names: Nevek
service_filtersOpen: Nyílt
loyaltyCards_regular: Fizikai
admin_selectAll: Mindet kiválaszt
loyaltyCards_usedInPeriod: Az időszak alatt használt
loyaltyCards_virtual: Virtuális
loyaltyCards_withFounds: Alapokkal
loyaltyCards_withInvoice: Kiállított számla
loyaltyCards_withNames: Nevekkel
loyaltyCards_withoutFounds: Alap nélkül
loyaltyCards_withoutInvoice: Nincs számla
loyaltyCards_withoutNames: Nincsenek nevek
fiscal_transactions.details.heading: A tranzakció részletei
fiscal_transactions.export.name: fiscal_transactions
fiscal_transactions.for: erre
fiscal_transactions.from: innen
fiscal_transactions.heading: Adóügyi tranzakciók
fiscal_transactions.modal_add.heading: Adjon hozzá fiskális tranzakciót
fiscal_transactions.modal_add.isu: ISU kód
fiscal_transactions.modal_add.location: Helykód
fiscal_transactions.modal_add.password: Tanúsítvány jelszava
fiscal_transactions.source.CAR_WASH: Pozíció
fiscal_transactions.source.DISTRIBUTOR: Adagoló
fiscal_transactions.source.MONEY_CHANGER: Pénzváltótól
fiscal_transactions.source.UNKNOWN: Ismeretlen
fiscal_transactions.source.VACUUM_CLEANER: Porszívó
fiscal_transactions.table.carwash: Autómosó
fiscal_transactions.table.date: Dátum
fiscal_transactions.table.fiscal: Állapot
fiscal_transactions.table.type: A tranzakció típusa
fiscal_transactions.table.value: Érték
fiscal_transactions.to: ez
fiscal_transactions.type.BANKCARDS: Bankkártyás fizetés
fiscal_transactions.type.BANK_CARDS: Bankkártyás fizetés
fiscal_transactions.type.CARWASH_MANAGER: Az autómosó tulajdonosa manuálisan hozzáadta a tranzakciót
fiscal_transactions.type.CASH: Készpénz
fiscal_transactions.type.CASHLESS: Készpénzmentes
fiscal_transactions.type.CHARGE_BAY: Pozíciók feltöltése a pelenkázógépből
fiscal_transactions.type.COINS: Fizetés érmékkel
fiscal_transactions.type.HOPPER_A: Hopper A
fiscal_transactions.type.HOPPER_B: Hopper B
fiscal_transactions.type.LOYALTY_PAYING: Fizetés törzsvásárlói kártyával
fiscal_transactions.type.LOYALTY_PROMO: Hűségkártya promóciós feltöltése
fiscal_transactions.type.LOYALTY_RECHARGE: Hűségkártya feltöltése
fiscal_transactions.type.LOYALTY_SELLING: Hűségkártya értékesítés
fiscal_transactions.type.MOBILE: Mobil fizetések
fiscal_transactions.type.NOTES: Fizetés bankjegyekkel
fiscal_transactions.type.PROMO: Lelátók promóciós feltöltése
fiscal_transactions.type.SERVICE: Szolgáltatási impulzusok
fiscal_transactions.type.TOKENS: Fizetés tokenekkel
common_formAddress: Cím
common_city: Város
common_clientBasicData: Základní údaje
common_clientInvoiceData: A számla adatok
loyaltyCards_confirmCustomerData: Erősítse meg az ügyfél számlázási adatait
common_country: Ország
loyaltyCards_currency: Pénznem
common_fullCustomerName: A szerződő fél teljes neve
common_formName: Utónév
common_postCode: Postai irányítószám
common_surname: Vezetéknév
common_fieldRequired: Field required
form.validation.file_max_size_mb: "A fájl mérete nem haladhatja meg a {size}MB értéket"
loyaltyCards_infoValidKey: Használnia kell a "0"tól - "9"ig és az "a"tól "f"ig.
loyaltyCards_infoValidKeyLength: 8 karakter hosszúnak kell lennie.
loyalApp_invalidValue: Helytelen érték.
loyaltyCards_phoneNumber: Helytelen telefonszám
loyaltyCards_topUpPositiveNumberOnly: A feltöltésnek pozitívnak kell lennie.
loyaltyCards_topUpValueToBig: A feltöltési összeg magas.
user_accountNumber: Bank account number (iban)
user_contactEmail: Kapcsolattartó e-mail
user_contactEmailTooltip: E -mail a kapcsolattartóhoz az ügyfeleknek küldött e -mailek láblécében.
common_dataToInvoice: Data for the invoice
user_editInvoiceDataAlert: "To change the blocked fields, contact us via the email address:"
common_emailCopyEmail: Email addresses of invoice copies
common_invoiceCompanySettingsName: Full name of the company
common_pressEnterToAddNew: Nyomja meg az Enter billentyűt egy új cím hozzáadásához
common_taxNumber: Tax identification number
loyaltyCards_additionalInfo: További információ
loyaltyCards_invoiceSettings: Számlázási beállítások
loyaltyCards_invoiceNumerator: Számlaszám
common_logo: Logó a számla
loyaltyCards_longMonthFormat: Hónap 01 formátumban (januárra)
loyaltyCards_longYearFormat: év a 2018 -as formátumban
user_nextInvoiceNumber: Újabb számla száma
loyaltyCards_notOwnerAlert: Csak az autómosó tulajdonosa változtathatja meg a számlázási beállításokat
common_paymentPeriod: Fizetési határidő
loyaltyCards_settings: Beállítások
loyaltyCards_shortMonthFormat: hónap az 1. formátumban (januárra)
loyaltyCards_shortYearFormat: Év 18 formátumban
loyaltyCards_vatTax: Áfakulcs
common_loading: Betöltés...
subscription_logerPeriodBetterPrice: Egy hosszabb időszak egy jobb árat
contact_header: Kijelentkezés
contact_loggedOutMessage: Kijelentkezett, mert a munkamenet lejárt. Jelentkezzen be újra.
loyalApp_balanceFleetMemberNotify: Flotta
loyalApp_balanceFleetMemberTooltip: Account tag flotta. Minden művelet ralizowanie számlaegyenleg flottamenedzser.
loyalApp_fleetManagerActive: Vartotojas yra parko direktorius
loyalApp_fleetManagerActiveMessage: Paspaudę jungiklio ženklų vartotojui kaip reguliariai sąskaitą.
loyalApp_fleetManagerInactive: Vartotojas laivyno valdytojas
loyalApp_fleetManagerInactiveMessage: Paspaudę jungiklį pažymės jus kaip laivyno sąskaitą.
common_invoices: Sąskaitos faktūros
loyal-app-manager.no-invoice-data-message: Nincs adat a számlázás, kapcsolat a flotta menedzser ahhoz, hogy befejezze őket.
loyalApp_payments: Kifizetések
loyalApp_promotionalCodes: Reklaminius kodus
loyalApp_promotionalPackages: Reklaminiai paketai
loyalApp_regularUser: Vartotojas
loyalApp_trustedPartner: Patikimas partneris
loyalApp_trustedPartnerActive: Vartotojas yra patikimas partneris
loyalApp_trustedPartnerActiveMessage: Paspaudę jungiklio ženklų vartotojui kaip įprasta. Pripildykite Piggy Bank nedarant nedelsiant mokėjimas bus neįmanoma.
loyalApp_trustedPartnerInactive: Vartotojas nėra patikimas partneris
loyalApp_trustedPartnerInactiveMessage: Paspaudę jungiklis prekių ženklą kaip patikimas vartotojas. Tai leis įdaras iki vartotojas pusiausvyrą, nedarant nedelsiant mokėjimą. Sąskaitos faktūros bus išduodami automatiškai.
loyalApp_usersList: Narių sąrašas
loyalApp_usersListHeading: A hűségalkalmazás felhasználóinak listája
dashboard_heading: Hűségkártyák
dashboard_value: Érték
loyalsystem-widget.values-name.cardsTopUpSumFromCM: A felső -ablakok és promóciók összege a CM alkalmazásból
loyalsystem-widget.values-name.cardsTopUpSumFromCarwash: A kiadás és a promóciók összege a kiadásból
loyalsystem-widget.values-name.cardsUsedCount: A használt kártyák száma
loyalsystem-widget.values-name.transactionsPayments: A fizetés összege
loyalsystem-widget.values-name.transactionsTopups: A felső -alsó összeg
loyaltyCards_loyaltyTopupsHistoryHeading: Historia doładowań kart lojalnościowych
menu_administration: Adminisztráció
common_administrationCarwashes: Mosó
menu_administrationUsers: Felhasználók
common_alarmActive: Aktív riasztások
common_alarmHistory: Riasztás történele
common_chart: Paraméteradatok grafikonja
loyaltyCards_clients: Ügyfelek
menu_cmdashboard: Keresés
common_companyData: A vállalat adata
menu_finance: Pénzügy
common_financeCarwashRates: Autómosás árak
menu.finance-fiscaltransactions: Adóügyi tranzakciók
common_financeMobilePayments: Mobil fizetések
common_financeTurnover: Forgalom
menu_logout: Kijelentkezés
common_loyalAppManager: Hűség alkalmazás
common_loyalsystem: Hűségrendszer
common_moneycollect: Pénzgyűjtés
common_processData: Folyamatadatok
menu_profile: Saját profilom
common_service: Támogatási jegyek
menu_support: Support
common_users: Felhasználók
common_markRead: Jelöld olvasottként
messages_message: Üzenet
menu_more: Több
messages_noReadMessages: Nincsenek olvasott üzenetek
common_noUnreadMessages: Nincsenek olvasatlan üzenetek
messages_read: Olvas
messages_unread: Nem olvasott
messages_when: Mikor
common_mobilePaymentInvoicesDate: Dátum
common_download: Letöltés
mobile_payment_invoices.heading: Invoices
common_invoiceNumber: Számlaszám
common_period: Időszak
common_valueGross: Bruttó érték
finance_for: erre
finance_from: innen
finance_heading: Mobil fizetés
finance_carwash: Autómosó
finance_date: Dátum
finance_paymentType: Fizetési típus
finance_standCode: Mosóállomás kódja
finance_total: Összeg
common_totalOnPage: Összesen az aktuális oldalon
finance_value: Érték
finance_to: ez
loyaltyCards_cardNumber: Kártya száma
loyaltyCards_createNew: Létrehozzon egy új BKF Key
loyaltyCards_creationHint: A 8 számjegynél hosszabb BKF Card és BKF Key esetén az első 8 alfanumerikus karaktert át kell írni
loyaltyCards_deleteHint: "Megjegyzés: eltávolítás után nem adhat hozzá kártyát."
loyaltyCards_hint: A kártya eltávolításakor eltűnik a hűségkártya képernyőjén lévő listáról. Az eltávolított kártyát nem lehet újra hozzáadni a rendszerhez.
loyalApp_userEdit: Felhasználó szerkesztése
loyaltyCards_emailInfo: A számlát az Ön e-mail címére küldjük.
loyaltyCards_emailSend: A számlát a megadott e-mail címre küldjük.
modal.loyal_card_top_up_invoice.error.card_no_carwash: A kapcsolódó autómosó nem található
modal.loyal_card_top_up_invoice.error.card_no_client: A kártyához nem rendeltek ügyfelet
modal.loyal_card_top_up_invoice.error.invoice_exists: A számla már létezik a rendszerben
modal.loyal_card_top_up_invoice.error.invoice_generation_failed: A számlakészítés sikertelen
modal.loyal_card_top_up_invoice.error.invoice_not_generated: A számlakészítés sikertelen
modal.loyal_card_top_up_invoice.error.invoice_send_failed: A számla elküldése sikertelen
modal.loyal_card_top_up_invoice.error.owner_no_country: Az autómosó tulajdonosa nem rendelkezik országgal, amelyet felállítottak.
moneycollect.devices.CAR_WASH: Pozíció
moneycollect.devices.DISTRIBUTOR: Adagoló
moneycollect.devices.VACUUM_CLEANER: Porszívó
finance_exchangerMoneyCollections: Pénz gyűjtés a pénzváltó története
moneycollect.export.name: moneycollect
finance_moneycollectFor: erre
finance_moneycollectFrom: innen
finance_standMoneyCollections: Pozíció pénz gyűjtemény
finance_balance: egyenleg
finance_bankCards: Kártyával fizetve
finance_details: A pénzgyűjtés részletei
finance_emergencyDrops: Vészlerakók
finance_safe: Széf
finance_sorted: Rendezés
finance_sucked: Letöltve
finance_sumCash: Készpénzben fizetve
finance_sumHoppers: Kiengedve (hoppers)
finance_sumSale: Értékesítés
finance_sumSorted: Cserélhető és biztonságos
finance_sumSucked: Autómosóból vették
finance_units: db.
finance_unrecognized: Ismeretlen
finance_yeti: YETI gyűjtemény részletei
finance_moneycollectTo: ez
finance_yetiMoneyCollections: YETI pénz gyűjtemény
name: Név
subscription_noCarwashAttached: Ehhez felhasználóhoz nics csatolt autómosó
subscription_alertContent: The company's data is not completed correctly, please complete them.
common_0: 0 nap
common_1: 1 nap
common_10: 10 nap
common_14: 14 nap
common_3: 3 nap
loyalApp_30: 30 nap
common_5: 5 nap
common_7: 7 nap
subscription_subscription: Fizetés az előfizetés
loyalApp_value: A kifizetések értéke
processData_chart: Diagram
processData_parameter: Paraméter
common_updateTime: Utolsó frissítés
processData_value: Érték
common_itemOld: Az érték elavult lehet
user_account: Felhasználói fiók
user_baseData: Alapinformációk
user_notificationNotice: Az alapcsomagban elérhető értesítési funkció elérhető
user_notificationsSettings: Értesítési beállítások
user_moneyCollectEmail: Küldjön értesítéseket az elfogadásról az e -mail címre
user_sendAlarmNotificationEmail: Riasztási értesítések az e -mail címre
user_sendAlarmNotificationMobile: A riasztás értesítése egy mobil alkalmazáshoz
user_regionalSettings: Területi beállítások
user_reportsAndNotifications: Jelentések és értesítések
profile-configuration.reports-notice: Az alapcsomagban elérhető e -mail jelentés funkció elérhető
profile-configuration.reports-settings: Jelentésbeállítások
profile-configuration.reports.fiscal-transactions: Költségvetési tranzakciós jelentés
profile-configuration.reports.mobile-payments: Mobil fizetési jelentés
profile-configuration.reports.program-usage: Program használja a jelentést
profile-configuration.reports.used-funds: Jelentés az alapok felhasználásáról
user_error: Küldje el a "hiba" szintre
user_info: Küldje el az "Információ" szintre
user_warning: Küldje el a "figyelmeztetés" szintre
user_dontSend: ne küldjön
profile-configuration.sending-options.periodic.daily: Küldje el minden nap
profile-configuration.sending-options.periodic.monthly: Küldje el minden hónapot
profile-configuration.sending-options.periodic.weekly: Küldje el minden héten
user_send: Küld
user_siteTitle: A profilom
finance_carwashUsage: Érintés nélküli autómosók használata
programsusage.daily: Napi használata
finance_programsusageFor: erre
finance_programsusageFrom: innen
common_title: Óránkénti használati ütemezés
finance_overTimeTitle: A programok idővel történő felhasználása
finance_title: A programok százalékos aránya
finance_rolloverUsage: Portálos autómosók használata
programsusage.table.brush: Kefe
programsusage.table.degreaser: Zsírtalanító
programsusage.table.foam: Hab
programsusage.table.glossing: Fényesítés
programsusage.table.mainwash: Porvegyszer
programsusage.table.prewash: Turbo
programsusage.table.program: Program
programsusage.table.rims: Felnik
programsusage.table.rinsing: Öblítés
programsusage.table.sum: Összeg
programsusage.table.wash: x mosás
programsusage.table.wasxing: Wax
programsusage.table.water-awerage: Átlagos fogyasztás víz
programsusage.table.water-usage: Teljes fogyasztás víz
finance_programsusageTo: ez
common_total: Használata
finance_mtime: módosítás dátuma
finance_name: Autómosó neve
finance_time: Megjelenítés másodpercben
service_button: Hiba jelentése
service_text: Támogatási jegy létrehozásához lépjen a következő oldalra
service_serviceHeading: Támogatási jegyek
service.status.desc.completed: Befejezett
service.status.desc.open: Nyílt
common_carwash: Autómosó
admin_issueId: ID
service_issueReportSource: Forrás
service_issueReportedBy: Jelentette
service_issueTitle: Tárgy
service_noContent: Nincs tartalom
service_tableStatus: Állapot
service_time: Jelentés dátuma
service.table.type: Típus
subscription_state: állapot
subscription.carwash-type.STANDARD: Alapértelmezett
subscription.carwash-type.UNSUBSCRIBED: Előfizetés nélkül
subscription.carwash-type.WARRANTY: Garancia
subscription.table.STANDARD: Post -Warranty mosás
subscription.table.UNSUBSCRIBED: Autómosás előfizetés nélkül
subscription.table.WARRANTY: Garancia mosás
subscription.table.discount: Kedvezmény
subscription.table.position: Pozíció
subscription.table.price-after-discount: eladási ár
subscription.table.price-before-discount: Ár a kedvezmény előtt
subscription.table.sum: Együtt
subscription.table.summary: Összefoglalás
subscription.table.type: típus
admin_whoPays: Ki fizet?
subscriptions.actions.chose-and-pay: Válassza ki és fizesse az előfizetést
common_canceled: törölve
subscription_chose: A választás az előfizetés
subscription_chose2: Válasszon előfizetési
subscriptions.delete-question: Szeretné törölni a kiválasztott előfizetést?
admin_clientDiscount: vásárlói kedvezmény
subscription_goToDataCompletion: Ugrás az adatok kitöltéséhez
subscription_historyList: a települések történetében
subscription_loyalcardsInvoicing: Hűségkártyák számlázási számlázása
subscription_technicalData: Műszaki adatok
common_paid: fizetett
subscription_paymentSummary: Számlázási összesítő
subscription_priceForCarwashForMonth: Ár 1 hónap (nettó ár) 1 autómosók
common_processing: feldolgozás
subscription_goToAbonamentLength: Ide választani az időszak abonamentowania
subscription_orderWithPaymentObligation: Order with a payment obligation
subscriptions.subscription: előfizetés
subscription_subscriptionContent: Tartalmát előfizetés
subscription_toPay: kifizetendő
subscriptions.types.basic: Elsődleges
subscriptions.types.free: Ingyenes
subscriptions.types.premium: Prémium
subscription_yourSubscription: Az előfizetéshez
common_alarmId: Riasztásazonosító
common_alarmLevel: Riasztási szint
common_all: Minden
loyalApp_averagePaymentValue: Átlagos fizetési érték
loyalApp_balance: Egyenleg
common_carwashes: Autómosók
common_client: Ügyfél
table.company_name: Company name
common_tableCreateDate: Létrehozásának dátuma
common_currency: Pénznem
loyaltyCards_cyclic: Ciklikus
common_dataUnknown: Adatok hiánya
common_tableDate: Dátum
common_email: Email
admin_filters: Szűrők
common_firstName: Keresztnév
loyalApp_fleetManager: Flottamenedzser
common_invoice: Számla
loyalApp_invoiceNumber: Invoice number
loyalApp_issuanceDate: Issuance date
common_language: Nyelv
common_lastLogin: Utolsó bejelentkezés
dashboard_lastActualizaction: Utolsó frissítés
common_lastName: Vezetéknév
common_lastUsage: Utoljára használt
common_noData: Nincsenek megjeleníthető adatok
common_notFullySent: Nem küldött teljesen
loyalApp_payment: Fizetés
loyalApp_paymentCount: Fizetések száma
common_paymentDate: Payment date
loyaltyCards_payments: Kifizetések
common_roles: Felhasználói szerepkörök
common_rowsPerPage: "Sorok oldalanként:"
common_search: Keresés
common_sendToCard: Küldött
common_stand: Pozíció
common_state: Állapot
loyalApp_sumPaymentValue: Fizetések összértéke
common_timezone: Időzóna
common_toSent: Várakozás
loyaltyCards_topup: Az érték a feltöltés
common_topupSent: Feltöltés
loyalApp_tableTrustedPartner: Megbízható partner
common_type: Típus
common_username: Username
common_value: Érték
loyaltyCards_valueAfterTransaction: Az egyenleg a tranzakció után
loyaltyCards_tableVirtualCard: Virtuális kártya
finance_minutes: min
transactions.balance_adjustment: Beállítás
transactions.car_wash: Autómosástól
transactions.distributor: Elosztó
transactions.export_error: Probléma a csv generálásával
transactions.history: Tranzakció története
transactions.history_for_card: "Kártya tranzakciók története a következőhöz:"
transactions.internet: Az internetről
transactions.money_changer: Pénzváltótól
transactions.payment: Fizetés
transactions.payments: Kifizetések
transactions.promotions: Promóció
transactions.refill_for: Töltse fel a kártyát
transactions.topup: Feltöltés
transactions.topup_card_history: Card Top-up történelem
transactions.topup_history: Top-up történelem
transactions.topups: Feltöltésig
transactions.unknown: Ismeretlen
transactions.vacuum_cleaner: Porszívó
turnover.devices.distributor.plural: Adagolók
turnover.devices.distributor.singular: Adagoló
turnover.devices.stand.plural: Pozíciók
turnover.devices.stand.singular: Pozíció
turnover.devices.vacuum.plural: Porszívók
turnover.devices.vacuum.singular: Porszívó
turnover.exchanger: Pénzváltó
finance_exportName: turnover
common_filtersCarwash: Autómosó
common_deviceType: Eszköz típusa
finance_turnoverFor: erre
finance_turnoverFrom: innen
finance_fromLastCollection: az utolsó pénzgyűjteményből
finance_compareWithPreviousYear: Hasonlítsa össze az előző évet
finance_linechartTitle: Forgalom időbeli alakulása
finance_ofAllCarwashes: minden autómosó
finance_ofCarwash: 'autómosó #'
finance_paymenttypesharepieName: Fizetési típusok részesedése
common_paymenttypesharepieTitle: Fizetési típusok részesedése
turnover.table.balance: Mérleg
turnover.table.bankCards: Bankkártya
turnover.table.banknotes: Bankjegyek
turnover.table.bkfCardPay: BKF Card fizetés
turnover.table.bkfCardSale: BKF Card eladás
turnover.table.bkfKey: BKF Card
turnover.table.bkfKeyRecharge: BKF Card feltöltése
turnover.table.carwash: Autómosó
turnover.table.carwashEarnings: Autómosó bevétele
turnover.table.carwashRecharge: Pozíció feltöltés
turnover.table.cash: Készpénzes fizetés
turnover.table.cashless: Készpénzmentes fizetés
turnover.table.clients: Ügyfelek
turnover.table.coins: Érmék
turnover.table.counter: Felhasználások száma
turnover.table.date: Dátum
turnover.table.details: Az autómosó részletei
turnover.table.detailsRollover: A portál autómosó részletei
turnover.table.exchanger: Pénzváltó adatai
turnover.table.exchangerHoppers: Pénzváltások
turnover.table.exchangerSale: Pénzváltó eladás
turnover.table.exchanges: Cserék
turnover.table.hopperA: Hopper A
turnover.table.hopperB: Hopper B
turnover.table.mobilePayments: Mobil
turnover.table.name: Eszköz
turnover.table.paid: Fizetett
turnover.table.post: Feltöltések pénzváltóból
turnover.table.prepaid: Előre fizetett fizetések
turnover.table.programsSale: Programok értékesítése
turnover.table.promotion: Promóció
turnover.table.saleValue: Értékesítési érték
turnover.table.sell: Eladni
turnover.table.service: Szolgáltatás
turnover.table.sum: Összeg
turnover.table.terminal: Fizetési terminál adatai
turnover.table.tokens: Tokenek
turnover.table.total: Együtt
common_daily: Napi
finance_detailed: Részletes
common_monthly: Havi
finance_tabsTotal: Teljes
finance_turnoverTitle: Forgalom
finance_turnoverTo: ez
finance_turnover: forgalom
finance_cubicMeters: "m³"
finance_litres: l
loyalApp_discount: Kedvezmény
loyalApp_max: Maximális
loyalApp_min: Minimális
loyalApp_term: Fizetési határidő
subscription_whyThisPrice: Miért ez az ár?
subscription_whyThisPriceModalHint: Az alábbi táblázat bemutatja az előfizetési összeg kiszámítását a saját autómosók és a kedvezmények alapján.