loyalApp_no: <PERSON>ee
loyalApp_yes: Ja
actions.actions: Actie
actions.add_attachment: Voeg bijlage toe
actions.add_client: Klant toevoegen
actions.add_code: Voeg promotiecode toe
actions.add_key: Klantenkaart toevoegen
actions.add_multiple_key: Opwaardere<PERSON> van meerdere klantenkaarten
actions.add_notification: Melding toevoegen
actions.add_package: Promotiepakket toevoegen
actions.add_user: Voeg gebruiker toe
actions.cancel: Annuleren
actions.cancel_send: Annuleer het verzenden
actions.chose: Selecteer
actions.clear: Du<PERSON>lijk
actions.click_to_show_more_details: Klik om meer details weer te geven
actions.close: Dichtbij
actions.delete: Verwijderen
actions.edit_user: Bewerk gebruiker
actions.export: Exporteren naar
actions.export_csv: CSV-export
actions.export_pdf: PDF-export
actions.export_summary: Samenvatting exporteren
actions.export_transactions: Transacties exporteren
actions.export_xlsx: XLSX exporteren
actions.generate: Genereer
actions.generate_and_download: Genereer en download
actions.generate_invoice: Een factuur genereren en verzenden
actions.invite_user: Gebruikersprompt
actions.lock: Slot
actions.refill: Opladen
actions.refill_and_issue_invoice: Opwaarderen en een factuur genereren
actions.refill_card: Kaart opladen
actions.return_to_list: Terug naar de lijst
actions.save: Redden
actions.save-and-add-other: Opslaan en andere toevoegen
actions.send: Versturen
actions.send_file: Verstuur bestand
actions.send_invitation: Verstuur uitnodiging
actions.send_invoice: Factuur verzenden
actions.show: show
actions.show-more-details: Zie meer details
actions.show_doc: Ga naar de documentatie
actions.unlock: Ontgrendelen
actions.update: Redden
common_address: Adres
admin_country: Land
admin_details: Details van de wasstraat
admin_heading: Lijst met alle autowasstraten in cm
common_owner: Eigenaar
admin_ownerBkf: Eigenaar BKF
admin_product: Product
admin_serialNumber: Serienummer
admin_startDate: Begin datum
admin_warrantyVoided: Einde garantie
admin_add: Voeg een abonnement toe
admin_alreadyCancel: Het abonnement is al opgezegd
admin_automaticPayment: Automatische betaling.
admin_cancel: Annuleer het abonnement
admin_manualCancelNotPossible: Handmatige annulering
administration.subscription.status.canceled: Geannuleerd
administration.subscription.status.initiated: Verwerkt
administration.subscription.status.manually_canceled: Geannuleerd
administration.subscription.status.paid: Betaald
administration.subscription.status.unknown: Onbekend
admin_usersHeading: Lijst met alle gebruikers in cm
admin_isOwner: Is eigenaar
admin_lastLogin: Laatste aanmelding
common_usersOwner: Eigenaar
admin_subscriptionCode: Actief abonnement
admin_subscriptionEnds: Einde van het laatste abonnement
common_createDate: Aanmaakdatum alarm
common_endDate: Einddatum alarm
loyalApp_trustedPartnerWithoutFleetManager: Een normale gebruiker die geen wagenparkbeheerder is, kan een vertrouwde partner zijn. Vloot Alleen de gebruiker kan een vertrouwde partner zijn.
common_actionSucced: De actie was succesvol
service_sending: Bezig met verzenden...
loyaltyCards_topUpActionSucceed: Opwaarderingen zijn succesvol toegevoegd
common_errorHeader: Applicatiefout
contact_message: Er is een toepassingsfout opgetreden! Neem contact op met de ontwikkelaar van de applicatie om het op te lossen!
loyaltyCards_clientLockWarning: Handmatige facturering is geblokkeerd. Om ze in te schakelen, wijzigt u de factureringsinstellingen voor de geselecteerde klant.
common_cardActive: Kaart actief
loyaltyCards_activeMessage: Als u op de schakelaar klikt, wordt voorkomen dat de kaart in de wasstraat wordt gebruikt. Het beschikbare saldo en de kaartgebruiksgeschiedenis veranderen niet.
common_cardBlocked: Kaart geblokkeerd
loyaltyCards_blockedMessage: Door op de schakelaar te klikken, kunt u de kaart opnieuw gebruiken bij de wasstraat.
loyaltyCards_card: Kaart
loyaltyCards_cardFundsTooltip: Laatste informatie over het geld op de kaart die door autowasstraten wordt verzonden
common_cards: Kaarten
loyaltyCards_cardsList: Lijst met loyaliteitskaarten
loyaltyCards_cleanHistoryAndBalance: wist het geld op de kaart en de gebruiksgeschiedenis.
loyaltyCards_foundsWaitingForSent: Geld dat wacht op verzending
loyaltyCards_invoiceLoading: Factuur wordt gegenereerd...
loyaltyCards_invoiceSendLoading: Factuur verzenden...
loyaltyCards_modifyTime: Verander de datum
loyaltyCards_name: Kaart naam
loyaltyCards_number: Kaartnummer
loyaltyCards_removal: Kaart verwijderen
loyaltyCards_source: Bron
loyaltyCards_sourceStatus: Bron/status
loyalApp_topup: Opladen
common_topups: Opwaarderingen
card.transactions: Kaarttransacties
card.transactions_list: Transactiegeschiedenis van loyaliteitskaart
loyaltyCards_client: Klant
common_heading: Facturen
loyaltyCards_invoiceNumber: Factuurnummer
loyaltyCards_issuanceDate: datum van uitgifte
loyaltyCards_serviceDate: Onderhoudsdatum
loyaltyCards_valueGross: Bruto waarde
loyaltyCards_valueNet: Netto waarde
carwash: Carwash
common_rollover: Rollover autowasstraat
common_selfService: Autowasstraat met zelfbediening
common_carwashSn: Serienummer van de carwash
finance_error: Fout
loyalApp_qrCode: QR code
common_standCode: Standcode
finance_stands: Standaarden
finance_topupDisabled: Opwaarderen van de beschikbare positie in het Premium-abonnement op een wasstraat die mobiele betalingen ondersteunt.
finance_topup: Opwaarderen
finance_refillFor: Vul de positie aan Fr.
finance_topupStand: Vul de positie aan
loyaltyCards_generateStrategy: Strategie voor het genereren van facturen
client-modal.invoice.strategies.auto-after-top-up: Bewijs na het bijvullen
client-modal.invoice.strategies.block: Niet uitgeven
client-modal.invoice.strategies.manual: Op aanvraag op te zetten
client-modal.invoice.strategies.undefined: "-"
loyaltyCards_editClient: Klant bewerken
loyaltyCards_heading: Klantenlijst
loyaltyCards_invoicing: Facturering
common_alarms: Alarmen
common_date: Tijd
dashboard.moneycollect.CAR_WASH: Standaarden
dashboard.moneycollect.MONEY_CHANGER: Geld wisselen
dashboard.moneycollect.YETI: YETI
dashboard.moneycollect.carwash: Carwash
dashboard.moneycollect.title: Geld verzamelen geschiedenis
common_name: Naam
dashboard_noPermission: Er zijn geen toegewezen machtigingen. Neem contact op met de sitebeheerder om deze toe te wijzen.
common_noAlarms: Geen alarmen
dashboard_header: Betalingen delen
common_p14d: Laatste 14 dagen
common_p7d: Laatste 7 dagen
common_last: Sinds de laatste geldinzameling
common_now: Vandaag
common_sinceMonthStart: Deze maand
common_yesterday: Gisteren
common_software: Software
dashboard.subscription.component_header: Het abonnement op Carwash Manager loopt af
admin_subscription: Abonnement
dashboard.subscription.subscriptionClick: Wilt u uw abonnement verlengen, klik dan op onderstaande knop.
dashboard.subscription.subscriptionEnd: Uw abonnement op Carwash Manager is verlopen.
dashboard.subscription.subscriptionEndIn: "Uw abonnement op Carwash Manager eindigt over: "
dashboard.subscription.subscriptionEndTitle: Subskrypcja wygasła
dashboard.subscription.subscriptionHelpText: "Als u vragen heeft, neem dan contact met ons op, telefoon: +48 691 070 171 of per e-mail: "
dashboard_sum: Som
dashboard_summary: Samenvatting
dashboard_yourCarwash: Uw auto wast
date.length.12m: 12 maanden
date.length.1m: 1 maand
date.length.3m: 3 maanden
date.length.6m: 6 maanden
common_selected: "{0} geselecteerd"
finance_0: Zondag
finance_1: Maandag
finance_2: Dinsdag
finance_3: Woensdag
finance_4: Donderdag
finance_5: Vrijdag
finance_6: Zaterdag
service_attachment: Bijlage
service_description: Beschrijving
service_heading: Nieuwe inzending
service_close: Vraag sluiting van het probleem aan
service_closedAt: Gesloten
service_createdAt: Gemaakt
service_eventsClose: Probleem gesloten
service_readyToClose: Klant heeft gevraagd om de kwestie te sluiten
service_start: Probleem gemaakt
service_waitingForResponse: Wachten op reactie van de klant
service_listHeading: Problemen
service_status: Toestand
service_statusesClose: Gesloten
service_new: Nieuw
service_open: Open
service_statusesReadyToClose: Klaar
service_waiting: Wachten op reactie
service_subject: Onderwerp
service_user: Uitgever
service_replySent: Uw reactie is verzonden
service_replySentProblem: Er is een probleem opgetreden bij het verzenden van uw antwoord
service_reportSent: Foutrapport is verzonden
service_reportSentProblem: Er is een probleem opgetreden bij het verzenden van uw rapport
service_clientAdd: Klant voegt probleem toe
service_financeData: Probleem met financiële gegevens
service_loyaltyCards: Probleem met klantenkaarten
service_other: Ander probleem dat geen storing in de wasstraat is
service_subscirption: Probleem met abonnement
service_respond: Schrijf uw reactie...
service_errorReportSubject: Onderwerp van het verzoek
contact_title: Steun
loyalApp_accessDenied: heeft geen rechten om deze bewerking te verkrijgen.
loyaltyCards_addExisting: De gegeven kaart is al geregistreerd in het systeem.
loyaltyCards_blockedTopup: De kaart moet actief zijn als u uw account wilt opwaarderen.
loyalApp_userAlreadyExists: Gebruiker bestaat al. U kunt geen bestaande gebruiker uitnodigen
loyaltyCards_filtersActive: Actief
loyaltyCards_activity: Activiteit
service_allFeminine: Alle
loyaltyCards_blocked: Geblokkeerd
loyalApp_codeUsed: Code gebruikt
service_completed: Voltooid
loyalApp_ctime: Tijd van creatie
filters.daterange.all: Altijd
common_currentYear: Huidige jaar
common_custom: Aangepast
common_previousMonth: Vorige maand
common_previousYear: Vorig jaar
common_today: Vandaag
common_daterangeYesterday: Gisteren
loyaltyCards_funds: Afmetingen
loyalApp_groupName: Groepsnaam
common_inPeriod: In periode
common_inPeriodCustom: Kies een datumbereik
loyaltyCards_names: Namen
service_filtersOpen: Open
loyalApp_unusedCode: Ongebruikte code
loyaltyCards_usedInPeriod: Gebruikt in periode
loyaltyCards_withFounds: Met fondsen
loyaltyCards_withInvoice: Uitgereikte factuur
loyaltyCards_withNames: Met namen
loyaltyCards_withoutFounds: Zonder geld
loyaltyCards_withoutInvoice: Geen factuur
loyaltyCards_withoutNames: Zonder namen
fiscal_transactions.details.heading: Transactie details
fiscal_transactions.export.name: fiscale_transacties
fiscal_transactions.for: voor
fiscal_transactions.from: van
fiscal_transactions.heading: Belastingtransacties
fiscal_transactions.modal_add.heading: Voeg belastingtransactie toe
fiscal_transactions.modal_add.isu: ISU-code
fiscal_transactions.modal_add.location: Locatie code
fiscal_transactions.modal_add.password: Certificaatwachtwoord
fiscal_transactions.source.CAR_WASH: Stellage
fiscal_transactions.source.DISTRIBUTOR: Distributeur
fiscal_transactions.source.MONEY_CHANGER: Geld wisselen
fiscal_transactions.source.UNKNOWN: Onbekend
fiscal_transactions.source.VACUUM_CLEANER: Stofzuiger
fiscal_transactions.table.carwash: Carwash
fiscal_transactions.table.date: Datum
fiscal_transactions.table.fiscal: Toestand
fiscal_transactions.table.net: Netto
fiscal_transactions.table.type: Typische transactie
fiscal_transactions.table.value: Waarde
fiscal_transactions.table.vat: Vat
fiscal_transactions.to: naar
fiscal_transactions.type.BANKCARDS: Betaling met bankkaarten
fiscal_transactions.type.BANK_CARDS: Betaling met bankkaarten
fiscal_transactions.type.CARWASH_MANAGER: Transactie handmatig toegevoegd door de eigenaar van de wasstraat
fiscal_transactions.type.CASH: Contant geld
fiscal_transactions.type.CASHLESS: Geldloos
fiscal_transactions.type.CHARGE_BAY: Opwaarderen van posities vanuit de wisselautomaat
fiscal_transactions.type.COINS: Betaling met munten
fiscal_transactions.type.HOPPER_A: Hopper
fiscal_transactions.type.HOPPER_B: Hopper B
fiscal_transactions.type.LOYALTY_PAYING: Betaling met klantenkaart
fiscal_transactions.type.LOYALTY_PROMO: Promotionele opwaardering van een klantenkaart
fiscal_transactions.type.LOYALTY_RECHARGE: Een klantenkaart opwaarderen
fiscal_transactions.type.LOYALTY_SELLING: Klantenkaart vuil
fiscal_transactions.type.MOBILE: Mobiele betalingen
fiscal_transactions.type.NOTES: Betaling met bankbiljetten
fiscal_transactions.type.PROMO: Promotionele opwaarderingen van carwash-stands
fiscal_transactions.type.SERVICE: Dienstimpulsen
fiscal_transactions.type.TOKENS: Betaling met tokens
common_formAddress: Adres
common_city: Stad
common_clientBasicData: Basis data
common_clientInvoiceData: Gegevens nodig voor de factuur
loyaltyCards_confirmCustomerData: Bevestig de gegevens van de klant voor facturering
common_country: Land
loyaltyCards_currency: Munteenheid
common_fullCustomerName: Volledige klantnaam
common_formName: naam
common_postCode: Postcode
common_surname: achternaam
common_fieldRequired: Veld vereist
loyalApp_infoPositiveNumberOnly: De waarde moet een positief getal zijn.
loyaltyCards_infoValidKey: U moet "0" tot "9" en letters van "a" tot "f" gebruiken.
loyaltyCards_infoValidKeyLength: BKF-sleutelnummer moet 8 tekens lang zijn.
loyalApp_infoValidNumberLength: De waarde moet 4 tekens lang zijn.
loyalApp_infoValidQuantity: U moet een nummer tussen "0" en "9" gebruiken.
loyalApp_invalidValue: Ongeldige waarde.
loyaltyCards_phoneNumber: Onjuist telefoonnummer
loyaltyCards_topUpPositiveNumberOnly: Het opwaardeerbedrag moet een positieve waarde zijn.
loyaltyCards_topUpValueToBig: Het opwaardeerbedrag is te hoog.
loyalApp_validDescriptionLength: De beschrijving mag maximaal 120 tekens bevatten.
user_accountNumber: Bankrekeningnummer (iban)
user_contactEmail: E-mailcontact
user_contactEmailTooltip: 'E-mail voor contact in de voettekst van e-mails die naar klanten worden verzonden.'
common_dataToInvoice: Gegevens voor de factuur
user_editInvoiceDataAlert: 'Om de geblokkeerde velden te wijzigen, kunt u contact met ons opnemen via het e-mailadres:'
common_emailCopyEmail: E-mailadressen van kopieën van facturen
common_invoiceCompanySettingsName: Volledige naam van het bedrijf
common_pressEnterToAddNew: Druk op Enter om een nieuw adres toe te voegen
common_taxNumber: Identificatienummer van de belasting
loyaltyCards_invoiceSettings: Facturatie-instellingen
loyaltyCards_invoiceNumerator: Factuurnummer
common_logo: Logo op de factuur
loyaltyCards_longMonthFormat: maand in formaat 01 (voor januari)
loyaltyCards_longYearFormat: jaar in het formaat 2018
user_nextInvoiceNumber: Nog een factuurnummer
loyaltyCards_notOwnerAlert: Alleen de eigenaar van de wasstraat kan de facturatie-instellingen wijzigen
common_paymentPeriod: Betalingsdeadline
loyaltyCards_settings: Instellingen
loyaltyCards_shortMonthFormat: maand in formaat 1 (voor januari)
loyaltyCards_shortYearFormat: jaar in formaat 18
loyaltyCards_vatTax: BTW-tarief
common_loading: Bezig met laden...
subscription_logerPeriodBetterPrice: Langere periode betere prijs
contact_header: Uitloggen
contact_loggedOutMessage: U bent uitgelogd omdat uw sessie is verlopen. Log opnieuw in.
login_email: E-mailadres
login_login: Log in
login_loginFailure: Inloggen mislukt.
login_password: Wachtwoord
login_passwordForgot: Wachtwoord vergeten
loyalApp_balanceFleetMemberNotify: Vloot
loyalApp_balanceFleetMemberTooltip: Vlootledenaccount. Alle handelingen worden uitgevoerd op basis van het saldo van de wagenparkbeheerder.
loyalApp_fleetManagerActive: Gebruiker is wagenparkbeheerder
loyalApp_fleetManagerActiveMessage: Als u op de schakelaar klikt, wordt de gebruiker gemarkeerd als een normaal account.
loyalApp_fleetManagerInactive: Gebruiker is geen wagenparkbeheerder
loyalApp_fleetManagerInactiveMessage: Als u op de schakelaar klikt, wordt de gebruiker gemarkeerd als wagenparkaccount.
common_invoices: Facturen
loyal-app-manager.no-invoice-data-message: Geen gegevens voor facturatie, neem contact op met wagenparkbeheerder om deze in te vullen.
loyalApp_promotionalCodes: Promotiecodes
loyalApp_promotionalPackages: Promotionele pakketten
loyalApp_regularUser: Regelmatige gebruiker
loyalApp_trustedPartner: Vertrouwde partner
loyalApp_trustedPartnerActive: Gebruiker is een vertrouwde partner
loyalApp_trustedPartnerActiveMessage: Als u op de schakelaar klikt, wordt de gebruiker zoals gewoonlijk gemarkeerd. Het opwaarderen van het spaarvarken zonder onmiddellijke betaling is onmogelijk.
loyalApp_trustedPartnerInactive: Gebruiker is een vertrouwde partner
loyalApp_trustedPartnerInactiveMessage: Klik op de schakelmarkering als vertrouwde gebruiker. Hierdoor kan de gebruiker het saldo aanvullen, zonder onmiddellijke betaling uit te voeren. De factuur wordt automatisch uitgegeven.
loyalApp_usersList: Gebruikers lijst
loyalApp_usersListHeading: Lijst met gebruikers van de loyaliteitsapp
loyaltyCards_loyaltyTopupsHistoryHeading: Geschiedenis van opwaarderen van loyaliteitskaarten
menu_administration: Administratie
common_administrationCarwashes: Autowasstraten
menu_administrationUsers: Gebruikers
common_alarmHistory: Alarmgeschiedenis
loyaltyCards_clients: Klanten
menu_cmdashboard: Bureaublad
common_companyData: Gegevens van het bedrijf
menu_finance: Financiën
menu.finance-fiscaltransactions: Belastingtransacties
common_financeMobilePayments: Mobiele betalingen
common_financeTurnover: Afzet
menu_logout: Uitloggen
common_loyalAppManager: Loyaliteitsapp
common_loyalsystem: Loyaliteitssysteem
common_moneycollect: Geldinzameling
common_processData: Data verwerken
menu_profile: Mijn profiel
common_service: Serviceverzoeken
common_subscription: Abonnement
menu_support: Steun
common_users: Gebruikers
common_markRead: Markeer als gelezen
messages_message: Bericht
menu_more: Meer
messages_noReadMessages: Geen gelezen berichten
common_noUnreadMessages: Geen ongelezen berichten
messages_read: Lezen
messages_unread: Ongelezen
messages_when: Wanneer
common_mobilePaymentInvoicesDate: Datum
common_download: Downloaden
mobile_payment_invoices.heading: Facturen
common_invoiceNumber: Factuurnummer
common_period: Periode
common_valueGross: Bruto waarde
mobile_payments.export.name: mobiele betalingen
finance_for: voor
finance_from: van
finance_heading: Mobiele betalingen
finance_carwash: Carwash
finance_date: Datum
finance_paymentType: Betalingswijze
finance_standCode: Standcode
finance_total: Totaal
common_totalOnPage: Totaal op huidige pagina
finance_value: Waarde
finance_to: naar
loyaltyCards_cardNumber: Kaartnummer
loyaltyCards_createNew: Maak een nieuwe klantenkaart aan
loyaltyCards_creationHint: Als de BKF-kaart en BKF-sleutel meer dan 8 cijfers lang zijn, moet u de eerste 8 alfanumerieke tekens herschrijven
loyaltyCards_deleteHint: Belangrijk! U kunt geen kaart toevoegen nadat deze is verwijderd.
loyaltyCards_rechargeHint: "Elk record in het geüploade bestand moet het volgende formaat hebben: 'kaartnummer'; 'opwaardeerwaarde'"
loyaltyCards_rechargeInfoHint: Upload een CSV-bestand met het bijvullen van klantenkaarten
loyaltyCards_rechargeMultiple: Meerdere navulkaarten
loyaltyCards_hint: Als u de kaart verwijdert, verdwijnt deze uit de lijst op het klantenkaartscherm. Een verwijderde kaart kan niet opnieuw aan het systeem worden toegevoegd.
loyalApp_addCode: Maak nieuwe promotiecodes aan
loyalApp_addPackage: Nieuw promotiepakket maken
loyalApp_editPackage: Pakket bewerken
loyalApp_userEdit: Bewerk gebruiker
loyaltyCards_emailInfo: De factuur wordt naar het e-mailadres van de klant verzonden
loyaltyCards_emailSend: Factuur wordt naar het opgegeven e-mailadres verzonden
modal.loyal_card_top_up_invoice.error.card_no_carwash: Kan geen bijbehorende carwash vinden
modal.loyal_card_top_up_invoice.error.card_no_client: Aan deze klantenkaart is geen klant gekoppeld
modal.loyal_card_top_up_invoice.error.invoice_exists: De factuur bestaat al in het systeem
modal.loyal_card_top_up_invoice.error.invoice_generation_failed: Het genereren van facturen is mislukt
modal.loyal_card_top_up_invoice.error.invoice_not_generated: Het genereren van facturen is mislukt
modal.loyal_card_top_up_invoice.error.invoice_send_failed: Het verzenden van de factuur is mislukt
modal.loyal_card_top_up_invoice.error.owner_no_country: De eigenaar van de carwash heeft de landinformatie niet ingesteld
modal.loyal_notifications.creation_hint: Probeer korte tekst te schrijven
modal.loyal_notifications.description: Tekst voor klanten
modal.loyal_notifications.new: Nieuwe melding
modal.loyal_notifications.preview: Voorbeeldmelding
modal.loyal_notifications.title: Titel
moneycollect.devices.CAR_WASH: Stellage
moneycollect.devices.DISTRIBUTOR: Distributeur
moneycollect.devices.VACUUM_CLEANER: Stofzuiger
finance_exchangerMoneyCollections: Geldwisselcollecties
moneycollect.export.name: geld verzamelen
finance_moneycollectFor: voor
finance_moneycollectFrom: van
finance_standMoneyCollections: Geldinzamelingen staan
finance_balance: Evenwicht
finance_bankCards: Betaald met een kaart
finance_details: Gegevens over het innen van geld
finance_emergencyDrops: Noodstortplaatsen
finance_safe: Veilig
finance_sorted: Gesorteerd
finance_sucked: Verzameld
finance_sumCash: Contant betaald
finance_sumHoppers: Besteed (hoppers)
finance_sumSale: verkoop
finance_sumSorted: Gesorteerd in geldwisselaar en kluis
finance_sumSucked: Opgehaald bij de wasstraat
finance_units: stuks
finance_unrecognized: Niet herkend
finance_yeti: YETI-collectiedetails
finance_moneycollectTo: naar
finance_yetiMoneyCollections: YETI-geldinzamelingen
name: Naam
subscription_noCarwashAttached: Er is geen carwash gekoppeld voor deze gebruiker.
subscription_alertContent: De gegevens van het bedrijf zijn niet correct ingevuld. Vul deze a.u.b. in.
number: NR.
subscription_subscription: Betaling abonnement
loyalApp_value: Betalingswaarde
processData_parameter: Parameter
common_updateTime: Laatste update
processData_value: Waarde
common_itemOld: De waarde kan verouderd zijn
user_account: Gebruikersaccount
user_baseData: Basis informatie
user_notificationNotice: Meldingsfunctie beschikbaar in het basispakket
user_notificationsSettings: Notificatie instellingen
user_moneyCollectEmail: Stuur meldingen over incassering naar het e-mailadres
user_sendAlarmNotificationEmail: Alarmmeldingen naar het e-mailadres
user_sendAlarmNotificationMobile: Alarmmeldingen voor een mobiele applicatie
user_regionalSettings: Regionale instellingen
user_reportsAndNotifications: Rapporten en meldingen
profile-configuration.reports-notice: E-mailrapportagefunctie beschikbaar in het basispakket
profile-configuration.reports-settings: Rapportinstellingen
profile-configuration.reports.fiscal-transactions: Belastingtransactierapport
profile-configuration.reports.mobile-payments: Mobiele betalingsrapportage
profile-configuration.reports.program-usage: Rapport over programmagebruik
profile-configuration.reports.used-funds: Een rapport over het gebruik van fondsen
user_error: Verzend naar het niveau "fout".
user_info: Verzend naar het niveau “Informatie”.
user_warning: Verzend naar het niveau "waarschuwing".
user_dontSend: niet verzenden
profile-configuration.sending-options.periodic.daily: stuur elke dag
profile-configuration.sending-options.periodic.monthly: elke maand versturen
profile-configuration.sending-options.periodic.weekly: elke week versturen
user_send: versturen
user_siteTitle: Mijn profiel
finance_carwashUsage: Gebruik van contactloze autowasstraten
programsusage.daily: Dagelijks gebruik
finance_programsusageFor: voor
finance_programsusageFrom: van
common_title: Verdeling van het verbruik per uur
finance_title: Percentage programma's deelt
finance_rolloverUsage: Gebruik van portaalautowasstraten
programsusage.table.brush: Borstel
programsusage.table.degreaser: Ontvetter
programsusage.table.foam: Schuim
programsusage.table.glossing: Glanzend
programsusage.table.mainwash: Handenwassen
programsusage.table.prewash: Voorwas
programsusage.table.program: Programma
programsusage.table.rims: Velgen
programsusage.table.rinsing: Spoelen
programsusage.table.sum: Totaal
programsusage.table.wash: x wassen
programsusage.table.wasxing: Waxen
finance_programsusageTo: naar
common_total: Gebruik
loyalApp_quantity: Hoeveelheid
loyalApp_packageValue: Pakketwaarde
loyalApp_loyaltyAppUserTopups: Gebruiksgeschiedenis van loyaliteitsapp
loyalApp_showAppUsageHistory: Toon de gebruiksgeschiedenis van de Loyalty-app
login_backToLogin: Terug naar Inloggen
login_forgotSuccess: Er is een link voor het opnieuw instellen van het wachtwoord naar uw e-mailadres verzonden
login_reset: Wachtwoord opnieuw instellen
login_resettingBackToLogin: Terug naar Inloggen
login_changePassword: Verander wachtwoord
login_confirmPassword: herhaal wachtwoord
login_changeSuccess: Succes! Je wachtwoord is gewijzigd
login_tokenExpired: De link voor het opnieuw instellen van het wachtwoord is verlopen
login_newPassword: Nieuw paswoord
service_button: Meld een storing
service_text: Ga naar om een serviceaanvraag aan te maken
service_serviceHeading: Serviceverzoeken
service.status.desc.completed: Voltooid
service.status.desc.open: Open
common_carwash: Carwash
admin_issueId: ID kaart
service_issueReportSource: Bron
service_issueReportedBy: Aangegeven door
service_issueTitle: Onderwerp
service_noContent: Nee blij
service_tableStatus: Toestand
service_time: Datum van publicatie
service.table.type: Vriendelijk
subscription_state: toestand
subscriptions.actions.chose-and-pay: Ding en betaal abonnement
common_canceled: geannuleerd
subscription_chose: Ding abonnement
subscription_chose2: Ding abonnement
admin_clientDiscount: korting klant
common_subscriptionsEnddate: Naar
subscription_goToDataCompletion: Ga naar gegevensaanvulling
subscription_historyList: geschiedenis van nederzettingen
common_paid: betaald
subscription_paymentSummary: Betalingsoverzicht
common_price: Grote prijs
subscription_priceForCarwashForMonth: Prijs voor 1 maand (nettoprijs) voor 1 carwash
common_processing: verwerken
common_startDate: Van
subscription_goToAbonamentLength: Ga naar factureringsperiode kiezen
subscription_orderWithPaymentObligation: Bestellen met betalingsverplichting
subscriptions.subscription: Abonnement
subscription_subscriptionContent: Abonnement inclusief
subscription_toPay: betalen
subscriptions.types.basic: Basis
subscriptions.types.free: Vrij
subscriptions.types.premium: Premie
subscription_yourSubscription: Uw abonnement
common_alarmId: Alarm-ID
common_alarmLevel: Alarmniveau
common_all: Alle
loyalApp_averagePaymentValue: Gemiddelde betalingswaarde
loyalApp_balance: Evenwicht
loyaltyCards_cardFunds: Geld op de kaart
common_carwashes: Autowasstraten
common_client: Klant
table.company_name: Bedrijfsnaam
common_tableCreateDate: Datum aanmaken
common_currency: Munteenheid
common_dataUnknown: Geen gegevens
common_tableDate: Datum
common_tableDescription: Beschrijving
common_email: E-mailadres
admin_filters: Filters
common_firstName: Voornaam
loyalApp_fleetManager: Vlootmanager
common_invoice: Factuur
loyalApp_invoiceNumber: Factuurnummer
loyalApp_issuanceDate: Datum van publicatie
common_language: Taal
common_lastLogin: Laatste aanmelding
dashboard_lastActualizaction: Laatste update
common_lastName: Achternaam
common_lastUsage: Laatste gebruik
common_noData: geen gegevens om weer te geven
common_notFullySent: Niet volledig verzonden
loyalApp_payment: Betaling
loyalApp_paymentCount: Aantal betalingen
common_paymentDate: Betaaldatum
loyaltyCards_payments: Betalingen
common_phone: Telefoon
loyalApp_promotionalCode: Promotiecode
common_roles: Rollen
common_rowsPerPage: "Rijen per pagina: "
common_search: Zoekopdracht
common_sendToCard: Verzonden
common_stand: Stand
common_state: Staat
loyalApp_sumPaymentValue: Totale waarde van betalingen
table.tax_number: Btw nummer
common_timezone: Tijdzone
loyalApp_title: Titel
common_toSent: Aan het wachten
loyaltyCards_toSend: Wachten om te worden verzonden
loyaltyCards_topUpsToSent: Opwaarderingen wachten op verzending
loyaltyCards_topup: Opwaardeerwaarde
common_topupSent: Vullingen
loyalApp_tableTrustedPartner: Vertrouwde partner
common_type: Vriendelijk
common_username: Gebruikersnaam
common_value: Waarde
loyaltyCards_valueAfterTransaction: Saldo na transactie
common_accept: Aanvaarden
common_cancel: Annuleren
common_termsHeading: REGLEMENT APP “CAR WASH MANAGER”
common_terms: Regulamine
common_validFrom: van toepassing vanaf 25 mei 2020
finance_minutes: min
transactions.balance_adjustment: aanpassing
transactions.car_wash: Van autowasstraat
transactions.distributor: Distributeur
transactions.export_error: Probleem met het genereren van csv
transactions.history: Transactie Geschiedenis
transactions.history_for_card: Kaarttransactiegeschiedenis voor
transactions.internet: Van het internet
transactions.money_changer: Van geldwissel
transactions.payment: Betaling
transactions.payments: Betalingen
transactions.promotions: Promotie
transactions.refill_for: Navulkaart voor
transactions.topup: Vul bij
transactions.topup_card_history: Geschiedenis van het opwaarderen van de kaart
transactions.topup_history: Opwaardeergeschiedenis
transactions.topups: Opwaarderingen
transactions.unknown: Onbekend
transactions.vacuum_cleaner: Stofzuiger
turnover.devices.distributor.plural: Distributeurs
turnover.devices.distributor.singular: Distributeur
turnover.devices.stand.plural: Standaarden
turnover.devices.stand.singular: Stellage
turnover.devices.vacuum.plural: Stofzuigers
turnover.devices.vacuum.singular: Stofzuiger
turnover.exchanger: Aandelenbeurs
finance_exportName: afzet
common_filtersCarwash: Carwash
common_deviceType: Soort apparaat
finance_turnoverFor: voor
finance_turnoverFrom: van
finance_fromLastCollection: van de laatste geldinzameling
finance_compareWithPreviousYear: Vergelijk met vorig jaar
finance_linechartTitle: Omzet in de loop van de tijd
finance_ofAllCarwashes: van alle autowasstraten
finance_ofCarwash: 'van wasstraat #'
finance_paymenttypesharepieName: Betaalmethoden delen
common_paymenttypesharepieTitle: Betaalmethoden delen
turnover.table.balance: Evenwicht
turnover.table.bankCards: Bankkaarten
turnover.table.banknotes: Bankbiljetten
turnover.table.bkfCardPay: BKF-kaartbetaling
turnover.table.bkfCardSale: BKF-kaart vuil
turnover.table.bkfKey: BKF-kaart
turnover.table.bkfKeyRecharge: BKF-kaart opwaarderen
turnover.table.carwash: Carwash
turnover.table.carwashEarnings: Som
turnover.table.carwashRecharge: Stand-top-ups
turnover.table.cash: Contante betalingen
turnover.table.cashless: Contante betalingen
turnover.table.clients: Klanten
turnover.table.coins: Hoeken
turnover.table.counter: Aantal toepassingen
turnover.table.date: Datum
turnover.table.details: Details van autowasstraat
turnover.table.detailsRollover: Portaal carwashdetails
turnover.table.exchanger: Details van geldwisselaar
turnover.table.exchangerHoppers: Geld wisselen
turnover.table.exchangerSale: Verkoop van geldwissels
turnover.table.exchanges: Uitwisselingen
turnover.table.hopperA: Hopper
turnover.table.hopperB: Hopper B
turnover.table.mobilePayments: Mobiel
turnover.table.name: Toestelnaam
turnover.table.paid: Betaald
turnover.table.post: Opwaarderen via geldwisselaar
turnover.table.prepaid: Vooruitbetaalde betalingen
turnover.table.programsSale: Programma verkoop
turnover.table.promotion: Promotie
turnover.table.saleValue: Verkoopwaarde
turnover.table.sell: Verkopen
turnover.table.service: Dienst
turnover.table.sum: Som
turnover.table.terminal: Gegevens betaalterminal
turnover.table.tokens: Munten
turnover.table.total: Totaal
common_daily: Dagelijks
finance_detailed: Gedetailleerd
common_monthly: Maandelijks
finance_tabsTotal: Totaal
finance_turnoverTitle: Omzet carwash
finance_turnoverTo: naar
finance_turnover: afzet
loyalApp_invitationInfo: Uitgenodigde gebruikers kunnen het geld van wagenparkbeheerders gebruiken om te betalen voor carwash.
subscription_whyThisPrice: Waarom deze prijs?
subscription_whyThisPriceModalHint: Onderstaande tabel geeft de berekening weer van het abonnementsbedrag op basis van eigen wasbeurten en toegekende kortingen.