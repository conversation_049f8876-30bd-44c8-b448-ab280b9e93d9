loyalApp_no: Нет
loyalApp_yes: Да
actions.actions: Действия
actions.actualize: Обновить
actions.add: Добавить
actions.add_attachment: Добавить дополнениe
actions.add_notification: Добавить уведомление
actions.add_client: Добавить дополнениe
actions.add_code: Добавить промо-коды
actions.add_key: Добавить карту лояльности
actions.add_multiple_key: Пополнить несколько карт лояльности
actions.add_package: Добавить промо-пакет
actions.add_user: добавить пользователя
actions.cancel: Отмена
actions.cancel_send: Отмена отправки
actions.chose: Выбрать
actions.clear: Очистить
actions.click_to_show_more_details: Нажмите для получения подробной информации
actions.close: Закрыть
actions.delete: Удалять
actions.download-cards-usage-report: Загрузите отчет о картах клиентов
actions.edit: Редактировать
actions.edit_user: Изменить пользователя
actions.export: Экспорт в
actions.export_csv: Экспорт CSV
actions.export_pdf: PDF экспорт
actions.export_summary: Экспорт отчётности
actions.export_transactions: Экспорт транзакций
actions.export_xlsx: Экспорт XLSX
actions.generate: Создать
actions.generate_and_download: Сгенерировать и загрузить
actions.generate_invoice: Генерировать счет
actions.invite_user: Пригласить пользователя
actions.lock: Блок
actions.postpone: Отложить
actions.refill: Купить
actions.refill_and_issue_invoice: Пополнить и выдать счета
actions.refill_card: Пополнить карту
actions.return_to_list: Назад к списку
actions.save: Сохранить
actions.save-and-add-other: Cохранить и добавить другие
actions.send: Отправить
actions.send_file: Загрузить файл
actions.send_invitation: Отправить приглашение
actions.send_invoice: Отправить счет-фактуру'
actions.service: Сервис
actions.show: Показать
actions.show-more-details: Посмотреть дополнительную информацию
actions.show_doc: Перейти к документации
actions.switch_user: Переключиться на пользователя
actions.unlock: Разблокировать
actions.update: Обновить
loyaltyCards_addedBy: Добавлено
common_address: Адрес
admin_allowSubscription: Подписка
admin_country: Страна
admin_details: Детали автомойки
admin_heading: Список всей автомойки в CM
common_owner: Владелец
admin_ownerBkf: Владелец BKF
admin_product: Товар
admin_serialNumber: Серийный номер
admin_startDate: Дата начала
admin_warrantyVoided: Конец гарантии
common_dealer: Дилер
admin_subscribersDetails: Информация о пользователе
admin_detailsButton: Подробности
admin_subscribersHeading: Список всех пользователей
admin_isDealer: Является дилером
common_nip: Налоговый номер
admin_add: Добавьте подписку
admin_alreadyCancel: Подписка уже отменена
admin_automaticPayment: Автоматический платеж.'
admin_cancel: Отменить подписку
admin_comment: Комментарий
admin_confirm: Подтвердить
admin_information: Информация о подписке
admin_manualCancelNotPossible: Ручная отмена
admin_save: Сохранить
administration.subscription.status.canceled: Аннулировано
administration.subscription.status.error: Ошибка
administration.subscription.status.initiated: Обработанный
administration.subscription.status.initiated_proforma: Создана проформа
administration.subscription.status.manually_canceled: Отменен
administration.subscription.status.paid: Оплаченный
administration.subscription.status.unknown: Неизвестный
admin_usersDetails: Данные о пользователе
admin_usersHeading: Список всех пользователей в CM
admin_isOwner: Владелец
admin_lastLogin: Последний Войти
common_usersOwner: Владелец
admin_subscriptionCode: Активная подписка
admin_subscriptionReport: Отчет по подписке
admin_subscriptionEnds: Конец последней подписки
loyaltyCards_carwash: Автомойка
common_createDate: Дата создания будильника
common_AlarmDuration: продолжительность тревоги
common_timeDuration: продолжительность
common_endDate: Дата окончания тревоги
loyalApp_trustedPartnerWithoutFleetManager: Обычный пользователь, не являющийся управляющим автопарком, не может быть доверенным партнером. Доверенным партнером может быть только пользователь автопарка'
common_actionSucced: Действие прошло успешно
service_sending: Отправка...'
loyaltyCards_topUpActionSucceed: Успешное пополнение приложения
common_errorHeader: Ошибка приложения
contact_message: Произошла ошибка приложения! Обратитесь к разработчику приложения для его решения!
dashboard_average: Иметь в виду
dashboard_max: Макс
dashboard_min: Минимум
dashboard_standsTurnover: Оборот
loyaltyCards_virtualCard: Виртуальная карта
loyaltyCards_activeTopups: Активные тоски
loyaltyCards_clientLockWarning: Ручное выставление счетов заблокировано. Чтобы включить их, измените настройки выставления счетов для выбранного клиента.'
loyaltyCards_cyclicTopUpList: Активные циклические топ
loyaltyCards_notSentList: Пополнительные в ожидании отправки
common_cardActive: Активная карта
loyaltyCards_activeMessage: Нажатие на переключатель предотвратит использование карты на автомойке. Доступные средства и история использования карты не меняются..'
common_cardBlocked: Карта заблокирована
loyaltyCards_blockedMessage: Нажатие на переключатель позволит снова использовать карту на автомойке.'
loyaltyCards_card: Kарта
loyaltyCards_cardFundsTooltip: Последняя информация на карточке, отправленная с автомойки.'
common_cards: Карты
loyaltyCards_cardsList: Список карт лояльности
loyaltyCards_cleanHistoryAndBalance: очистка карты и истории использования.
loyaltyCards_foundsWaitingForSent: Невыплаченные средства
loyaltyCards_invoiceLoading: Формирование счетов-фактур в процессе...'
loyaltyCards_invoiceSendLoading: Отправка счета-фактуры...
loyaltyCards_modifyTime: Дата модификации
loyaltyCards_name: Название карты
loyaltyCards_number: Номер карты
loyaltyCards_removal: Удаление карты
loyaltyCards_source: Источник
loyaltyCards_sourceStatus: Источник / статус
loyalApp_topup: Пополнение
common_topups: Пополнение
card.transactions: Карта транзакции
card.transactions_list: История транзакций по картам лояльности
loyaltyCards_client: Клиент
common_heading: Счета
common_invoices_heading: Счета
loyaltyCards_invoiceNumber: Номер счета
loyaltyCards_issuanceDate: Дата выпуска
loyaltyCards_serviceDate: Дата выплаты
loyaltyCards_valueGross: Валовая стоимость
loyaltyCards_valueNet: Чистая стоимость
carwash: Автомойка
common_rollover: Портальная автомойка
common_selfService: Бесконтактная автомойка
common_carwashSn: Серийный номер автомобиля
finance_error: Ошибка
loyalApp_qrCode: QR-код
common_standCode: Код поста
finance_stands: Пост
finance_topupDisabled: Пополнение баланса доступно по премиальной подписке на автомойке, поддерживающей мобильные платежи.'
finance_topup: Пополнение
finance_refillFor: Пополнить пост на
finance_topupStand: Пополнить пост
loyaltyCards_configuration: Конфигурация
loyaltyCards_generateStrategy: Стратегия формирования счетов-фактур
common_paymentMethod: Метод майки
common_cash: Денежные средства
common_transfer: Перевод
client-modal.invoice.strategies.aggregated_month: Выставить в конце месяца
client-modal.invoice.strategies.auto-after-top-up: Выставить после пополнения
client-modal.invoice.strategies.block: Не выставлять
client-modal.invoice.strategies.manual: Выставить по запросу
client-modal.invoice.strategies.undefined: '-'
loyaltyCards_editClient: Чешская Республика
loyaltyCards_heading: Редактировать клиента
loyaltyCards_invoicing: Список клиентов
Czechy: Выставление счетов
loyaltyCards_comment: Комментарий
loyaltyCards_discount: Скидка
loyaltyCards_endTime: Конец
common_cyclicTopUpsHeading: Циклические топ -образные'
loyaltyCards_lastCal: Последний звонок
loyaltyCards_startTime: Начинать
loyaltyCards_active: Активный
loyaltyCards_state: Положение дел
loyaltyCards_cyclicAdd: Циклич на сумму
loyaltyCards_cyclicAlign: Циклическое пополнение баланса
loyaltyCards_oneTime: Один раз
loyaltyCards_topUpType: Верхний тип
loyaltyCards_type: Тип
loyaltyCards_value: Ценить
common_alarms: Сообщения об ошибках
common_date: Дата
dashboard.moneycollect.CAR_WASH: Стенды
dashboard.moneycollect.MONEY_CHANGER: Разменный аппарат
dashboard.moneycollect.YETI: YETI
dashboard.moneycollect.carwash: Автомойка
dashboard.moneycollect.title: История инкасации
common_name: Название
dashboard_noPermission: Отсутствие назначенных разрешений. Для назначения, пожалуйста, свяжитесь с администратором сайта.'
common_noAlarms: нет сообщений об ошибках
dashboard_header: Доля оплаты
common_p14d: Последние 14 дней
common_p7d: Последние 7 дней
common_last: С момента последней инкассации
common_now: Cегодня
dashboard_previousMonth: Предыдущий месяц
common_sinceMonthStart: С начала текущего месяца
common_yesterday: Вчерашний день
common_software: Программного обеспечения
dashboard.subscription.component_header: Подписка на Carwash Manager заканчивается
admin_dealer: Дилер
admin_subscription: Подписка
dashboard.subscription.subscriptionClick: Если вы хотите продлить подписку, нажмите кнопку ниже.
dashboard.subscription.subscriptionEnd: Срок вашей подписки на Carwash Manager истек
dashboard.subscription.subscriptionEndIn: Ваша подписка на "Carwash Manager " заканчивается
dashboard.subscription.subscriptionEndTitle: Подписка закончилась
dashboard.subscription.subscriptionHelpText: Если у вас есть вопросы, пожалуйста, свяжитесь с нами по электронной почте
dashboard_sum: Сумма
dashboard_summary: Всего
dashboard_yourCarwash: Ваши Автомойки
date.length.12m: 12 месяцев
date.length.1m: 1 месяц
date.length.3m: 3 месяца
date.length.6m: 6 месяцев
common_selected: "{0} выбрано'"
finance_0: Воскресенье
finance_1: Понедельник
finance_2: Вторник
finance_3: Среда
finance_4: Четверг
finance_5: Пятница
finance_6: Суббота
user_deleteAccountInfo: Если вы хотите удалить свою учетную запись, пожалуйста, свяжитесь с нами по электронной почте'
service_attachment: Вложение
service_description: Описание заявки
service_heading: Новое представление
service_attachments: Вложение
service_close: Запросить закрытие проблемы
service_closedAt: Закрытый
service_createdAt: Создано
service_eventsClose: Отчет закрыт
service_readyToClose: Клиент попросил закрыть вопрос
service_start: Отчет создан
service_waitingForResponse: Ожидание ответа клиента
service_listHeading: Уведомления
service_status: Статус
service_statusesClose: Закрытый
service_new: Новый
service_open: Открыт
service_statusesReadyToClose: Готовый
service_waiting: В ожидании реакции
service_subject: Тема
service_user: Инициатор
service_replySent: Ваш ответ отправлен
service_replySentProblem: Возникла проблема с отправкой вашего ответа
service_reportSent: Отчёт об ошибке отправлен
service_reportSentProblem: При отправке отчета произошла ошибка
service_clientAdd: Проблема с добавлением клиента
service_financeData: Проблема с финансовыми данными
service_loyaltyCards: Проблема с картами лояльности
service_other: Другая проблема, которая не является неисправностью автомойки'
service_subscirption: Проблема с подпиской
service_respond: Напишите свой ответ...'
service_errorReportSubject: Тема представления
contact_title: Поддержка
loyalApp_accessDenied: У вас нет разрешения на выполнение этой операции.'
loyaltyCards_addExisting: Карта уже зарегистрирована в системе.
loyaltyCards_invalidData: Неправильные данные карты.'
loyaltyCards_blockedTopup: Карта должна быть активной для пополнения счета.'
loyalApp_problem: Ошибка
loyalApp_userAlreadyExists: Указанный пользователь уже существует. Вы не можете пригласить существующего пользователя.'
loyaltyCards_filtersActive: Aктивный
loyaltyCards_activity: Деятельность
service_allFeminine: Все
loyaltyCards_blocked: Запертый
loyaltyCards_cardType: Kortelės tipas
loyalApp_codeUsed: Использованный код
service_completed: Завершено
loyalApp_ctime: Время создания
common_currentYear: Этот год
common_custom: Любой
common_previousMonth: Предыдущий месяц
common_previousYear: Предыдущий год
common_today: Сегодня
common_daterangeYesterday: Вчера
admin_deselectAll: Снять все
loyaltyCards_funds: Меры
loyalApp_groupName: Название группы
common_inPeriod: В период
common_inPeriodCustom: Выберите диапазон дат
loyaltyCards_names: Имена
service_filtersOpen: Открыть
loyaltyCards_regular: Fizinis
admin_selectAll: выбрать все
loyalApp_unusedCode: Неиспользованный код
loyaltyCards_usedInPeriod: Использовано в течение периода
loyaltyCards_virtual: Virtualus
loyaltyCards_withFounds: С фондами
loyaltyCards_withInvoice: Выставленный счет
loyaltyCards_withNames: С именами
loyaltyCards_withoutFounds: Без средств
loyaltyCards_withoutInvoice: Нет счета-фактуры'
loyaltyCards_withoutNames: Нет имен
fiscal_transactions.details.heading: Детали сделки
fiscal_transactions.export.name: fiscal_transactions
fiscal_transactions.for: за
fiscal_transactions.from: с
fiscal_transactions.heading: Фискальные транзакции
fiscal_transactions.modal_add.heading: Добавить фискальную операцию
fiscal_transactions.modal_add.isu: Код ИСУ
fiscal_transactions.modal_add.location: Код местоположения
fiscal_transactions.modal_add.password: Пароль сертификата
fiscal_transactions.source.CAR_WASH: Стенд
fiscal_transactions.source.DISTRIBUTOR: Дистрыбутор
fiscal_transactions.source.INTERNET: Интернет
fiscal_transactions.source.MONEY_CHANGER: Разменный аппарат
fiscal_transactions.source.SCRIPT: Сервер
fiscal_transactions.source.TERMINAL: Терминал
fiscal_transactions.source.UNKNOWN: Неизвестный
fiscal_transactions.source.VACUUM_CLEANER: Пылесос
fiscal_transactions.table.carwash: Автомойка
fiscal_transactions.table.date: дата
fiscal_transactions.table.fiscal: Статус
fiscal_transactions.table.net: Нетто
fiscal_transactions.table.type: Тип сделки
fiscal_transactions.table.value: Квота
fiscal_transactions.table.vat: Vat
fiscal_transactions.table.fiscal_device: Фискальное устройство
fiscal_transactions.to: до
fiscal_transactions.type.BANKCARDS: Оплата банковскими картами
fiscal_transactions.type.BANK_CARDS: Оплата банковскими картами
fiscal_transactions.type.CARWASH_MANAGER: Транзакция добавлена вручную владельцем автомойки
fiscal_transactions.type.CASH: Наличный
fiscal_transactions.type.CASHLESS: Безналичный
fiscal_transactions.type.CHARGE_BAY: Пополнение позиций со сменного автомата
fiscal_transactions.type.COINS: Оплата монетами
fiscal_transactions.type.HOPPER_A: Хоппер А
fiscal_transactions.type.HOPPER_B: Хоппер Б
fiscal_transactions.type.LOYALTY_PAYING: Оплата картой лояльности
fiscal_transactions.type.LOYALTY_PROMO: Акционное пополнение карты лояльности
fiscal_transactions.type.LOYALTY_RECHARGE: Пополнение карты лояльности
fiscal_transactions.type.LOYALTY_SELLING: Продажа карт лояльности
fiscal_transactions.type.MOBILE: Мобильные платежи
fiscal_transactions.type.NOTES: Оплата банкнотами
fiscal_transactions.type.PROMO: Рекламные пополнения стендов
fiscal_transactions.type.SERVICE: Сервисные импульсы
fiscal_transactions.type.TOKENS: Оплата токенами
fiscal_transactions.transactions: Транзакции
common_formAddress: Адрес
common_city: Город
common_clientBasicData: Основные данные
common_clientInvoiceData: Данные для счета-фактуры'
loyaltyCards_confirmCustomerData: Подтвердите данные для выставления счета вашим клиентом
common_country: Страна
loyaltyCards_currency: Валюта
common_fullCustomerName: Полное наименование договаривающейся стороны
common_formName: Имя
common_postCode: Почтовый индекс
common_surname: Фамилия
form.validation.file_max_size_mb: Размер файла не должен превышать {size}MB'
loyalApp_infoPositiveNumberOnly: Значение должно быть положительным
loyaltyCards_infoValidKey: Вы должны использовать "0" для "9" и буквы от "a" до "f".'
loyaltyCards_infoValidKeyLength: Номер BKF Key должен быть 8 символов.'
loyalApp_infoValidNumberLength: Величина не должна быть больше 4-х цифр.'
loyalApp_infoValidNumberLength19: Значение не должно содержать более 19 цифр.'
loyalApp_infoValidQuantity: Вы должны использовать число между "0" и "9".'
loyalApp_invalidValue: Неверное значение.'
login_min: Введенное значение слишком короткое
login_passwordSame: Пароли должны быть одинаковыми
loyaltyCards_phoneNumber: Неверный номер телефона
loyaltyCards_topUpPositiveNumberOnly: Пополнение должно быть положительным.'
loyaltyCards_topUpValueToBig: Сумма пополнения слишком высокая.'
loyalApp_validDescriptionLength: Описание должно быть не более 120 символов.'
user_accountNumber: Номер банковского счета (iban)
user_contactEmail: Почта для связи
user_contactEmailTooltip: Электронная почта для контакта в нижнем колонтитуле электронных писем, отправленных клиентам.'
common_dataToInvoice: Данные для счета
user_editInvoiceDataAlert: Чтобы изменить заблокированные поля, свяжитесь с нами по адресу электронной почты
common_emailCopyEmail: Адреса электронной почты копий счета
invoiceCompanyData_logo: Логотип
common_invoiceCompanySettingsName: Полное название компании
common_pressEnterToAddNew: Нажмите Enter, чтобы добавить новый адрес'
subscription_subscriptionBuyMissingData: Чтобы обновить свои данные, пожалуйста, свяжитесь с нами
common_taxNumber: Идентификационный номер налогоплательщика
user_invoiceSettingsAccountNumber: Номер банковского счета (IBAN)
loyaltyCards_additionalInfo: Дополнительная информация
loyaltyCards_clientCardSummaryReport: Отправить отчет об использовании карты на конец месяца
loyaltyCards_invoiceSettings: Настройки выставления счетов
loyaltyCards_invoiceNumerator: Номер счета
common_logo: Логотип
common_invoice_logo: Логотип на счете
loyaltyCards_longMonthFormat: Месяц в формате 01 (для января)
loyaltyCards_longYearFormat: Год в формате 2018 года
user_nextInvoiceNumber: Еще один номер счета
loyaltyCards_notOwnerAlert: Только владелец автомойки может изменить настройки выставления счетов
common_paymentPeriod: Срок оплаты
loyaltyCards_settings: Настройки
loyaltyCards_shortMonthFormat: Месяц в формате 1 (для января)
loyaltyCards_shortYearFormat: Год в формате 18
loyaltyCards_vatTax: Скорость НДС
common_action: Действия
admin_actionHistory: История действий
common_administratorDetails: Данные администратора
admin_alarmActive: Активные тревоги (эта проблема)
admin_alarmActiveAllIssues: Активные тревоги (все проблемы)
admin_alarmDetails: Сведения о проблеме
admin_alarmId: Идентификатор проблемы
admin_alarmLevel: Уровень проблемы
admin_allAlarmsForIssue: Неактивные сигналы тревоги (данная проблема)
common_awc: AWC
common_awk: AWK
common_awp: AWP
common_carwashIp: IP
admin_carwashStatus: Статус автомойки
common_close: Закрыто
admin_closeAllCarwashes: Закрыть все автомойки
predictiveMaintenance_comment: Комментарий
admin_contactDetails: Контакты и заметки
common_description: Комментарий
admin_deselectAllIssues: Отменить выбор всех проблем
admin_dueDate: Дата отсрочки
common_duedate: Отсрочка
common_employeesDetails: Данные о сотруднике
admin_etime: Дата закрытия
common_issueHistory: История проблемы
common_issueQuantity: Количество проблем
admin_issuesList: Список тревог
common_jr: Отправлено в JR
common_lastAlarm: Обновление тревог
common_lastOnline: Мобильные платежи
common_lastSynchro: Обновление оборота
common_lastTime: Последнее событие
admin_lastUser: Последний пользователь
common_lastAttendance: Последнее подтверждение присутствия
admin_lastComment: Последний комментарий
common_lastVisit: Последнее завершенное посещение
common_management: Управление заявкой
common_new: Новый
common_nextVisit: Следующий запланированный визит
common_none: Отсутствует
common_noteDetails: Заметки об устройстве
common_open: Открытые
admin_openAllCarwashes: Открыть все автомойки
admin_otherIssues: Другие проблемы
admin_pastIssues: Проблемы закрыты
common_postpone: Отложите тревогу до дня
common_postponeForOneHour: Отложить на 60 мин.'
admin_previousIssue: Дата закрытия предыдущей проблемы
common_priority: Приоритет оповещения
common_quantity: Количество активных тревог
common_refersTo: Применяется к сигналам тревоги
admin_selectAllIssues: Выберите все проблемы
common_status: Статус проблемы
common_loading: Загрузка...
contact_header: Более длительный период - лучшая цена'
contact_loggedOutMessage: выход из системы
subscription_logerPeriodBetterPrice: Вы вышли из системы, так как срок действия вашей сессии истек. Войдите снова.'
login_email: Адрес электронной почты
login_login: Вход в систему
login_loginFailure: Вход в систему не удался. Неверный логин или пароль'
login_password: Пароль
login_passwordForgot: Забыл пароль
loyalApp_balanceFleetMemberNotify: Автопарк
loyalApp_balanceFleetMemberTooltip: Счет пользователя автопарка. Все операции осуществляются на счету управляющего автопарком.'
loyalApp_fleetManagerActive: Пользователь является управляющим автопарком
loyalApp_fleetManagerActiveMessage: Нажатие на переключатель пометит пользователя как обычного пользователя
loyalApp_fleetManagerInactive: Пользователь не является управляющим автопарком
loyalApp_fleetManagerInactiveMessage: Нажатие на переключатель пометит пользователя как учетную запись автопарка.'
common_invoices: Cчета-фактуры'
loyal-app-manager.no-invoice-data-message: Нет информации для выставления счетов, свяжитесь с менеджером вашего автопарка, чтобы заполнить их.'
loyalApp_payments: Платежи
loyalApp_promotionalCodes: Промо-коды
loyalApp_promotionalPackages: Промо-пакеты'
loyalApp_receipts: Чеки
loyalApp_regularUser: Обычный пользователь
loyalApp_selfInvoices: Самостоятельное выставление счетов
loyal-app-manager.transactions: Транзакции
loyalApp_trustedPartner: Надежный партнер
loyalApp_trustedPartnerActive: Пользователь является надежным партнером
loyalApp_trustedPartnerActiveMessage: Нажатие на переключатель обозначит пользователя как обычного. Пополнение счета-копилки без немедленной оплаты будет невозможно.'
loyalApp_trustedPartnerInactive: Пользователь не является надежным партнером
loyalApp_trustedPartnerInactiveMessage: Нажатие на переключатель пометит пользователя как надежного партнера. Это позволит пользователю пополнить свой баланс, не производя немедленной оплаты. Счет-фактура будет выставлена автоматически.'
loyalApp_client: Клиент
loyalApp_info: Информация
loyalApp_limit: Лимиты
loyalApp_stats: Статистика
loyalApp_usersList: Список пользователей
loyalApp_usersListHeading: Список пользователей приложения лояльности
loyalApp_confirmPaymentAndInvoie: Вы уверены, что хотите подтвердить оплату?'
dashboard_heading: Карты лояльности
dashboard_value: Ценить
loyalsystem-widget.values-name.cardsTopUpSumFromCM: Сумма верхних и акций от приложения CM
loyalsystem-widget.values-name.cardsTopUpSumFromCarwash: Сумма верхней и акции от релиза
loyalsystem-widget.values-name.cardsUsedCount: Количество используемых карт
loyalsystem-widget.values-name.transactionsPayments: Сумма оплаты
loyalsystem-widget.values-name.transactionsTopups: Сумма топов
loyaltyCards_loyaltyTopupsHistoryHeading: История пополнения карты лояльности
menu_administration: Администрация
common_administrationCarwashes: Шайба
menu_administrationUsers: Пользователи
common_alarmActive: активные тревоги
common_alarmHistory: История сообщений об ошибках
common_alarmManagement: Проблемы на автомойке
predictiveMaintenance_alarmsHistory: Архив проблем
menu_allSubscription: Все подписки
common_chart: График данных параметров
loyaltyCards_clients: Клиенты
menu_cmdashboard: Пульт управления
common_companyData: Данные компании
menu_finance: Финансы
common_financeCarwashRates: Ставки на карназации
menu.finance-fiscaltransactions: Фискальные транзакции
finance_financeFiscalSummary: Итоги фискальных операций
finance_summary: Итоговая информация
common_financeMobilePayments: Мобильные платежи
common_financeTurnover: Оборот
menu_logout: Выйти
common_loyalAppManager: White Label Application
common_loyalsystem: Система лояльности
common_moneycollect: Инкассация
common_processData: Данные процессов
menu_profile: Мой профиль
common_service: Билеты технической поддержки
menu_subscribers: Подписчики
common_subscription: Подписки
menu_support: Support
common_users: Пользователи
common_markRead: пометить, как прочитанное'
messages_message: Сообщение
menu_more: Более
messages_noReadMessages: Нет прочитанных сообщений
common_noUnreadMessages: Нет непрочитанных сообщений
messages_read: Читать
messages_unread: Не прочитано
messages_when: Когда
common_by: Через
common_clickToConfirm: Нажмите для подтверждения
common_confirm: Подтвердить
common_confirmHint: Вы уверены, что хотите подтвердить счет-фактуру?'
common_confirmation: Подтверждение
common_mobilePaymentInvoicesDate: Date
common_download: Скачать
mobile_payment_invoices.heading: Счета
common_invoiceConfirmed: Счет-фактура подтвержден'
common_invoiceNumber: Номер счета
loyalApp_issuerName: Имя продавца
loyalApp_issuerVatId: Vat поставщика
common_notConfirmed: Неподтвержденный
mobile_payment_invoices.not-for-app: Вид недоступен для приложения
common_period: Период
common_valueGross: Валовая стоимость
mobile_payments.export.name: Мобильные платежи
finance_for: за
finance_from: с
finance_heading: Мобильные платежи
finance_carwash: Автомойка
finance_date: Дата
finance_paymentType: Способ оплаты
finance_standCode: Код станции мойки
finance_total: Сумма
common_totalOnPage: Сумма на текущей странице
finance_value: Ценность
finance_to: до
loyaltyCards_cardNumber: Номер карты
loyaltyCards_createNew: Создать новую карту лояльности
loyaltyCards_creationHint: В случае BKF Card и BKF Key, которые имеют более 8 цифр необходимо переписать первые 8 алфавитно-цифровых символов
loyaltyCards_deleteHint: Обратите внимание, вы не можете добавить карту после удаления.
loyaltyCards_errorDescription: Описание ошибки
loyaltyCards_errorDetails: Подробности
loyaltyCards_fileLine: Линия в файле
loyaltyCards_lastFileUploadErrors: Ошибки в загруженном файле
loyaltyCards_rechargeHint: Каждая строка в загруженном файле должна иметь следующий формат "номер карты"; "стоимость пополнения".
loyaltyCards_rechargeInfoHint: Загрузите файл CSV с пополнением карты лояльности.
loyaltyCards_rechargeMultiple: Пополнить несколько карт
loyaltyCards_hint: При удалении карты она исчезнет из списка на экране карты лояльности. Удаленная карта не может быть добавлена в систему снова.
user_hint: Вы уверены, что хотите удалить этого пользователя?
loyalApp_addCode: Создать новые промо-коды
loyalApp_addPackage: Создать новый промо пакет
loyalApp_editPackage: Отредактировать пакет
loyalApp_userEdit: Изменить пользователя
loyaltyCards_emailInfo: Счет будет отправлен на ваш адрес электронной почты
loyaltyCards_emailSend: Счет будет отправлен на указанный адрес электронной почты
modal.loyal_card_top_up_invoice.error.card_no_carwash: Соответствующая автомойка не может быть найдена
modal.loyal_card_top_up_invoice.error.card_no_client: Карте не присвоен клиент
modal.loyal_card_top_up_invoice.error.invoice_exists: Счет-фактура уже существует в системе
modal.loyal_card_top_up_invoice.error.invoice_generation_failed: Не удалось сформировать счет-фактуру
modal.loyal_card_top_up_invoice.error.invoice_not_generated: Не удалось сформировать счет-фактуру
modal.loyal_card_top_up_invoice.error.invoice_send_failed: Отправка счета не удалась
modal.loyal_card_top_up_invoice.error.owner_no_country: У владельца автомойки нет загородного участка
modal.loyal_notifications.creation_hint: Старайтесь писать короткие тексты
modal.loyal_notifications.description: Текст, отправленный клиентам
modal.loyal_notifications.new: Новое уведомление
modal.loyal_notifications.preview: Предварительный просмотр уведомления
modal.loyal_notifications.title: Название
moneycollect.devices.CAR_WASH: Стенд
moneycollect.devices.DISTRIBUTOR: Дистрыбутор
moneycollect.devices.VACUUM_CLEANER: Пылесос
finance_exchangerMoneyCollections: Инкассация разменного аппарата
moneycollect.export.name: moneycollect
finance_moneycollectFor: за
finance_moneycollectFrom: с
finance_standMoneyCollections: Инкассация Стендов
finance_balance: Баланс
finance_bankCards: Оплачено картой
finance_details: Детали инкассации
finance_emergencyDrops: Аварийный сброс
finance_safe: Сейф
finance_sorted: Отсортированно
finance_sucked: Собрано
finance_sumCash: Оплачено наличными
finance_sumHoppers: Выдано (хопперы)
finance_sumSale: Продажа
finance_sumSorted: Отсортировано в разменный аппарат и сейф
finance_sumSucked: Собрано с автомойки
finance_units: шт.
finance_unrecognized: Нераспознанные
finance_yeti: Подробности инкассации YETI
finance_moneycollectTo: до
finance_yetiMoneyCollections: Инкассация YETI
name: Название
subscription_noCarwashAttached: Нет подключенной мойки для данного пользователя.
subscription_alertContent: Данные компании не выполнены правильно, пожалуйста, заполните их.
number: ПН.
user_others: Другое
common_0: 0 дней
common_1: 1 дня
common_10: 10 дней
common_14: 14 дней
common_3: 3 дня
loyalApp_30: 30 дней
common_5: 5 дней
common_7: 7 дней
subscription_subscription: Оплата подписки
loyalApp_value: Cтоимость оплаты
processData_chart: Диаграмма
processData_parameter: Параметр
common_updateTime: Последнее обновление
processData_value: Значение
common_itemOld: Ценность может быть устаревшей
user_account: Учетная запись пользователя
user_baseData: Основная информация
user_notificationNotice: Функция уведомления доступна в базовом пакете
user_notificationsSettings: Настройки уведомлений
user_moneyCollectEmail: Отправить уведомления об инвазации на адрес электронной почты
user_sendAlarmNotificationEmail: Уведомления о тревоге по адресу электронной почты
user_sendAlarmNotificationMobile: Уведомления о тревоге для мобильного приложения
user_regionalSettings: Региональные настройки
user_reportsAndNotifications: Отчеты и уведомления
profile-configuration.reports-notice: Функция отчета по электронной почте доступна в базовом пакете
profile-configuration.reports-settings: Настройки отчета
profile-configuration.reports.fiscal-transactions: Отчет о фискальной транзакции
profile-configuration.reports.mobile-payments: Отчет о мобильном платеже
profile-configuration.reports.program-usage: Отчет об использовании программы
profile-configuration.reports.used-funds: Отчет об использовании средств
user_error: Отправить с уровень "ошибки"
user_info: Отправить с уровень "информации"
user_warning: Отправить с уровень "предупреждения"
user_dontSend: не отправляй
profile-configuration.sending-options.periodic.daily: отправлять каждый день
profile-configuration.sending-options.periodic.monthly: отправлять каждый месяц
profile-configuration.sending-options.periodic.weekly: отправлять каждую неделю
user_send: Отправить
user_siteTitle: Мой профиль
finance_carwashUsage: Использование бесконтактных автомоек
programsusage.daily: Дневное использование программ
finance_programsusageFor: за
finance_programsusageFrom: с
common_title: Почасовой график
finance_overTimeTitle: Programų naudojimas laikui bėgant
finance_title: Процентная доля программ
finance_programs: Использование программ
finance_rolloverUsage: Использование портальных автомоек
programsusage.rollover_total: Итого
programsusage.table.sum: Сумма
programsusage.table.wash: x мытье
programsusage.table.water-average: Среднее потребление вода
programsusage.table.water-usage: Общее потребление вода
finance_programsusageTo: до
common_total: Использования программ
loyalApp_quantity: Количество
loyalApp_packageValue: Стоимость пакета
finance_mtime: дата модификациикундах/импульс
finance_name: Название автомойки
finance_pause: Удерживается
finance_time: Оценить просмотр в се
finance_valueForCredit: показать в
loyalApp_createDate: Дата выдачи
loyalApp_number: Номер
loyalApp_paymentDate: Дата оплаты
loyalApp_loyaltyAppUserTopups: История пользователя приложения лояльности
loyalApp_showAppUsageHistory: Просмотр истории использования приложения лояльности
login_backToLogin: Возврат к входу в систему
login_forgotSuccess: Мы отправили ссылку для сброса пароля на указанный вами адрес электронной почты
login_reset: Сбросить пароль
login_resettingBackToLogin: Вернуться к входу в систему
login_changePassword: Изменить пароль
login_confirmPassword: Повторите пароль
login_changeSuccess: Успех! Пароль изменен
login_tokenExpired: Срок действия ссылки для сброса пароля истек
login_newPassword: Новый пароль
service_button: Сообщить о неисправности
service_text: Чтобы создать билет в службу технической поддержки, перейдите на страницу
service_serviceHeading: Билеты технической поддержки
service.status.desc.completed: Завершено
service.status.desc.open: Открыть
common_carwash: Автомойка
admin_issueId: ID
service_issueReportSource: Источник
service_issueReportedBy: Сообщено
service_issueTitle: Тема
service_noContent: Нет содержания
service_tableStatus: Статус
service_time: Дата отчета
subscription_state: Статус
admin_subscriptionListHeading: Список всех подписок
subscription.carwash-type.STANDARD: Стандартный
subscription.carwash-type.UNSUBSCRIBED: Без подписки
subscription.carwash-type.WARRANTY: Гарантия
admin_subscriptionDealer: Дилер
common_document: Документ
subscription.document.issue: Документ
subscription.document.send: Выставить
admin_issueAndSend: Выставить и отправить
admin_subscriber: Подписчик
subscription.table.STANDARD: Пост -Warranty Washes
subscription.table.UNSUBSCRIBED: Автомойки без подписки
subscription.table.WARRANTY: Гарантия
subscription.table.discount: Скидка
subscription.table.position: Позиция
subscription.table.price-after-discount: Цена со скидкой
subscription.table.price-before-discount: Цена до скидки
subscription.table.sum: Итоговая сумма
subscription.table.summary: Краткое содержание
subscription.table.type: Тип
admin_whoPays: Кто платит?
subscriptions.actions.chose-and-pay: Выберите и оплатите подписку
subscription_back: вернуться
subscription_chose: Выбор подписки
subscription_chosePaymentPeriod: Выберите период оплаты
subscription_chose2: Выберите подписку
subscriptions.delete-question: Вы хотите отменить выбранную подписку?
admin_clientDiscount: Скидка клиента
common_subscriptionsEnddate: До
subscription_goToDataCompletion: Перейти к заполнению данных
subscription_historyList: История бухгалтерского учёта
subscription_alarmsList: Список сигналов тревоги - активных и исторических
subscription_cyclicFiscalMailReports: Периодические финансовые отчеты по электронной почте
subscription_loyalcardsCyclicTopup: Циклическое пополнение карт лояльности
subscription_loyalcardsInvoicing: Выставление счетов по картам лояльности
subscription_loyalcardsRemoteTopups: Удаленное пополнение постов мойки
subscription_loyalcardsReport: Отчет об использовании карт лояльности для клиентов
subscription_loyalcardsTransactionHistory: История операций на автомойке вместе с подробной фискальной информацией
subscriptions.options.loyalcards-transactions-history: CRM - история транзакций, удаленное пополнение счета, блокировка карт лояльности
subscription_mailReports: Отчеты по электронной почте
subscription_mobileAppAccess: Доступ с помощью мобильного приложения
subscription_moneycollectControll: Контроль за поступлением наличных денег с автомойки (отчеты о состоянии денежных средств)
subscription_newAlarmNotifications: Уведомления о новых тревогах на мойке (электронная почта, push)
subscription_serviceTicketPreview: Просмотр статуса заявок на обслуживание
subscription_technicalData: Просмотр текущих рабочих параметров автомойки
subscription_unlimitedUsers: Неограниченное количество пользователей и распределение индивидуальных прав
subscription_usageStatistics: Статистика использования программ на мойке
subscription_wwwAccess: Доступ через веб-браузер
common_error: Ошибка
common_paid: Оплачено
subscription_paymentSummary: Сводка платежей
common_price: Цена с НДС
subscription_priceForCarwashForMonth: Цена за 1 месяц (нетто) за 1 автомойку
common_processing: Обработка
common_refund: Возврат
common_startDate: Од
subscription_checkInvoiceData: Проверьте данные счета
subscription_goToAbonamentLength: Перейти к выбору периода подписки
subscription_orderWithPaymentObligation: Заказ с платежным обязательством
subscriptions.subscription: Подписка
subscription_subscriptionContent: Cодержимое подписки
subscription_summary: Всего
common_timeout: Время превышено
subscription_toPay: К оплате
subscriptions.types.basic: Базовый
subscriptions.types.free: Бесплатно
subscriptions.types.premium: Премиум
common_unknown: Неизвестный
admin_vat: Vat
subscription_yourSubscription: Ваша подписка
common_success: Успех
loyalApp_account: Счет
admin_added: Добавлено
common_alarmDetails: Детали тревоги
common_alarmId: Идентификатор тревоги
common_alarmLevel: Уровень тревоги
common_all: Все
loyalApp_averagePaymentValue: Средняя стоимость платежа
loyalApp_balance: Счет
loyaltyCards_cardFunds: Средства на карте
common_carwashes: Автомойки
common_client: Kлиент
common_comment: Комментарий
table.company_name: Название компании
common_tableCreateDate: Дата создания
common_currency: Валюта
loyaltyCards_cyclic: Циклический
common_dataUnknown: Oтсутствие данных
common_tableDate: Дата
common_tableDescription: Описание
common_discount: Скидка
common_email: Email
loyalApp_externalId: Внешний идентификатор
loyalApp_externalType: Тип оплаты
loyalApp_externalValue: Сумма оплаты
admin_filters: Фильтры
common_firstName: Имя
loyalApp_fleetManager: Управляющий флотом
common_id: идентификатор
common_invoice: Фактура
loyalApp_invoiceNumber: Номер счёта-фактуры
loyalApp_issuanceDate: Дата выдачи
common_language: Язык
common_lastLogin: Последний вход
dashboard_lastActualizaction: Последнее обновление
common_lastName: Фамилия
common_lastUsage: Последнее использование
loyalApp_licensePlate: Номерной знак
loyalApp_managerEmail: Адрес менеджера автопарка
common_noData: Нет данных для отображения
common_notFullySent: Не полностью отправлено
admin_tableOwnerBkf: ID владельца БКФ
loyalApp_payment: Оплата
loyalApp_paymentCount: Количество платежей
common_paymentDate: Дата оплаты
common_phone: Телефон
loyalApp_promotionalCode: Промо-код
common_receipt: Paragon
common_regonNumber: Regon
common_roles: Роли
common_rowsPerPage: 'Количество строк на странице:'
loyalApp_salesDocument: Продажный документ
common_search: Поиск
common_sendToCard: Отправлено
common_stand: Стенд
common_state: Статус
loyaltyCards_sum: Всего
loyalApp_sumPaymentValue: Общая стоимость платежей
table.tax_number: ИНН
loyalApp_taxNumber: ИНН
common_timezone: часовой пояс
loyalApp_title: Название
common_toSent: Ожидающий
loyaltyCards_toSend: ожидание отправки
loyaltyCards_topUpsToSent: Пополнения, ожидающие реализации
loyaltyCards_topup: стоимость пополнения
loyalApp_topupBonus: Бонус
common_topupCode: Промо-Код
common_topupSent: Перезаряды
common_transactionType: Тип транзакции
loyalApp_transactionValue: Стоимость транзакции
loyalApp_tableTrustedPartner: Надежный партнер
common_type: Тип
common_user: Пользователь
common_username: Имя пользователя
common_value: Квота транзакции
loyaltyCards_valueAfterTransaction: Баланс после транзакции
loyalApp_vat: Vat
loyaltyCards_tableVirtualCard: Virtualioji kortelė
admin_whoAdded: Добавлено пользователем
common_accept: Принять
common_cancel: Отменить
common_termsHeading: РЕГЛАМЕНТ К ПРИЛОЖЕНИЯ CAR WASH MANAGER
common_terms: Регламент
common_validFrom: вступает в силу с 25 мая 2020 года
finance_minutes: мин
transactions.balance_adjustment: Выравнивание
transactions.car_wash: Автомойка
transactions.distributor: Дистрибьютор
transactions.export_error: Проблема с экспортом отчета
transactions.history: История транзакций
transactions.history_for_card: История транзакций по карте для
transactions.internet: Интернет
transactions.money_changer: Разменный аппарат
transactions.payment: Оплата
transactions.payments: Платежи
transactions.promotions: Распродажа
transactions.refill_for: Пополнить карту с
transactions.top-up-code: Промо-код
transactions.topup: Пополнение
transactions.topup_card_history: История пополнения карты
transactions.topup_history: История пополнения
transactions.topups: Пополнение
transactions.unknown: Неизвестный
transactions.vacuum_cleaner: Пылесос
turnover.devices.distributor.plural: Дистрибьюторы
turnover.devices.distributor.singular: Дистрыбутор
turnover.devices.stand.plural: Стенды
turnover.devices.stand.singular: Стенд
turnover.devices.vacuum.plural: Пылесосы
turnover.devices.vacuum.singular: Пылесос
finance_exportName: turnover
common_filtersCarwash: Автомойка
common_deviceType: Тип устройства
finance_turnoverFor: за
finance_turnoverFrom: с
finance_fromLastCollection: из последнего сбора денег
finance_compareWithPreviousYear: Сравните с предыдущим годом
finance_linechartTitle: Финансовый оборот за период времени
finance_ofAllCarwashes: все автомойки
finance_ofCarwash: 'автомойка #'
finance_paymenttypesharepieName: Доля типов платежей
common_paymenttypesharepieTitle: Доля типов платежей
turnover.table.balance: Остаток
turnover.table.bankCards: Банковские карты
turnover.table.banknotes: Банкноты
turnover.table.bkfCardPay: Оплата BKF Card
turnover.table.bkfCardSale: Продажа карты BKF
turnover.table.bkfKey: BKF Card
turnover.table.bkfKeyRecharge: Пополнение карты BKF
turnover.table.carwash: Автомойка
turnover.table.carwashEarnings: заработок на автомойке
turnover.table.carwashRecharge: Пополнение моющих постов
turnover.table.cash: Наличные платежи
turnover.table.cashless: Безналичный расчет
turnover.table.clients: Клиенты
turnover.table.coins: Монеты
turnover.table.counter: Количество применений
turnover.table.date: Дата
turnover.table.details: Детали автомойки
turnover.table.detailsRollover: Подробная информация о портале автомойки
turnover.table.exchanger: Реквизиты обменного пункта
turnover.table.exchangerHoppers: Обмен денег
turnover.table.exchangerSale: Обменный пункт продать
turnover.table.exchanges: Биржи
turnover.table.hopperA: Hopper A
turnover.table.hopperB: Hopper B
turnover.table.mobilePayments: Мобильный
turnover.table.name: Устройство
turnover.table.paid: Платно
turnover.table.post: Пополнения с разменного аппарата
turnover.table.prepaid: Предоплаченные платежи
turnover.table.programsSale: Продажа программ
turnover.table.promotion: Акция
turnover.table.saleValue: Стоимость продаж
turnover.table.sell: Продать
turnover.table.service: Сервис
turnover.table.sum: Сумма
turnover.table.terminal: Реквизиты платежного терминала
turnover.table.tokens: Жетоны
turnover.table.total: всего
common_daily: Ежедневный
finance_detailed: Подробный
common_monthly: Ежемесячный
finance_tabsTotal: Общий
finance_turnoverTitle: оборот
finance_turnoverTo: до
finance_turnover: финансовый оборот
unknown: Неизвестный
finance_cubicMeters: m³
finance_litres: l
loyalApp_discount: Скидка
loyalApp_max: Максимум
loyalApp_min: Минимум
loyalApp_term: Условия оплаты
common_userEmailAlreadyExist: Пользователь с указанным адресом электронной почты уже существует в нашей базе данных.
loyalApp_invitationInfo: Приглашенный пользователь сможет воспользоваться средствами со счета управляющего автопарком.
subscription_whyThisPrice: Почему именно такая цена?
subscription_whyThisPriceModalHint: В таблице ниже приведен расчет стоимости подписки на основании данных о принадлежащих автомойках и полученных скидках.
loyalApp_topupAccountTitle: 'Пополнить счет пользователя '
loyalApp_topupType: 'Тип пополнения '
loyalApp_bonus: 'Бонус '
loyalApp_invoice: Фактура
loyalApp_topupByAmount: 'Полполнить счет на квоту '
loyalApp_topupAccountTopup: 'Пополнить '
common_exportAsyncTitle: Создание отчета
common_generating: Формирование отчета
common_export: Экспорт
common_downloadReport: Скачать отчет
common_reportReady: Отчет готов к скачиванию
common_clearReport: Очистить
actions.export_csv_summary: Экспорт в формате CSV
actions.export_xlsx_summary: Экспорт в формате XLSX
actions.export_pdf_summary: Экспорт в формате PDF
actions.chose_report_to_generate: Выбор отчета для формирования
common_reports: 'Отчеты '
common_reportDownloadOnList: Отчет для скачивания в списке отчетов
finance_header: "Список сгенерированных отчетов\t"
finance_createTime: Дата создания
finance_endTime: "Дата формирования\t"
finance_status: Статус
finance_reportName: Имя отчета
finance_user: Пользователь
common_progressNew: Новый
common_process: Обработка
common_done: Готовый
common_progressError: Ошибка
finance.fiscal-transactions: Фискальные транзакции
card_client_report.card_report_email_header: Отчет об использовании клиентских карт
common_turnoverFrom: Использованы средства на автомойке за период
card_client_report.cards_transactions: Карты переходов
common_mobilePayments: Мобильные платежи
common_programUsage: Использование программ
fiscal.source.CAR_WASH: Пост
fiscal.source.DISTRIBUTOR: Дистрибьютор
fiscal.source.MONEY_CHANGER: Разменный аппарат
fiscal.source.UNKNOWN: Неизвестный
fiscal.source.VACUUM_CLEANER: Пылесос
finance_success: Успех
finance_initiated: Инициировано
finance_refused: Отклонено
dashboard.table-details: Подробности
programs.brush: Щетка
programs.degreaser: Обезжириватель
programs.foam: Пена
programs.glossing: Блеск + сушка
programs.mainwash: Основная мойка
programs.prewash: Предварительная мойка
programs.rims: Диски
programs.rinsing: Ополаскивание
programs.wasxing: Воск
table.serialnumber: Последовательный
thisWeek: На этой неделе
lastWeek: На прошлой неделе
discountPercentage: Скидка (%)
discountValue: Сумма скидки
yes: Да
no: Нет
amountToPay: Сумма к оплате
downloadInvoice: Скачать счет
common_actions: Действия
loyalSystem_packages: Пакеты
loyalApp_stands: Пост
loyalApp_carwashBasicData: Основные данные
filter_paid: Оплачено
filter_cancelled: Отменен
filter_canceled: Отменен
filter_confirmed: Подтверждено
filter_timeout: Время превышено
filter_refunded: Возврат
filter_unknown: Неизвестный
filter_error: Ошибка
transaction_type_TOP_UP: Пополнение
transaction_type_PAYMENT: Оплата
transaction_type_TOP_UP_BONUS: Бонус
