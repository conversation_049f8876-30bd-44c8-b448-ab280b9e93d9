loyalApp_no: No
loyalApp_yes: Yes
actions.actions: Action
actions.actualize: Actualize
actions.add: Add
actions.add_attachment: Add attachment
actions.add_notification: Add notification
actions.add_client: Add client
actions.add_code: Add promotional code
actions.add_key: Add loyalty card
actions.add_multiple_key: Recharge multiple loyalty cards
actions.add_package: Add promotional package
actions.add_user: Add user
actions.cancel: Cancel
actions.cancel_send: Cancel sending
actions.chose: Select
actions.clear: Clear
actions.click_to_show_more_details: Click to show more details
actions.close: Close
actions.delete: Delete
actions.download-cards-usage-report: Download the customer cards report
actions.edit: Edit
actions.edit_user: Edit user
actions.export: Export to
actions.export_csv: Export CSV
actions.export_pdf: Export PDF
actions.export_summary: Summary export
actions.export_transactions: Transactions export
actions.export_xlsx: Export XLSX
actions.generate: Generate
actions.generate_and_download: Generate and download
actions.generate_invoice: Generate an invoice
actions.invite_user: Invite user
actions.lock: Lock
actions.postpone: Postpone
actions.refill: Topup
actions.refill_and_issue_invoice: Recharge and Generate an invoice
actions.refill_card: Recharge
actions.return_to_list: Back to the list
actions.save: Save
actions.save-and-add-other: Save and add other
actions.send: Send
actions.send_file: Send file
actions.send_invitation: Send invitation
actions.send_invoice: Send invoice
actions.service: Service
actions.show: Show
actions.show-more-details: Show more details
actions.show_doc: Go to the documentation
actions.switch_user: Switch user
actions.unlock: Unlock
actions.update: Save
loyaltyCards_addedBy: Added by
common_address: Address
admin_allowSubscription: Subscription active
admin_country: Country
admin_details: Details of the car wash
admin_heading: List of all car wash in cm
common_owner: Owner
admin_ownerBkf: Owner BKF
admin_product: Product
admin_serialNumber: Serial number
admin_startDate: Start date
admin_warrantyVoided: End of warranty
common_dealer: Dealer
admin_subscribersDetails: Subscriber details
admin_detailsButton: Details
admin_subscribersHeading: All subscribers list
admin_isDealer: Is dealer
common_nip: Tax number
admin_add: Add a subscription
admin_alreadyCancel: The subscription has already been canceled
admin_automaticPayment: Automatic payment.
admin_cancel: Cancel the subscription
admin_comment: Comment
admin_confirm: Confirm
admin_information: Subscription information
admin_manualCancelNotPossible: Manual subscription canceled not possible
admin_save: Save
administration.subscription.status.refund: Refund
administration.subscription.status.canceled: Canceled
administration.subscription.status.error: Error
administration.subscription.status.initiated: Processing
administration.subscription.status.initiated_proforma: Proforma initiated
administration.subscription.status.manually_canceled: Canceled manually
administration.subscription.status.paid: Paid
administration.subscription.status.unknown: Unknown
admin_usersDetails: Users details
admin_usersHeading: List of all users in cm
admin_isOwner: Is owner
admin_lastLogin: Last login
common_usersOwner: Owner
admin_subscriptionCode: Active subscription
admin_subscriptionReport: Subscription report
admin_subscriptionEnds: End of the last subscription
loyaltyCards_carwash: Carwash
common_createDate: Alarm create date
common_AlarmDuration: Alarm duration
common_timeDuration: Duration
common_endDate: Alarm end date
loyalApp_trustedPartnerWithoutFleetManager: A normal user who is not a fleet manager can be a trusted partner. Fleet Only the user can be a trusted partner.
common_actionSucced: The action was successful
service_sending: Sending...
loyaltyCards_topUpActionSucceed: Top-ups added successfully
common_errorHeader: Application error
contact_message: An application error occurred! Contact the developer of the application to solve it!
dashboard_average: Mean
dashboard_max: Max
dashboard_min: Minimum
dashboard_standsTurnover: Stand turnover
loyaltyCards_virtualCard: Virtual card
loyaltyCards_activeTopups: Active topups
loyaltyCards_clientLockWarning: Manual invoicing is blocked. To turn them on, change the invoicing settings for the selected client.
loyaltyCards_cyclicTopUpList: Active cyclic topups
loyaltyCards_notSentList: Top ups waiting to send
common_cardActive: Card active
loyaltyCards_activeMessage: Clicking the switch will prevent the card from being used on the car wash. Available funds and card usage history will not change.
common_cardBlocked: Card blocked
loyaltyCards_blockedMessage: Clicking the switch will allow you to reuse the card at the car wash.
loyaltyCards_card: Card
loyaltyCards_cardFundsTooltip: Last information about the funds on the card sent by car washes
common_cards: Cards
loyaltyCards_cardsList: Loyalty card list
loyaltyCards_cleanHistoryAndBalance: clears the funds on card, and usage history.
loyaltyCards_foundsWaitingForSent: Funds waiting to be sent
loyaltyCards_invoiceLoading: Invoice generation in progress...
loyaltyCards_invoiceSendLoading: Sending invoice...
loyaltyCards_modifyTime: Modification date
loyaltyCards_name: Card name
loyaltyCards_number: Card number
loyaltyCards_removal: Remove card
loyaltyCards_source: Source
loyaltyCards_sourceStatus: Source / status
loyalApp_topup: Recharge
common_topups: Topups
card.transactions: Cards transactions
card.transactions_list: Loyalty card transaction history
loyaltyCards_client: Client
common_heading: Invoices
common_invoices_heading: Invoices
loyaltyCards_invoiceNumber: Invoice Number
loyaltyCards_issuanceDate: Issuance Date
loyaltyCards_serviceDate: Service Date
loyaltyCards_valueGross: Gross value
loyaltyCards_valueNet: Net value
carwash: Car wash
common_rollover: Rollover car wash
common_selfService: Self-service car wash
common_carwashSn: Carwash serial number
finance_error: Error
loyalApp_qrCode: QR code
common_standCode: Stand code
finance_stands: Stands
finance_topupDisabled: Topping up the position available in the Premium subscription on a car wash supporting mobile payments.
finance_topup: Top Up
finance_refillFor: Top up the position Fr.
finance_topupStand: Top up the position
loyaltyCards_configuration: Configuration
loyaltyCards_generateStrategy: Invoice generation strategy
common_paymentMethod: Payment method
common_cash: Cash
common_transfer: Transfer
client-modal.invoice.strategies.aggregated_month: Generate at the end of the month
client-modal.invoice.strategies.auto-after-top-up: Generate after top-up
client-modal.invoice.strategies.block: Do not issue
client-modal.invoice.strategies.manual: Generate on request
client-modal.invoice.strategies.undefined: '-'
loyaltyCards_editClient: Edit customer
loyaltyCards_heading: Customer List
loyaltyCards_invoicing: Invoicing
Czechy: Czech Republic
loyaltyCards_comment: Comment
loyaltyCards_discount: Discount
loyaltyCards_endTime: End
common_cyclicTopUpsHeading: Cyclic top-ups
loyaltyCards_lastCal: Last call
loyaltyCards_startTime: Start
loyaltyCards_active: Active
loyaltyCards_state: State
loyaltyCards_cyclicAdd: Cyclical for the amount
loyaltyCards_cyclicAlign: Cyclical recharging to the balance
loyaltyCards_oneTime: Onetime
loyaltyCards_topUpType: Top-up type
loyaltyCards_type: Type
loyaltyCards_value: Value
common_alarms: Alarms
common_date: Time
dashboard.moneycollect.CAR_WASH: Stands
dashboard.moneycollect.MONEY_CHANGER: Money changer
dashboard.moneycollect.YETI: YETI
dashboard.moneycollect.carwash: Carwash
dashboard.moneycollect.title: Money collect history
common_name: Name
dashboard_noPermission: There are no assigned permissions. To assign please contact site administrator.
common_noAlarms: No alarms
dashboard_header: Payments share
common_p14d: Last 14 days
common_p7d: Last 7 days
common_last: Since last money collection
common_now: Today
dashboard_previousMonth: Previous month
common_sinceMonthStart: Current month
common_yesterday: Yesterday
common_software: Software
dashboard.subscription.component_header: Carwash Manager Subscription Ends
admin_dealer: Dealer
admin_subscription: Subscription
dashboard.subscription.subscriptionClick: If you want to extend your subscription, click the button below.
dashboard.subscription.subscriptionEnd: Your subscription to Carwash Manager has expired
dashboard.subscription.subscriptionEndIn: 'Your subscription to Carwash Manager end in:'
dashboard.subscription.subscriptionEndTitle: Subskrypcja wygasła
dashboard.subscription.subscriptionHelpText: 'If you have questions, please contact us by e-mail:'
dashboard_sum: Sum
dashboard_summary: Summary
dashboard_yourCarwash: Your carwashes
date.length.12m: 12 months
date.length.1m: 1 month
date.length.3m: 3 months
date.length.6m: 6 months
common_selected: '{0} selected'
finance_0: Sunday
finance_1: Monday
finance_2: Tuesday
finance_3: Wednesday
finance_4: Thursday
finance_5: Friday
finance_6: Saturday
user_deleteAccountInfo: To delete your account in the application, please contact us by e-mail
service_attachment: Attachment
service_description: Description
service_heading: New submission
service_attachments: Attachments
service_close: Request issue closure
service_closedAt: Closed
service_createdAt: Created
service_eventsClose: Issue closed
service_readyToClose: Client has asked for the issue to be closed
service_start: Issue created
service_waitingForResponse: Waiting for client's response
service_listHeading: Issues
service_status: Status
service_statusesClose: Closed
service_new: New
service_open: Open
service_statusesReadyToClose: Ready
service_waiting: Waiting for reaction
service_subject: Subject
service_user: Issuer
service_replySent: Your response has been sent
service_replySentProblem: There was a problem sending your response
service_reportSent: Error report has been sent
service_reportSentProblem: There was a problem sending your report in
service_clientAdd: Klient add problem
service_financeData: Finance data problem
service_loyaltyCards: Loyalty cards problem
service_other: Other problem that is not a car wash malfunction
service_subscirption: Subscription problem
service_respond: Write your response...
service_errorReportSubject: Subject of the request
contact_title: Support
loyalApp_accessDenied: You don't have permissions to obtain this operation.
loyaltyCards_addExisting: Given card has already been registered in the system.
loyaltyCards_invalidData: Wrong card data
loyaltyCards_blockedTopup: The card must be active if you want to top up your account.
loyalApp_problem: Error
loyalApp_userAlreadyExists: User already exists. You can't invite existing user
loyaltyCards_filtersActive: Active
loyaltyCards_activity: Activity
service_allFeminine: All
loyaltyCards_blocked: Blocked
loyaltyCards_cardType: Card type
loyalApp_codeUsed: Code used
service_completed: Completed
loyalApp_ctime: Time of creation
common_currentYear: Current year
common_custom: Custom
common_previousMonth: Previous month
common_previousYear: Previous year
common_today: Today
common_daterangeYesterday: Yesterday
admin_deselectAll: Deselect all
loyaltyCards_funds: Measures
loyalApp_groupName: Group name
common_inPeriod: In period
common_inPeriodCustom: Choose date range
loyaltyCards_names: Names
service_filtersOpen: Open
loyaltyCards_regular: Physical
admin_selectAll: Select all
loyalApp_unusedCode: Unused code
loyaltyCards_usedInPeriod: Used in period
loyaltyCards_virtual: Virtual
loyaltyCards_withFounds: With funds
loyaltyCards_withInvoice: Invoice issued
loyaltyCards_withNames: With names
loyaltyCards_withoutFounds: Without funds
loyaltyCards_withoutInvoice: No invoice
loyaltyCards_withoutNames: Without names
fiscal_transactions.details.heading: Transaction details
fiscal_transactions.export.name: fiscal_transactions
fiscal_transactions.for: for
fiscal_transactions.from: from
fiscal_transactions.heading: Transactions
fiscal_transactions.modal_add.heading: Add transaction
fiscal_transactions.modal_add.isu: ISU code
fiscal_transactions.modal_add.location: Location code
fiscal_transactions.modal_add.password: Certificate password
fiscal_transactions.source.CAR_WASH: Stand
fiscal_transactions.source.DISTRIBUTOR: Distributor
fiscal_transactions.source.INTERNET: Internet
fiscal_transactions.source.MONEY_CHANGER: Money changer
fiscal_transactions.source.SCRIPT: Server
fiscal_transactions.source.TERMINAL: Terminal
fiscal_transactions.source.UNKNOWN: Unknown
fiscal_transactions.source.VACUUM_CLEANER: Vacuum cleaner
fiscal_transactions.table.carwash: Car wash
fiscal_transactions.table.date: Date
fiscal_transactions.table.fiscal: Status
fiscal_transactions.table.net: Net
fiscal_transactions.table.type: Transaction type
fiscal_transactions.table.value: Value
fiscal_transactions.table.vat: Vat
fiscal_transactions.table.fiscal_device: Fiscal device
fiscal_transactions.to: to
fiscal_transactions.type.BANKCARDS: Payment by bank cards
fiscal_transactions.type.BANK_CARDS: Payment by bank cards
fiscal_transactions.type.CARWASH_MANAGER: Transaction added manually by the owner of the car wash
fiscal_transactions.type.CASH: Cash
fiscal_transactions.type.CASHLESS: Cashless
fiscal_transactions.type.CHARGE_BAY: Top-up of positions from the changing machine
fiscal_transactions.type.COINS: Payment by coins
fiscal_transactions.type.HOPPER_A: Hopper A
fiscal_transactions.type.HOPPER_B: Hopper B
fiscal_transactions.type.LOYALTY_PAYING: Payment by loyalty card
fiscal_transactions.type.LOYALTY_PROMO: Promotional top-up of a loyalty card
fiscal_transactions.type.LOYALTY_RECHARGE: Topping up a loyalty card
fiscal_transactions.type.LOYALTY_SELLING: Loyalty card sale
fiscal_transactions.type.MOBILE: Mobile payments
fiscal_transactions.type.NOTES: Payment with banknotes
fiscal_transactions.type.PROMO: Promotional top-ups of carwash stands
fiscal_transactions.type.SERVICE: Service impulses
fiscal_transactions.type.TOKENS: Payment with tokens
fiscal_transactions.transactions: Transactions
common_formAddress: Address
common_city: City
common_clientBasicData: Basic data
common_clientInvoiceData: Data needed for the invoice
loyaltyCards_confirmCustomerData: Confirm customer's data for invoicing
common_country: Country
loyaltyCards_currency: Currency
common_fullCustomerName: Full customer name
common_formName: name
common_postCode: Post code
common_surname: surname
common_fieldRequired: Field is required
form.validation.file_max_size_mb: File size can't be bigger than {size}MB
loyalApp_infoPositiveNumberOnly: The value should be positive number.
loyaltyCards_infoValidKey: You have to use "0" to "9" and letters from "a" to "f".'
loyaltyCards_infoValidKeyLength: BKF Key number has to be 8 characters long.
loyalApp_infoValidNumberLength: Value has to be max 4 characters long.
loyalApp_infoValidNumberLength19: Value has to be max 19 characters long.
loyalApp_infoValidQuantity: You have to use number between "0" to "9".'
loyalApp_invalidValue: Invalid value.
login_min: Value is to short
login_passwordSame: Passwords must be identical
loyaltyCards_phoneNumber: Incorrect phone number
loyaltyCards_topUpPositiveNumberOnly: The top-up amount should be positive value.
loyaltyCards_topUpValueToBig: The top-up amount is to high.
loyalApp_validDescriptionLength: The description should have a maximum of 120 characters.
finance_noWhitespacesAtBeginEnd: Whitespaces at front and end not allowed.
user_accountNumber: Bank account number (iban)
user_contactEmail: Contact email
user_contactEmailTooltip: Email for contact in the footer of emails sent to customers.
common_dataToInvoice: Data for the invoice
user_editInvoiceDataAlert: 'To change the blocked fields, contact us via the email address:'
common_emailCopyEmail: Email addresses of invoice copies
invoiceCompanyData_logo: Logo
common_invoiceCompanySettingsName: Full name of the company
common_pressEnterToAddNew: Press Enter to add a new address
subscription_subscriptionBuyMissingData: 'To actualize company invoice data please contact with: '
common_taxNumber: Tax identification number
user_invoiceSettingsAccountNumber: Bank account number (IBAN)
loyaltyCards_additionalInfo: Additional information
loyaltyCards_clientCardSummaryReport: Send end of month card usage report
loyaltyCards_invoiceSettings: Invoicing settings
loyaltyCards_invoiceNumerator: Invoice number
common_logo: Logo
common_invoice_logo: Logo on the invoice
loyaltyCards_longMonthFormat: month in format 01 (for January)
loyaltyCards_longYearFormat: year in the 2018 format
user_nextInvoiceNumber: Another invoice number
loyaltyCards_notOwnerAlert: Only the owner of the car wash can change the invoicing settings
common_paymentPeriod: Payment deadline
loyaltyCards_settings: Settings
loyaltyCards_shortMonthFormat: month in format 1 (for January)
loyaltyCards_shortYearFormat: year in format 18
loyaltyCards_vatTax: VAT rate
common_action: Action
admin_actionHistory: Issue history
common_administratorDetails: Administrator data
admin_alarmActive: Active alarms (this issue)
admin_alarmActiveAllIssues: Active alarms (all issues)
admin_alarmDetails: Problem details
admin_alarmId: Issue id
admin_alarmLevel: Issue level
admin_allAlarmsForIssue: Inactive alarms (this issue)
common_awc: AWC
common_awk: AWK
common_awp: AWP
common_carwashIp: IP
admin_carwashStatus: Car wash status
common_close: Closed
admin_closeAllCarwashes: Close all car washes
predictiveMaintenance_comment: Comment
admin_contactDetails: Contacts and Notes
common_description: Comment
admin_deselectAllIssues: Uncheck all issues
admin_dueDate: Postpone date
common_duedate: Postponed
common_employeesDetails: Employees data
admin_etime: Closing date
common_issueHistory: Issue history
common_issueQuantity: Issue quantity
admin_issuesList: Alarm list
common_jr: Sent to JR
common_lastAlarm: Alarm update
common_lastOnline: Mobile payments
common_lastSynchro: Trading update
common_lastTime: Last occurrence
admin_lastUser: Last user
common_lastAttendance: Last confirmation of attendance
admin_lastComment: Last comment
common_lastVisit: Last completed visit
common_management: Management of submissions
common_new: New
common_nextVisit: Next planned visit
common_none: None
common_noteDetails: Device notes
common_open: Opened
admin_openAllCarwashes: Open all car washes
admin_otherIssues: Other issues
admin_pastIssues: Closed issues
common_postpone: Put the alarm off until the day
common_postponeForOneHour: Postpone for 60 min.
admin_previousIssue: Previous issue end date
common_priority: Issue priority
common_quantity: Number of active alarms
common_refersTo: Applies to alarms
admin_selectAllIssues: Select all issues
common_status: Problem status
common_loading: Loading...
contact_header: Logout
contact_loggedOutMessage: You have been logged out because your session has expired. Log in again.
subscription_logerPeriodBetterPrice: Longer period better price
login_email: E-mail address
login_login: Login
login_loginFailure: Login failure. Nieprawidłowy login lub hasło
login_password: Password
login_passwordForgot: Forgot password
loyalApp_balanceFleetMemberNotify: Fleet
loyalApp_balanceFleetMemberTooltip: Fleet member account. All operations are realize on fleet manager account balance.
loyalApp_fleetManagerActive: User is fleet manager
loyalApp_fleetManagerActiveMessage: Clicking the switch marks the user as a regular account.
loyalApp_fleetManagerInactive: User is not fleet manager
loyalApp_fleetManagerInactiveMessage: Clicking the switch will mark user as a fleet account.
common_invoices: Invoices
loyal-app-manager.no-invoice-data-message: No data for invoicing, contact fleet manager in order to complete them.
loyalApp_payments: Payments
loyalApp_promotionalCodes: Promotional codes
loyalApp_promotionalPackages: Promotional packages
loyalApp_receipts: Receipts
loyalApp_regularUser: Regular user
loyalApp_selfInvoices: Self-billing
loyal-app-manager.transactions: Transactions
loyalApp_trustedPartner: Trusted partner
loyalApp_trustedPartnerActive: User is a trusted partner
loyalApp_trustedPartnerActiveMessage: Clicking the switch marks the user as usual. Topping up the piggy bank without making immediate payment will be impossible.
loyalApp_trustedPartnerInactive: User is a trusted partner
loyalApp_trustedPartnerInactiveMessage: Clicking the switch mark as a trusted user. This will allow topping up the balance by the user, without making immediate payment. The invoice will be issued automatically.
loyalApp_client: Client
loyalApp_info: Information
loyalApp_limit: Limits
loyalApp_stats: Statistics
loyalApp_usersList: Users list
loyalApp_usersListHeading: List of loyalty app users
loyalApp_confirmPaymentAndInvoie: Do you want to confirm payment ?
dashboard_heading: Loyalty cards
dashboard_value: Value
loyalsystem-widget.values-name.cardsTopUpSumFromCM: Sum of top -ups and promotions from the CM application
loyalsystem-widget.values-name.cardsTopUpSumFromCarwash: The sum of top -up and promotions from the release
loyalsystem-widget.values-name.cardsUsedCount: Number of cards used
loyalsystem-widget.values-name.transactionsPayments: Sum of payment
loyalsystem-widget.values-name.transactionsTopups: Sum of top -ups
loyaltyCards_loyaltyTopupsHistoryHeading: Loyal cards top-ups history
menu_administration: Administration
common_administrationCarwashes: Carwashes
menu_administrationUsers: Users
common_alarmActive: Active alarms
common_alarmHistory: Alarm history
common_alarmManagement: Alarm management
predictiveMaintenance_alarmsHistory: Alarms history
menu_allSubscription: Subscriptions
common_chart: Parameter data graph
loyaltyCards_clients: Clients
menu_cmdashboard: Desktop
common_companyData: Company's data
menu_finance: Finance
common_financeCarwashRates: Carwash rates
menu.finance-fiscaltransactions: Transactions
finance_financeFiscalSummary: Fiscal summary
finance_summary: Summary
common_financeMobilePayments: Mobile payments
common_financeTurnover: Turnover
menu_logout: Logout
common_loyalAppManager: White Label Application
common_loyalsystem: Loyalty system
common_moneycollect: Money collection
common_processData: Process data
menu_profile: My profile
common_service: Service requests
menu_subscribers: Subscribers
common_subscription: Subscription
menu_support: Support
common_users: Users
common_markRead: Mark as read
messages_message: Message
menu_more: More
messages_noReadMessages: No read messages
common_noUnreadMessages: No unread messages
messages_read: Read
messages_unread: Unread
messages_when: When
common_by: By
common_clickToConfirm: Click to confirm
common_confirm: Confirm
common_confirmHint: Are you want to confirm invoice ?
common_confirmation: Confirmation
common_confirmed: Confirmed
common_mobilePaymentInvoicesDate: Date
common_download: Download
mobile_payment_invoices.heading: Invoices
common_invoiceConfirmed: Invoice confirmed at
common_invoiceNumber: Invoice Number
loyalApp_issuerName: Issuer name
loyalApp_issuerVatId: Issuer Tax ID
common_notConfirmed: Not confirmed
mobile_payment_invoices.not-for-app: 'Site inaccessible for application:'
common_period: Period
common_valueGross: Gross value
mobile_payments.export.name: mobilepayments
finance_for: for
finance_from: from
finance_heading: Mobile payments
finance_carwash: Carwash
finance_date: Date
finance_paymentType: Payment type
finance_standCode: Stand code
finance_total: Total
common_totalOnPage: Total on current page
finance_value: Value
finance_to: to
loyaltyCards_cardNumber: Card number
loyaltyCards_createNew: Create a new loyalty card
loyaltyCards_creationHint: In the case of BKF Card and BKF Key being more than 8 digits long you must rewrite the first 8 alphanumeric characters
loyaltyCards_deleteHint: Important! You cannot add a card once removed.
loyaltyCards_errorDescription: Error description
loyaltyCards_errorDetails: Details
loyaltyCards_fileLine: Line in file
loyaltyCards_lastFileUploadErrors: Errors in uploaded file
loyaltyCards_rechargeHint: "Each record in the uploaded file must have the following format: 'card number'; 'top up value'"
loyaltyCards_rechargeInfoHint: Upload a CSV file with loyalty cards recharges
loyaltyCards_rechargeMultiple: Recharge multiple cards
loyaltyCards_hint: Removing the card will make it disappear from the list on the loyalty card screen. A removed card cannot be added to the system again.
user_hint: Are you sure you want to delete user?
loyalApp_addCode: Create new promotional codes
loyalApp_addPackage: Create new promotional package
loyalApp_editPackage: Edit package
loyalApp_userEdit: Edit user
loyaltyCards_emailInfo: The invoice will be sent to customer's e-mail address
loyaltyCards_emailSend: Invoice will be sent to the specified email address
modal.loyal_card_top_up_invoice.error.card_no_carwash: Could not find any associated car wash
modal.loyal_card_top_up_invoice.error.card_no_client: This loyalty card has no client attached
modal.loyal_card_top_up_invoice.error.invoice_exists: The invoice already exists in the system
modal.loyal_card_top_up_invoice.error.invoice_generation_failed: Invoice generation has failed
modal.loyal_card_top_up_invoice.error.invoice_not_generated: Invoice generation has failed
modal.loyal_card_top_up_invoice.error.invoice_send_failed: Invoice sending failed
modal.loyal_card_top_up_invoice.error.owner_no_country: The car wash owner has not set the country information
modal.loyal_notifications.creation_hint: Try to write short text
modal.loyal_notifications.description: Text for clients
modal.loyal_notifications.new: New notification
modal.loyal_notifications.preview: Notification preview
modal.loyal_notifications.title: Title
moneycollect.devices.CAR_WASH: Stand
moneycollect.devices.DISTRIBUTOR: Distributor
moneycollect.devices.VACUUM_CLEANER: Vacuum cleaner
finance_exchangerMoneyCollections: Money changer collections
moneycollect.export.name: moneycollect
finance_moneycollectFor: for
finance_moneycollectFrom: from
finance_standMoneyCollections: Stand money collections
finance_balance: Balance
finance_bankCards: Paid by card
finance_details: Money collection details
finance_emergencyDrops: Emergency dumps
finance_safe: Safe
finance_sorted: Sorted
finance_sucked: Collected
finance_sumCash: Paid in cash
finance_sumHoppers: Spent (hoppers)
finance_sumSale: Sales
finance_sumSorted: Sorted into money changer and safe
finance_sumSucked: Collected from the car wash
finance_units: pcs.
finance_unrecognized: Unrecognized
finance_yeti: YETI collection details
finance_moneycollectTo: to
finance_yetiMoneyCollections: YETI money collections
name: Name
subscription_noCarwashAttached: No carwash attached for this user.
subscription_alertContent: The company's data is not completed correctly, please complete them.
number: NR.
user_others: inne
common_0: 0 day
common_1: 1 day
common_10: 10 days
common_14: 14 days
common_3: 3 days
loyalApp_30: 30 days
common_5: 5 days
common_7: 7 days
subscription_subscription: Subscription payment
loyalApp_value: Payment value
processData_chart: Chart
processData_parameter: Parameter
common_updateTime: Last update
processData_value: Value
common_itemOld: The value can be outdated
user_account: User's account
user_baseData: Basic information
user_notificationNotice: Notification function available in the basic package
user_notificationsSettings: Notification settings
user_moneyCollectEmail: Send notifications about incasation to the email address
user_sendAlarmNotificationEmail: Alarm notifications to the email address
user_sendAlarmNotificationMobile: Notifications of alarm for a mobile application
user_regionalSettings: Regional Settings
user_reportsAndNotifications: Reports and notifications
profile-configuration.reports-notice: Email report function available in the basic package
profile-configuration.reports-settings: Report settings
profile-configuration.reports.fiscal-transactions: Fiscal transaction report
profile-configuration.reports.mobile-payments: Mobile payment report
profile-configuration.reports.program-usage: Program use report
profile-configuration.reports.used-funds: A report on the use of funds
user_error: Send from the "error" level'
user_info: Send from the "Information" level'
user_warning: Send from the "warning" level'
user_dontSend: do not send
profile-configuration.sending-options.periodic.daily: send every day
profile-configuration.sending-options.periodic.monthly: send every month
profile-configuration.sending-options.periodic.weekly: send every week
user_send: send
user_siteTitle: My profile
finance_carwashUsage: Usage of touchless car washes
programsusage.daily: Daily usage
finance_programsusageFor: for
finance_programsusageFrom: from
common_title: Hourly distribution of usage
finance_overTimeTitle: Usage of programs over time
finance_title: Percentage of programs share
finance_programs: Usage of programs
finance_rolloverUsage: Usage of portal car washes
programsusage.rollover_total: Total
programsusage.table.sum: Total
programsusage.table.wash: x wash
programsusage.table.water-average: Average consumption water
programsusage.table.water-usage: Total consumption water
programsusage.table.availability: Dostępność
finance_programsusageTo: to
common_total: Usage
loyalApp_quantity: Quantity
loyalApp_packageValue: Package value
finance_mtime: Modification date
finance_name: Carwash name
finance_pause: Paused
finance_time: Rate view in seconds/pulse
finance_valueForCredit: Show in
loyalApp_createDate: Issuence date
loyalApp_number: Numebr
loyalApp_paymentDate: Payment date
loyalApp_loyaltyAppUserTopups: Loyalty app use history
loyalApp_showAppUsageHistory: Show Loyalty app use history
login_backToLogin: Back to login
login_forgotSuccess: Reset password link was sent to your email
login_reset: Reset password
login_resettingBackToLogin: Back to login
login_changePassword: Change password
login_confirmPassword: Repeat password
login_changeSuccess: Success! Your password has been changed
login_tokenExpired: Password reset link has expired
login_newPassword: New password
service_button: Report a fault
service_text: To create a service request, go to
service_serviceHeading: Service requests
service.status.desc.completed: Completed
service.status.desc.open: Open
common_carwash: Car wash
admin_issueId: ID
service_issueReportSource: Source
service_issueReportedBy: Reported by
service_issueTitle: Subject
service_noContent: No content
service_tableStatus: Status
service_time: Issuance date
subscription_state: state
admin_subscriptionListHeading: Subscriptions list
subscription.carwash-type.STANDARD: Standard
subscription.carwash-type.UNSUBSCRIBED: Without subscription
subscription.carwash-type.WARRANTY: Guarantee
admin_subscriptionDealer: Dealer
common_document: Document
subscription.document.issue: Issue document
subscription.document.send: Send
subscription.advanced: Advanced
admin_issueAndSend: Issue and send
admin_subscriber: Subscriber
subscription.table.STANDARD: Post -warranty washes
subscription.table.UNSUBSCRIBED: Car washes without subscription
subscription.table.WARRANTY: Warranty washes
subscription.table.discount: Discount
subscription.table.position: Position
subscription.table.price-after-discount: Sale price
subscription.table.price-before-discount: Price before the discount
subscription.table.sum: Together
subscription.table.summary: Summary
subscription.table.type: Type
admin_whoPays: Who pays ?
subscriptions.actions.chose-and-pay: Chose and pay subscription
subscription_back: back
common_canceled: Canceled
subscription_chose: Chose subscription
subscription_chosePaymentPeriod: Chose payment period
subscription_chose2: Chose subscription
subscriptions.delete-question: Do you want to cancel the selected subscription?
admin_clientDiscount: client discount
common_subscriptionsEnddate: To
subscription_goToDataCompletion: Go to data completion
subscription_historyList: history of settlements
subscription_alarmsList: List of alarms - active and historical
subscription_cyclicFiscalMailReports: Periodic e-mail financial reports
subscription_loyalcardsCyclicTopup: Cyclical top-ups of loyalty cards
subscription_loyalcardsInvoicing: Invoicing loyalty cards
subscription_loyalcardsRemoteTopups: Remote top-up of washing bays
subscription_loyalcardsReport: Report of the use of loyalty cards for customers
subscription_loyalcardsTransactionHistory: The history of transactions in the car wash together with detailed fiscal information
subscriptions.options.loyalcards-transactions-history: CRM - Transaction history, remote top-ups, loyalty cards locks
subscription_mailReports: Email reports
subscription_mobileAppAccess: Access via mobile app
subscription_moneycollectControll: Control of cash collection from the car wash (cash status reports)
subscription_newAlarmNotifications: Notifications of new alarms in the car wash (e-mail, push)
subscription_serviceTicketPreview: Podgląd stanu zgłoszeń serwisowych
subscription_technicalData: Preview of the current operating parameters of the car wash
subscription_unlimitedUsers: Unlimited users and allocation of individual rights
subscription_usageStatistics: Wash program and car wash usage statistics
subscription_wwwAccess: Access via web browser
common_error: Error
common_paid: Paid
subscription_paymentSummary: Payment summary
common_price: Gross price
subscription_priceForCarwashForMonth: Price for 1 month (net price) for 1 carwashe
common_processing: Processing
common_refund: Refund
common_startDate: From
subscription_checkInvoiceData: Check invoice data
subscription_goToAbonamentLength: Go to choose billing period
subscription_orderWithPaymentObligation: Order with a payment obligation
subscriptions.subscription: Subscription
subscription_subscriptionContent: Subscription include
subscription_summary: Summary
common_timeout: Timeout
subscription_toPay: to pay
subscriptions.types.basic: Basic
subscriptions.types.free: Free
subscriptions.types.premium: Premium
common_unknown: Unknown
admin_vat: Vat
subscription_yourSubscription: Your subscription
common_success: Success
loyalApp_account: Account
loyalApp_accountType: Bill type
admin_added: Added
common_alarmDetails: Alarm details
common_alarmId: Alarm identifier
common_alarmLevel: Alarm level
common_all: All
loyalApp_averagePaymentValue: Average payment value
loyalApp_balance: Balance
loyaltyCards_cardFunds: Funds on the card
common_carwashes: Car washes
common_client: Client
common_comment: Comment
table.company_name: Company name
common_tableCreateDate: Create date
common_currency: Currency
loyaltyCards_cyclic: Cyclic
common_dataUnknown: No data
common_tableDate: Date
common_tableDescription: Description
common_discount: Discount
common_email: Email adress
loyalApp_externalId: External Id
loyalApp_externalType: Transfer type
loyalApp_externalValue: Transfer value
admin_filters: Filters
common_firstName: First name
loyalApp_fleetManager: Fleet manager
common_id: Id
common_invoice: Invoice
loyalApp_invoiceNumber: Invoice number
loyalApp_issuanceDate: Issuance date
common_language: Language
common_lastLogin: Last login
dashboard_lastActualizaction: Last update
common_lastName: Last name
common_lastUsage: Last usage
loyalApp_licensePlate: LIcense plate
loyalApp_managerEmail: Fleet manager address
common_noData: No data to display
common_notFullySent: Not fully sent
admin_tableOwnerBkf: Owner BKF
loyalApp_payment: Payment
loyalApp_paymentCount: Number of payments
common_paymentDate: Payment date
loyaltyCards_payments: Payments
common_phone: Phone
loyalApp_promotionalCode: Promotional code
common_receipt: Recipt
common_regonNumber: Regon
common_roles: Roles
common_rowsPerPage: 'Rows per page:'
loyalApp_salesDocument: Sales document
common_search: Search
common_sendToCard: Sent
common_stand: Stand
common_state: State
loyaltyCards_sum: Sum
loyalApp_sumPaymentValue: Total value of payments
table.tax_number: Tax number
loyalApp_taxNumber: Tax number
common_timezone: Timezone
loyalApp_title: Title
common_toSent: Waiting
loyaltyCards_toSend: Waiting to be sent
loyaltyCards_topUpsToSent: Top ups waiting to sent
loyaltyCards_topup: Top-up value
loyalApp_topupBonus: Topup bonus
common_topupCode: Promotion code
common_topupSent: Recharges
common_transactionType: Transaction type
loyalApp_transactionValue: Transaction value
loyalApp_tableTrustedPartner: Trusted partner
common_type: Type
common_user: User
common_username: Username
common_value: Value
loyaltyCards_valueAfterTransaction: Balance after transaction
loyalApp_vat: Vat
loyaltyCards_tableVirtualCard: Virtual card
admin_whoAdded: Added by
common_accept: Accept
common_cancel: Cancel
common_termsHeading: APP REGULATIONS „CAR WASH MANAGER”
common_terms: Terms
common_validFrom: applicable from 25 May 2020
finance_minutes: min
transactions.balance_adjustment: Adjustment
transactions.car_wash: From car wash
transactions.distributor: Distributor
transactions.export_error: Problem with report generating
transactions.history: Transaction history
transactions.history_for_card: Card transaction history for
transactions.internet: From internet
transactions.money_changer: From money changer
transactions.payment: Payment
transactions.payments: Payments
transactions.promotions: Promotion
transactions_refill_to_balance: Balance after topup
transactions.refill_for: Refill card for
transactions.top-up-code: Promotion code
transactions.topup: Top up
transactions.topup_card_history: Card top-up history
transactions.topup_history: Top-up history
transactions.topups: Top ups
transactions.unknown: Unknown
transactions.vacuum_cleaner: Vacuum cleaner
turnover.devices.distributor.plural: Distributors
turnover.devices.distributor.singular: Distributor
turnover.devices.stand.plural: Stands
turnover.devices.stand.singular: Stand
turnover.devices.vacuum.plural: Vacuum cleaners
turnover.devices.vacuum.singular: Vacuum cleaner
finance_exportName: turnover
common_filtersCarwash: Carwash
common_deviceType: Device type
finance_turnoverFor: for
finance_turnoverFrom: from
finance_fromLastCollection: from last money collection
finance_compareWithPreviousYear: Compare with previous year
finance_linechartTitle: Turnover over time
finance_ofAllCarwashes: of all carwashes
finance_ofCarwash: 'of carwash #'
finance_paymenttypesharepieName: Payment types share
common_paymenttypesharepieTitle: Payment types share
turnover.table.balance: Balance
turnover.table.bankCards: Bank cards
turnover.table.banknotes: Banknotes
turnover.table.bkfCardPay: BKF Card payment
turnover.table.bkfCardSale: BKF Card sale
turnover.table.bkfKey: BKF Card
turnover.table.bkfKeyRecharge: BKF Card recharge
turnover.table.carwash: Carwash
turnover.table.carwashEarnings: Sum
turnover.table.carwashRecharge: Stand top-ups
turnover.table.cash: Cash payments
turnover.table.cashless: Cashless payments
turnover.table.clients: Clients
turnover.table.coins: Coins
turnover.table.counter: Number of uses
turnover.table.date: Date
turnover.table.details: Details of carwash
turnover.table.detailsRollover: Portal car wash details
turnover.table.exchanger: Details of money changer
turnover.table.exchangerHoppers: Money exchanges
turnover.table.exchangerSale: Money changer sales
turnover.table.exchanges: Exchanges
turnover.table.hopperA: Hopper A
turnover.table.hopperB: Hopper B
turnover.table.mobilePayments: Mobile
turnover.table.name: Device name
turnover.table.paid: Paid
turnover.table.post: Top-ups from money changer
turnover.table.prepaid: Prepaid payments
turnover.table.programsSale: Program sales
turnover.table.promotion: Promotion
turnover.table.saleValue: Sales value
turnover.table.sell: Sell
turnover.table.service: Service
turnover.table.sum: Sum
turnover.table.terminal: Payment terminal details
turnover.table.tokens: Tokens
turnover.table.total: Total
common_daily: Daily
finance_detailed: Detailed
common_monthly: Monthly
finance_tabsTotal: Total
finance_turnoverTitle: Carwash turnover
finance_turnoverTo: to
finance_turnover: turnover
unknown: Unknown
finance_cubicMeters: m³
finance_litres: l.
loyalApp_discount: Discount
loyalApp_max: Maximum
loyalApp_min: Minimum
loyalApp_term: Payment term
common_userEmailAlreadyExist: User with given email already exists.
loyalApp_invitationInfo: Invited user can use fleet manager funds to pay on carwash.
subscription_whyThisPrice: Why this price ?
subscription_whyThisPriceModalHint: Table below presents the calculation of the subscription amount based on owned car washes and granted discounts.
loyalApp_topupAccountTitle: Top up user
loyalApp_topupType: Topup type
loyalApp_bonus: Bonus
loyalApp_invoice: Invoice
loyalApp_topupByAmount: Top up balance by value
loyalApp_topupAccountTopup: Top up
loyalApp_loyalappExpandDetailsToSeeAlerts: Expand details to get user alerts.
loyalApp_loyalappUserAlert: Alerts
common_exportAsyncTitle: Generate report
common_userTitle: Custom title
common_generating: Report generationg
common_export: Export
common_downloadReport: Download report
common_reportReady: Report ready to download
common_clearReport: Clear
common_canotGenerateReport: Can't generate report
actions.export_csv_summary: Export summary CSV
actions.export_xlsx_summary: Export summary XLSX
actions.export_pdf_summary: Export summary PDF
actions.chose_report_to_generate: Chose report to generate
common_reports: Reports
common_reportDownloadOnList: You can download finisched reports at reports list
finance_header: Generated reports list
finance_createTime: Create date
finance_endTime: Generate Time
finance_status: Status
finance_reportName: Report name
finance_user: User
common_progressNew: New
common_process: Processing
common_done: Ready
common_progressError: Error
reports_list.progress.QUEUE: Queued
finance.fiscal-transactions: Transactions
card_client_report.card_report_email_header: Customer cards use report
common_turnoverFrom: Turnover from
card_client_report.cards_transactions: Cards transactions
common_mobilePayments: Mobile payments
common_programUsage: Program usage
fiscal.source.CAR_WASH: Stand
fiscal.source.DISTRIBUTOR: Distributor
fiscal.source.MONEY_CHANGER: Money changer
fiscal.source.UNKNOWN: Unknown
fiscal.source.VACUUM_CLEANER: Vacuum cleaner
finance_success: Success
finance_initiated: Initiated
finance_refused: Refused
dashboard.table-details: Details of carwash
invoices.send-date: Send date
programs.brush: Brush
programs.degreaser: Degreaser
programs.foam: Foam
programs.glossing: Glossing
programs.mainwash: Main wash
programs.prewash: Pre wash
programs.rims: Rims
programs.rinsing: Rinsing
programs.wasxing: Waxing
table.serialnumber: Serial number
fiscal_transactions.grouped: Daily summary
loyalApp_showOnMap: Show on map
loyalApp_paymentEnabled: Payments enabled
loyalApp_paymentNotEnabled: Payments not enabled
loyalApp_productionCarwash: Production carwash
loyalApp_testCarwash: Test carwash
carwashes_list.last_connection: Last connection
loyalApp_mobileOk: Mobile Ready
finance_periodError: Report don't support given dates. To generate report please chose different period.
other_lastOnline: Last online
loyalApp_online: Carwashes online
loyalApp_offline: Carwashes offline
loyalApp_invoicedAfterTransaction: I want to automaticly receive invoice to my email address.
common_cyclic: Cyclic
common_onetime: One time
common_typeType: Report type
common_frequency: Frequency
common_frequency_daily: Every day
common_frequency_weekly: Every week
common_frequency_monthly: Every month
finance_listHeading: Cyclic reports
finance_extension: Extension
thisWeek: This week
lastWeek: Last week
discountPercentage: Discount (%)
discountValue: Discount value
yes: Yes
no: No
amountToPay: Amount to pay
downloadInvoice: Download invoice
loyalApp_valid_time: Valid Time (days)
loyalApp_invoiceStatus: Invoice Status
mobileAppVersion: Mobile app version
fiscal_transactions.lastReceipt: Last receipt
fiscal_transactions.fiscalDevicesDetails: Fiscal devices details
admin_subscribentReports: Subscribent reports
admin_fiscalization: Fiscalization
finance_dosage: Dosage
finance_carwashRates_synchronization: Synchronization
common_refund_question: Do you whant to refund chosen payment ?
commonAction_refund: Refund
common_viewRelocatedTo: 'View relocated to site '
common_goToSite: Go to
invoiceConfig_department_identificator: Department identificator
invoiceConfig_category: Category
invoiceConfig_token: Token
invoiceConfig_url: Url address
invoiceConfigType_Disabled: Disabled
invoiceConfigType_Internal: Carwash Manager
invoiceConfigType_Fakturownia: fakturownia.pl
invoiceConfigType: Invoicing
financeRollover_programHistory: Rollover program usage history
financeRollover_rides: Rides
financeRollover_step: Step
financeRollover_duration: Duration
financeRollover_programName: Program name
financeRollover_status: Status
common_reset: Reset
cyclicReport_deleteQuestion: Are you sure you want to delete cyclic report ?
common_accountDelete: Account delete
loyalty_PaymentReportTitle: Loyalty cards payment report
common_initiated: Initiated
common_waiting: Waiting
common_pending: Pending
common_rejected: Rejected
loyaltyCards_paymentSettings: Payments settings
loyaltyApp_startTime: Start time
loyaltyApp_endTime: End time
common_startEndDateValidationInfo: End date must be later than start date
client-modal.clientAlerts.editRequired: Edit required
loyaltyCards_lockFund: Clear balance after card lock
wla_user_card_name: Name
wla_user_card_balance: Balance
wla_user_card_end_time: Expiring
wla_user_card_list_header: User Cards
loyalApp_Reports: Reports
loyalApp_CarwashStats: Carwash stats
common_actions: Action
loyalSystem_packages: Packages
validation_discount: The discount value ranges from 0 to 100.
validation_integer: Value must be integer
loyalApp_stands: Stands
loyalApp_cashback: Cashback
loyalApp_carwashBasicData: Basic data
loyalApp_subscriptions: Subscriptions
loyalApp_user: User
loyalApp_fleet: Fleet
loyalApp_usage: Usage
loyalApp_subscriptionsPackagesList: List of loyalty app subscription packages
loyalApp_carwash_photo: Carwash photo
common_left: Left
fiscal_transactions_configDetails: Fiscal device certificate
fiscal_transactions_config_oib: OIB
fiscal_transactions_config_errorCounter: Error counter
fiscal_transactions_config_expiration: Expiration date
fiscal_transactions_config_validFrom: Valid from
fiscal_transactions_config_subject: Subject
fiscal_transactions_config_error: Failed to fetch certificate data
fiscal_transactions_config_noData: No certificate
fiscal_transactions_certificate_uploadBtn: Upload certificate
fiscal_transactions_certificate_file: Certificate file (.p12)
fiscal_transactions_certificate_oibFormat: OIB should consist of 11 digits
fiscal_transactions_certificate_fileFormat: '"Allowed file extension: .p12"'
fiscal_transactions_certificate_error: Failed to upload certificate
common_upload: Upload
common_select_file: Select file
carwash_software_info: Software Information
carwash_software_plc: PLC
carwash_software_mac: MAC
carwash_software_serial_number: Serial Number
carwash_software_owner: Owner
carwash_software_ip: IP
carwash_software_config: Configuration
carwash_software_current_version: Current Version
carwash_software_target_version: Target Version
carwash_software_ip_country: IP Country
carwash_software_ip_city: IP City
carwash_software_start_date: Start Date
carwash_software_warranty_to: Warranty Until
carwash_software_no_data: No software data available
carwash_software_target_software_version: Target software version
carwash_software_select_version: Select software version
carwash_software_loading_versions: Loading versions list...
carwash_software_no_versions: No available versions
common_set: Set
common_error_occurred: An error occurred
loyalApp_statistics_heading: Loyalty app statistics
loyalApp_statistics_chart_payments_users: Payments and users
loyalApp_statistics_chart_by_source: Account balance values
loyalApp_statistics_users_previous_year: Users (previous year)
loyalApp_statistics_table_heading: Payments, users and balance
common_monetary_values: Monetary values
SUBSCRIPTION: Subscription
BKFPAY: BKFPay
loyalApp_directPayments: Direct payments
loyalApp_promotional_package: Promotional package
filter_paid: Paid
filter_pending: Pending
filter_cancelled: Canceled
filter_canceled: Canceled
filter_confirmed: Confirmed
filter_initiated: Initiated
filter_timeout: Timeout
filter_rejected: Rejected
filter_waiting: Waiting
filter_refunding: Refunding
filter_refunded: Refunded
filter_unknown: Unknown
filter_error: Error
transaction_type_TOP_UP: Recharge
transaction_type_PAYMENT: Payment
transaction_type_TOP_UP_BONUS: Topup bonus
transaction_type_ACCOUNT_DELETE: Account delete
transaction_issuer_BKFPAY: Application account
transaction_issuer_EXTERNAL_PAYMENT: External payment
transaction_issuer_SUBSCRIPTION: Package
loyalApp_agreement: Agreement
loyalApp_no_carwash_photo: No carwash photo
common_online: Online
loyalSystem_package_delete_question: Do you whant to delete package ?
loyalApp_invitationAccepted: Invitation accepted
loyalApp_fleetUsers: Fleet users
loyalApp_inviteFleetUser: Invite fleet user
loyalApp_editFleetUserLimits: Edit user limits
loyalApp_fleetUserLimits: User limits
loyalApp_resendInvitation: Resend invitation
loyalApp_invitationSent: Invitation sent
loyalApp_invitationResent: Invitation resent
loyalApp_hourlyLimit: Hourly limit
loyalApp_hourlyUsage: Hourly usage
loyalApp_dailyLimit: Daily limit
loyalApp_dailyUsage: Daily usage
loyalApp_monthlyLimit: Monthly limit
loyalApp_monthlyUsage: Monthly usage
common_count: Count
common_invalidEmail: Invalid e-mail address
common_fetchError: Fetch data error
common_copiedToClipboard: Copied to clipboard
common_copy: Copy
common_sendNotification: Send notification
common_notificationTitle: Notification title
common_notificationContent: Notification content
common_sendNotificationToAll: Send notification to all
