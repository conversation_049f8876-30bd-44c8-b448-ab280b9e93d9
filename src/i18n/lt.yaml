actions.actions: <PERSON><PERSON><PERSON><PERSON>
actions.actualize: <PERSON><PERSON><PERSON><PERSON><PERSON>
actions.add_attachment: <PERSON><PERSON><PERSON><PERSON> priedą
actions.add_client: <PERSON><PERSON><PERSON><PERSON> klientą
actions.add_key: <PERSON><PERSON><PERSON>ti BKF Key
actions.add_user: <PERSON><PERSON><PERSON><PERSON> vartotoją
actions.cancel: Atšaukti
actions.chose: Pasirinkite
actions.clear: ai<PERSON><PERSON>
actions.click_to_show_more_details: Spustelėkite norėdami peržiūr<PERSON>ti detales
actions.close: Uždaryti
actions.delete: Panaikinti
actions.download-cards-usage-report: Atsisiųskite klientų kortelių ataskaitą
actions.edit_user: Redaguoti naudotoją
actions.export: Eksportuok
actions.export_csv: Eksportuoti CSV
actions.export_summary: Santraukos eksportas
actions.export_transactions: Sandorių eksportas
actions.export_xlsx: Eksportuoti XLSX
actions.generate: Sukurti
actions.generate_and_download: Generuoti ir atsisiųsti
actions.generate_invoice: Generuoti išsiųsti faktūrą
actions.lock: B<PERSON>kas
actions.refill: Pak<PERSON><PERSON>ma
actions.refill_and_issue_invoice: Įpildykite ir išleiskite sąskaitą faktūrą
actions.return_to_list: Grįžti iki s<PERSON>ra<PERSON>
actions.save: Išsaugoti
actions.save-and-add-other: Išsaugokite ir pridėkite kitų
actions.send_invoice: Siųsti sąskaitą faktūrą
actions.show_doc: Eikite į dokumentus
actions.unlock: Atrakinti
common_address: Adresas
admin_country: Šalis
admin_details: Išsami informacija apie automobilių plovyklą
admin_heading: Visų automobilių plovyklų sąrašas CM
common_owner: Savininkas
admin_ownerBkf: BKF savininkas
admin_product: Produktas
admin_serialNumber: Serijos numeris
admin_startDate: Pradžios data
admin_warrantyVoided: Garantijos pabaiga
admin_add: Pridėkite prenumeratą
admin_alreadyCancel: Prenumerata jau buvo atšaukta
admin_automaticPayment: 'Automatinis mokėjimas.'
admin_cancel: Atšaukti prenumeratą
admin_manualCancelNotPossible: Rankinis atšaukimas
administration.subscription.status.canceled: Panaikintas
administration.subscription.status.initiated: Apdorota
administration.subscription.status.manually_canceled: Atšauktas
administration.subscription.status.paid: Mokama
administration.subscription.status.unknown: Nežinoma
admin_usersHeading: Visų CM vartotojų sąrašas
admin_isOwner: Yra savininkas
admin_lastLogin: Paskutinis prisijungimas
common_usersOwner: Savininkas
admin_subscriptionCode: Aktyvi prenumerata
admin_subscriptionEnds: Paskutinės prenumeratos pabaiga
common_createDate: pavojaus signalo sukūrimo data
common_duration: trauksmes ilgums
common_endDate: aliarmo pabaigos data
loyalApp_trustedPartnerWithoutFleetManager: 'Paprastas vartotojas, kuris nėra laivyno valdytojas gali būti patikimas partneris. Laivyno Tik vartotojas gali būti patikimas partneris.'
common_actionSucced: Veiksmas buvo sėkmingas
service_sending: 'Siunčiama...'
common_errorHeader: Programos klaida
contact_message: 'Pasitaikė programos klaida! Susisiekite su programos kūrėju, kad ją išspręstumėte!'
dashboard_average: Vidurkis
dashboard_max: Maks
dashboard_min: Minimumas
dashboard_title: Apyvartos stendas
loyaltyCards_virtualCard: Virtualioji kortelė
loyaltyCards_activeTopups: Aktyvūs topups
loyaltyCards_clientLockWarning: 'Rankinės sąskaitos faktūros užblokuota. Norėdami juos įjungti, pakeiskite pasirinkto kliento sąskaitų faktūrų nustatymus.'
loyaltyCards_cyclicTopUpList: Aktyvūs cikliniai papildymai
loyaltyCards_notSentList: Populiariausi laukia siųsti
common_cardActive: Kortelė aktyvi
loyaltyCards_activeMessage: 'Paspaudus jungiklį, kortelė nebus naudojama automobilių plovykloje. Galimos lėšos ir kortelių naudojimo istorija nepasikeis.'
common_cardBlocked: Kortelė užblokuota
loyaltyCards_blockedMessage: 'Paspaudus jungiklį, galėsite pakartotinai naudoti kortelę automobilių plovykloje.'
loyaltyCards_card: Kortelė
common_cards: Kortelės
loyaltyCards_cardsList: Lojalumo kortelių sąrašas
loyaltyCards_cleanHistoryAndBalance: 'išvalo lėšas kortelėje ir naudojimo istoriją.'
loyaltyCards_invoiceLoading: 'Vyksta sąskaitos faktūros generavimas...'
loyaltyCards_invoiceSendLoading: 'Sąskaitos faktūros siuntimas...'
loyaltyCards_modifyTime: Modifikacijos data
loyaltyCards_name: Kortelės pavadinimas
loyaltyCards_number: Kortelės numerį
loyaltyCards_removal: Kortelės ištrynimas
loyaltyCards_source: Šaltinis
loyaltyCards_sourceStatus: 'Šaltinis / statusas'
loyalApp_topup: Perkrauti
common_topups: Pripūtimo
card.transactions: Kortelių operacijos
card.transactions_list: Lojalumo kortelių operacijų istorija
loyaltyCards_client: Klientas
common_heading: Sąskaitos faktūros
loyaltyCards_invoiceNumber: Sąskaitos numeris
loyaltyCards_issuanceDate: Išleidimo data
loyaltyCards_serviceDate: Mokėjimo data
loyaltyCards_valueGross: Bendroji vertė
loyaltyCards_valueNet: Grynoji vertė
carwash: Automobilių plovykla
common_rollover: Portalo automobilių plovykla
common_selfService: Bepakopė automobilių plovykla
common_carwashSn: Automobilių plovimo serijos numeris
finance_error: Klaida
loyalApp_qrCode: QR kodas
common_standCode: Padėties kodas
finance_stands: Stendai
finance_topupDisabled: 'Užpildykite poziciją, kurią galima įsigyti „Premium“ prenumeratos metu, palaikant mobiliuosius mokėjimus.'
finance_topup: Prisipilti
finance_refillFor: 'Papildykite poziciją kun.'
finance_topupStand: Užpildykite poziciją
loyaltyCards_configuration: Konfigūracija
loyaltyCards_generateStrategy: Sąskaitos faktūros generavimo strategija
client-modal.invoice.strategies.auto-after-top-up: Paroda po to
client-modal.invoice.strategies.block: Neišleisk
client-modal.invoice.strategies.manual: Nustatytas pagal užklausą
client-modal.invoice.strategies.undefined: "-"
loyaltyCards_editClient: Redaguoti klientą
loyaltyCards_heading: Klientų sąrašas
loyaltyCards_invoicing: Sąskaitos faktūra
loyaltyCards_comment: Komentaras
loyaltyCards_discount: Nuolaida
loyaltyCards_endTime: Galas
common_cyclicTopUpsHeading: Cikliniai viršutiniai dalykai
loyaltyCards_lastCal: Paskutinis skambutis
loyaltyCards_startTime: Pradėti
loyaltyCards_active: Aktyvus
loyaltyCards_state: Būsena
loyaltyCards_cyclicAdd: Cikliškas sumai
loyaltyCards_cyclicAlign: Cikliškas įkrovimas į pusiausvyrą
loyaltyCards_oneTime: Kartą
loyaltyCards_topUpType: 'Viršutinė -Up tipas'
loyaltyCards_type: Tipas
loyaltyCards_value: Vertė
common_alarms: Signalai
common_date: Laikas
dashboard.moneycollect.CAR_WASH: Pozicijas
dashboard.moneycollect.MONEY_CHANGER: Keitiklis
dashboard.moneycollect.YETI: YETI
common_name: Vardas
common_noAlarms: Jokių signalizacijų
dashboard_header: Apmokėjimai dalimis
common_p14d: Paskutinės 14 dienų
common_p7d: Paskutinės 7 dienos
common_last: Nuo paskutinio pinigų rinkimo
common_now: Šiandien
dashboard_previousMonth: Ankstesnis mėnuo
common_sinceMonthStart: Nuo dabartinio mėnesio pradžioje
common_yesterday: Vakar
common_software: Programinė įranga
dashboard.subscription.component_header: Baigiasi prenumeratos Carwash Manager
admin_subscription: Prenumerata
dashboard.subscription.subscriptionClick: 'Jei norite pratęsti savo prenumeratą, spustelėkite žemiau esantį mygtuką.'
dashboard.subscription.subscriptionEnd: 'Jūsų „Carwash Manager“ prenumerata baigėsi.'
dashboard.subscription.subscriptionEndIn: "Jūsų prenumerata „Carwash Manager“ baigiasi iki:"
dashboard.subscription.subscriptionEndTitle: Prenumerata baigėsi
dashboard.subscription.subscriptionHelpText: "Jei turite klausimų, susisiekite su mumis el. Paštu: "
dashboard_sum: Suma
dashboard_summary: Kartu
dashboard_yourCarwash: Jūsų automobilių plovyklos
date.length.12m: 12 mėnesių
date.length.1m: 1 mėnesio
date.length.3m: 3 mėnesių
date.length.6m: 6 mėnesių
finance_0: Sekmadienis
finance_1: Pirmadienis
finance_2: Antradienis
finance_3: Trečiadienis
finance_4: Ketvirtadienis
finance_5: Penktadienis
finance_6: Šeštadienis
service_attachment: Priedas
service_description: Aprašymas
service_heading: Naujas pateikimas
service_close: Prašyti problemos uždarymo
service_closedAt: Uždaras
service_createdAt: Sukurta
service_eventsClose: Ataskaita uždaryta
service_readyToClose: Klientas paprašė išspręsti problemą
service_start: Sukurta ataskaita
service_waitingForResponse: Laukiame kliento atsakymo
service_listHeading: Pranešimai
service_status: Statusas
service_statusesClose: Uždaras
service_new: Naujas
service_open: Atidaryta
service_statusesReadyToClose: Paruošta
service_waiting: Laukiama reakcijos
service_subject: Tema
service_user: Iniciatorius
service_replySent: Jūsų atsakymas išsiųstas
service_replySentProblem: Siunčiant atsakymą kilo problema
service_reportSent: Klaidų ataskaita išsiųsta
service_reportSentProblem: Kilo problema siunčiant ataskaitą
service_clientAdd: Kliento pridėjimo problema
service_financeData: Finansinių duomenų problema
service_loyaltyCards: Lojalumo kortelių problema
service_other: Kita problema, kuri nėra plovyklos gedimas
service_subscirption: Prenumeratos problema
service_respond: 'Parašyk savo atsakymą...'
service_errorReportSubject: Pateikimo objektas
contact_title: Parama
loyalApp_accessDenied: 'Jūs neturite leidimo atlikti šią operaciją.'
loyaltyCards_addExisting: 'Kortelė jau užregistruota sistemoje.'
loyaltyCards_blockedTopup: 'Kortelė turi būti aktyvi, kad galėtumėte papildyti savo sąskaitą.'
loyalApp_userAlreadyExists: 'Nurodytas vartotojas jau egzistuoja. Jūs negalite pakviesti esamą vartotoją.'
loyaltyCards_filtersActive: Aktyvus
loyaltyCards_activity: Veikla
service_allFeminine: Visi
loyaltyCards_blocked: Užrakintas
loyaltyCards_cardType: Kortelės tipas
service_completed: Užbaigta
filters.daterange.all: Visą laiką
common_currentYear: Šiais metais
common_custom: Bet koks
common_previousMonth: Ankstesnis mėnuo
common_previousYear: Ankstesni metai
common_today: Šiandien
common_daterangeYesterday: Vakar
admin_deselectAll: Noņemiet atzīmi no visiem
loyaltyCards_funds: Priemonės
common_inPeriod: Laikotarpiu
common_inPeriodCustom: Pasirinkite dienų seką
loyaltyCards_names: Vardai
service_filtersOpen: Atviras
loyaltyCards_regular: Fizinis
admin_selectAll: Izvēlēties visus
loyaltyCards_usedInPeriod: Panaudota per laikotarpį
loyaltyCards_virtual: Virtualus
loyaltyCards_withFounds: Su lėšomis
loyaltyCards_withInvoice: Išrašyta sąskaita faktūra
loyaltyCards_withNames: Su pavadinimais
loyaltyCards_withoutFounds: Be lėšų
loyaltyCards_withoutInvoice: Sąskaitos faktūros nėra
loyaltyCards_withoutNames: Nr vardai
fiscal_transactions.details.heading: Išsami informacija apie sandorį
fiscal_transactions.export.name: fiscal_transactions
fiscal_transactions.for: už
fiscal_transactions.from: nuo
fiscal_transactions.heading: Fiskalinės operacijos
fiscal_transactions.modal_add.heading: Pridėti fiskalinę operaciją
fiscal_transactions.modal_add.isu: ISU kodas
fiscal_transactions.modal_add.location: Vietos kodas
fiscal_transactions.modal_add.password: Sertifikato slaptažodis
fiscal_transactions.source.CAR_WASH: Pozicija
fiscal_transactions.source.DISTRIBUTOR: Dozatorius
fiscal_transactions.source.MONEY_CHANGER: Keitiklis
fiscal_transactions.source.UNKNOWN: Nežinomas
fiscal_transactions.source.VACUUM_CLEANER: Dulkių siurblys
fiscal_transactions.table.carwash: Automobilių plovykla
fiscal_transactions.table.date: Data
fiscal_transactions.table.fiscal: Statusas
fiscal_transactions.table.type: Sandorio tipas
fiscal_transactions.table.value: Vertė
fiscal_transactions.to: iki
fiscal_transactions.type.BANKCARDS: Atsiskaitymas banko kortelėmis
fiscal_transactions.type.BANK_CARDS: Atsiskaitymas banko kortelėmis
fiscal_transactions.type.CARWASH_MANAGER: Sandorį rankiniu būdu pridėjo plovyklos savininkas
fiscal_transactions.type.CASH: Grynieji pinigai
fiscal_transactions.type.CASHLESS: Be grynųjų pinigų
fiscal_transactions.type.CHARGE_BAY: Padėčių papildymas iš persirengimo mašinos
fiscal_transactions.type.COINS: Atsiskaitymas monetomis
fiscal_transactions.type.HOPPER_A: Bunkeris A
fiscal_transactions.type.HOPPER_B: Bunkeris B
fiscal_transactions.type.LOYALTY_PAYING: Atsiskaitymas lojalumo kortele
fiscal_transactions.type.LOYALTY_PROMO: Reklaminis lojalumo kortelės papildymas
fiscal_transactions.type.LOYALTY_RECHARGE: Lojalumo kortelės papildymas
fiscal_transactions.type.LOYALTY_SELLING: Pardavimas lojalumo kortelėmis
fiscal_transactions.type.MOBILE: Mobilieji mokėjimai
fiscal_transactions.type.NOTES: Atsiskaitymas banknotais
fiscal_transactions.type.PROMO: Reklaminiai stendų papildymai
fiscal_transactions.type.SERVICE: Paslaugų impulsai
fiscal_transactions.type.TOKENS: Atsiskaitymas žetonais
common_formAddress: Adresas
common_city: Miestas
common_clientBasicData: Pagrindinė informacija
common_clientInvoiceData: Sąskaitos faktūros duomenys
loyaltyCards_confirmCustomerData: Patvirtinkite kliento sąskaitos faktūros duomenis
common_country: Šalis
loyaltyCards_currency: Valiuta
common_fullCustomerName: Visas susitariančiosios šalies pavadinimas
common_formName: Vardas
common_postCode: Pašto kodas
common_surname: Pavardė
common_fieldRequired: Reikalingas laukas
form.validation.file_max_size_mb: "Failo dydis neturi viršyti {size}MB"
loyaltyCards_infoValidKey: 'Turite naudoti "0" į "9" ir raides "a" į "f".'
loyaltyCards_infoValidKeyLength: 'BKF Key numeris turi būti 8 simbolių.'
loyalApp_invalidValue: 'Neteisinga reikšmė.'
loyaltyCards_phoneNumber: Neteisingas telefono numeris
loyaltyCards_topUpPositiveNumberOnly: 'Papildomas kiekis turi būti teigiamas.'
loyaltyCards_topUpValueToBig: 'Papildoma suma yra didelė.'
user_accountNumber: Banko sąskaitos numeris (IBAN)
user_contactEmail: Kontaktinis Elektroninis paštas
user_contactEmailTooltip: 'El. Paštas kontaktui klientams atsiųstų el. Laiškų poraštes.'
common_dataToInvoice: Sąskaitos faktūros duomenys
user_editInvoiceDataAlert: "Norėdami pakeisti užblokuotus laukus, susisiekite su mumis el. Pašto adresu:"
common_emailCopyEmail: 'Sąskaitos faktūros kopijų el. Pašto adresai'
common_invoiceCompanySettingsName: Visas įmonės pavadinimas
common_pressEnterToAddNew: 'Paspauskite Enter, kad pridėtumėte naują adresą'
common_taxNumber: Mokesčių identifikavimo numeris
loyaltyCards_additionalInfo: Papildoma informacija
loyaltyCards_invoiceSettings: Sąskaitų faktūrų nustatymai
loyaltyCards_invoiceNumerator: Sąskaitos numeris
common_logo: Logotipas sąskaitoje faktūroje
loyaltyCards_longMonthFormat: 'Mėnuo 01 forma (sausio mėn.)'
loyaltyCards_longYearFormat: Metai 2018 m
user_nextInvoiceNumber: Kitas sąskaitos faktūros numeris
loyaltyCards_notOwnerAlert: Tik automobilių plovyklos savininkas gali pakeisti sąskaitų faktūrų nustatymus
common_paymentPeriod: Mokėjimo terminas
loyaltyCards_settings: Nustatymai
loyaltyCards_shortMonthFormat: 'mėnuo 1 formate (sausio mėn.)'
loyaltyCards_shortYearFormat: Metai 18 formate
loyaltyCards_vatTax: PVM norma
common_loading: 'Įkeliama...'
subscription_logerPeriodBetterPrice: Ilgesnis laikotarpis yra geresnis kaina
contact_header: Atsijungti
contact_loggedOutMessage: 'Buvo išjungtas, nes baigėsi jūsų sesija. Prisijunkite dar kartą.'
loyalApp_balanceFleetMemberNotify: Laivynas
loyalApp_balanceFleetMemberTooltip: 'Sąskaitos nario parkus. Visos operacijos yra ralizowanie sąskaitos balansas laivyno valdytojas.'
loyalApp_fleetManagerActive: Użytkownik jest menadżerem floty
loyalApp_fleetManagerActiveMessage: 'Kliknięcie przełącznika oznaczy użytkownika jako zwykłe konto.'
loyalApp_fleetManagerInactive: Użytkownik nie jest menadżerem floty
loyalApp_fleetManagerInactiveMessage: 'Kliknięcie przełącznika oznaczy użytkownika jako konto flotowe.'
common_invoices: Faktury
loyal-app-manager.no-invoice-data-message: 'Nėra duomenų apie sąskaitų faktūrų, kreipkitės į savo riedmenų parko valdytojas, siekiant jas užbaigti.'
loyalApp_payments: Mokėjimai
loyalApp_promotionalCodes: Kody promocyjne
loyalApp_promotionalPackages: Pakiety promocyjne
loyalApp_regularUser: Zwykły użytkownik
loyalApp_trustedPartner: Zaufany partner
loyalApp_trustedPartnerActive: Użytkownik jest zaufanym partnerem
loyalApp_trustedPartnerActiveMessage: 'Kliknięcie przełącznika oznaczy użytkownika jako zwykłego. Doładowanie konta skarbonki bez dokonania natychmiastowej płatności będzie niemożliwe.'
loyalApp_trustedPartnerInactive: Użytkownik nie jest zaufanym partnerem
loyalApp_trustedPartnerInactiveMessage: 'Kliknięcie przełącznika oznaczy użytkownika jako zaufanego. Umożliwi to doładowanie salda przez użytkownika, bez dokonania natychmiastowej płatności. Faktura zostanie automatycznie wystawiona.'
loyalApp_usersList: Lista użytkowników
loyalApp_usersListHeading: Lojalumo programėlės naudotojų sąrašas
dashboard_heading: Lojalumo kortelės
dashboard_value: Vertė
loyalsystem-widget.values-name.cardsTopUpSumFromCM: CM programos populiariausių ir reklamų suma
loyalsystem-widget.values-name.cardsTopUpSumFromCarwash: Išleidimo viršaus ir akcijų suma
loyalsystem-widget.values-name.cardsUsedCount: Naudotų kortelių skaičius
loyalsystem-widget.values-name.transactionsPayments: Mokėjimo suma
loyalsystem-widget.values-name.transactionsTopups: Viršutinių patiekalų suma
loyaltyCards_loyaltyTopupsHistoryHeading: Lojalumo kortelės papildymo istorija
menu_administration: Administracija
common_administrationCarwashes: Skalbykla
menu_administrationUsers: Vartotojai
common_alarmActive: Allarmi attivi
common_alarmHistory: Signalizacijos istorija
common_chart: Parametru datu grafiks
loyaltyCards_clients: Klientai
menu_cmdashboard: Darbalaukis
common_companyData: Bendrovės duomenys
menu_finance: Finansai
common_financeCarwashRates: Automobilių plovimo įkainiai
menu.finance-fiscaltransactions: Fiskalinės operacijos
common_financeMobilePayments: Mokėjimai telefonu
common_financeTurnover: Apyvarta
menu_logout: Atsijungti
common_loyalAppManager: Lojalumo programa
common_loyalsystem: Lojalumo sistema
common_moneycollect: 'Pinigų rinkimas/koletorius'
common_processData: Proceso duomenys
menu_profile: Mano profilis
common_service: Paramos bilietai
menu_support: Support
common_users: Nariai
common_markRead: Atzīmēt kā lasītu
messages_message: Ziņa
menu_more: Vairāk
messages_noReadMessages: Nav lasītu ziņojumu
common_noUnreadMessages: Nav nelasītu ziņojumu
messages_read: Lasīt
messages_unread: Nelasīts
messages_when: Kad
common_mobilePaymentInvoicesDate: Data
common_download: Parsisiųsti
mobile_payment_invoices.heading: Sąskaitos faktūros
common_invoiceNumber: Sąskaitos numeris
common_period: Laikotarpį
common_valueGross: Bendroji vertė
finance_for: už
finance_from: nuo
finance_heading: Mobilieji mokėjimai
finance_carwash: Automobilių plovykla
finance_date: Data
finance_paymentType: Mokėjimo tipas
finance_standCode: Skalbimo stoties kodas
finance_total: Suma
common_totalOnPage: Iš viso dabartiniame puslapyje
finance_value: Vertė
finance_to: iki
loyaltyCards_cardNumber: Kortelės numerį
loyaltyCards_createNew: Sukurti naują BKF Key
loyaltyCards_creationHint: 'Jei BKF Card ir BKF Key yra ilgesnis nei 8 skaitmenys, turite perrašyti pirmuosius 8 raidinius ir skaitmeninius simbolius'
loyaltyCards_deleteHint: 'Atminkite, kad pašalintos kortelės pridėti negalite.'
loyaltyCards_hint: 'Išėmę kortelę, ji išnyks iš lojalumo kortelės ekrano sąrašo. Išimtos kortelės nebegalima pridėti prie sistemos.'
loyalApp_userEdit: Redaguoti vartotojas
loyaltyCards_emailInfo: 'Sąskaita faktūra bus išsiųsta jūsų el. pašto adresu.'
loyaltyCards_emailSend: 'Sąskaita faktūra bus išsiųsta nurodytu el. pašto adresu.'
modal.loyal_card_top_up_invoice.error.card_no_carwash: Susijusios automobilių plovyklos negalima rasti
modal.loyal_card_top_up_invoice.error.card_no_client: Kortelei nebuvo priskirtas klientas
modal.loyal_card_top_up_invoice.error.invoice_exists: Sąskaita faktūra jau egzistuoja sistemoje
modal.loyal_card_top_up_invoice.error.invoice_generation_failed: Nepavyko sukurti sąskaitos faktūros
modal.loyal_card_top_up_invoice.error.invoice_not_generated: Nepavyko sukurti sąskaitos faktūros
modal.loyal_card_top_up_invoice.error.invoice_send_failed: Nepavyko išsiųsti sąskaitos faktūros
modal.loyal_card_top_up_invoice.error.owner_no_country: Automobilių plovyklos savininkas neturi įsteigtos šalies
moneycollect.devices.CAR_WASH: Pozicija
moneycollect.devices.DISTRIBUTOR: Dozatorius
moneycollect.devices.VACUUM_CLEANER: Dulkių siurblys
finance_exchangerMoneyCollections: Pinigų surinkimo istorija
moneycollect.export.name: moneycollect
finance_moneycollectFor: už
finance_moneycollectFrom: nuo
finance_standMoneyCollections: Pozicijų rinkimas
finance_balance: Egyenleg
finance_bankCards: Kártyával fizetve
finance_details: Išsami informacija apie pinigų surinkimą
finance_emergencyDrops: Avariniai sąvartynai
finance_safe: Seifas
finance_sorted: Surūšiuota
finance_sucked: Paimta
finance_sumCash: Készpénzben fizetve
finance_sumHoppers: Kiengedve (hoppers)
finance_sumSale: Értékesítés
finance_sumSorted: Surūšiuota keisti ir saugiai
finance_sumSucked: Paimta iš plovyklos
finance_units: 'db.'
finance_unrecognized: Neatpažintas
finance_yeti: YETI kolekcijos informacija
finance_moneycollectTo: iki
finance_yetiMoneyCollections: YETI rinkimas
name: Pavadinimas
subscription_noCarwashAttached: Plovykla nėra pridėta šiam klientui
subscription_alertContent: 'Bendrovės duomenys nėra tinkamai užpildyti, prašome juos užpildyti.'
common_0: 0 dienų
common_1: 1 dienos
common_10: 10 dienų
common_14: 14 dienų
common_3: 3 dienos
loyalApp_30: 30 dienų
common_5: 5 dienos
common_7: 7 dienos
subscription_subscription: Apmokėjimas už prenumeratą
loyalApp_value: mokėjimų vertė
processData_chart: Diagramma
processData_parameter: Parametras
common_updateTime: Paskutinis atnaujinimas
processData_value: Vertė
common_itemOld: Vertė gali būti pasenusi
user_account: Vartotojo paskyra
user_baseData: Pagrindinė informacija
user_notificationNotice: 'Pranešimo funkcija, prieinama pagrindiniame pakete'
user_notificationsSettings: Pranešimų nustatymai
user_moneyCollectEmail: 'Siųsti pranešimus apie inkazaciją į el. Pašto adresą'
user_sendAlarmNotificationEmail: 'Aliarmo pranešimai el. Pašto adresu'
user_sendAlarmNotificationMobile: Pranešimai apie mobiliosios programos aliarmą
user_regionalSettings: Regioniniai nustatymai
user_reportsAndNotifications: Ataskaitos ir pranešimai
profile-configuration.reports-notice: El. Pašto ataskaitos funkcija, prieinama pagrindiniame pakete
profile-configuration.reports-settings: Ataskaitos nustatymai
profile-configuration.reports.fiscal-transactions: Fiskalinių sandorių ataskaita
profile-configuration.reports.mobile-payments: Mobilioji mokėjimo ataskaita
profile-configuration.reports.program-usage: Programos naudojimo ataskaita
profile-configuration.reports.used-funds: Fondų naudojimo ataskaita
user_error: Siųsti į "klaidos" lygį
user_info: Siųsti į "informacijos" lygį
user_warning: Siųsti į "įspėjimo" lygį
user_dontSend: Nesiųsti
profile-configuration.sending-options.periodic.daily: Siųsti kiekvieną dieną
profile-configuration.sending-options.periodic.monthly: Siųsti kiekvieną mėnesį
profile-configuration.sending-options.periodic.weekly: Siųsti kiekvieną savaitę
user_send: Siųsti
user_siteTitle: Mano profilis
finance_carwashUsage: Bekontakčių plovyklų naudojimas
programsusage.daily: Kasdienio naudoti
finance_programsusageFor: už
finance_programsusageFrom: nuo
common_title: Valandinis naudojimo grafikas
finance_overTimeTitle: Programų naudojimas laikui bėgant
finance_title: Programų procentinė dalis
finance_rolloverUsage: Portalo automobilių plovyklų naudojimas
programsusage.table.brush: Šepetys
programsusage.table.degreaser: Riebalų šalinimo priemonė
programsusage.table.foam: Putos
programsusage.table.glossing: Blizginimas
programsusage.table.mainwash: Milteliai
programsusage.table.prewash: Turbo
programsusage.table.program: Programa
programsusage.table.rims: Ratlankiai
programsusage.table.rinsing: Skalavimas
programsusage.table.sum: Suma
programsusage.table.wash: x plovimas
programsusage.table.wasxing: Vaškavimas
finance_programsusageTo: iki
common_total: Naudoti
service_button: Pranešti apie gedimą
service_text: Norėdami sukurti pagalbos bilietą, eikite į
service_serviceHeading: Paramos bilietai
service.status.desc.completed: Užbaigta
service.status.desc.open: Atviras
common_carwash: Automobilių plovykla
admin_issueId: ID
service_issueReportSource: Šaltinis
service_issueReportedBy: Pranešė
service_issueTitle: Tema
service_noContent: Nėra turinio
service_tableStatus: Statusas
service_time: Ataskaitos data
service.table.type: Tipas
subscription_state: statusas
subscription.carwash-type.STANDARD: Standartas
subscription.carwash-type.UNSUBSCRIBED: Be prenumeratos
subscription.carwash-type.WARRANTY: Garantija
subscription.table.STANDARD: '„Post -arrantty“ plovimai'
subscription.table.UNSUBSCRIBED: Automobilių plovyklos be prenumeratos
subscription.table.WARRANTY: Garantija plauna
subscription.table.discount: Nuolaida
subscription.table.position: Padėtis
subscription.table.price-after-discount: pardavimo kaina
subscription.table.price-before-discount: Kaina iki nuolaidos
subscription.table.sum: Kartu
subscription.table.summary: Santrauka
subscription.table.type: Tipas
admin_whoPays: 'Kas moka?'
subscriptions.actions.chose-and-pay: Pasirinkite ir sumokėkite prenumeratą
common_canceled: atšauktas
subscription_chose: Prenumeratos pasirinkimas
subscription_chose2: Pasirinkite prenumerata
subscriptions.delete-question: Ar norite atšaukti pasirinktą prenumeratą?
admin_clientDiscount: klientų nuolaida
subscription_goToDataCompletion: Pereikite prie duomenų pabaigos
subscription_historyList: istorija gyvenviečių
subscription_loyalcardsInvoicing: Sąskaitų faktūros ištikimos lojalumo kortelės
subscription_technicalData: Techniniai duomenys
common_paid: apmokėtas
subscription_paymentSummary: Atsiskaitymų suvestinė
subscription_priceForCarwashForMonth: Kaina už 1 mėnesį (grynoji kaina) 1 plovyklose
common_processing: apdorojimas
subscription_goToAbonamentLength: Eiti pasirinkti laikotarpį abonamentowania
subscription_orderWithPaymentObligation: Order with a payment obligation
subscriptions.subscription: prenumerata
subscription_subscriptionContent: Turinys prenumerata
subscription_toPay: mokėtinas
subscriptions.types.basic: Pagrindinis
subscriptions.types.free: Nemokamai
subscriptions.types.premium: Premija
subscription_yourSubscription: Jūsų prenumerata
loyalApp_account: Sąskaita
common_alarmId: Aliarmo identifikatorius
common_alarmLevel: Signalo lygis
common_all: Visi
loyalApp_averagePaymentValue: Vidutinė mokėjimo vertė
loyalApp_balance: Balansas
common_carwashes: Automobilių plovyklos
common_client: Klientas
table.company_name: Company name
common_tableCreateDate: Sukūrimo data
common_currency: Valiuta
loyaltyCards_cyclic: Cikliškas
common_dataUnknown: Trūksta duomenų
common_tableDate: Data
common_email: Email
admin_filters: Filtrai
common_firstName: Vardas
loyalApp_fleetManager: Laivyno valdytojas
common_invoice: Sąskaita faktūra
loyalApp_invoiceNumber: Invoice number
loyalApp_issuanceDate: Issuance date
common_language: Kalba
common_lastLogin: Paskutinis prisijungimas
dashboard_lastActualizaction: paskutinis atnaujinimas
common_lastName: Pavardė
common_lastUsage: Paskutinis naudojimas
loyalApp_licensePlate: Valstybinis numeris
common_noData: Nėra duomenų, kuriuos būtų galima rodyti
common_notFullySent: Nėra visiškai išsiųstas
loyalApp_payment: Mokėjimas
loyalApp_paymentCount: Mokėjimų skaičius
common_paymentDate: Payment date
loyaltyCards_payments: Mokėjimo
common_roles: Vaidmenys
common_rowsPerPage: "Eilutės puslapyje:"
loyalApp_salesDocument: Pardavimų dokumentas
common_search: Paieška
common_sendToCard: Išsiųsta
common_stand: Pozicija
common_state: Statusas
loyalApp_sumPaymentValue: Bendra mokėjimų vertė
common_timezone: Laiko juosta
common_toSent: 'Laukimas. \ T'
loyaltyCards_topup: Iš papildymo vertė
loyalApp_topupBonus: '„TopUp“ premija'
common_topupCode: Paaukštinimo kodas
common_topupSent: Įkrauti
loyalApp_tableTrustedPartner: Patikimas partneris
common_type: Įveskite
common_user: Vartotojas
common_username: Username
common_value: Vertė
loyaltyCards_valueAfterTransaction: Balansas po sandorio
loyaltyCards_tableVirtualCard: Virtualioji kortelė
finance_minutes: min
transactions.balance_adjustment: Koregavimas
transactions.car_wash: Iš automobilių plovimo
transactions.distributor: Izplatītājs
transactions.export_error: Problema generuojant csv
transactions.history: Istorija sandorių
transactions.history_for_card: Kortelės operacijų istorija
transactions.internet: Iš interneto
transactions.money_changer: Iš pinigų keityklos
transactions.payment: Mokėjimas
transactions.payments: Mokėjimai
transactions.promotions: Promozione
transactions.refill_for: Papildyti kortelę su
transactions.topup: Perkrauti
transactions.topup_card_history: Kortelės papildoma istorija
transactions.topup_history: Populiariausios istorija
transactions.topups: Pakartotinai
transactions.unknown: Nezināms
transactions.vacuum_cleaner: Dulkių siurblys
turnover.devices.distributor.plural: Dozatoriai
turnover.devices.distributor.singular: Dozatorius
turnover.devices.stand.plural: Pozicijas
turnover.devices.stand.singular: Pozicija
turnover.devices.vacuum.plural: Dulkių siurbliai
turnover.devices.vacuum.singular: Dulkių siurblys
turnover.exchanger: Keitiklis
finance_exportName: turnover
common_filtersCarwash: Automobilių plovykla
common_deviceType: Įrenginio tipas
finance_turnoverFor: už
finance_turnoverFrom: nuo
finance_fromLastCollection: iš paskutinio pinigų surinkimo
finance_compareWithPreviousYear: Palyginkite su ankstesniais metais
finance_linechartTitle: Apyvarta laikui bėgant
finance_ofAllCarwashes: visos automobilių plovyklos
finance_ofCarwash: 'automobilių plovykla #'
finance_paymenttypesharepieName: Mokėjimo tipų dalis
common_paymenttypesharepieTitle: Mokėjimo tipų dalis
turnover.table.balance: Balansas
turnover.table.bankCards: Banko kortelės
turnover.table.banknotes: Banknotai
turnover.table.bkfCardPay: BKF Card mokėjimas
turnover.table.bkfCardSale: BKF Card pardavimas
turnover.table.bkfKey: BKF Card
turnover.table.bkfKeyRecharge: BKF Card papildymas
turnover.table.carwash: Automobilių plovykla
turnover.table.carwashEarnings: Suma
turnover.table.carwashRecharge: Papildymo pozicijos
turnover.table.cash: Mokėjimai grynaisiais
turnover.table.cashless: Mokėjimai be grynųjų
turnover.table.clients: Klientai
turnover.table.coins: Monetos
turnover.table.counter: Naudojimo būdų skaičius
turnover.table.date: Data
turnover.table.details: Automobilio plovyklos informacija
turnover.table.detailsRollover: Išsami informacija apie portalo automobilių plovyklą
turnover.table.exchanger: Informacija apie pinigų keitiklį
turnover.table.exchangerHoppers: Pinigų mainai
turnover.table.exchangerSale: Parduodu pinigų keitiklį
turnover.table.exchanges: Biržos
turnover.table.hopperA: Hopper A
turnover.table.hopperB: Hopper B
turnover.table.mobilePayments: Mobilusis
turnover.table.name: Įrenginys
turnover.table.paid: Mokama
turnover.table.post: Pinigų keitiklio papildymai
turnover.table.prepaid: Išankstinio apmokėjimo
turnover.table.programsSale: Programų pardavimas
turnover.table.promotion: Reklama
turnover.table.saleValue: Pardavimo vertė
turnover.table.sell: Parduoti
turnover.table.service: Paslauga
turnover.table.sum: Suma
turnover.table.terminal: Mokėjimo terminalo duomenys
turnover.table.tokens: Žetonai
turnover.table.total: Iš viso
common_daily: Dienos
finance_detailed: Išsamiai
common_monthly: Mėnesio
finance_tabsTotal: Bendra
finance_turnoverTitle: Apyvarta
finance_turnoverTo: iki
finance_turnover: apyvarta
loyalApp_discount: Nuolaida
loyalApp_max: Maksimalus
loyalApp_min: Minimumas
loyalApp_term: Mokėjimo terminas
subscription_whyThisPrice: 'Kodėl ši kaina ?'
subscription_whyThisPriceModalHint: 'Žemiau esančioje lentelėje pateiktas abonentinės sumos apskaičiavimas remiantis turimomis automobilių plovyklomis ir suteiktomis nuolaidomis.'
Czechy: Čekijos Respublika
loyalApp_no: Ne
loyalApp_yes: Taip
actions.add: 'Pridėti/Papildyti'
actions.add_code: Pridėti nuolaidos kodą
actions.add_multiple_key: Įtraukti kelias lojalumo korteles
actions.add_notification: Pridėti pranešimą
actions.add_package: 'Pridėti nuolaidos komplektą/paketą'
actions.cancel_send: Atšaukti siuntimą
actions.edit: Redaguoti
actions.export_pdf: Eksportuoti PDF
actions.invite_user: Pakviesti naudotoją
actions.postpone: Atidėti
actions.refill_card: Papildyti kortelę
actions.send: Siųsti
actions.send_file: Siųsti failą
actions.send_invitation: Siųsti kvietimą
actions.service: Aptarnavimas
actions.show: Rodyti
actions.show-more-details: Rodyti daugiau informacijos
actions.switch_user: Pakeisti vartotoją
actions.update: Saugoti
loyaltyCards_addedBy: Pridėjo
admin_allowSubscription: Prenumerata aktyvi
common_dealer: Pardavėjas
admin_subscribersDetails: Prenumeratoriaus informacija
admin_detailsButton: Informacija
admin_subscribersHeading: Visų prenumeratorių sąrašas
admin_isDealer: Pardavėjas
common_nip: Mokesčio numeris
admin_comment: Komentaras
admin_confirm: Patvirtinti
admin_information: Prenumeratos informacija
admin_save: Išsaugoti
administration.subscription.status.error: Klaida
administration.subscription.status.initiated_proforma: Proforma pradėta
admin_usersDetails: Vartotojų informacija
admin_subscriptionReport: Prenumeratos ataskaita
loyaltyCards_carwash: Automobilių plovykla
loyaltyCards_topUpActionSucceed: Papildymas pridėtas sėkmingai
loyaltyCards_cardFundsTooltip: Paskutinė informacija apie lėšas kortelėje atsiųsta iš automobilių plovyklos
loyaltyCards_foundsWaitingForSent: Lėšos laukia siuntimo
common_paymentMethod: Mokėjimo būdas
common_cash: Grynieji pinigai
common_transfer: Perkėlimas
client-modal.invoice.strategies.aggregated_month: Sukurkite mėnesio pabaigoje
dashboard.moneycollect.carwash: Automobilių plovykla
dashboard.moneycollect.title: Surinktų pinigų istorija
dashboard_noPermission: 'Nėra paskirtų leidimų. Norėdami priskirti, susisiekite su svėtainės administratoriumi'
admin_dealer: Pardavėjas
common_selected: Pasirinkta
user_deleteAccountInfo: 'Norėdami ištrinti savo paskyrą programoje, susisiekite su mumis el. paštu'
service_attachments: Priedai
loyaltyCards_invalidData: Neteisingi kortelės duomenys
loyalApp_problem: Klaida
loyalApp_codeUsed: Kodas panaudotas
loyalApp_ctime: Kūrimo laikas
loyalApp_groupName: Grupės pavadinimas
loyalApp_unusedCode: Kodas nenaudojamas
fiscal_transactions.source.INTERNET: Internetas
fiscal_transactions.source.SCRIPT: Serveris
fiscal_transactions.source.TERMINAL: Terminalas
fiscal_transactions.table.net: Suma
fiscal_transactions.table.vat: PVM
loyalApp_infoPositiveNumberOnly: 'vertė/kaina turi būti teigiamas skaičius'
loyalApp_infoValidNumberLength: vertė turi būti 4 simbolių ilgio
loyalApp_infoValidNumberLength19: vertė turi būti 4 simbolių ilgio
loyalApp_infoValidQuantity: Turite naudoti skaičius nuo 0 iki 9
login_min: Vertė per maža
login_passwordSame: Slaptažodžiai turi būti identiški
loyalApp_validDescriptionLength: Aprašą turi sudaryti daugiausiai 120 simbolių
invoiceCompanyData_logo: Logotipas
subscription_subscriptionBuyMissingData: 'Norėdami aktualizuoti įmonės sąskaitos faktūros duomenis, kreipkitės:'
user_invoiceSettingsAccountNumber: 'Banko sąskaitos numeris (IBAN)'
loyaltyCards_clientCardSummaryReport: Siųsti mėnesio pabaigos kortelės naudojimo ataskaitą
common_action: Veiksmas
admin_actionHistory: Probelmos istorija
common_administratorDetails: Administratoriaus duomenys
admin_alarmActive: 'Aktyvūs signalai (ši problema)'
admin_alarmActiveAllIssues: 'Aktyvūs signalai (Visos problemos)'
admin_alarmDetails: Probelmos išsamiau
admin_alarmId: Problemos ID
admin_alarmLevel: Problemos lygis
admin_allAlarmsForIssue: Neaktyvūs signalai (ši problema)
common_awc: AWC
common_awk: AWK
common_awp: AWP
common_carwashIp: IP
admin_carwashStatus: Automobilio plovimo būsena
common_close: Uždaryta
admin_closeAllCarwashes: Uždarytos visos automobilių plovyklos
predictiveMaintenance_comment: Komentaras
admin_contactDetails: Kontaktai ir užrašai
common_description: Pastaba
admin_deselectAllIssues: Panaikinkite visų problemų žymėjimą
admin_dueDate: Atidėti data
common_duedate: Atidėta
common_employeesDetails: Darbuotojų duomenys
admin_etime: Pabaigos data
common_issueHistory: Problemų istorija
common_issueQuantity: Problemos kiekis
admin_issuesList: Pavajojaus signalai
common_jr: Išsiųstas į JR
common_lastAlarm: Pavojaus signalų atnaujinimas
common_lastOnline: Mobilieji mokėjimai
common_lastSynchro: Sinchronizacijos atnaujinimas
common_lastTime: Paskutinis įvykis
admin_lastUser: Paskutinis vartotojas
common_lastAttendance: Paskutinis dalyvavimo patvirtinimas
admin_lastComment: Paskutinis komentaras
common_lastVisit: Paskutinis pabaigtas apsilankymas
common_management: Pateikimų valdymas
common_new: Naujas
common_nextVisit: Kitas planuojamas apsilankymas
common_none: Trūkumas
common_noteDetails: Įrenginio pastabos
common_open: Atidaryta
admin_openAllCarwashes: Atidarykite visas automobilių plovyklas
admin_otherIssues: Kitos problemos
admin_pastIssues: Uždarytos problemos
common_postpone: Išjunkite signalizacija iki dienos
common_postponeForOneHour: Atidėti 60 min
admin_previousIssue: Ankstesnės problemos pabaigos data
common_priority: Problemos prioritetas
common_quantity: Pavojaus signalų skaičius
common_refersTo: Taikoma pavojaus signalai
admin_selectAllIssues: Pasirinkti visas problemas
common_status: Būsena
login_email: Elektroninio pašto adresas
login_login: Prisijungti
login_loginFailure: Prisijungimo klaida
login_password: Slaptažodis
login_passwordForgot: Pamiršote slaptažodį
loyalApp_receipts: Kvitai
loyalApp_selfInvoices: Savarankiškas atsiskaitymas
loyal-app-manager.transactions: Sandoriai
loyalApp_client: Klientas
loyalApp_info: Informacija
loyalApp_limit: Limitai
loyalApp_stats: Statistika
loyalApp_confirmPaymentAndInvoie: 'Ar norite patvirtinti mokėjimą?'
common_alarmManagement: Pavojaus signalų valdymas
predictiveMaintenance_alarmsHistory: Pavojaus signalų istorija
menu_allSubscription: Prenumeratos
menu_subscribers: Prenumeratoriai
common_subscription: Prenumerata
common_by: Pagal
common_clickToConfirm: 'Spauskite, kad patvirtintumėte'
common_confirm: Patvirtinti
common_confirmHint: 'Ar norite patvirtinti sąskaitą faktūrą?'
common_confirmation: Patvirtinimas
common_confirmed: Patvirtinta
common_invoiceConfirmed: Sąskaitą patvirtino
loyalApp_issuerName: Išdavėjo pavadinimas
loyalApp_issuerVatId: Išdavėjo mokesčių ID
common_notConfirmed: Nepatvirtintas
mobile_payment_invoices.not-for-app: 'Svetainė neprieinama programai:'
mobile_payments.export.name: Mobilieji mokėjimai
loyaltyCards_errorDescription: Klaidos aprašymas
loyaltyCards_errorDetails: Informacija
loyaltyCards_fileLine: Eilutė faile
loyaltyCards_lastFileUploadErrors: Klaidos įkeltame faile
loyaltyCards_rechargeHint: 'Kiekvienas įkelto failo įrašas turi būti tokio formato: „kortelės numeris“; „papildymo vertę“'
loyaltyCards_rechargeInfoHint: Įkelkite CSV failą su lojalumo kortelių papildymu
loyaltyCards_rechargeMultiple: Papildyti kelias korteles?
user_hint: Ar tikrai norite ištrinti naudotoją?
loyalApp_addCode: Sukurkite naujus reklamos kodus
loyalApp_addPackage: Sukurkite naują reklaminį paketą
loyalApp_editPackage: Redaguoti paketą
modal.loyal_notifications.creation_hint: Pabandykite parašyti trumpą tekstą
modal.loyal_notifications.description: Tekstas klientams
modal.loyal_notifications.new: Naujas pranešimas
modal.loyal_notifications.preview: Pranešimo peržiūra
modal.loyal_notifications.title: Pavadinimas
number: 'Numeriai (NR.)'
user_others: Kiti
finance_programs: Programų naudojimas
programsusage.rollover_total: Iš viso
programsusage.table.water-awerage: Vidutinis vandens sunaudojimas
programsusage.table.water-usage: Bendras suvartojamo vandens kiekis
loyalApp_quantity: Kiekis
loyalApp_packageValue: Paketo vertė
finance_mtime: Modifikavimo data
finance_name: Automobilių plovyklos pavadinimas
finance_pause: Pristabdyta
finance_time: Laikas skaičiuojamas sekundėmis
finance_valueForCredit: Pasirodyti
loyalApp_createDate: Išdavimo data
loyalApp_number: Numeris
loyalApp_paymentDate: Mokėjimo diena
loyalApp_loyaltyAppUserTopups: Programėlės naudojimo istorija
loyalApp_showAppUsageHistory: Rodyti programėlės naudojimo istoriją
login_backToLogin: Grįžti į prisijungimą
login_forgotSuccess: 'Naujo slaptažodžio nuoroda išsiųsta Jūsų el. Paštu'
login_reset: Atstatyti slaptažodį
login_resettingBackToLogin: Grįžti į prisijungimą
login_changePassword: Pakeisti slaptažodį
login_confirmPassword: Pakartokite slaptažodį
login_changeSuccess: 'Sėkmė! Jūsų slaptažodis buvo pakeistas'
login_tokenExpired: Slaptažodžio atnaujinta nuoroda nebegalioja
login_newPassword: Naujas slaptažodis
admin_subscriptionListHeading: Prenumeratų sąrašas
admin_subscriptionDealer: Pardavėjas
common_document: Dokumentas
admin_dontIssue: Neišduoti
admin_issue: Išduoti
admin_issueAndSend: Išduoti ir išsiųsti
admin_subscriber: Abonentas
subscription_back: Atgal
subscription_chosePaymentPeriod: Pasirinkti mokėjimo laikotarpį
common_subscriptionsEnddate: Pabaigos data
subscription_alarmsList: Signalų sąrašas
subscription_cyclicFiscalMailReports: 'Periodiškai el. paštu finansinės ataskaitos'
subscription_loyalcardsCyclicTopup: Periodinis lojalumo kortelių papildymas
subscription_loyalcardsRemoteTopups: Nuotolinis plovimo vietų papildymas
subscription_loyalcardsReport: Klientų lojalumo kortelių naudojimo ataskaita
subscription_loyalcardsTransactionHistory: Automobilių plovykloje operacijų istorija ir išsami fiskalinė informacija
subscriptions.options.loyalcards-transactions-history: 'CRM - operacijų istorija, nuotolinis papildymas, lojalumo kortelių užraktai'
subscription_mailReports: 'Ataskaitos el. Paštu'
subscription_mobileAppAccess: Prieiga per mobiliąją programėlę
subscription_moneycollectControll: 'Iš plovyklos grynųjų pinigų surinkimas - kontrolė (grynųjų pinigų ataskaitos)'
subscription_newAlarmNotifications: 'Pranešimai apie naujus signalus plovykloje (el.paštu)'
subscription_serviceTicketPreview: Peržiūrėkite paslaugų užklausų būseną
subscription_unlimitedUsers: Neribotas vartotojų skaičius ir asmens teisių paskirstymas
subscription_usageStatistics: Plovimo programa ir automobilių plovimo naudojimo statistika
subscription_wwwAccess: Priega prie interneto naršyklės
common_price: Kaina
common_refund: Pinigų grąžinimas
common_startDate: Nuo
subscription_checkInvoiceData: Patikrinkite sąskaitos faktūros duomenis
subscription_summary: Suvestinė
common_timeout: Laikas baigėsi
common_unknown: Nežinoma
admin_vat: PVM
common_success: Sėkmingai
loyalApp_accountType: Sąskaitos tipas
admin_added: Pridėta
common_alarmDetails: Signalizacijos detalės
loyaltyCards_cardFunds: Lėšos kortelėje
common_comment: Komentuoti
common_tableDescription: Aprašymas
common_discount: Nuolaida
loyalApp_externalId: Išorinis ID
loyalApp_externalType: Perdavimo tipas
loyalApp_externalValue: Perdavimo vertė
common_id: ID
loyalApp_managerEmail: 'Valdytojo e- mail'
admin_tableOwnerBkf: Savininko BKF
common_phone: Telefonas
loyalApp_promotionalCode: Reklaminis kodas
common_receipt: Kvitas
common_regonNumber: Kvito numeris
loyaltyCards_sum: Sumos
table.tax_number: Mokesčio numeris
loyalApp_title: Pavadinimas
loyaltyCards_toSend: Laukiama, kol bus išsiųsta
loyaltyCards_topUpsToSent: Papildymai laukia išsiuntimo
common_transactionType: Sandorio tipas
loyalApp_transactionValue: Sandorio vertė
loyalApp_vat: PVM
admin_whoAdded: Pridėjo
common_accept: Priimti
common_cancel: Atšaukti
common_termsHeading: 'PROGRAMĖLĖS NUOSTATAI „CAR WASH MANAGER“'
common_terms: Statusas
common_validFrom: 'Galioja nuo 2020m. Gegužės 25d'
transactions.top-up-code: Nuolaidos kodas
unknown: Nežinomas
finance_cubicMeters: 'm³'
finance_litres: l
common_userEmailAlreadyExist: 'Vartotojas su nurodytu el. Pašto adresu jau užregistruotas'
loyalApp_invitationInfo: 'Pakviestas naudotojas gali sumokėti už automobilio plovimą?'