loyalApp_no: <PERSON>ein
loyalApp_yes: Ja
actions.actions: Aktion
actions.actualize: Aktualisieren
actions.add_attachment: <PERSON><PERSON> hinz<PERSON>
actions.add_client: Kunden hinzufügen
actions.add_code: Gutscheine hinzufügen
actions.add_key: <PERSON><PERSON> hinzufügen
actions.add_multiple_key: Mehrere Karten aufladen
actions.add_notification: Meldung hinzufügen
actions.add_package: Promotionspaket hinzufügen
actions.add_user: Benutzer hinzufügen
actions.cancel: stornieren
actions.cancel_send: Senden stornieren
actions.chose: wählen
actions.clear: Löschen
actions.click_to_show_more_details: <PERSON><PERSON><PERSON>e hier, um Details anzuzeigen
actions.close: Schließen
actions.delete: Löschen
actions.download-cards-usage-report: Laden Sie den Kundkartenbericht herunter
actions.edit_user: Benutzer bearbeiten
actions.export: Export zu
actions.export_csv: Export CSV
actions.export_pdf: Export PDF
actions.export_summary: Export Zusammenfassung
actions.export_transactions: Export Trasaktionen
actions.export_xlsx: Export XLSX
actions.generate: Erzeugen Sie
actions.generate_and_download: Generieren und Herunterladen
actions.generate_invoice: Erstellen Sie eine Rechnung
actions.invite_user: Benutzer einladen
actions.lock: Blockieren
actions.refill: Aufladen
actions.refill_and_issue_invoice: Auffüllen und eine Rechnung ausgeben
actions.refill_card: Karte aufladen
actions.return_to_list: Zurück zur Liste
actions.save: Speichern
actions.save-and-add-other: Speichern und neue hinzufügen
actions.send: Senden
actions.send_file: Datei senden
actions.send_invitation: Einladung senden
actions.send_invoice: Rechnung senden
actions.show: zeigen
actions.show-more-details: Mehr Details zeigen
actions.show_doc: In die Dokumentation gehen
actions.unlock: Freigeben
common_address: Adresse
admin_country: Land
admin_details: Details der Autowäsche
admin_heading: Liste der gesamten Autowäsche in CM
common_owner: Eigentümer
admin_ownerBkf: Inhaber von BKF
admin_product: Produkt
admin_serialNumber: Seriennummer
admin_startDate: Anfangsdatum
admin_warrantyVoided: Ende der Garantie
admin_add: Fügen Sie ein Abonnement hinzu
admin_alreadyCancel: Das Abonnement wurde bereits storniert
admin_automaticPayment: Automatische Zahlung.
admin_cancel: Das Abonnement stornieren
admin_manualCancelNotPossible: Manuelle Stornierung
administration.subscription.status.canceled: Annulliert
administration.subscription.status.error: Fehler
administration.subscription.status.initiated: Verarbeitet
administration.subscription.status.manually_canceled: Abgesagt
administration.subscription.status.paid: Bezahlt
administration.subscription.status.unknown: Unbekannt
admin_usersHeading: Liste aller Benutzer in CM
admin_isOwner: Ist Besitzer
admin_lastLogin: Letzte Anmeldung
common_usersOwner: Eigentümer
admin_subscriptionCode: Aktive Abonnement
admin_subscriptionEnds: Ende des letzten Abonnements
common_createDate: Alarm Erstellungsdatum
common_duration: Alarmdauer
common_endDate: Alarmenddatum
loyalApp_trustedPartnerWithoutFleetManager: Normaler Benutzer kann nicht einen vertrauenen Flottenmanager zu werden. Nur der Flottenbenutzer kann einen vertrauenen Partner zu werden.
common_actionSucced: Aktion hat nicht geklappt
service_sending: Senden...
loyaltyCards_topUpActionSucceed: Aufladen war erfolgreich.
common_errorHeader: App Fehler
contact_message: Ein Fehler im App ist aufgetreten! Wenden Sie sich bitte an App Entwickler
dashboard_average: Bedeuten
dashboard_max: Max
dashboard_min: Minimum
dashboard_title: Umsatzstand
loyaltyCards_virtualCard: Virtuelle Karte
loyaltyCards_activeTopups: Aktive Aufpackungen
loyaltyCards_clientLockWarning: Manuelle Rechnungsstellung ist blockiert. Ändern Sie die Rechnungseinstellungen für den ausgewählten Kunden.
loyaltyCards_cyclicTopUpList: Aktive zyklische Aufpackungen
loyaltyCards_notSentList: Top -ups warten auf Senden
common_cardActive: Karte aktiv
loyaltyCards_activeMessage: Durch Anklicken des Schalters wird verhindert, dass die Karte in der Waschanlage verwendet werden kann. Der verfügbaren Gutschein und die Geschicht der Kartennutzung ändern sich nicht.
common_cardBlocked: Karte blockiert
loyaltyCards_blockedMessage: Durch klicken des Schalters kann die Karte wieder in der Waschanlage verwendet werden
loyaltyCards_card: Karte
loyaltyCards_cardFundsTooltip: Letzte Informationen über der Summe auf der Karte
common_cards: Karten
loyaltyCards_cardsList: Liste der Treuekarten
loyaltyCards_cleanHistoryAndBalance: Löschen Summe und Geschichte auf der Karte.
loyaltyCards_foundsWaitingForSent: Guthaben die auf Sendung warten
loyaltyCards_invoiceLoading: Rechnungserstellung im Gange...
loyaltyCards_invoiceSendLoading: Rechnung verschicken...
loyaltyCards_modifyTime: Änderungsdatum
loyaltyCards_name: Kartennamme
loyaltyCards_number: Kartennummer
loyaltyCards_removal: Karte löschen
loyaltyCards_source: Quelle
loyaltyCards_sourceStatus: Quelle / Status.
loyalApp_topup: Aufladen
common_topups: Aufladungen
card.transactions: Kartentransaktionen
card.transactions_list: Transaktionsverlauf der Kundenkarte
loyaltyCards_client: Klient
common_heading: Rechnungen
loyaltyCards_invoiceNumber: Rechnungsnummer
loyaltyCards_issuanceDate: Datum der Ausstellung
loyaltyCards_serviceDate: Zahlungsdatum
loyaltyCards_valueGross: Bruttowert
loyaltyCards_valueNet: Der Nettowert
carwash: Waschanlage
common_rollover: Portalwaschanlage
common_selfService: SB-Waschanlage
common_carwashSn: Autowäsche Seriennummer
finance_error: Fehler
loyalApp_qrCode: QR-Code
common_standCode: Positionscode
finance_stands: Steht
finance_topupDisabled: Auf dem neuesten Platz im Premium -Abonnement für eine Autowäsche, die mobile Zahlungen unterstützt.
finance_topup: Nachfüllen
finance_refillFor: Füllen Sie die Position Pater.
finance_topupStand: Die Position auffüllen
loyaltyCards_configuration: Aufbau
loyaltyCards_generateStrategy: Rechnungserzeugungsstrategie
common_paymentMethod: Mayment -Methode
common_cash: Kasse
common_transfer: Überweisen
client-modal.invoice.strategies.auto-after-top-up: Ausstellung nach dem Aufwärtsbewegung
client-modal.invoice.strategies.block: Nicht ausgeben
client-modal.invoice.strategies.manual: Auf Anfrage einrichten
client-modal.invoice.strategies.undefined: "-"
client-modal.payment-term.day.0: 0 Tage
client-modal.payment-term.day.1: 1 Tag
client-modal.payment-term.day.10: 10 Tage
client-modal.payment-term.day.14: 14 Tage
client-modal.payment-term.day.3: 3 Tage
client-modal.payment-term.day.30: 30 Tage
client-modal.payment-term.day.5: 5 Tage
client-modal.payment-term.day.7: 7 Tage
loyaltyCards_editClient: Kunden bearbeiten
loyaltyCards_heading: Kundenliste
loyaltyCards_invoicing: Fakturierung
loyaltyCards_comment: Kommentar
loyaltyCards_discount: Rabatt
loyaltyCards_endTime: Ende
common_cyclicTopUpsHeading: Zyklische Aufpackung
loyaltyCards_lastCal: Letzter Aufruf
loyaltyCards_startTime: Start
loyaltyCards_active: Aktiv
loyaltyCards_state: Status
loyaltyCards_cyclicAdd: Zyklisch für die Menge
loyaltyCards_cyclicAlign: Zyklische Aufladung zum Gleichgewicht
loyaltyCards_oneTime: Einmal
loyaltyCards_topUpType: Aufpackung Type
loyaltyCards_type: Typ
loyaltyCards_value: Wert
common_alarms: Alarmen
common_date: Datum
dashboard.moneycollect.CAR_WASH: Stellungen
dashboard.moneycollect.MONEY_CHANGER: Geldwechsler
dashboard.moneycollect.YETI: YETI
common_name: Name
common_noAlarms: Keine Alarme
dashboard.payment-share.amount: Betrag
dashboard.payment-share.bank_card: Bankkarten
dashboard.payment-share.bill: Banknoten
dashboard.payment-share.bkf_card: Kundenkarten
dashboard.payment-share.client: Kunden
dashboard.payment-share.clientCount: Anzahl von Kunden
dashboard.payment-share.coin: Münzen
dashboard_header: Zahlungsanteil
dashboard.payment-share.paymentType: Zahlungsart
dashboard.payment-share.percentage: Prozent
dashboard.payment-share.promotion: Beförderung
dashboard.payment-share.service: Bedienung
dashboard.payment-share.sum-payments: Zahlungssumme
dashboard.payment-share.token: Tokens
common_p14d: Letzte 14 Tage
common_p7d: Letzte 7 Tage
common_last: Seit der letzten Inkassierung
common_now: Heute
dashboard_previousMonth: Vorheriger Monat
common_sinceMonthStart: Seit Monatsbeginn
common_yesterday: Gestern
dashboard.subscription.component_header: Carwash Manager Subscription wird beendet
admin_subscription: Abonnement
dashboard.subscription.subscriptionClick: Wenn Sie Ihr Abonnement verlängern möchten, klicken Sie auf die Schaltfläche unten.
dashboard.subscription.subscriptionEnd: Die Subskription für Carwash Manager ist abgelaufen.
dashboard.subscription.subscriptionEndIn: Ihre Subskription für Carwash Manager wird abgelaufen
dashboard.subscription.subscriptionEndTitle: Das Abonnement ist abgelaufen
dashboard.subscription.subscriptionHelpText: "Bei Fragen wenden Sie sich bitte an die e-mailem: "
dashboard_sum: Summe
dashboard_summary: Zusammen
dashboard_yourCarwash: Deine SB Waschanlagen
date.length.12m: 12 Monaten
date.length.1m: 1 Monaten
date.length.3m: 3 Monaten
date.length.6m: 6 Monaten
finance_0: Sonntag
finance_1: Montag
finance_2: Dienstag
finance_3: Mittwoch
finance_4: Donnerstag
finance_5: Freitag
finance_6: Samstag
service_attachment: Anlage
service_description: Beschreibung der Anmeldung
service_heading: Neue Vorlage
service_close: Problemabschluss anfordern
service_closedAt: Geschlossen
service_createdAt: Erstellt
service_eventsClose: Antrag geschlossen
service_readyToClose: Der Kunde hat darum gebeten, dass das Problem geschlossen wird
service_start: Bericht erstellt
service_waitingForResponse: Warten auf die Antwort des Kunden
service_listHeading: Benachrichtigungen
service_status: Status
service_statusesClose: Geschlossen
service_new: Neu
service_open: Geöffnet
service_statusesReadyToClose: Bereit
service_waiting: Warten auf Reaktion
service_subject: Thema
service_user: Initiator
service_replySent: Ihre Antwort wurde gesendet
service_replySentProblem: Beim Senden Ihrer Antwort ist ein Problem aufgetreten
service_reportSent: Bericht über den Fehler gesendet
service_reportSentProblem: Problem beim Bericht Sendung aufgetreten
service_clientAdd: Problem beim Hinzufügen eines Kundes
service_financeData: Problem mit Finanzdaten
service_loyaltyCards: Problem mit Loyalitätskarten melden
service_other: Anderes Problem, das nicht mit der Waschanlage zusammenhängt
service_subscirption: Problem mit Abonnement
service_respond: Schreiben Sie Ihre Antwort...
service_errorReportSubject: Thema des Beitrags
contact_title: Unterstützung
loyalApp_accessDenied: Sie sind nicht berechtigt um den Vorgang durchzuführen.
loyaltyCards_addExisting: Die eingegebe Karte wurde schon im System registriert.
loyaltyCards_blockedTopup: Karte muss aktiv sein um das Konto aufzuladen.
loyaltyCards_invalidData: Falsche Kartendaten.
loyalApp_userAlreadyExists: Benutzer existiert nicht. Dieser Benutzer kann nicht eingeladen werden.
loyaltyCards_activity: Tätigkeit
service_allFeminine: Alle
loyaltyCards_blocked: Blockiert
loyaltyCards_cardType: Speicherkarten-Typ
loyalApp_codeUsed: Kode wurde benutzt
service_completed: Abgeschlossen
loyalApp_ctime: Errichtungszeit
filters.daterange.all: Die ganze Zeit
common_currentYear: Dieses Jahr
common_custom: Beliebig
common_previousMonth: Vorheriger Monat
common_previousYear: Voriges Jahr
common_today: Heute
common_daterangeYesterday: Gestern
admin_deselectAll: Alle deaktivieren
loyaltyCards_funds: Maßnahmen
loyalApp_groupName: Gruppenname
common_inPeriod: Im Zeitraum
common_inPeriodCustom: Wählen Sie einen Zeitraum aus
loyaltyCards_names: Namen
service_filtersOpen: Offen
loyaltyCards_regular: Physisch
admin_selectAll: Wählen Sie Alle
loyalApp_unusedCode: Nicht gebrauchte Kode
loyaltyCards_usedInPeriod: Während des Zeitraums verwendet
loyaltyCards_virtual: Virtuell
loyaltyCards_withFounds: Mit Summe
loyaltyCards_withInvoice: Ausgestellte Rechnung
loyaltyCards_withNames: Mit Summe
loyaltyCards_withoutFounds: Ohne Summe
loyaltyCards_withoutInvoice: Keine Rechnung
loyaltyCards_withoutNames: Ohne Namme
fiscal_transactions.details.heading: Details der Transaktion
fiscal_transactions.export.name: fiscal_transactions
fiscal_transactions.for: für
fiscal_transactions.from: von
fiscal_transactions.heading: Steuerliche Transaktionen
fiscal_transactions.modal_add.heading: Steuertransaktion hinzufügen
fiscal_transactions.modal_add.isu: ISU-Code
fiscal_transactions.modal_add.location: Standortcode
fiscal_transactions.modal_add.password: Passwort des Zertifikats
fiscal_transactions.source.CAR_WASH: Stellung
fiscal_transactions.source.DISTRIBUTOR: Zapfsäule
fiscal_transactions.source.MONEY_CHANGER: Geldwechsler
fiscal_transactions.source.UNKNOWN: Unbekannt
fiscal_transactions.source.VACUUM_CLEANER: Staubsauger
fiscal_transactions.table.carwash: Autowäsche
fiscal_transactions.table.date: Datum
fiscal_transactions.table.fiscal: Status
fiscal_transactions.table.type: Art der Transaktion
fiscal_transactions.table.value: Wert
fiscal_transactions.to: bis
fiscal_transactions.type.BANKCARDS: Zahlung per Bankkarte
fiscal_transactions.type.BANK_CARDS: Zahlung per Bankkarte
fiscal_transactions.type.CARWASH_MANAGER: Vom Eigentümer der Autowaschanlage manuell hinzugefügte Transaktion
fiscal_transactions.type.CASH: Bargeld
fiscal_transactions.type.CASHLESS: Bargeldlos
fiscal_transactions.type.CHARGE_BAY: Aufladen von Positionen aus dem Wechselautomaten
fiscal_transactions.type.COINS: Zahlung per Münzen
fiscal_transactions.type.HOPPER_A: Hopper A
fiscal_transactions.type.HOPPER_B: Hopper B
fiscal_transactions.type.LOYALTY_PAYING: Zahlung per Treuekarte
fiscal_transactions.type.LOYALTY_PROMO: Werbeaufladung einer Treuekarte
fiscal_transactions.type.LOYALTY_RECHARGE: Aufladen einer Kundenkarte
fiscal_transactions.type.LOYALTY_SELLING: Verkauf von Treuekarten
fiscal_transactions.type.MOBILE: Mobile Zahlungen
fiscal_transactions.type.NOTES: Zahlung mit Banknoten
fiscal_transactions.type.PROMO: Werbeaufladungen von Ständen
fiscal_transactions.type.SERVICE: Service-Impulse
fiscal_transactions.type.TOKENS: Zahlung mit Jetons
common_formAddress: Adresse
common_city: Stadt
common_clientBasicData: Basisdaten
common_clientInvoiceData: Die Daten für die Rechnung
loyaltyCards_confirmCustomerData: Bestätigen Sie die Angaben zur Rechnungsstellung Ihres Kunden
common_country: Land
loyaltyCards_currency: Währung
common_fullCustomerName: Vollständiger Name des Vertragspartners
common_formName: Vorname
common_postCode: Postleitzahl
common_surname: Nachname
common_fieldRequired: Feld erforderlich
form.validation.file_max_size_mb: "Dateigröße darf {size}MB nicht überschreiten"
loyalApp_infoPositiveNumberOnly: Wert soll + sein.
loyaltyCards_infoValidKey: 'Die erlaubten Zeichen sind Zahlen von "0" bis "9" und Buchstaben von "a" bis "f".'
loyaltyCards_infoValidKeyLength: BKF Schlüssel muss aus 8 Zeichnung bestehen.
loyalApp_infoValidNumberLength: Wert kann nicht mehr als 4 Zifern haben.
loyalApp_infoValidQuantity: 'Sie müssen Ziffern von "0" bis "9" nutzen.'
loyalApp_invalidValue: Wert ist falsch.
loyaltyCards_phoneNumber: Falsche Telefonnummer
loyaltyCards_topUpPositiveNumberOnly: Aufladung muss + Wert haben.
loyaltyCards_topUpValueToBig: Aufgeladene Wert ist zu hoch
loyalApp_validDescriptionLength: Beschreibung kann maximal 120 Zeichen haben.
user_accountNumber: Bankkontonummer (Iban)
user_contactEmail: Kontakt E-mail
user_contactEmailTooltip: E -Mail für Kontakt in der Fußzeile von E -Mails, die an Kunden gesendet werden.
common_dataToInvoice: Daten für die Rechnung
user_editInvoiceDataAlert: "Um die blockierten Felder zu ändern, kontaktieren Sie uns per E -Mail -Adresse:"
common_emailCopyEmail: E -Mail -Adressen von Rechnungskopien
common_invoiceCompanySettingsName: Vollständiger Name der Firma
common_pressEnterToAddNew: Drücken Sie die Eingabetaste, um eine neue Adresse hinzuzufügen
common_taxNumber: Steueridentifikationsnummer
loyaltyCards_additionalInfo: Zusätzliche Information
loyaltyCards_invoiceSettings: Rechnungseinstellungen
loyaltyCards_invoiceNumerator: Rechnungsnummer
common_logo: Logo auf der Rechnung
loyaltyCards_longMonthFormat: Monat im Format 01 (für Januar)
loyaltyCards_longYearFormat: Jahr im 2018 -Format
user_nextInvoiceNumber: Eine weitere Rechnung Nummer
loyaltyCards_notOwnerAlert: Nur der Besitzer der Autowäsche kann die Rechnungseinstellungen ändern
common_paymentPeriod: Zahlungsfrist
loyaltyCards_settings: Einstellungen
loyaltyCards_shortMonthFormat: Monat in Format 1 (für Januar)
loyaltyCards_shortYearFormat: Jahr im Format 18
loyaltyCards_vatTax: Mehrwertsteuerrate
common_loading: Wird geladen...
subscription_logerPeriodBetterPrice: Ein längerer Zeitraum ist ein besserer Preis
contact_header: Ausloggen
contact_loggedOutMessage: Sie wurden ausgelogt. Versuchen Sie sich bitte nochmal einloggen.
loyalApp_balanceFleetMemberNotify: Flotte
loyalApp_balanceFleetMemberTooltip: Konto des Flottenmitglieder. Alle Operationen werden auf dem Konto des Flottenbesitzers verbucht
loyalApp_fleetManagerActive: Benutzer ist Flotte Manager
loyalApp_fleetManagerActiveMessage: Wenn Sie darauf klicken, werden Sie als reguläres Konto markiert.
loyalApp_fleetManagerInactive: Benutzer wird keinen Flottenmanager
loyalApp_fleetManagerInactiveMessage: Wenn Sie darauf klicken, werden Sie als Flottenkonto markiert.
common_invoices: Rechnungen
loyal-app-manager.no-invoice-data-message: Keine Daten zur Rechnung. Wenden Sie sich bitte an den Flottenmanager um die Daten zu ergänzen.
loyalApp_payments: Zahlungen
loyalApp_promotionalCodes: Promotionskode
loyalApp_promotionalPackages: Promotionspaket
loyalApp_regularUser: Normaler Benutzer
loyalApp_trustedPartner: Vertrauer Partner
loyalApp_trustedPartnerActive: Benutzer ist keinen Vertrauen Partner
loyalApp_trustedPartnerActiveMessage: Wenn Sie darauf klicken, wird der Benutzer als normal markiert. Es ist nicht möglich, Ihr Spardosen-Konto aufzuladen, ohne eine sofortige Zahlung zu leisten.
loyalApp_trustedPartnerInactive: Benutzer ist keiner vertrauen Partner
loyalApp_trustedPartnerInactiveMessage: Wenn Sie darauf klicken, wird der Benutzer als normal markiert. Es ist nicht möglich, Ihr Spardosen-Konto aufzuladen, ohne eine sofortige Zahlung zu leisten. Rechnung wird automatisch ausgestellt.
loyalApp_usersList: Benutzerliste
loyalApp_usersListHeading: Liste der Nutzer der Treue-App
dashboard_heading: Kundenkarten
dashboard_value: Wert
loyalsystem-widget.values-name.cardsTopUpSumFromCM: Summe von Top -ups und Werbeaktionen aus der CM -Anwendung
loyalsystem-widget.values-name.cardsTopUpSumFromCarwash: Die Summe von Top -up und Werbeaktionen aus der Veröffentlichung
loyalsystem-widget.values-name.cardsUsedCount: Anzahl der verwendeten Karten
loyalsystem-widget.values-name.transactionsPayments: Zahlungssumme
loyalsystem-widget.values-name.transactionsTopups: Summe von Top -ups
loyaltyCards_loyaltyTopupsHistoryHeading: Historie des Aufladens von Treuekarten
menu_administration: Verwaltung
common_administrationCarwashes: Waschmaschine
menu_administrationUsers: Benutzer
common_alarmActive: Aktive Alarme
common_alarmHistory: Alarmen Geschichte
common_chart: Diagramm der Parameterdaten
loyaltyCards_clients: Kunden
menu_cmdashboard: Bedienpult
common_companyData: Daten des Unternehmens
menu_finance: Finanzen
common_financeCarwashRates: Waschpreise
menu.finance-fiscaltransactions: Steuerliche Transaktionen
common_financeMobilePayments: Mobile Zahlungen
common_financeTurnover: Umsatz
menu_logout: Ausloggen
common_loyalAppManager: Loyalitätsapp
common_loyalsystem: Loyalitätssystem
common_moneycollect: Inkasso
common_processData: Prozessdaten
menu_profile: Mein profil
common_service: Anfragen zur Betreuung
menu_support: Support
common_users: Benutzer
common_markRead: Als gelesen markieren
messages_message: Nachricht
menu_more: Mehr
messages_noReadMessages: Keine gelesenen Nachrichten
common_noUnreadMessages: Keine ungelesenen Nachrichten
messages_read: Gelesen
messages_unread: Ungelesen
messages_when: Wann
common_mobilePaymentInvoicesDate: Data
common_download: Herunterladen
mobile_payment_invoices.heading: Rechnungen
common_invoiceNumber: Rechnungsnummer
common_period: Zeitraum
common_valueGross: Bruttowert
finance_for: für
finance_from: von
finance_heading: Mobile Zahlungen
finance_carwash: Autowäsche
finance_date: Datum
finance_paymentType: Zahlungsart
finance_standCode: Waschplatz-Code
finance_total: Summe
common_totalOnPage: Summe auf aktueller Seite
finance_value: Wert
finance_to: bis
loyaltyCards_cardNumber: Kartennummer
loyaltyCards_createNew: Neue Loyalitätskarte erstellen
loyaltyCards_creationHint: Bei BKF-Karten und BKF-Schlüsseln, die mehr als 8 Ziffern haben, müssen die ersten 8 Zeichen abgeschrieben werden
loyaltyCards_deleteHint: Achtung, die gelöschte Karte kann nicht hinzugefügt werden.
loyaltyCards_rechargeHint: "Jede Zeile in hochgeladenen Datei muss das folgende Format haben: 'Kartennummer'; 'Aufladungswert'"
loyaltyCards_rechargeInfoHint: CSV Datei mit Loyalitätskartenaufladungen senden.
loyaltyCards_rechargeMultiple: Mehrere Kartenaufladen
loyaltyCards_hint: Karten löschen, die Karte wird nicht auf dem Bildschirm angezeigt. Die gelöschte Karte kann dem System nicht wieder hinzugefügt werden.
loyalApp_addCode: Neue Gutscheine generieren
loyalApp_addPackage: Neues Gutscheinpaket generieren
loyalApp_editPackage: Paket bearbeiten
loyalApp_userEdit: Benutzer bearbeiten
loyaltyCards_emailInfo: Die Rechnung wird an Ihre E-Mail Adresse geschickt
loyaltyCards_emailSend: Die Rechnung wird an die angegebene E-Mail Adresse geschickt
modal.loyal_card_top_up_invoice.error.card_no_carwash: Die zugehörige Autowaschanlage kann nicht gefunden werden
modal.loyal_card_top_up_invoice.error.card_no_client: Die Karte wurde keinem Kunden zugeordnet
modal.loyal_card_top_up_invoice.error.invoice_exists: Rechnung ist bereits im System vorhanden
modal.loyal_card_top_up_invoice.error.invoice_generation_failed: Rechnungserstellung fehlgeschlagen
modal.loyal_card_top_up_invoice.error.invoice_not_generated: Rechnungserstellung fehlgeschlagen
modal.loyal_card_top_up_invoice.error.invoice_send_failed: Rechnungsversand fehlgeschlagen
modal.loyal_card_top_up_invoice.error.owner_no_country: Besitzer einer Autowaschanlage hat kein Land eingerichtet
modal.loyal_notifications.creation_hint: Bilden Sie kurze Sätze
modal.loyal_notifications.description: Text an den Kunde geschickt
modal.loyal_notifications.new: Neue Mitteilung
modal.loyal_notifications.preview: Mitteilung-Übersicht
modal.loyal_notifications.title: Titel
moneycollect.devices.DISTRIBUTOR: Zapfsäule
moneycollect.devices.STANDS: Stellung
moneycollect.devices.VACUUM: Staubsauger
finance_exchangerMoneyCollections: Inkassogeschichte Geldwechsler
moneycollect.export.name: moneycollect
finance_moneycollectFor: für
finance_moneycollectFrom: von
finance_standMoneyCollections: Stellungen Inkassos
finance_balance: Bilanz
finance_bankCards: Mit Karte bezahlt
finance_details: Details zum Stellungen Inkasso
finance_emergencyDrops: Notfall-Dumps
finance_safe: Tresor
finance_sorted: Sortiert
finance_sucked: Heruntergeladen
finance_sumCash: Bar bezahlt
finance_sumHoppers: Freigegeben (Hoppers)
finance_sumSale: Verkäufe
finance_sumSorted: Sortiert zum Geldwechsler und Tresor
finance_sumSucked: Aus der Waschanlage geholt
finance_units: Stk.
finance_unrecognized: Unerkannt
finance_yeti: Details der YETI-Inkasso
finance_moneycollectTo: bis
finance_yetiMoneyCollections: YETI Inkassos
name: Name
subscription_noCarwashAttached: Keine Waschanlage mit dem Benutzer verbunden
subscription_alertContent: Die Daten des Unternehmens sind nicht korrekt abgeschlossen. Bitte füllen Sie sie aus.
number: LP.
subscription_subscription: Zahlung für Abonnement
loyalApp_value: Zahlungswert
processData_chart: Diagramm
processData_parameter: Parameter
common_updateTime: Letzte Aktualisierung
processData_value: Wert
common_itemOld: Wert kann veraltet sein
user_account: Benutzerkonto
user_baseData: Grundinformation
user_notificationNotice: Benachrichtigungsfunktion im Basispaket verfügbar
user_notificationsSettings: Benachrichtigungseinstellungen
user_moneyCollectEmail: Senden Sie Benachrichtigungen über Inkasation an die E -Mail -Adresse
user_sendAlarmNotificationEmail: Alarmbenachrichtigungen an die E -Mail -Adresse
user_sendAlarmNotificationMobile: Benachrichtigungen von Alarm für eine mobile Anwendung
user_regionalSettings: Regionale Einstellungen
user_reportsAndNotifications: Berichte und Benachrichtigungen
profile-configuration.reports-notice: E -Mail -Berichtsfunktion im Basispaket verfügbar
profile-configuration.reports-settings: Berichtseinstellungen
profile-configuration.reports.fiscal-transactions: Finanztransaktionsbericht
profile-configuration.reports.mobile-payments: Mobile Zahlungsbericht
profile-configuration.reports.program-usage: Programmnutzungsbericht
profile-configuration.reports.used-funds: Ein Bericht über die Verwendung von Mitteln
user_error: 'Senden Sie an die "Fehler" -Legelung'
user_info: 'Senden Sie an die Ebene "Information"'
user_warning: 'Senden Sie an die "Warnung"'
user_dontSend: nicht senden
profile-configuration.sending-options.periodic.daily: jeden Tag senden
profile-configuration.sending-options.periodic.monthly: jeden Monat senden
profile-configuration.sending-options.periodic.weekly: Jede Woche senden
user_send: senden
user_siteTitle: Mein Profil
finance_carwashUsage: Nutzung von berührungslosen Autowaschanlagen
programsusage.daily: Täglicher Verwendung
finance_programsusageFor: für
finance_programsusageFrom: von
common_title: Stündlicher Verwendungsplan
finance_overTimeTitle: Nutzung von Programmen im Laufe der Zeit
finance_title: Prozentsatz der Programme
finance_rolloverUsage: Nutzung von Portalwaschanlagen
programsusage.table.brush: Schaumbürste
programsusage.table.degreaser: Entfetter
programsusage.table.foam: Schaumkanone
programsusage.table.glossing: Glanzspülen
programsusage.table.mainwash: Hauptwäsche
programsusage.table.prewash: Vorwäsche
programsusage.table.program: Programm
programsusage.table.rims: Felgen
programsusage.table.rinsing: Klarpülen
programsusage.table.sum: Summe
programsusage.table.wash: x Waschen
programsusage.table.wasxing: Wachs
programsusage.table.water-awerage: Durchschnittlicher Verbrauch Wasser
programsusage.table.water-usage: Gesamtverbrauch Wasser
finance_programsusageTo: bis
common_total: Verwendung
loyalApp_quantity: Anzahl
loyalApp_packageValue: Paketwert
finance_mtime: Änderungsdatum
finance_name: Name der Autowaschanlage
finance_time: Rate-Ansicht in Sekunden/Puls
finance_valueForCredit: Zeigen in
service_button: Fehler melden
service_text: Um ein Support-Ticket zu erstellen, gehen Sie zu
service_serviceHeading: Anfragen zur Betreuung
service.status.desc.completed: Abgeschlossen
service.status.desc.open: Offen
common_carwash: Autowäsche
admin_issueId: ID
service_issueReportedBy: Berichtet von
service_issueTitle: Thema
service_noContent: Kein Inhalt
service_tableStatus: Status
service_time: Datum des Berichts
service.table.type: Typ
subscription_state: Status
subscription.carwash-type.STANDARD: Standard
subscription.carwash-type.UNSUBSCRIBED: Ohne Abonnement
subscription.carwash-type.WARRANTY: Garantie
subscription.table.STANDARD: Post -Warranty -Waschungen
subscription.table.UNSUBSCRIBED: Autowäsche ohne Abonnement
subscription.table.WARRANTY: Garantiewäsche
subscription.table.discount: Rabatt
subscription.table.position: Position
subscription.table.price-after-discount: Verkaufspreis
subscription.table.price-before-discount: Preis vor dem Rabatt
subscription.table.sum: Zusammen
subscription.table.summary: Zusammenfassung
subscription.table.type: Typ
admin_whoPays: Wer bezahlt ?
subscriptions.actions.chose-and-pay: Abonnement auswählen und bezahlen
common_canceled: Storniert
subscription_chose: Abonnement auswählen
subscription_chose2: Abonnement auswählen
subscriptions.delete-question: Möchten Sie das ausgewählte Abonnement kündigen?
admin_clientDiscount: Kundenrabatt
common_subscriptionsEnddate: Do
subscription_goToDataCompletion: Ergänzen Sie bitte die Daten
subscription_historyList: Abrechnungsgeschichte
subscription_alarmsList: Liste der Alarmmeldungen - aktive und historische
subscription_cyclicFiscalMailReports: Regelmäßige Finanzberichte per E-Mail
subscription_loyalcardsCyclicTopup: Periodische Aufladung von Treuekarten
subscription_loyalcardsInvoicing: Rechnungsstellung für Treuekarten
subscription_loyalcardsRemoteTopups: Fernaufladung von Waschboxen
subscription_loyalcardsReport: Aufstellungen zur Nutzung der Treuekarten von Endkunden
subscription_loyalcardsTransactionHistory: Transaktionshistorie der Waschanlage mit detaillierten steuerlichen Daten
subscriptions.options.loyalcards-transactions-history: CRM - Transaktionshistorie, Fernaufladung von Mittel, Sperrung von Treuekarten
subscription_mailReports: E-Mail-Berichte
subscription_mobileAppAccess: Zugang über die mobile App
subscription_moneycollectControll: Aufsicht über die Bargeldabholung bei Waschanlagen (Kassenstandsberichte)
subscription_newAlarmNotifications: Benachrichtigungen über neue Alarme in der Waschanlage (E-Mail, push)
subscription_serviceTicketPreview: Podgląd stanu zgłoszeń serwisowych
subscription_technicalData: Überblick über die aktuellen Funktionsparameter der Waschanlage
subscription_unlimitedUsers: Unbegrenzte Zahl von Nutzern und Zuweisung von individuellen Rechten
subscription_usageStatistics: Programme und Nutzungsstatistiken für Waschanlagen
subscription_wwwAccess: Zugang über einen Webbrowser
common_paid: Bezahlt
subscription_paymentSummary: Zahlungen Zusammenfassung
common_price: Brutto Preis
subscription_priceForCarwashForMonth: Preis für 1 Monat (Netto Preis) für 1 Waschanlage
common_processing: wird bearbeitet
common_startDate: Von
subscription_goToAbonamentLength: Gehen Sie zur Auswahl der Abonnementdauer
subscription_orderWithPaymentObligation: Ich bestelle mit Zahlungspflicht
subscriptions.subscription: Abonnement
subscription_subscriptionContent: Inhalt Abonnement
subscription_summary: Zusammen
subscription_toPay: zu zahlen
subscriptions.types.basic: Basic
subscriptions.types.free: Kostenlos
subscriptions.types.premium: Premium
subscription_yourSubscription: Dein Abonnement
summary: Zusammen
loyalApp_account: Konto
common_alarmId: Alarmkennung
common_alarmLevel: Alarmstufe
common_all: Alle
loyalApp_averagePaymentValue: Durchschnittlicher Zahlungswert
loyalApp_balance: Saldo
loyaltyCards_cardFunds: Summe auf der Karte
common_carwashes: Autowaschanlagen
common_client: Kunde
table.company_name: Firmenname
common_tableCreateDate: Errichtungsdatum
common_currency: Währung
loyaltyCards_cyclic: Zyklisch
common_dataUnknown: keine Daten
common_tableDate: Datum
common_tableDescription: Bechreibung
common_email: E-Mail Adresse
admin_filters: Filter
common_firstName: Vorname
loyalApp_fleetManager: Flottenmanager
common_invoice: Rechnung
loyalApp_invoiceNumber: Rechnungsnummer
loyalApp_issuanceDate: Austellungsdatum
common_language: Sprache
common_lastLogin: Letzte Anmeldung
dashboard_lastActualizaction: Letzte Aktualisierung
common_lastName: Nachname
common_lastUsage: Letzte Verwendung
loyalApp_licensePlate: Nummernschild
common_noData: Keine Daten werden aufgezeigt
common_notFullySent: Nicht vollständig gesendet
loyalApp_payment: Zahlung
loyalApp_paymentCount: Anzahl der Zahlungen
common_paymentDate: Zahlungsdatum
loyaltyCards_payments: Zahlungen
common_phone: Telefon
loyalApp_promotionalCode: Gutscheinkode
common_roles: Benutzerrollen
common_rowsPerPage: "Zeile auf der Seite:"
loyalApp_salesDocument: Verkaufsdokument
common_search: Suchen
common_sendToCard: Gesendet
common_stand: Stellung
loyalApp_sumPaymentValue: Gesamtwert der Zahlungen
table.tax_number: Steuernummer
common_timezone: Zeitzone
loyalApp_title: Titel
common_toSent: Warten
loyaltyCards_toSend: zu senden
loyaltyCards_topup: Aufladungswert
loyalApp_topupBonus: Aufladungsbonus
common_topupCode: Promotion -Code
common_topupSent: Aufladungen
loyalApp_tableTrustedPartner: Vertrauer Partner
common_type: Typ
common_user: Benutzer
common_username: Benutzername
common_value: Wert
loyaltyCards_valueAfterTransaction: Saldo nach der Trasaktionen
loyaltyCards_tableVirtualCard: Virtuelle Karte
finance_minutes: min
transactions.balance_adjustment: Ausgleich
transactions.car_wash: Aus der Waschanlage
transactions.distributor: Verteiler
transactions.export_error: Problem mit csv
transactions.history: Transaktiongeschichte
transactions.history_for_card: Kartentransaktionsgeschichte
transactions.internet: Internet
transactions.money_changer: Aus Geldwechsler
transactions.payment: Zahlung
transactions.payments: Zahlungen
transactions.promotions: Gutschein
transactions.refill_for: Kartenaufladung um
transactions.topup: Aufladung
transactions.topup_card_history: Karten-Aufladungsgeschichte
transactions.topup_history: Aufladungsgeschichte
transactions.topups: Aufladungen
transactions.unknown: Unbekannt
transactions.vacuum_cleaner: Staubsauger
turnover.devices.distributor.plural: Zapfsäulen
turnover.devices.distributor.singular: Zapfsäule
turnover.devices.stand.plural: Stellungen
turnover.devices.stand.singular: Stellung
turnover.devices.vacuum.plural: Staubsauger
turnover.devices.vacuum.singular: Staubsauger
turnover.exchanger: Geldwechsler
finance_exportName: turnover
common_filtersCarwash: Autowäsche
common_deviceType: Gerätetyp
finance_turnoverFor: für
finance_turnoverFrom: von
finance_fromLastCollection: vom letzten Geldeinzug
finance_compareWithPreviousYear: Vergleichen Sie mit dem Vorjahr
finance_linechartTitle: Umsatz im Zeitverlauf
finance_ofAllCarwashes: von alle Autowaschanlagen
finance_ofCarwash: 'von Autowaschanlage #'
finance_paymenttypesharepieName: Zahlungsarten teilen
common_paymenttypesharepieTitle: Zahlungsarten teilen
turnover.table.balance: Bilanz
turnover.table.bankCards: Bankkarten
turnover.table.banknotes: Banknoten
turnover.table.bkfCardPay: BKF-Kartenzahlung
turnover.table.bkfCardSale: BKF-Kartenverkauf
turnover.table.bkfKey: BKF Card
turnover.table.bkfKeyRecharge: BKF-Kartenaufladung
turnover.table.carwash: Autowäsche
turnover.table.carwashEarnings: Summe
turnover.table.carwashRecharge: Stellungen aufladen
turnover.table.cash: Barzahlungen
turnover.table.cashless: Bargeldlose Zahlungen
turnover.table.clients: Kunden
turnover.table.coins: Münzen
turnover.table.counter: Anzahl der Verwendungen
turnover.table.date: Datum
turnover.table.details: Details zur Waschanlage
turnover.table.detailsRollover: Details zum Portalwaschanlage
turnover.table.exchanger: Details zum Geldwechsler
turnover.table.exchangerHoppers: Geldwechsel
turnover.table.exchangerSale: Geldwechsler verkaufen
turnover.table.exchanges: Austausch
turnover.table.hopperA: Hopper A
turnover.table.hopperB: Hopper B
turnover.table.mobilePayments: Mobile
turnover.table.name: Gerätename
turnover.table.paid: Bezahlt
turnover.table.post: Aufladungen vom Geldwechsler
turnover.table.prepaid: Prepaid-Zahlungen
turnover.table.programsSale: Verkauf von Programmen
turnover.table.promotion: Förderung
turnover.table.saleValue: Verkaufswert
turnover.table.sell: Verkaufen
turnover.table.service: Dienst
turnover.table.sum: Summe
turnover.table.terminal: Angaben zum Zahlungsterminal
turnover.table.tokens: Token
turnover.table.total: Zusammen
common_daily: Täglich
finance_detailed: Ausführlich
common_monthly: Monatlich
finance_tabsTotal: Gesamt
finance_turnoverTitle: Umsatz
finance_turnoverTo: bis
finance_turnover: Umsatz
finance_cubicMeters: "m³"
finance_litres: l
loyalApp_discount: Rabatt
loyalApp_max: Maximal
loyalApp_min: Minimum
loyalApp_term: Zahlungszeit
loyalApp_invitationInfo: Der eingeladene Benutzer kann nicht aus dem Flottenkonto nutzen.
subscription_whyThisPrice: Wieso diese Preis?
subscription_whyThisPriceModalHint: Die folgende Tabelle zeigt die Berechnung des Abonnementbetrags auf der Grundlage der eigenen Waschanlagen und der gewährten Rabatte.