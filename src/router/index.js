import Vue from 'vue';
import VueRouter from 'vue-router';
import store from '@store';
import DashboardView from '@views/app/Dashboard.vue';
import LoginView from '@views/login/Login.vue';
import ResetPassword from '@views/login/ResetPassword.vue';
import Resetting from '@views/login/Resetting.vue';
import MessageList from '@views/app/messages/MessageList.vue';
import UserProfile from '@views/app/user/Profile.vue';
import ProcessData from '@views/app/process-data/ProcessData.vue';
import Users from '@views/app/users/Users.vue';
import Turnover from '@views/app/finance/Turnover.vue';
import MoneyCollect from '@views/app/finance/MoneyCollect.vue';
import MobilePayments from '@views/app/finance/MobilePayments.vue';
import ProgramsUsage from '@views/app/finance/ProgramsUsage.vue';
import FinanceReports from '@views/app/finance/Reports.vue';
import FiscalTransactions from '@views/app/finance/FiscalTransactions.vue';
import CarwashRates from '@views/app/finance/CarwashRates.vue';
import LoyalSystem from '@views/app/LoyalSystem.vue';
import LoyalApp from '@views/app/LoyalApp.vue';
import Service from '@views/app/Service.vue';
import Contact from '@views/app/Contact.vue';
import SubscriptionPage from '@views/app/SubscriptionPage.vue';
import InvoiceCompanyData from '@views/app/InvoiceCompanyData.vue';
import DeleteAccountInfo from '@views/DeleteAccountInfo.vue';
import FranchiseTabPage from '@views/app/finance/FranchiseTab.vue';

Vue.use(VueRouter);

const routes = [
  {
    path: '/',
    name: 'dashboard',
    component: DashboardView,
    meta: { requiresAuth: true },
  },
  {
    path: '/login',
    name: 'login',
    component: LoginView,
  },
  {
    path: '/reset-password',
    name: 'reset',
    component: ResetPassword,
  },
  {
    path: '/resetting',
    name: 'resetting',
    component: Resetting,
  },
  {
    path: '/messages',
    name: 'messages_list',
    component: MessageList,
  },
  {
    path: '/user/profile',
    name: 'user_profile',
    component: UserProfile,
  },
  {
    path: '/process-data',
    name: 'process_data',
    component: ProcessData,
  },
  {
    path: '/users',
    name: 'users',
    component: Users,
  },
  {
    path: '/finance/turnover',
    name: 'finance_turnover',
    component: Turnover,
  },
  {
    path: '/finance/moneycollect',
    name: 'finance_moneycollect',
    component: MoneyCollect,
  },
  {
    path: '/finance/mobile_payments',
    name: 'finance_mobile_payments',
    component: MobilePayments,
  },
  {
    path: '/finance/franchise',
    name: 'franchise',
    component: FranchiseTabPage,
  },
  {
    path: '/finance/programs_usage',
    name: 'finance_programsusage',
    component: ProgramsUsage,
  },
  {
    path: '/finance/fiscal_transactions',
    name: 'finance_fiscaltransactions',
    component: FiscalTransactions,
  },
  {
    path: '/finance/carwash_rates',
    name: 'finance_carwash_rates',
    component: CarwashRates,
  },
  {
    path: '/finance/reports',
    name: 'finance_reports',
    component: FinanceReports,
  },
  {
    path: '/loyal-system',
    name: 'loyal_system',
    component: LoyalSystem,
  },
  {
    path: '/loyal-app',
    name: 'loyal_app',
    component: LoyalApp,
  },
  {
    path: '/admin/carwashes',
    name: 'admin_carwashes',
    component: () => import(/* webpackChunkName: "admin" */ '@views/app/admin/Carwashes.vue'),
  },
  {
    path: '/admin/carwashes/:serialNumber',
    name: 'admin_carwash_details',
    component: () => import(/* webpackChunkName: "admin" */ '@views/app/admin/Carwash.vue'),
  },
  {
    path: '/admin/users',
    name: 'admin_users',
    component: () => import(/* webpackChunkName: "admin" */ '@views/app/admin/Users.vue'),
  },
  {
    path: '/admin/subscribers',
    name: 'admin_subscribers',
    component: () => import(/* webpackChunkName: "admin" */ '@views/app/admin/Subscribers.vue'),
  },
  {
    path: '/admin/users/:id',
    name: 'admin_users_details',
    component: () => import(/* webpackChunkName: "admin" */ '@views/app/admin/User.vue'),
  },
  {
    path: '/admin/subscribers/:id',
    name: 'admin_subscribers_details',
    component: () => import(/* webpackChunkName: "admin" */ '@views/app/admin/Subscriber.vue'),
  },
  {
    path: '/service',
    name: 'service',
    component: Service,
  },
  {
    path: '/contact',
    name: 'contact',
    component: Contact,
  },
  {
    path: '/subscription',
    name: 'subscription',
    component: SubscriptionPage,
  },
  {
    path: '/subscription/:paymentId',
    name: 'subscription_payment',
    component: SubscriptionPage,
  },
  {
    path: '/invoice_company_data',
    name: 'invoice_company_data',
    component: InvoiceCompanyData,
  },
  {
    path: '/admin/subscriptions_all',
    name: 'subscriptions_all',
    component: () => import(/* webpackChunkName: "admin" */ '@views/app/admin/SubscriptionsAll.vue'),
  },
  {
    path: '/delete_account_info',
    name: 'delete_account_info',
    component: DeleteAccountInfo,
  },
  {
    path: '/predictive_maintenance/issues',
    name: 'issues',
    component: () => import(/* webpackChunkName: "admin" */ '@views/app/predictive-maintenance/Issues.vue'),
  },
  {
    path: '/predictive_maintenance/issues_history',
    name: 'issues_history',
    component: () => import(/* webpackChunkName: "admin" */ '@views/app/predictive-maintenance/AlarmsHistory.vue'),
  },
  {
    path: '/predictive_maintenance/all_issues',
    name: 'all_issues',
    component: () => import(/* webpackChunkName: "admin" */ '@views/app/predictive-maintenance/IssuesList.vue'),
  },
  {
    path: '/predictive_maintenance/activity',
    name: 'activity',
    component: () => import(/* webpackChunkName: "admin" */ '@views/app/predictive-maintenance/ActivityList.vue'),
  },
  {
    path: '/service_new/dashboard',
    name: 'service_dashboard',
    component: () => import(/* webpackChunkName: "admin" */ '@views/app/service/DashboardPage.vue'),
  },
  {
    path: '/service_new/devices',
    name: 'service_devices',
    component: () => import(/* webpackChunkName: "admin" */ '@views/app/service/DevicesPage.vue'),
  },
  {
    path: '/service_new/clients',
    name: 'service_clients',
    component: () => import(/* webpackChunkName: "admin" */ '@views/app/service/ClientsPage.vue'),
  },
  {
    path: '/service_new/issues',
    name: 'service_issues',
    component: () => import(/* webpackChunkName: "admin" */ '@views/app/service/IssuesPage.vue'),
  },
  {
    path: '/service_new/tasks',
    name: 'service_tasks',
    component: () => import(/* webpackChunkName: "admin" */ '@views/app/service/TasksPage.vue'),
  },
  {
    path: '/service_new/visits',
    name: 'service_visits',
    component: () => import(/* webpackChunkName: "admin" */ '@views/app/service/VisitsPage.vue'),
  },
  {
    path: '/service_new/visit/:id',
    name: 'service_visit_details',
    component: () => import(/* webpackChunkName: "admin" */ '@views/app/service/VisitDetails.vue'),
  },
  {
    path: '/service_new/users',
    name: 'service_users',
    component: () => import(/* webpackChunkName: "admin" */ '@views/app/service/UsersPage.vue'),
  },
];

const router = new VueRouter({
  routes,
});

router.beforeEach((to, from, next) => {
  if (to.matched.some((record) => record.meta.requiresAuth)) {
    if (store.getters['auth/isLoggedIn']) {
      next();
      return;
    }
    next('/login');
  } else {
    // fix of redirecting to login if user is already login
    if (store.getters['auth/isLoggedIn'] && to.name === 'login') {
      next('/');
      return;
    }
    next();
  }
});

export default router;
